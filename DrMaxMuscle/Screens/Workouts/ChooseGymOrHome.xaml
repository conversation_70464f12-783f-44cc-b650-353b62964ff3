﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.Workouts.ChooseGymOrHome"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             Title="ChooseGymOrHome">
    <Grid
        Padding="0"
        Margin="0">
        <Grid
            Padding="15"
            RowSpacing="15">
        <Grid.RowDefinitions>
            <RowDefinition
            Height="*" />
            <RowDefinition
            Height="*" />
            <RowDefinition
            Height="*" />
        </Grid.RowDefinitions>

        <Frame
        Padding="0"
        CornerRadius="12"
        Grid.Row="0"
        HasShadow="False"
        IsClippedToBounds="True">
            <ffimageloading:CachedImage
            Source="top.png"
            Aspect="AspectFill" />
            <Frame.GestureRecognizers>
                <TapGestureRecognizer
                Tapped="GymWorkoutsButton_Clicked" />
            </Frame.GestureRecognizers>
        </Frame>
        <Frame
        Padding="0"
        CornerRadius="12"
        HasShadow="False"
        Grid.Row="1"
        IsClippedToBounds="True">
            <ffimageloading:CachedImage
            Source="middle.png"
            Aspect="AspectFill" />
            <Frame.GestureRecognizers>
                <TapGestureRecognizer
                Tapped="HomeWorkoutsButton_Clicked" />
            </Frame.GestureRecognizers>
        </Frame>
        <Frame
        Padding="0"
        CornerRadius="12"
        HasShadow="False"
        Grid.Row="2"
        IsClippedToBounds="True">
            <ffimageloading:CachedImage
            Source="bottom.png"
            Aspect="AspectFill" />
            <Frame.GestureRecognizers>
                <TapGestureRecognizer
                Tapped="BodyweightWorkoutsButton_Clicked" />
            </Frame.GestureRecognizers>
        </Frame>
        <t:DrMuscleButton
        x:Name="GymWorkoutsButton"
        Grid.Row="0"
        Style="{StaticResource highEmphasisButtonStyle}"
        BorderColor="Transparent"
        HorizontalOptions="Start"
        VerticalOptions="Start"
        HeightRequest="55"
        TextColor="White"
        BackgroundColor="Transparent"
        FontAttributes="None"
        Margin="15,0"
        Padding="0"
        FontSize="30">
        </t:DrMuscleButton>
        <t:DrMuscleButton
        x:Name="HomeWorkoutsButton"
        Grid.Row="1"
        Style="{StaticResource highEmphasisButtonStyle}"
        BorderColor="Transparent"
        HorizontalOptions="Start"
        VerticalOptions="Start"
        HeightRequest="55"
        TextColor="White"
        BackgroundColor="Transparent"
        FontAttributes="None"
        Margin="15,0"
        Padding="0"
        FontSize="30"></t:DrMuscleButton>
        <t:DrMuscleButton
        x:Name="BodyweightWorkoutsButton"
        Grid.Row="2"
        Style="{StaticResource highEmphasisButtonStyle}"
        BorderColor="Transparent"
        HorizontalOptions="Start"
        VerticalOptions="Start"
        HeightRequest="55"
        TextColor="White"
        BackgroundColor="Transparent"
        FontAttributes="None"
        Margin="15,0"
        Padding="0"
        FontSize="30"></t:DrMuscleButton>
        
    </Grid>
        <t:RightSideMasterPage Padding="0" IsVisible="false" Margin="0" x:Name="SlideMenu" VerticalOptions="FillAndExpand"/>
    </Grid>
</ContentPage>