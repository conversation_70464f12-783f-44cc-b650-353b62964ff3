﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.Workouts.ChooseYourCustomWorkoutPage"
             xmlns:helpers="clr-namespace:DrMaxMuscle.Helpers"
             xmlns:constnats="clr-namespace:DrMaxMuscle.Constants"
             xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             xmlns:effects="clr-namespace:DrMaxMuscle.Effects"
             Title="ChooseYourCustomWorkoutPage">
       <ContentPage.Resources>
        <ResourceDictionary>
            <helpers:NegateBooleanConverter x:Key="BooleanInverter" />
        </ResourceDictionary>
    </ContentPage.Resources>
    <ContentPage.Content>
    <AbsoluteLayout >
        

            <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,5,20,5" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
                <StackLayout BackgroundColor="Transparent" Padding="0" VerticalOptions="FillAndExpand">
                    <Label x:Name="LblMyWorkouts" Style="{StaticResource WorkoutLabelStyle}"  />
                    <Grid x:Name="workoutGrid" VerticalOptions="FillAndExpand">
                    <t:DrMuscleListView Grid.Row="0" x:Name="WorkoutListView" VerticalScrollBarVisibility="Never" Margin="0,0,0,5" BackgroundColor="Transparent" VerticalOptions="FillAndExpand" SeparatorColor="#264457" SeparatorVisibility="Default" ios:ListView.SeparatorStyle="FullWidth">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <ViewCell Height="45">
                                    <StackLayout Orientation="Horizontal" BackgroundColor="Transparent">
                                        <Label Text="{Binding Label}" HorizontalOptions="StartAndExpand" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" />
                                        <StackLayout Orientation="Horizontal" HorizontalOptions="End">
                                            <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                            <t:DrMuscleButton Clicked="OnRename" Text="{Binding [Rename].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextRenameButton}"  />
                                            <t:DrMuscleButton Clicked="OnEdit" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}"  />
                                            <!--<t:DrMuscleButton Clicked="OnDelete" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />-->
                                            <t:DrMuscleButton Clicked="OnReset" Text="{Binding [More].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextResetButton}" />
                                            <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" IsVisible="{Binding IsSystemExercise, Converter={StaticResource BooleanInverter}}"  Style="{StaticResource ItemContextMoreButton}" />
                                        </StackLayout>
                                    </StackLayout>
                                </ViewCell>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </t:DrMuscleListView>
                    <StackLayout Grid.Row="0" x:Name="EmptyWorkouts" VerticalOptions="Start" IsVisible="false"  Spacing="10" Padding="0,25,0,2">

                <Image WidthRequest="70" HeightRequest="70" HorizontalOptions="Center" VerticalOptions="Start" Source="lists.png" />


                    <Label Text="No custom workout yet" Margin="0,5,0,0" HorizontalOptions="Center" FontSize="20" FontAttributes="Bold" TextColor="Black" />
                    <Label Text="Tap &quot;+&quot; to create your custom workout" HorizontalOptions="Center"  HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>

            </StackLayout>
                        </Grid>
                    <Label x:Name="LblMyPrograms" Style="{StaticResource WorkoutLabelStyle}" />
                    <Grid VerticalOptions="FillAndExpand">
                    <t:DrMuscleListView Grid.Row="0" x:Name="ProgramListView" VerticalScrollBarVisibility="Never" BackgroundColor="Transparent" VerticalOptions="FillAndExpand"  SeparatorColor="#264457" SeparatorVisibility="Default" ios:ListView.SeparatorStyle="FullWidth">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <ViewCell Height="45">
                                    <StackLayout Orientation="Horizontal" BackgroundColor="Transparent">
                                        <Label Text="{Binding Label}" HorizontalOptions="StartAndExpand" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" />
                                        <!--                                        </StackLayout>-->
                                        <StackLayout Orientation="Horizontal" HorizontalOptions="End">
                                            <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                            <t:DrMuscleButton Clicked="OnRename" Text="{Binding [Rename].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextRenameButton}"  />
                                            <t:DrMuscleButton Clicked="OnEdit" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}"  />
                                            <t:DrMuscleButton Clicked="OnDelete" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                            <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" IsVisible="{Binding IsSystemExercise, Converter={StaticResource BooleanInverter}}"  Style="{StaticResource ItemContextMoreButton}" />
                                        </StackLayout>
                                    </StackLayout>
                                </ViewCell>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                        <!--<ListView.Footer>
                            <Border HeightRequest="100" BackgroundColor="Transparent" />
                        </ListView.Footer>-->
                    </t:DrMuscleListView>
                    
                <StackLayout Grid.Row="0 " x:Name="EmptyProgram" VerticalOptions="Start"  IsVisible="false"  Spacing="10" Padding="0,25,0,2">

                <Image WidthRequest="70" HeightRequest="70" HorizontalOptions="Center" VerticalOptions="Start" Source="lists.png" />


                    <Label Text="No custom program yet" Margin="0,5,0,0" HorizontalOptions="Center" FontSize="20"  FontAttributes="Bold" TextColor="Black" />
                    <Label x:Name="LbEmptyProgram" Text="Tap &quot;+&quot; to create your custom program" HorizontalOptions="Center"  HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>

            </StackLayout>
                    <!--<t:DrMuscleButton x:Name="WorkoutsButton" Text="New! Create on the Web" Style="{StaticResource buttonStyle}" Margin="0,15,0,0" />-->
                        </Grid>
                </StackLayout>


               




            </StackLayout>
        
        <!--<Image x:Name="PlusIcon" Margin="0,0,20,20" HeightRequest="70" WidthRequest="70" VerticalOptions="Center" HorizontalOptions="Center" Aspect="AspectFit" AbsoluteLayout.LayoutFlags="PositionProportional" AbsoluteLayout.LayoutBounds="1, 1, 90, 90" effects:TooltipEffect.Text="Tap me"
                                effects:TooltipEffect.BackgroundColor="{x:Static app:AppThemeConstants.ReysBlueColor}"
                                effects:TooltipEffect.TextColor="White"
                                effects:TooltipEffect.Position="Top"
                                effects:TooltipEffect.HasTooltip="True"
                                effects:TooltipEffect.HasShowTooltip="False" />
        <Image Source="plusblack.png" x:Name="PlusIcon2" Margin="0,0,20,20" HeightRequest="70" WidthRequest="70" VerticalOptions="Center" HorizontalOptions="Center" Aspect="AspectFit" AbsoluteLayout.LayoutFlags="PositionProportional" AbsoluteLayout.LayoutBounds="1, 1, 90, 90">
            <Image.GestureRecognizers>
                <TapGestureRecognizer Tapped="NewTapped" />
            </Image.GestureRecognizers>

        </Image>-->

            <Frame x:Name="PlusIcon"
                   BorderColor="Transparent"
                   Padding="0"
                   Style="{StaticResource GradientStackStyleBlue}"
                   Margin="20,10,20,20"
                   CornerRadius="6"
                   HeightRequest="68"
                   HorizontalOptions="FillAndExpand"
                   VerticalOptions="End"
                   AbsoluteLayout.LayoutFlags="All"
                   AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
                <Frame.GestureRecognizers>
                    <TapGestureRecognizer Tapped="NewTapped" />
                </Frame.GestureRecognizers>
                <t:DrMuscleButton Text="CREATE CUSTOM WORKOUT"
                                  x:Name="PlusIcon2"
                                  Style="{StaticResource highEmphasisButtonStyle}"
                                  VerticalOptions="Fill"
                                  HorizontalOptions="FillAndExpand"
                                  BackgroundColor="Transparent"
                                  CornerRadius="6"
                                  HeightRequest="68"
                                  FontSize="{x:Static constnats:AppThemeConstants.CapitalTitleFontSize}"
                                  BorderColor="Transparent"
                                  TextColor="White"
                                  Clicked="NewTapped" />
            </Frame>
            <StackLayout x:Name="ActionStack" IsVisible="false" BackgroundColor="#55000000" VerticalOptions="FillAndExpand" Padding="20,5,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
            <StackLayout VerticalOptions="EndAndExpand">
                <Frame
                    BorderColor="Transparent"
                    Style="{StaticResource GradientFrameStyleBlue}"
                    Padding="0"
                    Margin="0,15,0,0"
                    HorizontalOptions="FillAndExpand" CornerRadius="6"  >

                    <t:DrMuscleButton x:Name="workoutButton"  TextTransform="Uppercase" Text="Custom workout" Style="{StaticResource highEmphasisButtonStyle}"  BackgroundColor="Transparent" VerticalOptions="End" />
                </Frame>
                <Frame
                    Margin="0,8"
                    BorderColor="Transparent"
                    Padding="0"
                    Style="{StaticResource GradientFrameStyleBlue}"
                    HorizontalOptions="FillAndExpand" CornerRadius="6"  >

                    <t:DrMuscleButton x:Name="programButton"  TextTransform="Uppercase" Text="Custom program" Style="{StaticResource highEmphasisButtonStyle}" BorderWidth="0" BorderColor="Transparent" BackgroundColor="Transparent" VerticalOptions="End" Margin="0,0,0,0" />
                </Frame>
                <Frame
                    BorderColor="Transparent"
                    Padding="0"
                    Style="{StaticResource GradientFrameStyleBlue}"
                    HorizontalOptions="FillAndExpand"
                    CornerRadius="6"  >
 
                    <t:DrMuscleButton x:Name="UploadButton"  TextTransform="Uppercase" Text="Upload program" Style="{StaticResource highEmphasisButtonStyle}" BorderWidth="0" BorderColor="Transparent" BackgroundColor="Transparent" VerticalOptions="End" Margin="0,0,0,0" />
                </Frame>
                <!--<Image Source="plusblack.png"  Margin="0,0,0,20" HeightRequest="70" WidthRequest="70" VerticalOptions="End" HorizontalOptions="End" Aspect="AspectFit"  >
                    <Image.GestureRecognizers>
                        <TapGestureRecognizer Tapped="NewTapped" />
                    </Image.GestureRecognizers>
                </Image>-->
                    <Frame Margin="0,10,0,20"
                           BorderColor="Transparent"
                           IsClippedToBounds="true"
                           Padding="0"
                           VerticalOptions="End"
                           HorizontalOptions="FillAndExpand"
                           HeightRequest="68"
                           Style="{StaticResource GradientStackStyleBlue}"
                           CornerRadius="6">
                        <Frame.GestureRecognizers>
                            <TapGestureRecognizer Tapped="NewTapped" />
                        </Frame.GestureRecognizers>
                        <t:DrMuscleButton x:Name="createWorkoutButton"
                                          Text="CREATE CUSTOM WORKOUT"
                                          VerticalOptions="Fill"
                                          HeightRequest="68"
                                          CornerRadius="6"
                                          HorizontalOptions="FillAndExpand"
                                          Style="{StaticResource highEmphasisButtonStyle}"
                                          Clicked="DrMuscleButton_Clicked"
                                          BackgroundColor="Transparent"
                                          BorderColor="Transparent"
                                          FontSize="{x:Static constnats:AppThemeConstants.CapitalTitleFontSize}" />
                    </Frame>
                </StackLayout>
            <StackLayout.GestureRecognizers>
                <TapGestureRecognizer Tapped="NewTapped" />
            </StackLayout.GestureRecognizers>
        </StackLayout>
         <!--<StackLayout x:Name="createWorkoutButton" IsVisible="false" HorizontalOptions="FillAndExpand" VerticalOptions="End" BackgroundColor="Transparent" Padding="20,5,20,20" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0,0,1,1">
            <StackLayout VerticalOptions="EndAndExpand" HorizontalOptions="FillAndExpand">
                <Frame
                    BorderColor="Transparent"
                    Padding="0"
                    Style="{StaticResource GradientFrameStyleBlue}"
                                 Margin="0,15,0,0"
                
                                     HorizontalOptions="FillAndExpand" CornerRadius="0"  >

                    
                    <t:DrMuscleButton Text="Create custom workout" HeightRequest="60" Style="{StaticResource highEmphasisButtonStyle}" Clicked="DrMuscleButton_Clicked" BackgroundColor="Transparent" VerticalOptions="End" />
                </Frame>
              
            </StackLayout>
            
        </StackLayout>-->
        
        
           
        </AbsoluteLayout>
        </ContentPage.Content>

</ContentPage>
