﻿using Acr.UserDialogs;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Layout;
using DrMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Constants;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.IO;
using Newtonsoft.Json;
using DrMaxMuscle.Screens.Exercises;
using System.Reflection;
using DrMaxMuscle.Effects;
using Newtonsoft.Json.Linq;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Views;
using RGPopup.Maui.Services;
using DrMaxMuscle.Utility;
using CommunityToolkit.Maui.Views;

namespace DrMaxMuscle.Screens.Workouts;

public partial class AddExercisesToWorkoutPage : ContentPage
{

    public ObservableRangeCollection<BodyPartSection> ExeList { get; set; }
        = new ObservableRangeCollection<BodyPartSection>();
    private List<ExerciceModel> exercises;
    public ObservableCollection<SelectableExerciceModel> exerciseItems = new ObservableCollection<SelectableExerciceModel>();
    public ObservableCollection<SelectableExerciceModel> exerciseItemsResult = new ObservableCollection<SelectableExerciceModel>();

    public ObservableCollection<SelectableExerciceModel> selectedItems = new ObservableCollection<SelectableExerciceModel>();
    public ObservableCollection<SelectableExerciceModel> selectedItemsResult = new ObservableCollection<SelectableExerciceModel>();

    public ObservableCollection<SelectableExerciceModel> favouriteItems = new ObservableCollection<SelectableExerciceModel>();

    AddUserExerciseModel newAddUserExercise;
    public ObservableCollection<SelectableExerciceModel> customItems = new ObservableCollection<SelectableExerciceModel>();
    public ObservableCollection<ExerciceModel> customItems1 = new ObservableCollection<ExerciceModel>();
    private string partTitle = "";
    private string partMessage = "";
    private bool partIsenabled = false;
    private bool _showTooltipOnSwitch = false;
    private bool _showTooltipOnToggle = false;
    private bool _showTooltipOnNext = false;
    private long? _exerciseId;
    private StackLayout firstSwitch;
    public AddExercisesToWorkoutPage()
    {
        InitializeComponent();

        //ExerciseListView.ItemsSource = exerciseItemsResult;
        ExerciseListView.ItemTapped += ExerciseListView_ItemTapped;
        RefreshLocalized();

        NextButton.Clicked += NextButton_Clicked;

        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
        OnBeforeShow();
    }

    private void RefreshLocalized()
    {
        Title = AppResources.ChooseExercises;
        SearchEntry.Placeholder = AppResources.SearchExercises;
        BtnCancel.Text = AppResources.Cancel;
        NextButton.Text = AppResources.Next;
    }
    protected async override void OnAppearing()
    {
        base.OnAppearing();
        DependencyService.Get<IFirebase>().SetScreenName("add_exercise_to_workout_page");
        IsAppeared = true;
        //App.IsResizeScreen = true;
        if (partIsenabled)
        {
            
            CurrentLog.Instance.IsAddedExercises = true;
            partIsenabled = false;
            await Task.Delay(300);
            var p = await HelperClass.DisplayCustomPopup(partTitle,partMessage,"Turn off",AppResources.GotIt);
            p.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    SetupBodyPartPriority("");
                }
            };
            // ConfirmConfig p = new ConfirmConfig()
            // {
            //     Title = partTitle,
            //     Message = partMessage,
            //     OkText = "Turn off",
            //     CancelText = AppResources.GotIt,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // };
            // p.OnAction = (obj) =>
            // {
            //     if (obj)
            //     {
            //         //Delete Here
            //         SetupBodyPartPriority("");
            //         //Save
            //     }
            // };
            // UserDialogs.Instance.Confirm(p);
        }

        if (firstSwitch != null)
        {
            await Task.Delay(3000);
            _showTooltipOnToggle = true;
            TooltipEffect.SetHasShowTooltip(firstSwitch, true);
        }

    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        try
        {
            DependencyService.Get<IKeyboardHelper>().HideKeyboard();
        }
        catch (Exception ex)
        {

        }
        Task.Factory.StartNew(async () =>
        {
            await Task.Delay(1000);
            App.IsResizeScreen = false;
        });


    }
    bool IsAppeared = false;
    public  async void OnBeforeShow()
    {
        IsAppeared = false;
        this.ToolbarItems.Clear();
        await UpdateExerciseList();
    }

    void Handle_Toggled(object sender, ToggledEventArgs e)
    {
        try
        {

            if (_showTooltipOnToggle)
            {
                var first = ExeList.Where(x => x.Id == 3).FirstOrDefault();
                first.IsTooltipVisible = true;
                _showTooltipOnToggle = false;
                _showTooltipOnNext = true;
            }

        }
        catch (Exception ex)
        {

        }
    }

    private async void NextButton_Clicked(object sender, EventArgs e)
    {
        try
        {

            StackNext.Effects.Clear();
            if (exerciseItems.Count(i => i.IsSelected) == 0 && selectedItems.Count(i => i.IsSelected) == 0 && customItems.Count(i => i.IsSelected) == 0 && favouriteItems.Count(i => i.IsSelected) == 0)
            {
                // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = $"Choose 1 or more exercises to continue.",
                //     Title = "Choose exercises"
                // });

                await HelperClass.DisplayCustomPopupForResult("Choose exercises",
                        $"Choose 1 or more exercises to continue.","OK","");
                //UserDialogs.Instance.Alert(, AppResources.Error, AppResources.Ok);
                return;
            }

            if (CurrentLog.Instance.IsAddingExerciseLocally)
            {
                //try
                //{
                //    if (CurrentLog.Instance.CurrentWorkoutTemplateGroup != null && CurrentLog.Instance.CurrentWorkoutTemplateGroup.IsFeaturedProgram)
                //    {
                //        await PagesFactory.PushAsync<ChooseWorkoutExerciseOrder>();
                //        return;
                //    }
                //}
                //catch (Exception ex)
                //{

                //}
                CurrentLog.Instance.IsAddedExercises = true;
                var list = new List<ExerciceModel>();
                foreach (var ex in selectedItems.Where(x => x.IsSelected).ToList())
                {
                    list.Add(ex);
                }
                var IsCardioExist = list.Find(a => a.Id == 16508);
                if (IsCardioExist != null)
                {
                    var CurrentWorkoutId = LocalDBManager.Instance.GetDBSetting("CurrentWorkoutId")?.Value;
                    if (!string.IsNullOrEmpty(CurrentWorkoutId))
                    {
                        var data = new
                        {
                            Id = CurrentWorkoutId,
                            IsCardio = "true"
                        };
                        LocalDBManager.Instance.SetDBSetting("CurrentWorkoutCardioDetails", JsonConvert.SerializeObject(data));
                    }
                }
                else
                {
                    var CurrentWorkoutId = LocalDBManager.Instance.GetDBSetting("CurrentWorkoutId")?.Value;
                    var IsCardio = LocalDBManager.Instance.GetDBSetting("Cardio")?.Value;
                    if (!string.IsNullOrEmpty(CurrentWorkoutId) && IsCardio == "true")
                    {
                        var data = new
                        {
                            Id = CurrentWorkoutId,
                            IsCardio = "false"
                        };
                        LocalDBManager.Instance.SetDBSetting("CurrentWorkoutCardioDetails", JsonConvert.SerializeObject(data));
                    }
                }
                CurrentLog.Instance.CurrentWorkoutTemplate.Exercises = list;
                foreach (SelectableExerciceModel ex in exerciseItems.Where(i => i.IsSelected))
                {
                    if (CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Where(x => x.Label.Equals(ex.Label)).FirstOrDefault() == null)
                        CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Add(ex);
                }
                foreach (SelectableExerciceModel ex in favouriteItems.Where(i => i.IsSelected))
                {
                    if (CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Where(x => x.Label.Equals(ex.Label)).FirstOrDefault() == null)
                        CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Add(ex);
                }
                foreach (SelectableExerciceModel ex in customItems.Where(i => i.IsSelected))
                {
                    if (CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Where(x => x.Label.Equals(ex.Label)).FirstOrDefault() == null)
                        CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Add(ex);
                }
                if (CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Count > 0)
                {
                    CurrentLog.Instance.CurrentWorkoutTemplate.Exercises = CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.GroupBy(x => x.Id).Select(grp => grp.First()).ToList();
                }
                CurrentLog.Instance.WorkoutTemplateCurrentExercise = CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.FirstOrDefault();

                try
                {
                    if (CurrentLog.Instance.WorkoutsByExercise == null)
                        CurrentLog.Instance.WorkoutsByExercise = new Dictionary<long, List<ExerciceModel>>();
                    if (CurrentLog.Instance.WorkoutsByExercise.ContainsKey(CurrentLog.Instance.CurrentWorkoutTemplate.Id))
                        CurrentLog.Instance.WorkoutsByExercise.Remove(CurrentLog.Instance.CurrentWorkoutTemplate.Id);
                    ((App)Application.Current).WorkoutListContexts.WorkoutsByExercise = CurrentLog.Instance.WorkoutsByExercise;
                    ((App)Application.Current).WorkoutListContexts.SaveContexts();
                    //await PagesFactory.PopAsync();
                    await Navigation.PushAsync(new ChooseWorkoutExerciseOrder());
                }
                catch (Exception ex)
                {

                }

                
                return;

            }
            CurrentLog.Instance.CurrentWorkoutTemplate.Exercises = new List<ExerciceModel>();
            var olderlist = new List<ExerciceModel>();
            foreach (var ex in selectedItems.Where(x => x.IsSelected).ToList())
            {
                olderlist.Add(ex);

            }
            CurrentLog.Instance.CurrentWorkoutTemplate.Exercises = olderlist;



            foreach (SelectableExerciceModel ex in favouriteItems.Where(i => i.IsSelected))
            {
                if (CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Where(x => x.Label.Equals(ex.Label)).FirstOrDefault() == null)
                    CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Add(ex);
            }
            foreach (SelectableExerciceModel ex in customItems.Where(i => i.IsSelected))
            {
                if (CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Where(x => x.Label.Equals(ex.Label)).FirstOrDefault() == null)
                    CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Add(ex);
            }
            foreach (SelectableExerciceModel ex in exerciseItems.Where(i => i.IsSelected))
            {
                CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Add(ex);
            }

            CurrentLog.Instance.WorkoutTemplateCurrentExercise = CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.FirstOrDefault();
            CurrentLog.Instance.WorkoutStarted = false;
            await Navigation.PushAsync(new ChooseWorkoutExerciseOrder());

        }
        catch (Exception ex)
        {

        }
    }

    private async void ExerciseListView_ItemTapped(object sender, ItemTappedEventArgs e)
    {
        IsAppeared = false;
        try
        {

            SelectableExerciceModel selectableExerciceModel = (SelectableExerciceModel)e.Item;

            selectableExerciceModel.IsSelected = !selectableExerciceModel.IsSelected;
            if (_showTooltipOnNext)
            {
                TooltipEffect.SetHasShowTooltip(StackNext, true);
                _showTooltipOnNext = false;
            }
        }
        catch (Exception ex)
        {

        }
        IsAppeared = true;
    }
    private async void OnBindingContextChanged(object sender, EventArgs e)
    {
        base.OnBindingContextChanged();
        try
        {

            SelectableExerciceModel m = (SelectableExerciceModel)((BindableObject)sender).BindingContext;

            if (m != null && _exerciseId == m.Id)
            {
                //CurrentLog.Instance.ShowEditWorkouts = false;
                _showTooltipOnSwitch = true;
                firstSwitch = ((StackLayout)((StackLayout)((ViewCell)sender).View).Children[1]);

                TooltipEffect.SetPosition(firstSwitch, TooltipPosition.Bottom);
                TooltipEffect.SetBackgroundColor(firstSwitch, AppThemeConstants.BlueColor);
                TooltipEffect.SetTextColor(firstSwitch, Colors.White);
                TooltipEffect.SetText(firstSwitch, $"Toggle off exercises");
                TooltipEffect.SetHasTooltip(firstSwitch, true);
            }

        }
        catch (Exception ex)
        {

        }
    }

    private async void SetupBodyPartPriority(string bodypart)
    {
        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
        await DrMuscleRestClient.Instance.SetUserBodypartPriority(new UserInfosModel()
        {
            BodyPartPrioriy = bodypart
        });

    }

    void Section_Tapped(object sender, System.EventArgs e)
    {
        try
        {

            var obj = (BodyPartSection)((StackLayout)sender).BindingContext;
            MainThread.BeginInvokeOnMainThread(() => {
                obj.Expanded = !obj.Expanded;
                
                ExerciseListView.ItemsSource = null;
                ExerciseListView.ItemsSource = ExeList;
                ExerciseListView.ScrollTo(obj, ScrollToPosition.Start, false);
            });
            
            if (_showTooltipOnNext)
            {
                TooltipEffect.SetHasShowTooltip(StackNext, true);
                _showTooltipOnNext = false;
            }

        }
        catch (Exception ex)
        {

        }
    }

    private void StateImage_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        //if (e.PropertyName.Equals("Source"))
        //{
        //    var image = sender as Image;
        //    image.Opacity = 0;
        //    image.FadeTo(1, 1000);
        //}
    }

    private ExerciceModel GetSwappedExercise(long id)
    {
        try
        {

            SwapExerciseContext context = ((App)Application.Current).SwapExericesContexts.Swaps.First(c => c.TargetExerciseId == id);
            if (!string.IsNullOrEmpty(context.Label))
            {
                ExerciceModel model = new ExerciceModel()
                {
                    Id = (long)context.TargetExerciseId,
                    Label = context.Label,
                    IsBodyweight = context.IsBodyweight,
                    IsSwapTarget = true,
                    IsSystemExercise = context.IsSystemExercise,
                    VideoUrl = context.VideoUrl,
                    IsEasy = context.IsEasy,
                    BodyPartId = context.BodyPartId,
                    IsUnilateral = context.IsUnilateral,
                    IsTimeBased = context.IsTimeBased,
                    IsPlate = context.IsPlate,
                    IsFlexibility = context.IsFlexibility,
                    IsWeighted = context.IsWeighted
                };
                model.IsSwapTarget = true;

                return model;
            }

        }
        catch (Exception ex)
        {

        }
        return null;
    }

    private async Task UpdateExerciseList()
    {
        exerciseItems.Clear();
        exerciseItemsResult.Clear();

        try
        {
            try
            {

                string jsonFileName = "Exercises.json";
                ExerciceModel exerciseList = new ExerciceModel();
                var assembly = typeof(KenkoChooseYourWorkoutExercisePage).GetType().Assembly;
                var stream = await FileSystem.OpenAppPackageFileAsync(jsonFileName);
                using (var reader = new System.IO.StreamReader(stream))
                {
                    var jsonString = reader.ReadToEnd();

                    //Converting JSON Array Objects into generic list    
                    var list = JsonConvert.DeserializeObject<List<DBExerciseModel>>(jsonString);
                    exercises = new List<ExerciceModel>();
                    foreach (var item in list)
                    {
                        exercises.Add(new ExerciceModel()
                        {
                            Id = item.Id,
                            Label = item.Label,
                            BodyPartId = item.BodyPartId,
                            EquipmentId = item.EquipmentId,
                            IsBodyweight = item.IsBodyweight,
                            IsEasy = item.IsEasy,
                            IsMedium = item.IsMedium,
                            IsPlate = item.EquipmentId == 3,
                            IsSystemExercise = true,
                            VideoUrl = item.VideoUrl,
                            IsTimeBased = item.IsTimeBased,
                            IsUnilateral = item.IsUnilateral,
                            IsFlexibility = item.IsFlexibility
                        });
                    }
                    exercises = exercises.ToList();
                }
                //GetUserExerciseResponseModel itemsSource = await DrMuscleRestClient.Instance.GetCustomExerciseForUserWithLoader(LocalDBManager.Instance.GetDBSetting("email").Value);

                //foreach (var item in itemsSource.Exercises)
                //{
                //    exercises.Add(item);
                //}
            }
            catch
            {
            }
            if (exercises == null || exercises.Count == 0)
            {
                exercises = new List<ExerciceModel>();
                GetUserExerciseResponseModel itemsSource = await DrMuscleRestClient.Instance.GetUserExercise(LocalDBManager.Instance.GetDBSetting("email").Value);
                if (itemsSource != null && itemsSource.Exercises != null)
                {
                    exercises.AddRange(itemsSource.Exercises);
                }
            }




            List<SelectableExerciceModel> exo;
            List<ExerciceModel> exec;
            string bodypart = "", exerciseName = "";
            var undefined = exercises.Where(ex => ex.BodyPartId == 1).ToList();

            exec = exercises.Where(x => x.BodyPartId != 1).ToList();

            if (undefined.Count > 0)
                exec.AddRange(undefined);
            exercises = exec;
            try
            {
                selectedItems.Clear();
                if (CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise || CurrentLog.Instance.CurrentWorkoutTemplate.UserId == "89c52f09-240c-40a8-96df-9e8e152b7d63")
                {
                    var generalToolbarItem = new ToolbarItem("Restore", null, RestoreDefaultWorkoutAction, ToolbarItemOrder.Primary, 0);
                    this.ToolbarItems.Add(generalToolbarItem);
                }
                else
                    this.ToolbarItems.Clear();
                var exercise = CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.GroupBy(x => x.Id).Select(x => x.First()).ToList(); ;
                foreach (var e in exercise)
                {
                    var exe = e;
                    bool isSwapped = ((App)Application.Current).SwapExericesContexts.Swaps.Any(c => c.SourceExerciseId == exe.Id);
                    if (isSwapped)
                    {
                        long targetExerciseId = (long)((App)Application.Current).SwapExericesContexts.Swaps.First(c => c.SourceExerciseId == exe.Id).TargetExerciseId;
                        var swappedExe = GetSwappedExercise(targetExerciseId);
                        if (swappedExe != null)
                        {
                            exe = swappedExe;
                        }
                        else
                            isSwapped = false;
                    }

                    if (exercises.Any(x => x.Id == exe.Id))
                        exercises.Remove(exercises.Where(x => x.Id == exe.Id).First());
                    SelectableExerciceModel em = new SelectableExerciceModel();
                    em.Id = exe.Id;
                    em.IsSystemExercise = exe.IsSystemExercise;
                    em.Label = exe.Label;
                    em.BodyPartId = exe.BodyPartId;
                    em.VideoUrl = exe.VideoUrl;
                    em.IsUnilateral = exe.IsUnilateral;
                    em.IsTimeBased = exe.IsTimeBased;
                    em.IsBodyweight = exe.IsBodyweight;
                    em.IsPlate = exe.IsPlate;
                    if (CurrentLog.Instance.ShowEditWorkouts)
                    {
                        _exerciseId = exe.Id;
                        CurrentLog.Instance.ShowEditWorkouts = false;
                    }
                    em.IsSelected = CurrentLog.Instance.CurrentWorkoutTemplate.Id == -1 ? false : isSwapped ? true : CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Select(ex => ex.Id).Contains(exe.Id);

                    if (CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise)
                    {
                        if (em.Id == 16508)
                        {
                            var data = LocalDBManager.Instance.GetDBSetting("CurrentWorkoutCardioDetails")?.Value;
                            if (data != null)
                            {
                                var CurrentWorkoutId = LocalDBManager.Instance.GetDBSetting("CurrentWorkoutId")?.Value;
                                var currentWorkoutCardioDetails = JsonConvert.DeserializeObject(data);
                                var jObject = currentWorkoutCardioDetails as JObject;
                                if (jObject["Id"].ToString() == CurrentWorkoutId && jObject["IsCardio"].ToString() == "false")
                                {
                                    em.IsSelected = false;
                                }
                                else
                                {
                                    var isGlobalCardio = LocalDBManager.Instance.GetDBSetting("Cardio")?.Value;
                                    if (isGlobalCardio == "true" || (jObject["Id"].ToString() == CurrentWorkoutId && jObject["IsCardio"].ToString() == "true"))
                                        em.IsSelected = true;
                                    else
                                        em.IsSelected = false;
                                }
                            }
                            else
                            {
                                em.IsSelected = ((LocalDBManager.Instance.GetDBSetting("Cardio")?.Value) == "true") ? true : false;
                            }
                        }
                        if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value))
                        {
                            if (exe.IsBodypartPriority)
                                switch (em.Id)
                                {
                                    case 15778:
                                    case 18846:
                                        //ZottmanCurlExId
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Biceps")
                                        {
                                            bodypart = "Biceps";
                                            exerciseName = "Zottman Curl";

                                            partTitle = "Extra biceps exercise";
                                            partMessage = $"You selected biceps as a priority, so {exe.Label} will be added to your workout.";
                                            partIsenabled = true;

                                            continue;
                                        }
                                        break;
                                    case 12982:
                                    case 18847:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Chest")
                                        {
                                            bodypart = "Chest";
                                            exerciseName = "Diamond Push-up";

                                            partTitle = "Extra chest exercise";
                                            partMessage = $"You selected chest as a priority, so {exe.Label} will be added to your workout.";
                                            partIsenabled = true;
                                            continue;
                                        }
                                        break;
                                    case 20826:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Chest")
                                        {
                                            bodypart = "Chest";
                                            exerciseName = "Side to Side Push-up";

                                            partTitle = "Extra chest exercise";
                                            partMessage = $"You selected chest as a priority, so {exe.Label} will be added to your workout.";
                                            partIsenabled = true;
                                            continue;
                                        }
                                        break;
                                    case 17229:
                                    case 18845:
                                    case 18849:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Abs")
                                        {
                                            bodypart = "Abs";
                                            exerciseName = "V-Up";

                                            partTitle = "Extra abs exercise";
                                            partMessage = $"You selected abs as a priority, so {exe.Label} will be added to your workout.";
                                            partIsenabled = true;
                                            continue;
                                        }
                                        break;
                                    case 12989:
                                    case 18848:
                                    case 18851:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Legs")
                                        {
                                            bodypart = "Legs";
                                            exerciseName = "Single Leg Glute";

                                            partTitle = "Extra legs exercise";
                                            partMessage = $"You selected legs as a priority, so {em.Label} will be added to your workout.";
                                            partIsenabled = true;
                                            continue;
                                        }
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Glutes")
                                        {
                                            bodypart = "Legs";
                                            exerciseName = "Single Leg Glute";

                                            partTitle = "Extra glutes exercise";
                                            partMessage = $"You selected glutes as a priority, so {em.Label} will be added to your workout.";
                                            partIsenabled = true;
                                            continue; ;
                                        }
                                        break;
                                    case 17482:
                                    case 18850:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Biceps")
                                        {
                                            bodypart = "Biceps";
                                            exerciseName = "Doorway Curl";

                                            partTitle = "Extra biceps exercise";
                                            partMessage = $"You selected biceps as a priority, so {exe.Label} will be added to your workout.";
                                            partIsenabled = true;
                                            continue;
                                        }
                                        break;
                                        ;
                                    case 15934:
                                    case 15935:



                                        if (LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true" && LocalDBManager.Instance.GetDBSetting("Plate").Value == "false")
                                            switch (CurrentLog.Instance.CurrentWorkoutTemplate.Id)
                                            {
                                                case 110:
                                                case 426:
                                                case 874:
                                                case 877:
                                                case 883:
                                                case 2317:
                                                case 2322:
                                                case 2326:
                                                case 2330:
                                                case 2334:
                                                case 2338:
                                                case 2354:
                                                case 2358:
                                                case 2362:
                                                case 14157:
                                                case 14340:
                                                case 14358:
                                                    break;
                                                default:
                                                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Legs")
                                                    {
                                                        partTitle = "Extra legs exercise";
                                                        partMessage = $"You selected legs as a priority, so {em.Label} will be added to your workout.";
                                                        partIsenabled = true;
                                                        continue;
                                                    }
                                                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Glutes")
                                                    {
                                                        partTitle = "Extra glutes exercise";
                                                        partMessage = $"You selected glutes as a priority, so {em.Label} will be added to your workout.";
                                                        partIsenabled = true;
                                                        continue;
                                                    }

                                                    break;
                                            }
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Legs")
                                        {
                                            partTitle = "Extra legs exercise";
                                            partMessage = $"You selected legs as a priority, so {em.Label} will be added to your workout.";
                                            partIsenabled = true;
                                            continue;
                                        }
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Glutes")
                                        {
                                            partTitle = "Extra glutes exercise";
                                            partMessage = $"You selected glutes as a priority, so {em.Label} will be added to your workout.";
                                            partIsenabled = true;
                                            continue;
                                        }

                                        break;

                                    case 9661:
                                    case 25554:
                                    case 27419:
                                    case 17225:
                                    case 27420:
                                    case 27421:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Triceps")
                                        {
                                            partTitle = "Extra triceps exercise";
                                            partMessage = $"You selected triceps as a priority, so {exe.Label} will be added to your workout.";
                                            partIsenabled = true;
                                            continue;
                                        }
                                        break;
                                    case 15913:
                                    case 27422:
                                    case 27423:
                                    case 15911:
                                    case 27424:
                                    case 27425:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Shoulders")
                                        {
                                            partTitle = "Extra shoulders exercise";
                                            partMessage = $"You selected shoulders as a priority, so {exe.Label} will be added to your workout.";
                                            partIsenabled = true;
                                            continue;
                                        }
                                        break;
                                    case 15769:
                                    case 27428:
                                    case 27429:
                                    case 15768:
                                    case 27426:
                                    case 27427:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Traps")
                                        {
                                            partTitle = "Extra traps exercise";
                                            partMessage = $"You selected traps as a priority, so {exe.Label} will be added to your workout.";
                                            partIsenabled = true;
                                            continue;
                                        }
                                        break;
                                    case 17224:
                                    case 27430:
                                    case 27431:
                                    case 17360:
                                    case 27432:
                                    case 27433:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Upper back")
                                        {
                                            partTitle = "Extra upper back exercise";
                                            partMessage = $"You selected upper back as a priority, so {exe.Label} will be added to your workout.";
                                            partIsenabled = true;
                                            continue;
                                        }
                                        break;
                                    case 15938:
                                    case 15939:
                                    case 15940:
                                    case 14235:
                                    case 27435:
                                    case 27434:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Calves")
                                        {
                                            partTitle = "Extra calves exercise";
                                            partMessage = $"You selected calves as a priority, so {exe.Label} will be added to your workout.";
                                            partIsenabled = true;
                                            continue;
                                        }
                                        break;
                                    default:
                                        break;
                                }
                        }
                    }
                    if (selectedItems.Where(x => x.Id == em.Id).FirstOrDefault() == null)
                        selectedItems.Add(em);
                }
            }
            catch (Exception ex)
            {

            }

            try
            {

                foreach (ExerciceModel e in exercises)
                {
                    bool isSwapped = ((App)Application.Current).SwapExericesContexts.Swaps.Any(c => c.SourceExerciseId == e.Id);

                    SelectableExerciceModel em = new SelectableExerciceModel();
                    em.Id = e.Id;
                    em.IsSystemExercise = e.IsSystemExercise;
                    em.Label = e.Label;
                    em.BodyPartId = e.BodyPartId;
                    em.VideoUrl = e.VideoUrl;
                    em.IsUnilateral = e.IsUnilateral;
                    em.IsTimeBased = e.IsTimeBased;
                    em.IsBodyweight = e.IsBodyweight;
                    em.IsPlate = e.IsPlate;
                    em.IsFlexibility = e.IsFlexibility;
                    em.IsSelected = CurrentLog.Instance.CurrentWorkoutTemplate.Id == -1 ? false : CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Select(ex => ex.Id).Contains(e.Id);

                    if (isSwapped)
                    {
                        em.IsSelected = false;
                    }
                    if (CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise)
                    {
                        if (em.Id == 16508)
                        {
                            var data = LocalDBManager.Instance.GetDBSetting("CurrentWorkoutCardioDetails")?.Value;
                            if (data != null)
                            {
                                var CurrentWorkoutId = LocalDBManager.Instance.GetDBSetting("CurrentWorkoutId")?.Value;
                                var currentWorkoutCardioDetails = JsonConvert.DeserializeObject(data);
                                var jObject = currentWorkoutCardioDetails as JObject;
                                if (jObject["Id"].ToString() == CurrentWorkoutId && jObject["IsCardio"].ToString() == "false")
                                {
                                    em.IsSelected = false;
                                }
                                else
                                {
                                    var isGlobalCardio = LocalDBManager.Instance.GetDBSetting("Cardio")?.Value;
                                    if (isGlobalCardio == "true")
                                        em.IsSelected = true;
                                    else
                                        em.IsSelected = false;
                                }
                            }
                            else
                            {
                                em.IsSelected = ((LocalDBManager.Instance.GetDBSetting("Cardio")?.Value) == "true") ? true : false;
                            }
                        }
                        if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value))
                        {
                            if (e.IsBodypartPriority)
                                switch (em.Id)
                                {
                                    case 15778:
                                    case 18846:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Biceps")
                                        {

                                            continue;
                                        }
                                        break;
                                    case 12982:
                                    case 18847:
                                    case 20826:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Chest")
                                        {

                                            continue;
                                        }
                                        break;
                                    case 17229:
                                    case 18845:
                                    case 18849:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Abs")
                                        {

                                            continue;
                                        }
                                        break;
                                    case 12989:
                                    case 18848:
                                    case 18851:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Legs")
                                        {

                                            continue;
                                        }
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Glutes")
                                        {

                                            continue; ;
                                        }
                                        break;
                                    case 17482:
                                    case 18850:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Biceps")
                                        {

                                            continue;
                                        }
                                        break;
                                        ;
                                    case 15934:
                                    case 15935:



                                        if (LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true" && LocalDBManager.Instance.GetDBSetting("Plate").Value == "false")
                                            switch (CurrentLog.Instance.CurrentWorkoutTemplate.Id)
                                            {
                                                case 110:
                                                case 426:
                                                case 874:
                                                case 877:
                                                case 883:
                                                case 2317:
                                                case 2322:
                                                case 2326:
                                                case 2330:
                                                case 2334:
                                                case 2338:
                                                case 2354:
                                                case 2358:
                                                case 2362:
                                                case 14157:
                                                case 14340:
                                                case 14358:
                                                    break;
                                                default:
                                                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Legs")
                                                    {

                                                        continue;
                                                    }
                                                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Glutes")
                                                    {

                                                        continue;
                                                    }

                                                    break;
                                            }

                                        continue;


                                        break;

                                    case 9661:
                                    case 25554:
                                    case 27419:
                                    case 17225:
                                    case 27420:
                                    case 27421:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Triceps")
                                        {

                                            continue;
                                        }
                                        break;
                                    case 15913:
                                    case 27422:
                                    case 27423:
                                    case 15911:
                                    case 27424:
                                    case 27425:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Shoulders")
                                        {

                                            continue;
                                        }
                                        break;
                                    case 15769:
                                    case 27428:
                                    case 27429:
                                    case 15768:
                                    case 27426:
                                    case 27427:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Traps")
                                        {

                                            continue;
                                        }
                                        break;
                                    case 17224:
                                    case 27430:
                                    case 27431:
                                    case 17360:
                                    case 27432:
                                    case 27433:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Upper back")
                                        {

                                            continue;
                                        }
                                        break;
                                    case 15938:
                                    case 15939:
                                    case 15940:
                                    case 14235:
                                    case 27435:
                                    case 27434:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Calves")
                                        {

                                            continue;
                                        }
                                        break;
                                    default:
                                        break;
                                }
                        }
                    }
                    exerciseItems.Add(em);

                }


            }
            catch (Exception ex)
            {

            }

            var mobility = exerciseItems.Where(x => x.IsFlexibility).ToList();
            exo = exerciseItems.ToList();

            //exerciseItemsResult = exerciseItems;
            ////ExerciseListView.ItemsSource = exerciseItemsResult;



            ExeList.Clear();

            //foreach (ExerciceModel em in exo)
            //exerciseItems.Add(em);




            var customExerAdded = false;
            foreach (var groupItem in exo.Except(mobility).GroupBy(x => x.BodyPartId).ToList())
            {
                var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(groupItem.Key), Id = (int)groupItem.Key };
                foreach (var item in groupItem.OrderBy(x => x.Label))
                {
                    bodyPartShoulders.Exercices.Add(item);
                }

                BodyPartSection section = new BodyPartSection(bodyPartShoulders, false);
                ExeList.Add(section);
            }
            if (selectedItems.Count > 0)
            {
                var selectedpart = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(27), Id = 27 };
                foreach (var item in selectedItems)
                {
                    selectedpart.Exercices.Add(item);
                }
                BodyPartSection selectedSection = new BodyPartSection(selectedpart, true);
                ExeList.Insert(0, selectedSection);
            }

            ExerciseListView.ItemsSource = ExeList;



            exerciseItemsResult = exerciseItems;

            try
            {
                GetUserExerciseResponseModel itemsSource = await DrMuscleRestClient.Instance.GetCustomExerciseForUser(LocalDBManager.Instance.GetDBSetting("email").Value);

                if (itemsSource != null && itemsSource.Exercises != null)
                {
                    customItems.Clear();

                    var bPartSection = ExeList.Where(x => x.Id == 26).ToList();
                    if (bPartSection.Count > 0)
                    {
                        ExeList.Remove(bPartSection[0]);
                    }
                    foreach (var e in itemsSource.Exercises)
                    {
                        SelectableExerciceModel em = new SelectableExerciceModel();
                        em.Id = e.Id;
                        em.IsSystemExercise = e.IsSystemExercise;
                        em.Label = e.Label;
                        em.BodyPartId = e.BodyPartId;
                        em.VideoUrl = e.VideoUrl;
                        em.IsUnilateral = e.IsUnilateral;
                        em.IsTimeBased = e.IsTimeBased;
                        em.IsBodyweight = e.IsBodyweight;
                        em.IsPlate = e.IsPlate;

                        em.IsSelected = false;//CurrentLog.Instance.CurrentWorkoutTemplate.Id == -1 ? false : CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Select(ex => ex.Id).Contains(e.Id);

                        if (CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise)
                        {
                            if (em.Id == 16508)
                            {
                                var data = LocalDBManager.Instance.GetDBSetting("CurrentWorkoutCardioDetails")?.Value;
                                if (data != null)
                                {
                                    var CurrentWorkoutId = LocalDBManager.Instance.GetDBSetting("CurrentWorkoutId")?.Value;
                                    var currentWorkoutCardioDetails = JsonConvert.DeserializeObject(data);
                                    var jObject = currentWorkoutCardioDetails as JObject;
                                    if (jObject["Id"].ToString() == CurrentWorkoutId && jObject["IsCardio"].ToString() == "false")
                                    {
                                        em.IsSelected = false;
                                    }
                                    else
                                    {
                                        var isGlobalCardio = LocalDBManager.Instance.GetDBSetting("Cardio")?.Value;
                                        if (isGlobalCardio == "true")
                                            em.IsSelected = true;
                                        else
                                            em.IsSelected = false;
                                    }
                                }
                                else
                                {
                                    em.IsSelected = ((LocalDBManager.Instance.GetDBSetting("Cardio")?.Value) == "true") ? true : false;
                                }
                            }
                            if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value))
                            {
                                if (e.IsBodypartPriority)
                                    switch (em.Id)
                                    {
                                        case 15778:
                                        case 18846:
                                            if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Biceps")
                                            {

                                                continue;
                                            }
                                            break;
                                        case 12982:
                                        case 18847:
                                        case 20826:
                                            if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Chest")
                                            {

                                                continue;
                                            }
                                            break;
                                        case 17229:
                                        case 18845:
                                        case 18849:
                                            if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Abs")
                                            {

                                                continue;
                                            }
                                            break;
                                        case 12989:
                                        case 18848:
                                        case 18851:
                                            if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Legs")
                                            {

                                                continue;
                                            }
                                            if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Glutes")
                                            {

                                                continue; ;
                                            }
                                            break;
                                        case 17482:
                                        case 18850:
                                            if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Biceps")
                                            {

                                                continue;
                                            }
                                            break;
                                            ;
                                        case 15934:
                                        case 15935:



                                            if (LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true" && LocalDBManager.Instance.GetDBSetting("Plate").Value == "false")
                                                switch (CurrentLog.Instance.CurrentWorkoutTemplate.Id)
                                                {
                                                    case 110:
                                                    case 426:
                                                    case 874:
                                                    case 877:
                                                    case 883:
                                                    case 2317:
                                                    case 2322:
                                                    case 2326:
                                                    case 2330:
                                                    case 2334:
                                                    case 2338:
                                                    case 2354:
                                                    case 2358:
                                                    case 2362:
                                                    case 14157:
                                                    case 14340:
                                                    case 14358:
                                                        break;
                                                    default:
                                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Legs")
                                                        {

                                                            continue;
                                                        }
                                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Glutes")
                                                        {

                                                            continue;
                                                        }

                                                        break;
                                                }

                                            continue;


                                            break;
                                        case 9661:
                                        case 25554:
                                        case 27419:
                                        case 17225:
                                        case 27420:
                                        case 27421:
                                            if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Triceps")
                                            {

                                                continue;
                                            }
                                            break;
                                        case 15913:
                                        case 27422:
                                        case 27423:
                                        case 15911:
                                        case 27424:
                                        case 27425:
                                            if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Shoulders")
                                            {

                                                continue;
                                            }
                                            break;
                                        case 15769:
                                        case 27428:
                                        case 27429:
                                        case 15768:
                                        case 27426:
                                        case 27427:
                                            if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Traps")
                                            {

                                                continue;
                                            }
                                            break;
                                        case 17224:
                                        case 27430:
                                        case 27431:
                                        case 17360:
                                        case 27432:
                                        case 27433:
                                            if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Upper back")
                                            {

                                                continue;
                                            }
                                            break;
                                        case 15938:
                                        case 15939:
                                        case 15940:
                                        case 14235:
                                        case 27435:
                                        case 27434:
                                            if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Calves")
                                            {

                                                continue;
                                            }
                                            break;
                                        default:
                                            break;
                                    }
                            }
                        }
                        customItems.Add(em);
                    }
                }

            }

            catch (Exception ex)
            {

            }


            GetUserExerciseResponseModel itemsSources = await DrMuscleRestClient.Instance.GetFavoriteExercises();
            if (itemsSources != null && itemsSources.Exercises != null)
            {
                favouriteItems.Clear();

                var bPartSection = ExeList.Where(x => x.Id == 26).ToList();
                if (bPartSection.Count > 0)
                {
                    ExeList.Remove(bPartSection[0]);
                }

                foreach (var e in itemsSources.Exercises)
                {
                    SelectableExerciceModel em = new SelectableExerciceModel();
                    em.Id = e.Id;
                    em.IsSystemExercise = e.IsSystemExercise;
                    em.Label = e.Label;
                    em.BodyPartId = e.BodyPartId;
                    em.VideoUrl = e.VideoUrl;
                    em.IsUnilateral = e.IsUnilateral;
                    em.IsTimeBased = e.IsTimeBased;
                    em.IsBodyweight = e.IsBodyweight;
                    em.IsPlate = e.IsPlate;

                    em.IsSelected = false;// CurrentLog.Instance.CurrentWorkoutTemplate.Id == -1 ? false : CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Select(ex => ex.Id).Contains(e.Id);

                    if (CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise)
                    {
                        if (em.Id == 16508 && LocalDBManager.Instance.GetDBSetting("Cardio").Value == "true")
                            em.IsSelected = false;
                        if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value))
                        {
                            if (e.IsBodypartPriority)
                                switch (em.Id)
                                {
                                    case 15778:
                                    case 18846:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Biceps")
                                        {

                                            continue;
                                        }
                                        break;
                                    case 12982:
                                    case 18847:
                                    case 20826:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Chest")
                                        {

                                            continue;
                                        }
                                        break;
                                    case 17229:
                                    case 18845:
                                    case 18849:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Abs")
                                        {

                                            continue;
                                        }
                                        break;
                                    case 12989:
                                    case 18848:
                                    case 18851:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Legs")
                                        {

                                            continue;
                                        }
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Glutes")
                                        {

                                            continue; ;
                                        }
                                        break;
                                    case 17482:
                                    case 18850:
                                        if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Biceps")
                                        {

                                            continue;
                                        }
                                        break;
                                        ;
                                    case 15934:
                                    case 15935:



                                        if (LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true" && LocalDBManager.Instance.GetDBSetting("Plate").Value == "false")
                                            switch (CurrentLog.Instance.CurrentWorkoutTemplate.Id)
                                            {
                                                case 110:
                                                case 426:
                                                case 874:
                                                case 877:
                                                case 883:
                                                case 2317:
                                                case 2322:
                                                case 2326:
                                                case 2330:
                                                case 2334:
                                                case 2338:
                                                case 2354:
                                                case 2358:
                                                case 2362:
                                                case 14157:
                                                case 14340:
                                                case 14358:
                                                    break;
                                                default:
                                                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Legs")
                                                    {

                                                        continue;
                                                    }
                                                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Glutes")
                                                    {

                                                        continue;
                                                    }

                                                    break;
                                            }

                                        continue;


                                        break;
                                    default:
                                        break;
                                }
                        }
                    }
                    favouriteItems.Add(em);
                }
            }

            if (customItems.Count > 0)
            {
                //var selectedpart = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(26), Id = 26 };
                //foreach (var item in customItems)
                //{
                //    selectedpart.Exercices.Add(item);
                //}
                //BodyPartSection selectedSection = new BodyPartSection(selectedpart, false);
                //ExeList.Insert(0, selectedSection);
                var bodyPartSection = ExeList.Where(x => x.Id == 26).ToList();
                if (bodyPartSection.Count > 0)
                {

                    BodyPartSection body = bodyPartSection[0];
                    body.Clear();
                    if (body.Expanded)
                        body.AddRange(customItems);
                    body._bodyPart.Exercices.Clear();
                    body._bodyPart.Exercices.AddRange(customItems);
                    body.Exercises.Clear();
                    body.Exercises.AddRange(customItems);
                }
                else
                {
                    var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(26), Id = 26 };
                    bodyPartShoulders.Exercices.AddRange(customItems);
                    BodyPartSection section = new BodyPartSection(bodyPartShoulders, false);
                    ExeList.Insert(0, section);
                    //ExpandableList.ItemsSource = ExeList;
                    ExerciseListView.ItemsSource = null;
                    ExerciseListView.ItemsSource = ExeList;
                }
            }
            if (favouriteItems.Count > 0)
            {
                //var selectedpart = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(25), Id = 25 };
                //foreach (var item in favouriteItems)
                //{
                //    selectedpart.Exercices.Add(item);
                //}
                //BodyPartSection selectedSection = new BodyPartSection(selectedpart, false);


                var bodyPartSection = ExeList.Where(x => x.Id == 25).ToList();
                if (bodyPartSection.Count > 0)
                {

                    BodyPartSection body = bodyPartSection[0];
                    body.Clear();
                    if (body.Expanded)
                        body.AddRange(favouriteItems);
                    body._bodyPart.Exercices.Clear();
                    body._bodyPart.Exercices.AddRange(favouriteItems);
                    body.Exercises.Clear();
                    body.Exercises.AddRange(favouriteItems);
                }
                else
                {
                    var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(25), Id = 25 };
                    bodyPartShoulders.Exercices.AddRange(favouriteItems);
                    BodyPartSection section = new BodyPartSection(bodyPartShoulders, false);
                    ExeList.Insert(0, section);
                    //ExpandableList.ItemsSource = ExeList;
                    ExerciseListView.ItemsSource = null;
                    ExerciseListView.ItemsSource = ExeList;
                }

                //ExeList.Insert(0, selectedSection);
            }

            if (mobility.Count > 0)
            {
                //var selectedpart = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(25), Id = 25 };
                //foreach (var item in favouriteItems)
                //{
                //    selectedpart.Exercices.Add(item);
                //}
                //BodyPartSection selectedSection = new BodyPartSection(selectedpart, false);


                var mobileSection = ExeList.Where(x => x.Id == 28).ToList();
                if (mobileSection.Count > 0)
                {

                    BodyPartSection body = mobileSection[0];
                    body.Clear();
                    if (body.Expanded)
                        body.AddRange(mobility);
                    body._bodyPart.Exercices.Clear();
                    body._bodyPart.Exercices.AddRange(mobility);
                    body.Exercises.Clear();
                    body.Exercises.AddRange(mobility);
                }
                else
                {
                    var bodypartMobility = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(28), Id = 28 };
                    bodypartMobility.Exercices.AddRange(mobility);
                    BodyPartSection section = new BodyPartSection(bodypartMobility, false);
                    ExeList.Add(section);
                    //ExpandableList.ItemsSource = ExeList;
                    ExerciseListView.ItemsSource = null;
                    ExerciseListView.ItemsSource = ExeList;
                }

                //ExeList.Insert(0, selectedSection);
            }




            //ExerciseListView.ItemsSource = ExeList;



        }
        catch (Exception e)
        {
            //ConnectionErrorPopup();

        }
    }

    async void RestoreDefaultWorkoutAction()
    {
        try
        {


            CurrentLog.Instance.IsAddedExercises = true;
            partIsenabled = false;
            var alert = await HelperClass.DisplayCustomPopup("Restore workout?","All your changes to this workout will be reset.","Restore","Cancel");
            
            alert.ActionSelected += async (sender,action) => {
                if(action == PopupAction.OK){
                    try
                    {

                        //Delete Here
                        var result = await DrMuscleRestClient.Instance.RestoreUserWorkoutTemplate(new WorkoutTemplateGroupModel()
                        {
                            Id = CurrentLog.Instance.CurrentWorkoutTemplate.Id
                        });
                        if (result != null && result.Result)
                        {
                            CurrentLog.Instance.IsAddedExercises = true;
                            CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Clear();
                            if (Navigation?.NavigationStack?.Count > 1)
                            {
                                var previousPage = Navigation.NavigationStack[Navigation.NavigationStack.Count - 2] as KenkoChooseYourWorkoutExercisePage;

                                // Call the specific method on the previous page.
                                if(previousPage != null)
                                    previousPage?.OnBeforeShow();
                                try
                                {
                                    await Navigation.PopAsync();
                                }
                                catch (NotSupportedException ex)
                                {
                                }
                            }
                        }

                    }
                    catch (Exception ex)
                    {

                    }
                }
            };
            // ConfirmConfig p = new ConfirmConfig()
            // {
            //     Title = "Restore workout?",
            //     Message = "All your changes to this workout will be reset.",
            //     OkText = "Restore",
            //     CancelText = AppResources.Cancel,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // };
            // p.OnAction = async (obj) =>
            // {
            //     if (obj)
            //     {
            //         try
            //         {

            //             //Delete Here
            //             var result = await DrMuscleRestClient.Instance.RestoreUserWorkoutTemplate(new WorkoutTemplateGroupModel()
            //             {
            //                 Id = CurrentLog.Instance.CurrentWorkoutTemplate.Id
            //             });
            //             if (result != null && result.Result)
            //             {
            //                 CurrentLog.Instance.IsAddedExercises = true;
            //                 await Navigation.PopAsync();
            //             }

            //         }
            //         catch (Exception ex)
            //         {

            //         }
            //         //Save
            //     }
            // };
            // UserDialogs.Instance.Confirm(p);
        }
        catch (Exception ex)
        {

        }

    }
    void Handle_SearchTextChanged(object sender, TextChangedEventArgs e)
    {
        //var list = exerciseItems.Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant())).ToList();
        //exerciseItemsResult = new ObservableCollection<SelectableExerciceModel>(list);
        ////ExerciseListView.ItemsSource = exerciseItemsResult;
        //if (e.NewTextValue.Length > 0)
        //    BtnCancel.IsVisible = true;
        //else
        //    BtnCancel.IsVisible = false;
        var mobility = exerciseItems.Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant()) && x.IsFlexibility).ToList();
        var list = exerciseItems.Except(mobility).Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant())).ToList();
        var selectedlist = selectedItems.Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant())).ToList();
        var customlist = customItems.Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant())).ToList();
        var favlist = favouriteItems.Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant())).ToList();
        ExeList.Clear();
        //if (CurrentLog.Instance.SwapContext != null)

        //{
        //    var swapContext = CurrentLog.Instance.SwapContext;
        //    if (swapContext.SourceBodyPartId != null)
        //    {
        //        var bodyPart = list.Where(x => x.BodyPartId == swapContext.SourceBodyPartId).ToList();
        //        var notBodyPart = list.Where(x => x.BodyPartId != swapContext.SourceBodyPartId).ToList();

        //        var bodyPartShoulders = new BodyPartModel() { Label = $"{AppThemeConstants.GetBodyPartName(swapContext.SourceBodyPartId)} exercises" };
        //        foreach (var item in bodyPart)
        //        {
        //            bodyPartShoulders.Exercices.Add(item);
        //        }
        //        BodyPartSection section = new BodyPartSection(bodyPartShoulders);
        //        ExeList.Add(section);

        //        var otherBodyPartShoulders = new BodyPartModel() { Label = $"Other exercises" };
        //        foreach (var item in notBodyPart.OrderBy(x => x.BodyPartId))
        //        {
        //            otherBodyPartShoulders.Exercices.Add(item);
        //        }
        //        BodyPartSection otherSection = new BodyPartSection(otherBodyPartShoulders);
        //        ExeList.Add(otherSection);
        //        Device.BeginInvokeOnMainThread(() =>
        //        {
        //            //ExerciseListView.ItemsSource = ExeList;
        //        });
        //    }
        //    else
        //    {

        //    }
        //    System.Diagnostics.Debug.WriteLine($"SourceBodyPartId{swapContext.SourceBodyPartId}");
        //}

        if (ExeList.Count == 0)
        {
            //foreach (ExerciceModel em in exo)
            //exerciseItems.Add(em);

            foreach (var groupItem in list.GroupBy(x => x.BodyPartId).ToList())
            {
                var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(groupItem.Key), Id = (int)groupItem.Key };
                foreach (var item in groupItem)
                {
                    bodyPartShoulders.Exercices.Add(item);
                }
                BodyPartSection section = new BodyPartSection(bodyPartShoulders);
                ExeList.Add(section);
            }
            Device.BeginInvokeOnMainThread(() =>
            {
                //ExerciseListView.ItemsSource = ExeList.OrderBy(x => x.Name);
            });
            //foreach (var item in selectedlist)
            //{
            //    var bodyPartSection = ExeList.Where(x => x.Id == 27).ToList();
            //    if (bodyPartSection.Count > 0)
            //    {
            //        BodyPartSection body = bodyPartSection[0];
            //        if (body.Expanded)
            //            body.Add(item);
            //        body._bodyPart.Exercices.Add(item);
            //        body.Exercises.Add(item);
            //    }
            //    else
            //    {
            //        var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(27), Id = 27 };
            //        bodyPartFavourite.Exercices.Add(item);
            //        BodyPartSection sections = new BodyPartSection(bodyPartFavourite);
            //        ExeList.Insert(0, sections);
            //        Device.BeginInvokeOnMainThread(() =>
            //        {
            //            //ExerciseListView.ItemsSource = ExeList;
            //        });
            //    }
            //}
            foreach (var item in selectedlist)
            {
                var bodyPartSection = ExeList.Where(x => x.Id == 27).ToList();
                if (bodyPartSection.Count > 0)
                {
                    BodyPartSection body = bodyPartSection[0];
                    if (body.Expanded)
                        body.Add(item);
                    body._bodyPart.Exercices.Add(item);
                    body.Exercises.Add(item);
                }
                else
                {
                    var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(27), Id = 27 };
                    bodyPartFavourite.Exercices.Add(item);
                    BodyPartSection sections = new BodyPartSection(bodyPartFavourite);
                    ExeList.Insert(0, sections);
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        //ExerciseListView.ItemsSource = ExeList;
                    });
                }
            }
            foreach (var item in customlist)
            {
                var bodyPartSection = ExeList.Where(x => x.Id == 26).ToList();
                if (bodyPartSection.Count > 0)
                {
                    BodyPartSection body = bodyPartSection[0];
                    if (body.Expanded)
                        body.Add(item);
                    body._bodyPart.Exercices.Add(item);
                    body.Exercises.Add(item);
                }
                else
                {
                    var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(26), Id = 26 };
                    bodyPartFavourite.Exercices.Add(item);
                    BodyPartSection sections = new BodyPartSection(bodyPartFavourite);
                    ExeList.Insert(0, sections);
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        //ExerciseListView.ItemsSource = ExeList;
                    });
                }
            }
            foreach (var item in favlist)
            {
                var bodyPartSection = ExeList.Where(x => x.Id == 25).ToList();
                if (bodyPartSection.Count > 0)
                {
                    BodyPartSection body = bodyPartSection[0];
                    if (body.Expanded)
                        body.Add(item);
                    body._bodyPart.Exercices.Add(item);
                    body.Exercises.Add(item);
                }
                else
                {
                    var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(25), Id = 25 };
                    bodyPartFavourite.Exercices.Add(item);
                    BodyPartSection sections = new BodyPartSection(bodyPartFavourite);
                    ExeList.Insert(0, sections);
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        //ExerciseListView.ItemsSource = ExeList;
                    });
                }
            }

            foreach (var item in mobility)
            {
                var bodyPartSection = ExeList.Where(x => x.Id == 28).ToList();
                if (bodyPartSection.Count > 0)
                {
                    BodyPartSection body = bodyPartSection[0];
                    if (body.Expanded)
                        body.Add(item);
                    body._bodyPart.Exercices.Add(item);
                    body.Exercises.Add(item);
                }
                else
                {
                    var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(28), Id = 28 };
                    bodyPartFavourite.Exercices.Add(item);
                    BodyPartSection sections = new BodyPartSection(bodyPartFavourite);
                    ExeList.Add(sections);
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        //ExerciseListView.ItemsSource = ExeList;
                    });
                }
            }

        }
        if (e.NewTextValue.Length > 0)
            BtnCancel.IsVisible = true;
        else
            BtnCancel.IsVisible = false;
    }



    void Handle_CancelTapped(object sender, System.EventArgs e)
    {
        SearchEntry.Text = "";
    }

    protected override bool OnBackButtonPressed()
    {
        CurrentLog.Instance.IsAddingExerciseLocally = false;
        return base.OnBackButtonPressed();
    }


    void ExerciseListView_Scrolled(System.Object sender, Microsoft.Maui.Controls.ScrolledEventArgs e)
    {
        if (!SearchEntry.IsFocused && (PopupNavigation.Instance.PopupStack?.Count()) == 0)
        {
            SearchEntry.HideSoftInputAsync(CancellationToken.None);
        }
    }
    void SearchEntry_Focused(object sender, FocusEventArgs e)
    {
        SearchEntry.Focus();
    }
    void SearchEntry_Unfocused(object sender, FocusEventArgs e)
    {
        SearchEntry.HideSoftInputAsync(CancellationToken.None);
    }

    private void NewExerciseTapped(object sender, EventArgs e)
    {
        AddMyOwnExercise();
    }
    new
 private async void AddMyOwnExercise()
    {
        try
        {
            CustomPromptConfig customPromptConfig = new CustomPromptConfig(AppResources.NewExercise, AppResources.TapHereToEnterName, AppResources.Create,
             AppResources.Cancel, AppResources.LetsNameYourNewExercise, Keyboard.Text, "");

            customPromptConfig.ActionSelected += async (sender1, action) =>
            {
                if (action == PopupAction.OK)
                {
                    PromptResult result = new PromptResult(true, customPromptConfig.text);
                    if (string.IsNullOrWhiteSpace(customPromptConfig.text))
                    {
                        return;
                    }
                    await Task.Delay(500);
                    AddExercisesAction(action, customPromptConfig.text);
                }
            };
            await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);
        }
        catch (Exception ex)
        {

        }
        // PromptConfig p = new PromptConfig()
        // {
        //     InputType = InputType.Name,
        //     IsCancellable = true,
        //     Title = AppResources.NewExercise,
        //     Message = AppResources.LetsNameYourNewExercise,
        //     Placeholder = AppResources.TapHereToEnterName,
        //     OkText = AppResources.Create,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = new Action<PromptResult>(AddExercisesAction)
        // };
        // p.OnTextChanged += Name_OnTextChanged;
        // UserDialogs.Instance.Prompt(p);
    }
    private async void AddExercisesAction(PopupAction response, string Text)
    {
        if (response == PopupAction.OK)
        {
            try
            {
                ActionSheetConfig config = new ActionSheetConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };

                config.Add("Normal", () =>
                {
                    var userExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = false
                    };
                    AskForIsEasy(userExercise);
                });
                config.Add("Cardio", () =>
                {
                    var userExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = true,
                        IsFlexibility = false,
                        IsTimeBased = true,
                        BodyPartId = 12
                    };
                    AskForIsEasy(userExercise);
                });
                config.Add("Bodyweight", () =>
                {
                    var userExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = true
                    };
                    AskForIsEasy(userExercise);
                });
                config.Add("Weighted Bodyweight", () =>
                {
                    var userExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = false,
                        IsWeighted = true
                    };
                    AskForIsEasy(userExercise);
                });
                config.Add("Flexibility or mobility", () =>
                {
                    newAddUserExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = true,
                        IsFlexibility = true,
                        IsEasy = false,
                        IsMedium = false,
                        IsTimeBased = false
                    };
                    CreateNewExercise();
                });
                config.SetTitle("Exercise type?");
                UserDialogs.Instance.ActionSheet(config);
                //ConfirmConfig IsEasyPopUp = new ConfirmConfig()
                //{
                //    Title = AppResources.IsThisABodyweightExercise,
                //    //Title = $"Let's set up your {response.Text}",
                //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //    OkText = AppResources.YesBodyweight,
                //    CancelText = AppResources.No,
                //    OnAction = async (bool ok) =>
                //    {
                //        var userExercise = new AddUserExerciseModel()
                //        {
                //            Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                //            ExerciseName = response.Text,
                //            IsBodyweight = ok
                //        };
                //        AskForIsEasy(userExercise);
                //    }
                //};
                //await Task.Delay(100);
                //UserDialogs.Instance.Confirm(IsEasyPopUp);

            }
            catch (Exception e)
            {
                // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = AppResources.PleaseCheckInternetConnection,
                //     Title = AppResources.ConnectionError
                // });

                await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                            AppResources.PleaseCheckInternetConnection, "Try again", "");
                //await UserDialogs.Instance.AlertAsync(AppResources.PleaseCheckInternetConnection, AppResources.Error);
            }
        }
    }
    async void AskForIsEasy(AddUserExerciseModel newUserExercise)
    {
        newAddUserExercise = newUserExercise;
        CreatePopupForExerciseType();
    }
    async void CreatePopupForExerciseType()
    {
        try
        {
            if (newAddUserExercise.BodyPartId == 12)
            {
                CreateExercise();
                return;
            }
            ActionSheetConfig actionSheetConfig = new ActionSheetConfig();
            actionSheetConfig.AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray);

            actionSheetConfig.Add("Hard", () =>
            {
                CreateNewExercise();
            });

            actionSheetConfig.Add("Medium", () =>
            {
                newAddUserExercise.IsMedium = true;
                CreateNewExercise();
            });
            actionSheetConfig.Add("Easy", () =>
            {
                newAddUserExercise.IsEasy = true;
                CreateNewExercise();
            });
            actionSheetConfig.SetTitle("How hard should the exercise be?");
            UserDialogs.Instance.ActionSheet(actionSheetConfig);

        }
        catch (Exception ex)
        {

        }
    }
    async void CreateNewExercise()
    {
        try
        {
            if (newAddUserExercise.BodyPartId == 12)
            {
                CreateExercise();
                return;
            }
            ActionSheetConfig actionSheetConfig = new ActionSheetConfig();
            actionSheetConfig.AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray);

            actionSheetConfig.Add("Abs", () =>
            {
                newAddUserExercise.BodyPartId = 7;
                CreateExercise();
            });

            actionSheetConfig.Add("Back", () =>
            {
                newAddUserExercise.BodyPartId = 4;
                CreateExercise();
            });
            actionSheetConfig.Add("Lower back, glutes & hamstrings", () =>
            {
                newAddUserExercise.BodyPartId = 14;
                CreateExercise();
            });
            actionSheetConfig.Add("Biceps", () =>
            {
                newAddUserExercise.BodyPartId = 5;
                CreateExercise();
            });
            actionSheetConfig.Add("Calves", () =>
            {
                newAddUserExercise.BodyPartId = 9;
                CreateExercise();
            });
            //actionSheetConfig.Add("Cardio", () =>
            //{
            //    newAddUserExercise.BodyPartId = 12;
            //    CreateExercise();
            //});
            actionSheetConfig.Add("Chest", () =>
            {
                newAddUserExercise.BodyPartId = 3;
                CreateExercise();
            });
            //actionSheetConfig.Add("Flexibility & Mobility", () =>
            //{
            //    newAddUserExercise.BodyPartId = 13;
            //    CreateExercise();
            //});
            actionSheetConfig.Add("Forearm", () =>
            {
                newAddUserExercise.BodyPartId = 11;
                CreateExercise();
            });
            actionSheetConfig.Add("Legs", () =>
            {
                newAddUserExercise.BodyPartId = 8;
                CreateExercise();
            });
            actionSheetConfig.Add("Neck", () =>
            {
                newAddUserExercise.BodyPartId = 10;
                CreateExercise();
            });
            actionSheetConfig.Add("Shoulders", () =>
            {
                newAddUserExercise.BodyPartId = 2;
                CreateExercise();
            });

            actionSheetConfig.Add("Triceps", () =>
            {
                newAddUserExercise.BodyPartId = 6;
                CreateExercise();
            });

            actionSheetConfig.SetTitle("Body part");
            UserDialogs.Instance.ActionSheet(actionSheetConfig);
        }
        catch (Exception ex)
        {

        }

    }
    async void CreateExercise()
    {
        try
        {
            var ShowPopUp = await HelperClass.DisplayCustomPopup("Left/right side separately?", "Train your left and right sides separately, or both sides together?",
                 "Left/right separately", "Both sides together");
            ShowPopUp.ActionSelected += async (sender, action) =>
            {

                if (action == Views.PopupAction.OK)
                {
                    newAddUserExercise.IsUnilateral = true;
                }

                if (newAddUserExercise.IsFlexibility || newAddUserExercise.BodyPartId == 12)
                    FinalCreateExercise();
                else
                    AskForTimeBasedExercise();

            };
            //await Task.Delay(100);



            // ConfirmConfig IsEasyPopUp = new ConfirmConfig()
            // {
            //     Title = "Left/right side separately?",
            //     Message = "Train your left and right sides separately, or both sides together?",
            //     //Title = $"Let's set up your {response.Text}",
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = "Left/right separately",
            //     CancelText = "Both sides together",
            //     OnAction = async (bool ok) =>
            //     {
            //         newAddUserExercise.IsUnilateral = ok;
            //         if (newAddUserExercise.IsFlexibility || newAddUserExercise.BodyPartId == 12)
            //             FinalCreateExercise();
            //         else
            //             AskForTimeBasedExercise();
            //     }
            // };
            // await Task.Delay(100);
            // UserDialogs.Instance.Confirm(IsEasyPopUp);
        }
        catch (Exception ex)
        {

        }
    }
    async void AskForTimeBasedExercise()
    {
        try
        {
            var isConfirm = await HelperClass.DisplayCustomPopupForResult("", "Should this exercise progress in reps or seconds?",
                 "Seconds", AppResources.Reps);



            // ConfirmConfig TimebasePopUp = new ConfirmConfig()
            // {
            //     Message = "Should this exercise progress in reps or seconds?",
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = "Seconds",
            //     CancelText = AppResources.Reps,
            // };

            // var isConfirm = await UserDialogs.Instance.ConfirmAsync(TimebasePopUp);
            if (isConfirm == PopupAction.OK)
            {
                newAddUserExercise.IsTimeBased = true;
                if (!newAddUserExercise.IsWeighted)
                    newAddUserExercise.IsBodyweight = true;
            }
            else
            {
                newAddUserExercise.IsTimeBased = false;
            }
            FinalCreateExercise();
        }
        catch (Exception ex)
        {

        }
    }
    async void FinalCreateExercise()
    {
        try
        {

            ExerciceModel newExercise = await DrMuscleRestClient.Instance.CreateNewExercise(newAddUserExercise);
            CurrentLog.Instance.IsAddedNewExercise = true;
            newExercise.BodyPartId = newAddUserExercise.BodyPartId;
            //exercises.Add(newExercise);
            customItems1.Add(newExercise);
            await UpdateExerciseList();
            if (CurrentLog.Instance.SwapContext == null)
            {

                CurrentLog.Instance.IsFromExercise = true;
                CurrentLog.Instance.CurrentExercise = newExercise;

                if (!Config.IsFirstExerciseCreatedPopup && customItems.Count == 1)
                {
                    Config.IsFirstExerciseCreatedPopup = true;
                    var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
                    var modalPage = new Views.GeneralPopup("FirstWorkout.png", "Success!", "First exercise created", "Open exercise");
                    //TODO: MAUI
                    //modalPage.Disappearing += (sender2, e2) =>
                    //{
                    //    waitHandle.Set();
                    //};
                    //await PopupNavigation.Instance.PushAsync(modalPage);

                    //await Task.Run(() => waitHandle.WaitOne());
                }
                //       var kenko = new KenkoSingleExercisePage();
                //        await Navigation.PushAsync(kenko);

            }
        }
        catch (Exception ex)
        {

        }
    }

}

public class SelectableExerciceModel : ExerciceModel, INotifyPropertyChanged
{
    public event PropertyChangedEventHandler PropertyChanged;

    protected void SetObservableProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = "")
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return;
        field = value;
        OnPropertyChanged(propertyName);
    }

    protected virtual void OnPropertyChanged(string propertyName)
    {
        var changed = PropertyChanged;
        if (changed != null)
        {
            PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
        }
    }
    private bool _isSelected;
    public bool IsSelected
    {
        get
        { return _isSelected; }
        set
        {
            _isSelected = value;
            OnPropertyChanged("IsSelected");
        }
    }


}

