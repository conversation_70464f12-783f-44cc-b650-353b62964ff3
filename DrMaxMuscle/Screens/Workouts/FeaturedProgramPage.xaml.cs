using Acr.UserDialogs;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Resx;
using DrMuscleWebApiSharedModel;
using Microsoft.Maui.Networking;
using System.Collections.ObjectModel;
using DrMaxMuscle.Screens.Subscription;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Views;
using RGPopup.Maui.Services;
using DrMaxMuscle.Utility;

namespace DrMaxMuscle.Screens.Workouts;

public partial class FeaturedProgramPage : ContentPage
{
    public List<WorkoutTemplateGroupModel> workoutGroups;
    public ObservableCollection<WorkoutTemplateGroupModel> workoutOrderItems = new ObservableCollection<WorkoutTemplateGroupModel>();

    public FeaturedProgramPage()
    {
        InitializeComponent();
        workoutGroups = new List<WorkoutTemplateGroupModel>();
        lstProgram.ItemsSource = workoutOrderItems;
        lstProgram.ItemTapped += LstProgram_ItemTapped;
        RefreshLocalized();
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
    }

    private void RefreshLocalized()
    {
        Title = AppResources.ChoosePrograms;
    }
    //public override async void OnBeforeShow()
    //{
    //    base.OnBeforeShow();


    //}

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        var _firebase = (IFirebase)MauiProgram.ServiceProvider.GetService(typeof(IFirebase));
        _firebase.SetScreenName("featured_program_page");
        App.IsFeaturedPopup = false;
        if (workoutGroups == null || workoutGroups?.Count == 0)
        {
            try
            {
                GetUserWorkoutTemplateGroupResponseModel itemsGroupSource = await DrMuscleRestClient.Instance.GetUserFeaturedProgramGroup();
                workoutGroups = itemsGroupSource.WorkoutOrders.OrderBy(o => o.Label).ToList();
            }
            catch (Exception wx)
            {

            }
        }

        try
        {

            UpdateList();
            if (Config.SurprisePopup == false)
            {
                if (App.IsSurprisePopup)
                    return;
                App.IsSurprisePopup = true;

                var ShowRIRPopUp = await HelperClass.DisplayCustomPopup("",AppResources.TryCodeForSurprise,AppResources.GotIt,AppResources.RemindMe);
                ShowRIRPopUp.ActionSelected += (sender,action) => {
                    if(action == PopupAction.OK){
                         Config.SurprisePopup = true;
                    }
                };

                // ConfirmConfig ShowRIRPopUp = new ConfirmConfig()
                // {
                //     Title = "",
                //     Message = AppResources.TryCodeForSurprise,
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = AppResources.GotIt,
                //     CancelText = AppResources.RemindMe,
                //     OnAction = async (bool ok) =>
                //     {
                //         if (ok)
                //         {
                //             Config.SurprisePopup = true;
                //         }
                //     }
                // };
                // UserDialogs.Instance.Confirm(ShowRIRPopUp);
            }
        }
        catch (Exception ex)
        {

        }
    }
    private void UpdateList()
    {
        workoutOrderItems.Clear();
        foreach (WorkoutTemplateGroupModel em in workoutGroups)
            workoutOrderItems.Add(em);

        //WorkoutTemplateGroupModel addWorkoutOrderItem = new WorkoutTemplateGroupModel();
        //addWorkoutOrderItem.Id = -1;
        //addWorkoutOrderItem.IsSystemExercise = true;

        //addWorkoutOrderItem.Label = workoutOrderItems.Count > 0 ? AppResources.UnlockAnotherProgram : AppResources.UnlockProgram;
        //workoutOrderItems.Add(addWorkoutOrderItem);

        lstProgram.ItemsSource = workoutOrderItems;
    }
    async void NewUnlockTapped(object sender, EventArgs args)
    {
        try
        {
            await Navigation.PushAsync(new PinLockPage());
        }
        catch (Exception ex)
        {

        }
    }
    async void LstProgram_ItemTapped(object sender, ItemTappedEventArgs e)
    {
        try
        {
            if (Connectivity.NetworkAccess == NetworkAccess.Internet)
            {
                if (!App.IsV1UserTrial && !App.IsFreePlan)
                {
                    SubscriptionPage page = new SubscriptionPage();
                    page.OnBeforeShow();
                    await Navigation.PushAsync(page);
                    //await PagesFactory.PushAsync<SubscriptionPage>();
                    return;
                }
            }
            if (e.Item is WorkoutTemplateGroupModel)
            {
                if (((WorkoutTemplateGroupModel)e.Item).Id != -1)
                {
                    CurrentLog.Instance.CurrentWorkoutTemplateGroup = (WorkoutTemplateGroupModel)e.Item;
                    // uncomment code please
                    //await PagesFactory.PushAsync<ChooseYourWorkoutTemplateInGroup>();
                }
                else
                {
                    //AddMyOwnWorkoutTemplateOrder();
                    //Move to unlock code page
                    await Navigation.PushAsync(new PinLockPage());
                }
            }
        }
        catch (Exception ex)
        {

        }
    }
}
