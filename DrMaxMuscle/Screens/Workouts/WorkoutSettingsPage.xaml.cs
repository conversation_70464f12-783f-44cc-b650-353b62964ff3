﻿using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Acr.UserDialogs;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Layout;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Utility;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using RGPopup.Maui.Services;

namespace DrMaxMuscle.Screens.Workouts;

public partial class WorkoutSettingsPage : ContentPage
{
    bool IsLoading = false;
    int BackOff = 0;
    public WorkoutSettingsPage()
    {
        InitializeComponent();
        RefreshLocalized();
        //BackOffPicker.ItemsSource = new List<string>() { "Default", "Yes", "No" };
        DeleteButton.Clicked += DeleteButton_Clicked;
        CustomRepsSwitch.Toggled += async (sender, e) =>
        {
            if (CustomRepsSwitch.IsToggled)
            {
                LoadCustomReps();
                RepsStack.IsVisible = true;
            }
            else
            {
                RepsStack.IsVisible = false;
            }
            SaveCustomSettings();
        };

        RepsMinimumLess.Clicked += async (sender, e) =>
        {
            int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

            if (currentRepsMinimum > 5)
                currentRepsMinimum = currentRepsMinimum - 1;
            else
            {
                await HelperClass.DisplayCustomPopupForResult(AppResources.LessThan5Reps,
                        AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,"Got it","");
                // UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                //     Title = AppResources.LessThan5Reps
                // });
            }
            RepsMinimumLabel.Text = currentRepsMinimum.ToString();
        };

        RepsMinimumMore.Clicked += async (sender, e) =>
        {

            int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

            if (currentRepsMinimum >= currentRepsMaximum)
            await HelperClass.DisplayCustomPopupForResult("",
                        AppResources.PleaseIncreaseMaxRepsToIncreaseMinimumRepsFurther,"Got it","");
                // UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = AppResources.PleaseIncreaseMaxRepsToIncreaseMinimumRepsFurther,
                // });
            else
            {
                if (currentRepsMinimum < 30)
                    currentRepsMinimum = currentRepsMinimum + 1;
                else
                {
                    await HelperClass.DisplayCustomPopupForResult(AppResources.MoreThan30Reps,
                        AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,"Got it","");
                    // UserDialogs.Instance.AlertAsync(new AlertConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,
                    //     Title = AppResources.MoreThan30Reps
                    // });
                }
                RepsMinimumLabel.Text = currentRepsMinimum.ToString();
            }
        };
        RepsMaximumLess.Clicked += async (sender, e) =>
        {

            int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

            if (currentRepsMaximum <= currentRepsMinimum)
            await HelperClass.DisplayCustomPopupForResult("",
                        AppResources.PleaseDecreaseMinimumRepsToDecreaseMaxRepsFurther,"Got it","");
                // UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = AppResources.PleaseDecreaseMinimumRepsToDecreaseMaxRepsFurther,
                // });
            else
            {
                if (currentRepsMaximum > 5)
                    currentRepsMaximum = currentRepsMaximum - 1;
                else
                {
                    await HelperClass.DisplayCustomPopupForResult(AppResources.LessThan5Reps,
                        AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,"Got it","");
                    // UserDialogs.Instance.AlertAsync(new AlertConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                    //     Title = AppResources.LessThan5Reps
                    // });
                }
                RepsMaximumLabel.Text = currentRepsMaximum.ToString();
            }
        };

        RepsMaximumMore.Clicked += async (sender, e) =>
        {
            int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

            if (currentRepsMaximum < 30)
                currentRepsMaximum = currentRepsMaximum + 1;
            else
            {
                await HelperClass.DisplayCustomPopupForResult(AppResources.MoreThan30Reps,
                        AppResources.SetsOfMoreThan30RepsAreRatherPainfulTakeALongTimeToDoAndDoNotBuildMuscleFasterForBestResultsMaxRepsIs30,"Got it","");
                // UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = AppResources.SetsOfMoreThan30RepsAreRatherPainfulTakeALongTimeToDoAndDoNotBuildMuscleFasterForBestResultsMaxRepsIs30,
                //     Title = AppResources.MoreThan30Reps
                // });
            }

            RepsMaximumLabel.Text = currentRepsMaximum.ToString();
        };

        // Bouton save custom reps
        WorksetSwitch.Toggled += async (sender, e) =>
        {
            if (WorksetSwitch.IsToggled)
            {
                StackWarmup.IsVisible = true;
            }
            else
            {
                StackWarmup.IsVisible = false;
            }
            SaveCustomSettings();
        };

        SaveCustomRepsButton.Clicked += async (sender, e) =>
        {
            try
            {
                SaveCustomSettings();
            }
            catch (Exception)
            {
                await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                        AppResources.PleaseCheckInternetConnection,"Try again","");
                // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = AppResources.PleaseCheckInternetConnection,
                //     Title = AppResources.ConnectionError
                // });
            }
        };

        SaveSetButton.Clicked += async (sender, e) =>
        {
            try
            {
                if (!string.IsNullOrEmpty(WorksetEntry.Text))
                {
                    var count = int.Parse(WorksetEntry.Text.ReplaceWithDot());
                    if (count < 2 || count > 99)
                    {
                        await HelperClass.DisplayCustomPopupForResult("",
                        "At least 2 work sets",AppResources.Ok,"");
                        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                        // {
                        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        //     Message = "At least 2 work sets",
                        //     Title = AppResources.Ok
                        // });
                        return;
                    }
                    SaveCustomSettings();
                }
                else
                {
                    SaveCustomSettings();
                }

            }
            catch (Exception)
            {

            }
        };

        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
        OnBeforeShow();
        //BackOffPicker.Unfocused += BackOffPicker_Unfocused;
    }

    async void BackOffPicker_Unfocused(object sender, FocusEventArgs e)
    {
        SaveCustomSettings();
    }
    public async void LoadCustomReps()
    {
        try
        {
            RepsMinimumLabel.Text = LocalDBManager.Instance.GetDBSetting("repsminimum").Value;
            RepsMaximumLabel.Text = LocalDBManager.Instance.GetDBSetting("repsmaximum").Value;
        }
        catch (Exception)
        {
            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                        AppResources.PleaseCheckInternetConnection,"Try again","");
            // UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError
            // });
        }

    }

    private void RefreshLocalized()
    {

        LblNotes.Text = AppResources.Notes;

        LblSettings.Text = AppResources.SettingsUppercase;

        LblCustomReps.Text = AppResources.UseCustomReps;
        LblCustomWarmUp.Text = "Use custom sets";
        LblHowManyWarmups.Text = "Work sets";
        LblMin.Text = AppResources.Min;
        LblMax.Text = AppResources.Max;
        SaveCustomRepsButton.Text = AppResources.SaveCustomReps;


        DeleteButton.Text = AppResources.DeleteWorkout;
        LblMore.Text = AppResources.MoreUppercase;

    }

    public  async void OnBeforeShow()
    {
        DependencyService.Get<IFirebase>().SetScreenName("workout_settings_page");
        Title = CurrentLog.Instance.WorkoutTemplateSettings.Label;
        if (CurrentLog.Instance.WorkoutTemplateSettings.IsSystemExercise)
            StackCustom.IsVisible = false;
        else
            StackCustom.IsVisible = true;
        IsLoading = true;
        WorkoutTemplateSettingsModel workoutSettingsModel = await DrMuscleRestClient.Instance.GetWorkoutSettings(new WorkoutTemplateSettingsModel()
        {
            Id = CurrentLog.Instance.WorkoutTemplateSettings.Id,
        });

        if (CurrentLog.Instance.CurrentWorkoutTemplateGroup != null && CurrentLog.Instance.CurrentWorkoutTemplateGroup.IsFeaturedProgram == true)
        {
            LblMore.IsVisible = false;
            DeleteButton.IsVisible = false;
        }
        else
        {
            LblMore.IsVisible = true;
            DeleteButton.IsVisible = true;
        }
        if (workoutSettingsModel != null)
        {
            NotesEnrty.Text = workoutSettingsModel.Notes;

            CustomRepsSwitch.IsToggled = workoutSettingsModel.IsCustomReps;

            if (workoutSettingsModel.IsCustomReps)
            {
                RepsMinimumLabel.Text = workoutSettingsModel.RepsMinValue.ToString();
                RepsMaximumLabel.Text = workoutSettingsModel.RepsMaxValue.ToString();
            }
            if (workoutSettingsModel.IsBackOffSet == null)
                DefaultClick();
            else if ((bool)workoutSettingsModel.IsBackOffSet == true)
                YesClicked();
            else
                NoClicked();
            WorksetSwitch.IsToggled = workoutSettingsModel.IsCustomWorkSets;
            if (WorksetSwitch.IsToggled)
            {
                WorksetEntry.Text = Convert.ToString(workoutSettingsModel.SetCount);
            }
        }
        else
        {
            NotesEnrty.Text = "";
            CustomRepsSwitch.IsToggled = false;
            WorksetSwitch.IsToggled = false;
            WorksetEntry.Text = "";
            LoadCustomReps();
            DefaultClick();
        }
        IsLoading = false;
    }

    protected override void OnDisappearing()
    {
        DismissKeyboard();
    }

    protected override void OnAppearing()
    {
        DismissKeyboard();
    }

    void DismissKeyboard()
    {
        if (NotesEnrty.IsFocused)
            NotesEnrty.Unfocus();
    }
    void NotesEntry_Unfocused(object sender, FocusEventArgs e)
    {
        SaveCustomSettings();
    }

    async void DeleteButton_Clicked(object sender, EventArgs e)
    {

        WorkoutTemplateModel m = CurrentLog.Instance.WorkoutTemplateSettings;

        var supersetConfig = await HelperClass.DisplayCustomPopup(AppResources.DeleteWorkout,string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                            AppResources.Delete,AppResources.Cancel);
        supersetConfig.ActionSelected += async (sender,action) => {
            if(action == PopupAction.OK){
               DeleteWorkoutTemplateAction(m); 
            }
            
            
        };


        // ConfirmConfig p = new ConfirmConfig()
        // {
        //     Title = AppResources.DeleteWorkout,
        //     Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
        //     OkText = AppResources.Delete,
        //     CancelText = AppResources.Cancel,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
        // };
        // p.OnAction = (obj) =>
        // {
        //     if (obj)
        //     {
        //         DeleteWorkoutTemplateAction(m);
        //     }
        // };
        // UserDialogs.Instance.Confirm(p);
    }

    private async void DeleteWorkoutTemplateAction(WorkoutTemplateModel model)
    {
        await WorkoutIsInProgram(model);
        BooleanModel result = await DrMuscleRestClient.Instance.DeleteWorkoutTemplateModel(model);
        if (result.Result)
        {

            var page = Navigation.NavigationStack[Navigation.NavigationStack.Count - 2];
            if (page is ChooseYourWorkoutTemplateInGroup)
                await Utility.HelperClass.PopToPage<ChooseYourWorkoutTemplateInGroup>(this.Navigation);
            else
            {
                await Utility.HelperClass.PopToPage<ChooseYourCustomWorkoutPage>(this.Navigation);
                //PagesFactory.GetPage<ChooseYourCustomWorkoutPage>().workouts = new List<WorkoutTemplateModel>();
                //PagesFactory.GetPage<ChooseYourCustomWorkoutPage>().workoutGroups = new List<WorkoutTemplateGroupModel>();
                //if (Device.Android == Device.RuntimePlatform)
                //{
                //    await PagesFactory.PopAsync();
                //}
                //else
                //    await PagesFactory.PopToPage<ChooseYourCustomWorkoutPage>();
            }
        }
    }

    void SetEntry_TextChanged(System.Object sender, TextChangedEventArgs e)
    {
        const string textRegex = @"^\d+(?:\d{0,2})?$";
        var text = e.NewTextValue.Replace(",", ".");
        bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        if (IsValid == false && !string.IsNullOrEmpty(e.NewTextValue))
        {
            ((Entry)sender).Text = e.NewTextValue.Substring(0, e.NewTextValue.Length - 1);
        }
    }

    private async Task WorkoutIsInProgram(WorkoutTemplateModel model)
    {
        try
        {


            var workouts = CurrentLog.Instance.CurrentWorkoutTemplateGroup.WorkoutTemplates;

            List<WorkoutTemplateModel> newList = new List<WorkoutTemplateModel>();
            foreach (var item in workouts)
            {
                if (item != model)
                    newList.Add(item);
            }

            var workTemplateGroup = new WorkoutTemplateGroupModel()
            {
                Id = CurrentLog.Instance.CurrentWorkoutTemplateGroup.Id,
                WorkoutTemplates = newList
            };
            BooleanModel result = await DrMuscleRestClient.Instance.CreateNewWorkoutTemplateOrder(workTemplateGroup);
        }
        catch (Exception ex)
        {

        }
    }

    private async void SaveCustomSettings()
    {
        if (IsLoading)
            return;

        var workoutSettings = new WorkoutTemplateSettingsModel();
        workoutSettings.Id = CurrentLog.Instance.WorkoutTemplateSettings.Id;
        workoutSettings.Notes = NotesEnrty.Text;

        //ResetReco();


        workoutSettings.IsCustomReps = CustomRepsSwitch.IsToggled;

        if (CustomRepsSwitch.IsToggled)
        {
            int RepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int RepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);
            workoutSettings.RepsMinValue = RepsMinimum;
            workoutSettings.RepsMaxValue = RepsMaximum;
            RepsStack.IsVisible = true;
        }

        workoutSettings.IsCustomWorkSets = WorksetSwitch.IsToggled;
        if (WorksetSwitch.IsToggled)
        {

            if (!string.IsNullOrEmpty(WorksetEntry.Text))
            {
                var count = int.Parse(WorksetEntry.Text.ReplaceWithDot());
                if (count >= 2 || count <= 99)
                {
                    workoutSettings.SetCount = count;
                }
            }
            else
            {
                workoutSettings.SetCount = null;
                workoutSettings.IsCustomWorkSets = false;
            }
        }

        if (BackOff == 0)
            workoutSettings.IsBackOffSet = null;
        else if (BackOff == 1)
            workoutSettings.IsBackOffSet = true;
        else
            workoutSettings.IsBackOffSet = false;
        await DrMuscleRestClient.Instance.AddUpdateWorkoutSettings(workoutSettings);
    }


    void BtnDefault_Clicked(System.Object sender, System.EventArgs e)
    {
        DefaultClick();
        SaveCustomSettings();

    }

    void BtnYes_Clicked(System.Object sender, System.EventArgs e)
    {
        YesClicked();
        SaveCustomSettings();
    }

    void BtnNo_Clicked(System.Object sender, System.EventArgs e)
    {
        NoClicked();
        SaveCustomSettings();
    }


    void DefaultClick()
    {
        BackOff = 0;
        BtnYes.BackgroundColor = Colors.Transparent;
        YesGradient.BackgroundColor = Colors.Transparent;


        DefaultGradient.BackgroundColor = Constants.AppThemeConstants.BlueColor;

        BtnYes.TextColor = Color.FromHex("#0C2432");
        BtnDefault.TextColor = Colors.White;

        BtnNo.TextColor = Color.FromHex("#0C2432");
        NoGradient.BackgroundColor = Colors.Transparent;
        BtnNo.BackgroundColor = Colors.Transparent;
    }


    void YesClicked()
    {
        BackOff = 1;
        BtnYes.BackgroundColor = Colors.Transparent;
        //BtnKg.BackgroundColor = Color.FromHex("#5CD196");
        BtnYes.TextColor = Colors.White;
        BtnDefault.TextColor = Color.FromHex("#0C2432");
        YesGradient.BackgroundColor = Constants.AppThemeConstants.BlueColor;

        BtnNo.TextColor = Color.FromHex("#0C2432");
        NoGradient.BackgroundColor = Colors.Transparent;

    }


    void NoClicked()
    {
        BackOff = 2;
        BtnDefault.BackgroundColor = Colors.Transparent;
        BtnDefault.TextColor = Color.FromHex("#0C2432");
        BtnYes.TextColor = Color.FromHex("#0C2432");
        YesGradient.BackgroundColor = Colors.Transparent;
        DefaultGradient.BackgroundColor = Colors.Transparent;

        BtnNo.TextColor = Colors.White; ;
        NoGradient.BackgroundColor = Constants.AppThemeConstants.BlueColor; ;
    }
}

