using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Layout;
using DrMaxMuscle.Resx;
using Microsoft.Maui.Controls;

namespace DrMaxMuscle.Screens.Workouts;

public partial class ChooseGymOrHome : ContentPage
{
	public ChooseGymOrHome()
	{
		InitializeComponent();
        GymWorkoutsButton.Clicked += GymWorkoutsButton_Clicked;
        HomeWorkoutsButton.Clicked += HomeWorkoutsButton_Clicked;
        BodyweightWorkoutsButton.Clicked += BodyweightWorkoutsButton_Clicked;


        RefreshLocalized();
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
    }

    async void GymWorkoutsButton_Clicked(object sender, EventArgs e)
    {
        try
        {
            ChooseYourGymWorkoutPage chooseYourGymWorkoutPage = new ChooseYourGymWorkoutPage();
            chooseYourGymWorkoutPage.OnBeforeShow();
            await Navigation.PushAsync(chooseYourGymWorkoutPage);
        }
        catch (Exception ex)
        {

        }
    }

    async void HomeWorkoutsButton_Clicked(object sender, EventArgs e)
    {
        try
        {
            ChooseYourHomeWorkoutPage chooseYourHomeWorkoutPage = new ChooseYourHomeWorkoutPage();
            chooseYourHomeWorkoutPage.OnBeforeShow();
            await Navigation.PushAsync(chooseYourHomeWorkoutPage);
        }
        catch (Exception ex)
        {

        }
    }

    async void BodyweightWorkoutsButton_Clicked(object sender, EventArgs e)
    {
        try
        {
            await Navigation.PushAsync(new ChooseYourBodyweightWorkoutPage());
        }
        catch (Exception ex)
        {

        }
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();
        if (CurrentLog.Instance.SwapContext != null)
            CurrentLog.Instance.SwapContext = null;
        var _firebase = (IFirebase)MauiProgram.ServiceProvider.GetService(typeof(IFirebase));
        _firebase.SetScreenName("choose_gym_or_home");

    }
    private void RefreshLocalized()
    {
        Title = AppResources.ChooseWorkout;
        GymWorkoutsButton.Text = AppResources.Gym;
        HomeWorkoutsButton.Text = "Home gym";
        BodyweightWorkoutsButton.Text = AppResources.Bodyweight;
    }

    public void OnBeforeShow()
    {
        this.ToolbarItems.Clear();
        var generalToolbarItem = new ToolbarItem("Featured", "menu", SlideFeaturedWorkoutAction, ToolbarItemOrder.Primary, 0);
        this.ToolbarItems.Add(generalToolbarItem);
    }

    private void SlideFeaturedWorkoutAction()
    {
        SlideMenu.ShowFeaturedMenu();
        SlideMenu.ToggleMenu();
    }
}