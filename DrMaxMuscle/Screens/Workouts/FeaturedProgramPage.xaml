<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             x:Class="DrMaxMuscle.Screens.Workouts.FeaturedProgramPage"
             Title="FeaturedProgramPage">
    <AbsoluteLayout>
        <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">

            <t:DrMuscleListView
        x:Name="lstProgram"
        Margin="0,20,0,20"
                ItemsSource="{Binding workoutOrderItems}"
        BackgroundColor="Transparent"
        VerticalOptions="FillAndExpand"
        SeparatorColor="White">
                <t:DrMuscleListView.ItemTemplate>
                    <DataTemplate>
                        <ViewCell
                    Height="45">
                            <StackLayout
                        Orientation="Horizontal"
                        BackgroundColor="Transparent">
                                <StackLayout
                            Orientation="Horizontal"
                            HorizontalOptions="StartAndExpand">
                                    <Label
                                Text="{Binding Label}"
                                VerticalTextAlignment="Center"
                                Style="{StaticResource LabelStyle}" />
                                </StackLayout>
                            </StackLayout>
                        </ViewCell>
                    </DataTemplate>
                </t:DrMuscleListView.ItemTemplate>
                <!--<t:DrMuscleListView.Footer>
                    <BoxView HeightRequest="100" Color="Transparent" BackgroundColor="Transparent" />
                </t:DrMuscleListView.Footer>-->
            </t:DrMuscleListView>
        </StackLayout>
        <Image Source="plusblack.png" Margin="0,0,20,20" HeightRequest="70" WidthRequest="70" VerticalOptions="Center" HorizontalOptions="Center" Aspect="AspectFit" AbsoluteLayout.LayoutFlags="PositionProportional" AbsoluteLayout.LayoutBounds="1, 1, 90, 90" >
            <Image.GestureRecognizers>
                <TapGestureRecognizer Tapped="NewUnlockTapped" />
            </Image.GestureRecognizers>
        </Image>
    </AbsoluteLayout>
</ContentPage>