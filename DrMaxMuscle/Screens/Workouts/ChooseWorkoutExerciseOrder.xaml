﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="DrMaxMuscle.Screens.Workouts.ChooseWorkoutExerciseOrder"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:effects="clr-namespace:DrMaxMuscle.Effects"
    xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
    xmlns:t="clr-namespace:DrMaxMuscle.Layout"
    Title="ChooseWorkoutExerciseOrder">
    <StackLayout
        Padding="20,0,20,0"
        AbsoluteLayout.LayoutBounds="0, 0, 1, 1"
        AbsoluteLayout.LayoutFlags="All"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand">
        <StackLayout VerticalOptions="FillAndExpand">
            <StackLayout
                Padding="0"
                BackgroundColor="Transparent"
                VerticalOptions="FillAndExpand">
                <t:DrMuscleListView
                    x:Name="ExerciseListView"
                    effects:Sorting.IsSortable="true"
                    ios:ListView.SeparatorStyle="FullWidth"
                    BackgroundColor="Transparent"
                    RowHeight="50"
                    SeparatorColor="#264457"
                    SeparatorVisibility="Default"
                    VerticalOptions="FillAndExpand">
                    <ListView.ItemTemplate>
                        <DataTemplate>
                            <ViewCell Height="50" BindingContextChanged="OnBindingContextChanged">
                                <StackLayout Orientation="Horizontal">
                                    <StackLayout.Effects>
                                        <effects:ViewShadowEffect
                                            DistanceX="5"
                                            DistanceY="5"
                                            Radius="5"
                                            Color="Gray" />
                                    </StackLayout.Effects>
                                    <StackLayout
                                        HorizontalOptions="StartAndExpand"
                                        Orientation="Horizontal"
                                        VerticalOptions="Center">
                                        <Label
                                            HorizontalOptions="StartAndExpand"
                                            LineBreakMode="{OnIdiom Phone=WordWrap,
                                                                    Default=NoWrap}"
                                            MaxLines="{OnIdiom Phone=2,
                                                               Default=1}"
                                            Style="{StaticResource LabelStyle2}"
                                            Text="{Binding Label}"
                                            VerticalTextAlignment="Center"
                                            FontSize="16"
                                            Margin="0,0,10,0"/>

                                    </StackLayout>
                                    <StackLayout
                                        Margin="{OnPlatform iOS='0,0,-40,0',
                                                            Android='0'}"
                                        HorizontalOptions="End"
                                        VerticalOptions="Center"
                                        Orientation="Horizontal">
                                        <Image Source="dragindicator.png" />

                                    </StackLayout>
                                </StackLayout>
                            </ViewCell>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                </t:DrMuscleListView>
            </StackLayout>
        </StackLayout>
        <StackLayout
            Padding="0,0,0,20"
            Orientation="Horizontal"
            VerticalOptions="End">
            <t:DrMuscleButton
                x:Name="SaveWorkoutButton"
                TextTransform="Uppercase"
                BorderColor="#195377"
                HeightRequest="58"
                HorizontalOptions="FillAndExpand"
                Style="{StaticResource buttonStyle}" />
        </StackLayout>
    </StackLayout>
</ContentPage>
