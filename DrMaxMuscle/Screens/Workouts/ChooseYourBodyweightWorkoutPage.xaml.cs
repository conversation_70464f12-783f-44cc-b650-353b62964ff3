﻿using Acr.UserDialogs;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Resx;
using DrMuscleWebApiSharedModel;
using System.Collections.ObjectModel;
using DrMaxMuscle.Screens.Workouts;
using ConfirmConfig = Acr.UserDialogs.ConfirmConfig;
using UserDialogs = Acr.UserDialogs.UserDialogs;
using System.Collections.Generic;
using Microsoft.Maui.Controls;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Screens.Subscription;
using DrMaxMuscle.Views;
using RGPopup.Maui.Services;
using DrMaxMuscle.Utility;
using CommunityToolkit.Maui.Views;

namespace DrMaxMuscle.Screens.Workouts;

public partial class ChooseYourBodyweightWorkoutPage : ContentPage
{
    private List<WorkoutTemplateModel> workouts;
    private List<WorkoutTemplateGroupModel> workoutGroups;
    public ObservableCollection<WorkoutTemplateModel> workoutItems = new ObservableCollection<WorkoutTemplateModel>();
    public ObservableCollection<WorkoutTemplateModel> workoutItems2 = new ObservableCollection<WorkoutTemplateModel>();
    public ObservableRangeCollection<ProgramGroupSection> ExeList { get; set; }
      = new ObservableRangeCollection<ProgramGroupSection>();

    GetUserWorkoutTemplateGroupResponseModel programWorkout;

    public ChooseYourBodyweightWorkoutPage()
    {
        InitializeComponent();

        //WorkoutListView.ItemsSource = workoutItems;

        //WorkoutListView.ItemTapped += WorkoutListView_ItemTapped;
        ExpandableList.ItemsSource = ExeList;
        ExpandableList.ItemTapped += WorkoutListView_ItemTapped;

        if (LocalDBManager.Instance.GetDBSetting("WorkoutTypeList") == null)
            LocalDBManager.Instance.SetDBSetting("WorkoutTypeList", "0");

        if (LocalDBManager.Instance.GetDBSetting("WorkoutTypeListDayPerWeek") == null)
            LocalDBManager.Instance.SetDBSetting("WorkoutTypeListDayPerWeek", "0");

        if (LocalDBManager.Instance.GetDBSetting("WorkoutOrderList") == null)
            LocalDBManager.Instance.SetDBSetting("WorkoutOrderList", "0");

        RefreshLocalized();
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
        OnBeforeShow();
    }

    private void RefreshLocalized()
    {
        Title = AppResources.ChooseWorkout;
        //BodyWeightWorkouts.Text = AppResources.BodyweightWorkouts24xWk;
    }

    public async void OnBeforeShow()
    {
        DependencyService.Get<IFirebase>().SetScreenName("choose_bodyweight_workout");

        try
        {
            if (LocalDBManager.Instance.GetDBSetting("Equipment") == null)
                LocalDBManager.Instance.SetDBSetting("Equipment", "false");

            if (LocalDBManager.Instance.GetDBSetting("ChinUp") == null)
                LocalDBManager.Instance.SetDBSetting("ChinUp", "true");

            if (LocalDBManager.Instance.GetDBSetting("Dumbbell") == null)
                LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");

            if (LocalDBManager.Instance.GetDBSetting("Plate") == null)
                LocalDBManager.Instance.SetDBSetting("Plate", "true");

            if (LocalDBManager.Instance.GetDBSetting("Pully") == null)
                LocalDBManager.Instance.SetDBSetting("Pully", "true");

            //GetUserWorkoutTemplateResponseModel itemsSource;
            //if (LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true")
            //{
            //    itemsSource = await DrMuscleRestClient.Instance.GetCustomizedUserWorkout(new EquipmentModel()
            //    {
            //        IsEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true",
            //        IsChinUpBarEnabled = LocalDBManager.Instance.GetDBSetting("ChinUp").Value == "true",
            //        IsPullyEnabled = LocalDBManager.Instance.GetDBSetting("Pully").Value == "true"
            //    });
            //}
            //else
            //    itemsSource = await DrMuscleRestClient.Instance.GetUserWorkout();
            //programWorkout = await DrMuscleRestClient.Instance.GetSystemWorkoutGroup();
            if (programWorkout == null)
            {
                if (LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true")
                {
                    programWorkout = await DrMuscleRestClient.Instance.GetOnlyCustomizedSystemWorkoutGroup(
                        new EquipmentModel()
                        {
                            IsEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true",
                            IsChinUpBarEnabled = LocalDBManager.Instance.GetDBSetting("ChinUp").Value == "true",
                            IsPullyEnabled = LocalDBManager.Instance.GetDBSetting("Pully").Value == "true",
                            IsPlateEnabled = LocalDBManager.Instance.GetDBSetting("Plate").Value == "true",
                            IsDumbbellEnabled = LocalDBManager.Instance.GetDBSetting("Dumbbell").Value == "true"
                        });
                }
                else
                    programWorkout = await DrMuscleRestClient.Instance.GetOnlySystemWorkoutGroup();
            }

            //workouts = itemsSource.Workouts;
            await UpdateWorkoutList();
        }
        catch (Exception e)
        {
            ConnectionErrorPopup();

        }
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();

        // Ensure UI operations happen on the main thread
        Device.BeginInvokeOnMainThread(async () =>
        {
            if (App.IsBodyweightPopup)
                return;
            App.IsBodyweightPopup = true;

            var ShowPopUp = await HelperClass.DisplayCustomPopup("Bodyweight workouts",
            "Choose a level. Workouts have more exercises on higher levels. Save a workout to change program. Visit the Learn tab to get custom tips for your level.",
            AppResources.GotIt,AppResources.RemindMe);
            ShowPopUp.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    Config.ShowBodyweightPopup = true;
                }
                else
                {
                    Config.ShowBodyweightPopup = false;
                }
            };

            // ConfirmConfig ShowPopUp = new ConfirmConfig()
            // {
            //     Title = "Bodyweight workouts",
            //     Message = "Choose a level. Workouts have more exercises on higher levels. Save a workout to change program. Visit the Learn tab to get custom tips for your level.",
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = AppResources.GotIt,
            //     CancelText = AppResources.RemindMe,
            //     OnAction = async (bool ok) =>
            //     {
            //         if (ok)
            //         {
            //             Config.ShowBodyweightPopup = true;
            //         }
            //         else
            //         {
            //             Config.ShowBodyweightPopup = false;
            //         }
            //     }
            // };
            // await Task.Delay(100);
            // UserDialogs.Instance.Confirm(ShowPopUp);
        });

    }
    private async Task UpdateWorkoutList()
    {
        //if (workouts == null)
        //    return;
        //workoutItems.Clear();
        //List<WorkoutTemplateModel> wkt;

        //var request = workouts.Where(e => e.IsSystemExercise == true && e.Label.Contains("Bodyweight"));

        //wkt = request.ToList();

        //foreach (WorkoutTemplateModel em in wkt)
        //    workoutItems.Add(em);

        if (programWorkout == null || programWorkout.WorkoutOrders == null)
            return;

        ExeList.Clear();

        var bodyweightGroups = programWorkout.WorkoutOrders
            .Where(x => x.Label.Contains("Bodyweight"))
            .ToList();

        foreach (var groupItem in bodyweightGroups)
        {
            var bodyPartShoulders = new WorkoutTemplateGroupModel { Label = groupItem.Label };
            bodyPartShoulders.WorkoutTemplates = new List<WorkoutTemplateModel>();
            bodyPartShoulders.WorkoutTemplates.AddRange(groupItem.WorkoutTemplates);
            ProgramGroupSection section = new ProgramGroupSection(bodyPartShoulders, false);
            ExeList.Add(section);
        }
        // Fin du dup

    }

    void Section_Tapped(object sender, TappedEventArgs e)
    {
        var obj = (ProgramGroupSection)((StackLayout)sender).BindingContext;
        obj.Expanded = !obj.Expanded;

        try
        {
            var itemsSource = ExpandableList.ItemsSource;
            ExpandableList.ItemsSource = null;
            ExpandableList.ItemsSource = itemsSource;

            // Force the layout to update
            foreach (var item in ExpandableList.TemplatedItems)
            {
                if (item is ViewCell viewCell)
                {
                    viewCell.ForceUpdateSize();
                }
            }

        }
        catch (Exception ex)
        {

        }
    }
    private void StateImage_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName.Equals("Source"))
        {
            var image = sender as Image;
            image.Opacity = 0;
            image.FadeTo(1, 1000);
        }
    }


    public void OnReset(object sender, EventArgs e)
    {
        try
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is WorkoutTemplateModel)
            {
                WorkoutTemplateModel m = (WorkoutTemplateModel)mi.CommandParameter;
                CurrentLog.Instance.WorkoutTemplateSettings = m;
                OnCancelClicked(sender, e);
            }
            Navigation.PushAsync(new WorkoutSettingsPage());
        }
        catch (Exception ex)
        {

        }
    }
    void OnCancelClicked(object sender, System.EventArgs e)
    {
        StackLayout s = ((StackLayout)((Button)sender).Parent);
        SetVisibility(s.Children[0], false);
        SetVisibility(s.Children[1], false);
        SetVisibility(s.Children[2], true);

    }
    void SetVisibility(IView view, bool isVisible)
    {
        if (view is View mauiView)
        {
            mauiView.IsVisible = isVisible;
        }
    }

    void OnContextMenuClicked(object sender, System.EventArgs e)
    {
        StackLayout s = ((StackLayout)((Button)sender).Parent);
        SetVisibility(s.Children[0], true);
        SetVisibility(s.Children[1], true);
        SetVisibility(s.Children[2], false);
    }

    public async void OnEdit(object sender, EventArgs e)
    {
        var mi = ((Button)sender);
        if (mi.CommandParameter is WorkoutTemplateModel)
        {
            WorkoutTemplateModel m = (WorkoutTemplateModel)mi.CommandParameter;
            CurrentLog.Instance.CurrentWorkoutTemplate = m;
            // Uncomment code please
            //await PagesFactory.PushAsync<AddExercisesToWorkoutPage>();
        }
        else
        {
            WorkoutTemplateGroupModel m = (WorkoutTemplateGroupModel)mi.CommandParameter;
            CurrentLog.Instance.CurrentWorkoutTemplateGroup = m;
            // Uncomment code please
            //await PagesFactory.PushAsync<AddWorkoutToWorkoutOrderPage>();
        }

    }

    private void OnBindingContextChanged(object sender, EventArgs e)
    {
        base.OnBindingContextChanged();

        if (((BindableObject)sender).BindingContext == null)
            return;
        WorkoutTemplateModel m = (WorkoutTemplateModel)((BindableObject)sender).BindingContext;
        if (m.IsSystemExercise)
            ((Cell)sender).ContextActions.Clear();

    }



    private async void WorkoutListView_ItemTapped(object sender, ItemTappedEventArgs e)
    {
        try
        {
            //if (await CanGoFurther())
            //{
            if (e.Item is WorkoutTemplateModel workoutTemplate)
            {
                if (workoutTemplate.Id != -1)
                {
                    LocalDBManager.Instance.SetDBSetting("CurrentWorkoutId", workoutTemplate.Id.ToString());
                    CurrentLog.Instance.CurrentWorkoutTemplate = workoutTemplate;
                    if (workoutTemplate.Exercises.Count > 0)
                        CurrentLog.Instance.WorkoutTemplateCurrentExercise = workoutTemplate.Exercises.First();
                    //else
                    //    return;
                    CurrentLog.Instance.WorkoutStarted = true;
                    try
                    {

                        if (programWorkout != null && programWorkout.WorkoutOrders != null)
                        {
                            CurrentLog.Instance.CurrentWorkoutTemplateGroup = programWorkout?.WorkoutOrders?.FirstOrDefault(x => x.WorkoutTemplates.Contains(workoutTemplate));
                        }

                    }
                    catch (Exception ex)
                    {

                    }
                    var workoutModel = LocalDBManager.Instance.GetDBSetting($"Workout{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}")?.Value;
                    if (!string.IsNullOrEmpty(workoutModel))
                    {
                        var model = Newtonsoft.Json.JsonConvert.DeserializeObject<WorkoutTemplateModel>(workoutModel);
                        CurrentLog.Instance.CurrentWorkoutTemplate = model;
                    }
                    Device.BeginInvokeOnMainThread(async () =>
                    {
                        //Uncomment code please
                        //await PagesFactory.PushAsync<KenkoChooseYourWorkoutExercisePage>();
                        var page = new KenkoChooseYourWorkoutExercisePage();
                        App.ShowTopButtonWorkout = true;
                        page.OnBeforeShow();
                        await Navigation.PushAsync(page);

                    });
                }
                else
                {
                    AddMyOwnWorkout();
                }
            }
            if (e.Item is WorkoutTemplateGroupModel workoutTemplate1)
            {
                if (workoutTemplate1.Id != -1)
                {
                    CurrentLog.Instance.CurrentWorkoutTemplateGroup = (WorkoutTemplateGroupModel)e.Item;
                    // Uncomment code please
                    //await PagesFactory.PushAsync<ChooseYourWorkoutTemplateInGroup>();
                }
                else
                {
                    AddMyOwnWorkoutTemplateOrder();
                }
            }
        }
        catch (Exception ex)
        {

        }
        //}
        //else
        //{
        //    await PagesFactory.PushAsync<SubscriptionPage>();
        //}
    }

    private async void AddMyOwnWorkoutTemplateOrder()
    {
        CustomPromptConfig p = new CustomPromptConfig(AppResources.CreateNewProgram,AppResources.NameYourProgram,AppResources.CreateNew,AppResources.Cancel,
            "");

            p.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    AddWorkoutOrderAction(action,p.text);
                }
            };

        await Application.Current.MainPage.ShowPopupAsync(p);

        // PromptConfig p = new PromptConfig()
        // {
        //     InputType = InputType.Default,
        //     IsCancellable = true,
        //     Title = AppResources.CreateNewProgram,
        //     Placeholder = AppResources.NameYourProgram,
        //     OkText = AppResources.CreateNew,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = new Action<PromptResult>(AddWorkoutOrderAction)
        // };

        // UserDialogs.Instance.Prompt(p);
    }

    private async void AddMyOwnWorkout()
    {

        CustomPromptConfig p = new CustomPromptConfig(AppResources.CreateNewWorkout,AppResources.NameYourWorkout,AppResources.CreateNew,AppResources.Cancel,
            "");

            p.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    AddWorkoutAction(action,p.text);
                }
            };

        await Application.Current.MainPage.ShowPopupAsync(p);
        // PromptConfig p = new PromptConfig()
        // {
        //     InputType = InputType.Default,
        //     IsCancellable = true,
        //     Title = AppResources.CreateNewWorkout,
        //     Placeholder = AppResources.NameYourWorkout,
        //     OkText = AppResources.CreateNew,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = new Action<PromptResult>(AddWorkoutAction)
        // };

        // UserDialogs.Instance.Prompt(p);
    }

    private async void AddWorkoutOrderAction(PopupAction response,string Text)
    {
        if (response == PopupAction.OK)
        {
            try
            {
                WorkoutTemplateGroupModel newWorkoutOrder = new WorkoutTemplateGroupModel()
                {
                    Label = Text,
                    Id = -1
                };
                /*await DrMuscleRestClient.Instance.CreateNewWorkoutTemplate(new AddUserWorkoutTemplateModel()
            {
                WorkoutLabel = response.Text
            });
            */
                CurrentLog.Instance.CurrentWorkoutTemplateGroup = newWorkoutOrder;
                // Uncomment code please
                //await PagesFactory.PushAsync<AddWorkoutToWorkoutOrderPage>();
            }
            catch (Exception e)
            {
                // Uncomment code please
                //ConnectionErrorPopup();

            }
        }
    }

    private async void AddWorkoutAction(PopupAction response,string Text)
    {
        if (response == PopupAction.OK)
        {
            try
            {
                WorkoutTemplateModel newWorkout = new WorkoutTemplateModel()
                {
                    Label = Text,
                    Id = -1
                };
                /*await DrMuscleRestClient.Instance.CreateNewWorkoutTemplate(new AddUserWorkoutTemplateModel()
            {
                WorkoutLabel = response.Text
            });
            */
                CurrentLog.Instance.CurrentWorkoutTemplate = newWorkout;
                CurrentLog.Instance.WorkoutTemplateCurrentExercise = null;
                CurrentLog.Instance.WorkoutStarted = false;
                // Uncomment code please
                //await PagesFactory.PushAsync<AddExercisesToWorkoutPage>();
            }
            catch (Exception e)
            {
                // Uncomment code please
                //ConnectionErrorPopup();

            }
        }
    }
    async Task ConnectionErrorPopup()
    {
        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        // {
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     Message = AppResources.PleaseCheckInternetConnection,
        //     Title = AppResources.ConnectionError,
        //     OkText = "Try again"
        // });

        await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                        AppResources.PleaseCheckInternetConnection,"Try again","");
    }
}

