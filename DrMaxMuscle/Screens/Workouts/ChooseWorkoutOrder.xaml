﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:DrMaxMuscle"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
             xmlns:effects="clr-namespace:DrMaxMuscle.Effects"
             x:Class="DrMaxMuscle.Screens.Workouts.ChooseWorkoutOrder"
             Title="ChooseWorkoutOrder">
    <AbsoluteLayout>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
      <StackLayout VerticalOptions="FillAndExpand">
        <StackLayout BackgroundColor="Transparent" Padding="0" VerticalOptions="FillAndExpand">
          <t:DrMuscleListView x:Name="ExerciseListView" BackgroundColor="Transparent" VerticalOptions="FillAndExpand" SeparatorColor="#264457"
                              RowHeight="50"
                              SeparatorVisibility="Default"
                              ios:ListView.SeparatorStyle="FullWidth"
                              effects:Sorting.IsSortable="true">
            <ListView.ItemTemplate>
              <DataTemplate>
                <ViewCell BindingContextChanged="OnBindingContextChanged" Height="50">
                  <StackLayout Orientation="Horizontal">
                      <StackLayout.Effects>
						<effects:ViewShadowEffect Radius="5" DistanceX="5" DistanceY="5" Color="Gray">
<!--							<effects:ViewShadowEffect.Color>
								<OnPlatform x:TypeArguments="Color" iOS="Gray" Android="White" WinPhone="Red" />
							</effects:ViewShadowEffect.Color>-->
						</effects:ViewShadowEffect>
					</StackLayout.Effects>
                    <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand" >
                      <Label Text="{Binding Label}" HorizontalOptions="Start" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                    </StackLayout>
                    <StackLayout Orientation="Horizontal" HorizontalOptions="EndAndExpand" Margin="{OnPlatform Android='0', iOS='0,0,-40,0'}">
                        
                        <Image Source="dragindicator.png">
                            
                        </Image>
                      <!--<t:DrMuscleButton Image="uparrow.png" HorizontalOptions="End" Style="{StaticResource buttonTransparent}" WidthRequest="48"></t:DrMuscleButton>
                      <t:DrMuscleButton Image="downarrow.png" HorizontalOptions="End" Style="{StaticResource buttonTransparent}" WidthRequest="48"></t:DrMuscleButton>-->
                    </StackLayout>
                  </StackLayout>
                </ViewCell>
              </DataTemplate>
            </ListView.ItemTemplate>
          </t:DrMuscleListView>
        </StackLayout>
      </StackLayout>
      <StackLayout Orientation="Horizontal" VerticalOptions="End" Padding="0,0,0,20">
                <t:DrMuscleButton x:Name="SaveWorkoutButton"
                                  TextTransform="Uppercase"
                                  BorderColor="#195377"
                                  HorizontalOptions="FillAndExpand"
                                  Style="{StaticResource buttonStyle}">
                </t:DrMuscleButton>
      </StackLayout>
    </StackLayout>
  </AbsoluteLayout>
</ContentPage>

