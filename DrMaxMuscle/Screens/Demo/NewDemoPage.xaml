<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.Demo.NewDemoPage"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:locali="clr-namespace:DrMaxMuscle.Helpers"
             xmlns:cells="clr-namespace:DrMaxMuscle.Cells"
             xmlns:converter="clr-namespace:DrMaxMuscle.Convertors"
             xmlns:heaer="clr-namespace:DrMaxMuscle.Screens.Workouts"
             Title="NewDemoPage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <DataTemplate x:Key="KenkoRegularTemplate" x:Name="RegularTemplate">
                <ViewCell Height="100" BindingContextChanged="OnBindingContextChanged">
                    <Grid IsClippedToBounds="True">

                        <ffimageloading:CachedImage Grid.Row="0" Source="gradient_background" Aspect="Fill" Margin="4,10,4,0" ErrorPlaceholder="backgroundblack.png">
                            <ffimageloading:CachedImage.Triggers>
                                <DataTrigger TargetType="ffimageloading:CachedImage" Binding="{Binding IsFinishWorkoutExe}" Value="false">
                                    <Setter Property="IsVisible" Value="true" />
                                </DataTrigger>
                                <DataTrigger TargetType="ffimageloading:CachedImage" Binding="{Binding IsFinishWorkoutExe}" Value="true">
                                    <Setter Property="IsVisible" Value="false" />
                                </DataTrigger>
                            </ffimageloading:CachedImage.Triggers>
                        </ffimageloading:CachedImage>
                        <ffimageloading:CachedImage Grid.Row="0" Source="{Binding BodyPartId, Converter={StaticResource IdToTransBodyConverter}}" ErrorPlaceholder="backgroundblack.png" Aspect="Fill" Margin="4,10,4,0"/>
                        <Frame Margin="4,10,4,0" Grid.Row="0" HasShadow="False" BorderColor="Transparent"  CornerRadius="4" BackgroundColor="Transparent" HeightRequest="100" Padding="20,5">
                            <Frame.Triggers>
                                <DataTrigger TargetType="Frame" Binding="{Binding IsFinishWorkoutExe}" Value="true">
                                    <Setter Property="Margin" Value="8,10,8,0" />
                                    <Setter Property="BackgroundColor" Value="White" />
                                    <Setter Property="Padding" Value="15,5,15,10" />
                                </DataTrigger>
                                <DataTrigger TargetType="Frame" Binding="{Binding IsFinishWorkoutExe}" Value="false">
                                    <Setter Property="Margin" Value="4,10,4,0" />
                                    <Setter Property="Padding" Value="8,10,20,10" />
                                </DataTrigger>


                            </Frame.Triggers>
                            <StackLayout>
                                <StackLayout Orientation="Horizontal" VerticalOptions="CenterAndExpand">
                                    <StackLayout.Triggers>
                                        <DataTrigger TargetType="StackLayout" Binding="{Binding IsFinishWorkoutExe}" Value="false">
                                            <Setter Property="IsVisible" Value="true" />
                                        </DataTrigger>
                                        <DataTrigger TargetType="StackLayout" Binding="{Binding IsFinishWorkoutExe}" Value="true">
                                            <Setter Property="IsVisible" Value="false" />
                                        </DataTrigger>
                                    </StackLayout.Triggers>
                                    <Image Source="done_two" WidthRequest="21" Aspect="AspectFit" HorizontalOptions="Start" VerticalOptions="FillAndExpand" IsVisible="{Binding IsFinished}" Margin="10,0,0,0"/>
                                    <ffimageloading:CachedImage Source="{Binding BodyPartId, Converter={StaticResource IdToBodyConverter}}" ErrorPlaceholder="backgroundblack.png" HeightRequest="90" WidthRequest="65" Aspect="AspectFit">
                                       
                                    </ffimageloading:CachedImage>
                                    <StackLayout Spacing="0" VerticalOptions="Center" Margin="0,0,0,8">
                                        <Label Text="{Binding CountNo}" HorizontalOptions="Start" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" TextColor="#AAFFFFFF" FontSize="16" />
                                        <Label Text="{Binding Label}" HorizontalOptions="Start" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" TextColor="#FFFFFF" FontSize="19" MaxLines="2" />
                                    </StackLayout>
                                    <Image Source="swap" WidthRequest="10" Aspect="AspectFit" HorizontalOptions="Start" IsVisible="{Binding IsSwapTarget}" Margin="3,6" VerticalOptions="Start" />
                                    <Label HorizontalOptions="StartAndExpand" />
                                    <StackLayout Orientation="Horizontal" VerticalOptions="Center" HorizontalOptions="End">
                                      
                                        <Button Clicked="OnVideo" Text="{Binding [Video].Value, Mode=OneWay,  Source={x:Static locali:ResourceLoader.Instance}}"  CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextVideoButton}" TextColor="#0C2432" BackgroundColor="#ECFF92"  />
                                        <Button Clicked="OnSwap" Text="{Binding [Swap].Value, Mode=OneWay, Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextSwapButton}" TextColor="#0C2432" BackgroundColor="#ECFF92"/>
                                        <Button Clicked="OnRestore" Text="{Binding [Restore].Value, Mode=OneWay, Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextRestoreButton}" TextColor="#0C2432" BackgroundColor="#ECFF92" />
                                        <Button Clicked="OnDeload" Text="Deload" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextDeloadButton}" TextColor="#0C2432" BackgroundColor="#ECFF92" />
                                        <Button Clicked="OnChallenge" Text="{Binding [Challenge].Value, Mode=OneWay, Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextChallengeButton}" TextColor="#0C2432" BackgroundColor="#ECFF92" />
                                        <Button Clicked="OnReset" Text="{Binding [More].Value, Mode=OneWay, Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}"  IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextResetButton}" TextColor="#0C2432" BackgroundColor="#ECFF92" />
                                        <Button Clicked="OnVideo" Text="{Binding [Video].Value, Mode=OneWay,  Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}"  ContentLayout="Top,0" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextVideoButton}" TextColor="#97D2F3" BackgroundColor="Transparent" />
                                        <Button Clicked="OnContextMenuClicked" CommandParameter="{Binding .}"   WidthRequest="50" TextColor="#97D2F3" ContentLayout="Top,0"  Text="More" HorizontalOptions="End" VerticalOptions="Center" Margin="0,0,-3,0" Style="{StaticResource ItemContextVideoButton}"  BackgroundColor="Transparent">
                                        </Button>
                                        <!--<Button Clicked="OnVideo" Text="{Binding [Video].Value, Mode=OneWay,  Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}" Image="Play.png" ContentLayout="Top,0" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextVideoButton}" TextColor="#97D2F3" BackgroundColor="Transparent" />-->
                                        <!--<Button Clicked="OnContextMenuClicked" CommandParameter="{Binding .}"  Image="more_blue.png" WidthRequest="50" TextColor="#97D2F3" ContentLayout="Top,0"  Text="More" HorizontalOptions="End" VerticalOptions="Center" Margin="0,0,-3,0" Style="{StaticResource ItemContextVideoButton}"  BackgroundColor="Transparent">
                                        </Button>-->
                                    </StackLayout>
                                    <StackLayout.GestureRecognizers>

                                        <TapGestureRecognizer CommandParameter="{Binding .}" Tapped="CellHeaderTapped" />
                                    </StackLayout.GestureRecognizers>
                                </StackLayout>
                                <StackLayout x:Name="StackSets" IsVisible="{Binding IsFinishWorkoutExe}" Spacing="0">

                                    <!--<t:DrMuscleButton Text="Add exercises" TextColor="{x:Static app:AppThemeConstants.BlueColor}" Clicked="NewTapped" BackgroundColor="Transparent">
                        </t:DrMuscleButton>-->
                                    <Grid Margin="0,9,0,0">
                                        <Image Margin="-2,0" Source="finishset_orange.png" Grid.Row="0" Grid.Column="0" HorizontalOptions="FillAndExpand" Aspect="AspectFill" />
                                        <t:DrMuscleButton  Grid.Row="0" Grid.Column="0" TextColor="White" HorizontalOptions="FillAndExpand" Text="Finish &amp; save workout" BackgroundColor="Transparent" Clicked="SaveWorkoutButton_Clicked" HeightRequest="50">
                                        </t:DrMuscleButton>
                                    </Grid>
                                </StackLayout>
                            </StackLayout>
                        </Frame>
                    </Grid>
                </ViewCell>
            </DataTemplate>

            <DataTemplate x:Key="KenkoHeaderTemplate" x:Name="HeaderTemplate">
                <ViewCell  Height="90">
                    <Frame Margin="8,10,8,0" HasShadow="False" CornerRadius="4" BackgroundColor="Red" HeightRequest="90" Padding="15,10,15,10">
                        <StackLayout x:Name="StackSets" Spacing="0">
                            <Grid Margin="0,9,0,0">
                                <Image Margin="-2,0" Source="finishset_orange.png" Grid.Row="0" Grid.Column="0" HorizontalOptions="FillAndExpand" Aspect="AspectFill" />
                                <t:DrMuscleButton  Grid.Row="0" Grid.Column="0" TextColor="White" HorizontalOptions="FillAndExpand" Text="Finish &amp; save workout" HeightRequest="50" BackgroundColor="Transparent" Clicked="SaveWorkoutButton_Clicked">
                                </t:DrMuscleButton>
                            </Grid>
                        </StackLayout>
                    </Frame>
                </ViewCell>
            </DataTemplate>
            <heaer:KenkoHeaderDataTemplateSelector x:Key="kenkoHeaderDataTemplateSelector" RegularDateTemplate="{StaticResource KenkoRegularTemplate}" FooterExerciseTemplate="{StaticResource KenkoHeaderTemplate}">
            </heaer:KenkoHeaderDataTemplateSelector>
            <cells:SetDataTemplateSelector x:Key="SetDataTemplateSelector">
            </cells:SetDataTemplateSelector>
            <converter:IdToBodyPartConverter x:Key="IdToBodyConverter" />
            <converter:IdToTransparentBodyPartConverter x:Key="IdToTransBodyConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid x:Name="NavGrid" BackgroundColor="#D8D8D8" Padding="0,0,0,8" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" >
            <Grid.RowDefinitions>
                <RowDefinition x:Name="StatusBarHeight" Height="20" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />

            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Image Source="nav" HeightRequest="70" Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2" VerticalOptions="Start" Aspect="AspectFill" Grid.RowSpan="3" />

            <StackLayout Grid.Row="1" Padding="{OnPlatform Android= '15,0,4,0' , iOS='15,0,8,0' }" Grid.Column="0" Grid.ColumnSpan="2" Spacing="8" HorizontalOptions="FillAndExpand" VerticalOptions="Start" Orientation="Horizontal">
                

                <Label x:Name="LblWorkoutName" HorizontalOptions="StartAndExpand" TextColor="White" VerticalOptions="Center" VerticalTextAlignment="Center" LineBreakMode="TailTruncation" Style="{StaticResource BoldLabelStyle}" FontSize="24" />
                <ImageButton Source="menu" VerticalOptions="Center" HorizontalOptions="End" BackgroundColor="Transparent" Aspect="AspectFit" Padding="0" Margin="0" Clicked="Menu_Clicked" WidthRequest="40" />
            </StackLayout>
            <StackLayout Padding="{OnPlatform Android= '15,5,20,8' , iOS='8,-3,8,8' }" Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" VerticalOptions="Start" Orientation="Horizontal">
                
                <StackLayout Orientation="Vertical" HorizontalOptions="StartAndExpand" Spacing="0">


                </StackLayout>
                <Image Source="plate" IsVisible="false" BackgroundColor="Transparent" HeightRequest="40" WidthRequest="44" Aspect="AspectFit" HorizontalOptions="End" VerticalOptions="Start">

                </Image>
                <StackLayout
                    Spacing="0"
                    Orientation="Horizontal"
                    HorizontalOptions="FillAndExpand">
                <ImageButton x:Name="BtnTimer" WidthRequest="{OnPlatform Android='40',iOS='48'}" IsVisible="false" Source="stopwatch" HeightRequest="40"  BackgroundColor="Transparent" HorizontalOptions="End" VerticalOptions="Start" Padding="0" Margin="0" >
                </ImageButton>
                    <Label Text="" FontSize="24" TextColor="White" x:Name="BtnTimerLbl"/>
                </StackLayout>
                    
            </StackLayout>

            <t:DrMuscleListViewCache x:Name="ExerciseListView" Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" HasUnevenRows="True" BackgroundColor="#D8D8D8" SeparatorColor="Transparent" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand" SeparatorVisibility="None" IsGroupingEnabled="True" Header="{Binding}" ItemTemplate="{StaticResource SetDataTemplateSelector}" Footer="" GroupHeaderTemplate="{StaticResource kenkoHeaderDataTemplateSelector}" RowHeight="-1" >
                <t:DrMuscleListViewCache.GestureRecognizers>
                    <TapGestureRecognizer Tapped="ListTapped" />
                </t:DrMuscleListViewCache.GestureRecognizers>

            </t:DrMuscleListViewCache>

        </Grid>
            <!--<t:RightSideMasterPage Padding="0" Margin="0" x:Name="SlideMenu" HorizontalOptions="EndAndExpand" VerticalOptions="FillAndExpand"/>
        </Grid>-->
    </ContentPage.Content>
</ContentPage>