using Acr.UserDialogs;
using DrMaxMuscle.Resx;
using DrMuscleWebApiSharedModel;
using System.Collections.ObjectModel;
using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Series;
using static DrMaxMuscle.Screens.History.HistoryPage;
using System.Globalization;
using System.Text.RegularExpressions;
using IView = Microsoft.Maui.IView;
using Microsoft.Maui.Networking;
using DrMaxMuscle.Message;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Views;
using RGPopup.Maui.Services;
using DrMaxMuscle.Utility;
using CommunityToolkit.Maui.Views;

namespace DrMaxMuscle.Screens.Me;

public partial class MeCombinePage : ContentPage
{
    private Dictionary<string, long> ExercisesLabelToID;
    private Dictionary<string, TimeSpan> DurationToDays;
    ObservableCollection<HistoryItem> historyModel = new ObservableCollection<HistoryItem>();
    private Dictionary<double, string> IndexToDateLabel = new Dictionary<double, string>();
    private Dictionary<double, string> IndexToDateLabel2 = new Dictionary<double, string>();
    private List<string> IndexToDateLabel3 = new List<string>();
    GetUserWorkoutLogAverageResponse mainWorkoutLog;
    bool isEstimated = false;
    bool isHistoryLoaded = false;
    double chartWidth = 0;

    public MeCombinePage()
    {
        InitializeComponent();

        ExericsesPicker.Unfocused += ExericsesPicker_Unfocused;
        DatePicker.Unfocused += DatePicker_Unfocused;

        chartWidth = plotView.Width;
        RefreshLocalized();
        ButtonStartWorkout.Clicked += ButtonStartWorkout_Clicked;
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();

        });

        try
        {
            if(plotView != null)
            {
                plotView.Controller = new PlotController();
                plotView.Controller.UnbindMouseDown(OxyMouseButton.Left); // Disable default behavior for left click (single tap)
                plotView.Controller.UnbindTouchDown();
            }
            if (plotViewVolume != null)
            {
                plotViewVolume.Controller = new PlotController();
                plotViewVolume.Controller.UnbindMouseDown(OxyMouseButton.Left); // Disable default behavior for left click (single tap)
                plotViewVolume.Controller.UnbindTouchDown();

            }
            if (plotView3 != null)
            {
                plotView3.Controller = new PlotController();
                plotView3.Controller.UnbindMouseDown(OxyMouseButton.Left); // Disable default behavior for left click (single tap)
                plotView3.Controller.UnbindTouchDown();
            }
            

            
            
        }
        catch (Exception ex)
        {

        }

    }

    private void RefreshLocalized()
    {
        //LblCustomWarmUp.Text = AppResources.UseCustomWarmUps;

        Title = LocalDBManager.Instance.GetDBSetting("firstname") == null ? "Me" : string.Format("{0} {1}!", App.IsNewUser ? AppResources.Welcome : AppResources.WelcomeBack, LocalDBManager.Instance.GetDBSetting("firstname")?.Value);

        DurationToDays = new Dictionary<string, TimeSpan>();
        DurationToDays.Add(AppResources.Last3Workouts, new TimeSpan(0, 0, 0));
        DurationToDays.Add(AppResources.LastMonth, (DateTime.Now - DateTime.Now.AddMonths(-1)));
        DurationToDays.Add(AppResources.Last3Months, (DateTime.Now - DateTime.Now.AddMonths(-3)));
        DurationToDays.Add(AppResources.Last6Months, (DateTime.Now - DateTime.Now.AddMonths(-6)));
        DurationToDays.Add(AppResources.LastYear, (DateTime.Now - DateTime.Now.AddYears(-1)));
        DurationToDays.Add(AppResources.AllTime, (DateTime.Now - DateTime.Now.AddYears(-100)));
        //DatePicker.IsVisible = false;
        DatePicker.Items.Clear();
        DatePicker.Items.Add(AppResources.Last3Workouts);

        DatePicker.SelectedIndex = 0;
        LblTimeFrame.Text = "Show charts for:";// AppResources.SelectExercisesAndTimeframes;

    }

    public void OnBeforeShow()
    {
        plotView.Model = null;
        plotViewVolume.Model = null;
        isEstimated = false;
        Title = LocalDBManager.Instance.GetDBSetting("firstname") == null ? "Me" : string.Format("{0} {1}!", App.IsNewUser ? AppResources.Welcome : AppResources.WelcomeBack, LocalDBManager.Instance.GetDBSetting("firstname")?.Value);

        try
        {
            LoadDefaultChartOfflineAvailable();
            isHistoryLoaded = false;

        }
        catch (Exception e)
        {
        }
        LoadSavedWeights();
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        if (LocalDBManager.Instance.GetDBSetting("email") == null)
            return;
       var _IFirebase = (IFirebase)MauiProgram.ServiceProvider.GetService(typeof(IFirebase));
        _IFirebase.SetScreenName("me_page");

        if (Config.ViewWebHistoryPopup == false)
        {
            await Task.Delay(300);
            Config.ViewWebHistoryPopup = true;

           var ShowSorryPopUp = await HelperClass.DisplayCustomPopup(AppResources.ViewOnTheWeb,AppResources.ViewAnalyzeAndDownloadData,
                        AppResources.OpenWebApp,AppResources.Cancel);
                        bool isConfirm = false;
                ShowSorryPopUp.ActionSelected += async (sender,action) => {

                        if (action == PopupAction.OK)
                        {
                            //Move to history
                            Browser.OpenAsync($"https://my.dr-muscle.com", BrowserLaunchMode.SystemPreferred);
                            //Device.OpenUri(new Uri("https://my.dr-muscle.com"));
                        }
                };

               
            // ConfirmConfig ShowSorryPopUp = new ConfirmConfig()
            // {
            //     Title = AppResources.ViewOnTheWeb,
            //     Message = AppResources.ViewAnalyzeAndDownloadData,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = AppResources.OpenWebApp,
            //     CancelText = AppResources.Cancel,
            //     OnAction = async (bool ok) =>
            //     {
            //         if (ok)
            //         {
            //             //Move to history
            //             Browser.OpenAsync($"https://my.dr-muscle.com", BrowserLaunchMode.SystemPreferred);
            //             //Device.OpenUri(new Uri("https://my.dr-muscle.com"));
            //         }
            //     }
            // };
            // UserDialogs.Instance.Confirm(ShowSorryPopUp);
            RefreshLocalized();
        }
    }

    private async Task LoadSavedWeights()
    {
        try
        {

            plotView3.Model = null;
            var weightList = await DrMuscleRestClient.Instance.GetUserWeights();
            if (weightList.Count == 0)
                return;
            weightList = weightList.Take(10).ToList();
            if (LocalDBManager.Instance.GetDBSetting("email") == null)
                return;
            if (LocalDBManager.Instance.GetDBSetting("massunit") == null)
                return;
            var plotModel = new PlotModel
            {
                Title = "BODY WEIGHT",
                //Subtitle = "for the 3 last workouts",
                TitleFontSize = 13,

                Background = OxyColors.Transparent,
                //TitleFontWeight = FontWeights.Bold,
                TitleColor = OxyColor.Parse("#23253A"),

                PlotAreaBackground = OxyColors.Transparent,
                PlotAreaBorderColor = OxyColor.Parse("#23253A"),
                //LegendTextColor = OxyColor.Parse("#23253A"),

            };

            double minY;
            double maxY;

            switch (LocalDBManager.Instance.GetDBSetting("massunit")?.Value)
            {
                default:
                case "kg":
                    minY = (double)(Math.Floor(weightList.Min(o => o.Weight) / 10) * 10) - 100;
                    maxY = (double)(Math.Ceiling(weightList.Max(o => o.Weight) / 10) * 10) + 100;
                    break;
                case "lb":
                    minY = (double)(Math.Floor(weightList.Min(o => new MultiUnityWeight((decimal)o.Weight, "kg").Lb) / 10) * 10) - 100;
                    maxY = (double)(Math.Ceiling(weightList.Max(o => new MultiUnityWeight((decimal)o.Weight, "kg").Lb) / 10) * 10) + 100;
                    break;
            }

            LinearAxis yAxis = new LinearAxis { Position = AxisPosition.Left, Minimum = minY - 5, Maximum = maxY + 5, AxislineColor = OxyColor.Parse("#23253A"), ExtraGridlineColor = OxyColor.Parse("#23253A"), MajorGridlineColor = OxyColor.Parse("#23253A"), MinorGridlineColor = OxyColor.Parse("#23253A"), TextColor = OxyColor.Parse("#23253A"), TicklineColor = OxyColor.Parse("#23253A"), TitleColor = OxyColor.Parse("#23253A") };

            yAxis.IsAxisVisible = false;
            LinearAxis xAxis = new LinearAxis { Position = AxisPosition.Bottom, AxislineColor = OxyColor.Parse("#23253A"), ExtraGridlineColor = OxyColor.Parse("#23253A"), MajorGridlineColor = OxyColor.Parse("#23253A"), MinorGridlineColor = OxyColor.Parse("#23253A"), TextColor = OxyColor.Parse("#23253A"), TicklineColor = OxyColor.Parse("#23253A"), TitleColor = OxyColor.Parse("#23253A"), FontSize = 10, MinimumMajorStep = 0.3, MinorStep = 0.5, MajorStep = 0.5 };





            //xAxis.LabelFormatter = _formatter3;

            xAxis.MinimumPadding = 1;
            xAxis.IsPanEnabled = false;
            xAxis.IsZoomEnabled = false;

            xAxis.Minimum = 0.5;
            xAxis.Maximum = weightList.Count() + 0.5;



            IndexToDateLabel3.Clear();
            //IndexToDateLabel3.Add(xAxis.Minimum, "");
            //IndexToDateLabel3.Add(xAxis.Maximum, "");


            yAxis.IsPanEnabled = false;
            yAxis.IsZoomEnabled = false;

            //plotModel.Axes.Add(yAxis);
            //plotModel.Axes.Add(xAxis);

            // uncomment code please
            var barSeries = new BarSeries
            {
                LabelPlacement = LabelPlacement.Inside,
                LabelFormatString = "{0}",
                FontSize = 12,
                FillColor = OxyColor.Parse("#38418C"),
                TextColor = OxyColors.White,
                BarWidth = 15
            };


            var s1 = new LineSeries()
            {
                Color = OxyColor.Parse("#38418C"),
                MarkerType = MarkerType.Circle,
                MarkerSize = 6,
                MarkerStroke = OxyColor.Parse("#38418C"),
                MarkerFill = OxyColor.Parse("#38418C"),
                MarkerStrokeThickness = 1,
                LabelFormatString = "{1:0}",
                FontSize = 12,
                TextColor = OxyColor.Parse("#38418C")
            };

            int i = 1;
            foreach (UserWeight m in weightList.OrderBy(w => w.CreatedDate))
            {
                // uncomment code please
                switch (LocalDBManager.Instance.GetDBSetting("massunit")?.Value)
                {
                    default:
                    case "kg":

                        barSeries.Items.Add(new BarItem
                        {
                            Value = Convert.ToDouble(Math.Round(m.Weight, 2)),
                            Color = OxyColor.Parse("#3498db")
                        });
                        //s1.Points.Add(new DataPoint(i, Convert.ToDouble(m.Weight)));
                        break;
                    case "lb":
                        barSeries.Items.Add(new BarItem
                        {
                            Value = Convert.ToDouble(Math.Round(new MultiUnityWeight((decimal)m.Weight, "kg").Lb, 2)),
                            Color = OxyColor.Parse("#3498db")

                        });

                        //s1.Points.Add(new DataPoint(i, Convert.ToDouble(new MultiUnityWeight((decimal)m.Weight, "kg").Lb)));
                        break;
                }
                IndexToDateLabel3.Add(m.CreatedDate.ToLocalTime().ToString("MM/dd", CultureInfo.InvariantCulture));
                i++;
            }
                plotModel.Axes.Add(new CategoryAxis
            {
                Position = AxisPosition.Left,
                Key = "BODY WEIGHT",
                ItemsSource = IndexToDateLabel3,
                IsPanEnabled = false,
                IsZoomEnabled = false,
                Selectable = false,
            });
            plotModel.Axes.Add(new CategoryAxis()
            {
                AxislineStyle = LineStyle.Solid,
                Position = AxisPosition.Bottom,
                MinorTickSize = 0,
                MajorGridlineStyle = LineStyle.Solid,
                MinorGridlineStyle = LineStyle.Solid,
                IsAxisVisible = false
            });
            // uncomment code please
            plotModel.Series.Add(barSeries);



            //plotModel.Series.Add(s1);
            //plotView3.Model.InvalidatePlot(true);

            Device.BeginInvokeOnMainThread(() =>
            {
                plotView3.Model = plotModel;
            });
        }
        catch (Exception ex)
        {

        }
    }
    private void ViewMoreStats_Clicked(object sender, EventArgs e)
    {
        Browser.OpenAsync($"https://dashboard.dr-muscle.com/", BrowserLaunchMode.SystemPreferred);
        //Device.OpenUri(new Uri("https://dashboard.dr-muscle.com/"));
    }
    protected void FirsttimeExercisePopup_OnTextChanged(PromptTextChangedArgs obj)
    {

        const string textRegex = @"^\d+(?:[\.,]\d{0,5})?$";
        var text = obj.Value.Replace(",", ".");
        bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        if (IsValid == false && !string.IsNullOrEmpty(obj.Value))
        {
            double result;
            obj.Value = obj.Value.Substring(0, obj.Value.Length - 1);
            double.TryParse(obj.Value, out result);
            obj.Value = result.ToString();
        }
    }

    protected void ExerciseRepsPopup_OnTextChanged(PromptTextChangedArgs obj)
    {
        const string textRegex = @"^\d+(?:)?$";
        bool IsValid = Regex.IsMatch(obj.Value, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        if (IsValid == false && !string.IsNullOrEmpty(obj.Value))
        {
            double result;
            obj.Value = obj.Value.Substring(0, obj.Value.Length - 1);
            double.TryParse(obj.Value, out result);
            obj.Value = result.ToString();
        }
    }

    void OnCancelClicked(object sender, System.EventArgs e)
    {

        StackLayout s = ((StackLayout)((Button)sender).Parent);
        if (s.Children.Count == 4)
        {
            SetVisibility(s.Children[0], false);
            SetVisibility(s.Children[1], false);
            SetVisibility(s.Children[2], false);
            SetVisibility(s.Children[3], true);
        }
        else
        {
            SetVisibility(s.Children[0], false);
            SetVisibility(s.Children[1], false);
            SetVisibility(s.Children[2], true);
        }
    }
    void SetVisibility(IView view, bool isVisible)
    {
        if (view is View mauiView)
        {
            mauiView.IsVisible = isVisible;
        }
    }

    void OnContextMenuClicked(object sender, System.EventArgs e)
    {
        StackLayout s = ((StackLayout)((Button)sender).Parent);
        if (s.Children.Count == 4)
        {
            SetVisibility(s.Children[0], true);
            SetVisibility(s.Children[1], true);
            SetVisibility(s.Children[2], true);
            SetVisibility(s.Children[3], false);
        }
        else
        {
            SetVisibility(s.Children[0], true);
            SetVisibility(s.Children[1], true);
            SetVisibility(s.Children[2], false);
        }
    }


    private bool IsFromWorkoutPage()
    {
        var isMePage = false;
        // uncomment code please
        //foreach (var item in Navigation.NavigationStack)
        //{
        //    if (item is ChooseDrMuscleOrCustomPage || item is KenkoChooseYourWorkoutExercisePage)
        //    {
        //        isMePage = true;
        //        break;
        //    }
        //}
        return isMePage;
    }
    async void ChangeWorkoutClicked(object sender, EventArgs e)
    {
        try
        {
            if (IsFromWorkoutPage())
            {
                await Navigation?.PopToRootAsync(false);
                MeCombinePage meCombine = new MeCombinePage();
                meCombine.OnBeforeShow();
                await Navigation.PushAsync(meCombine);
                //await PagesFactory.PushAsync<MeCombinePage>();
            }
            // uncomment code please
            //await PagesFactory.PushAsync<ChooseDrMuscleOrCustomPage>();
        }
        catch (Exception ex)
        {

        }
    }

    async void DatePicker_Unfocused(object sender, FocusEventArgs e)
    {
        try
        {
            if (ExericsesPicker.SelectedIndex == 0)
            {
                LoadDefaultChartOfflineAvailable();
                return;
            }

            string selected = ExericsesPicker.Items[ExericsesPicker.SelectedIndex];
            long exerciseId = ExercisesLabelToID[selected];

            GetChart(exerciseId, DurationToDays[DatePicker.Items[DatePicker.SelectedIndex]]);

        }
        catch (Exception ex)
        {
            ConnectionErrorPopup();

        }
    }

    async void ExericsesPicker_Unfocused(object sender, EventArgs e)
    {
        try
        {
            if (ExericsesPicker.SelectedIndex == 0)
            {
                DatePicker.Items.Clear();
                DatePicker.Items.Add(AppResources.Last3Workouts);
                DatePicker.SelectedIndex = 0;
                LoadDefaultChartOfflineAvailable();

                return;
            }
            else
            {
                int currentDatePickerIndex = DatePicker.SelectedIndex;
                DatePicker.Items.Clear();
                foreach (var i in DurationToDays)
                    DatePicker.Items.Add(i.Key);
                DatePicker.Items.RemoveAt(0);
                DatePicker.SelectedIndex = currentDatePickerIndex;
            }

            string selected = ExericsesPicker.Items[ExericsesPicker.SelectedIndex];
            long exerciseId = ExercisesLabelToID[selected];

            GetChart(exerciseId, DurationToDays[DatePicker.Items[DatePicker.SelectedIndex]]);

        }
        catch (Exception ex)
        {
            ConnectionErrorPopup();

        }
    }

    private bool isPresented = false;
    protected async Task ConnectionErrorPopup()
    {
        if (isPresented)
            return;
        isPresented = true;
        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        // {
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     Message = AppResources.PleaseCheckInternetConnection,
        //     Title = AppResources.ConnectionError,
        //     OkText = "Try again"
        // });

        await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Try again","");
        isPresented = false;
    }
    private string _formatter(double d)
    {
        return IndexToDateLabel.ContainsKey(d) ? IndexToDateLabel[d] : "";
    }

    //private string _formatter3(double d)
    //{
    //    return IndexToDateLabel3.ContainsKey(d) ? IndexToDateLabel3[d] : "";
    //}

    private async void GetChart(long exerciseId, TimeSpan period)
    {
        //LblExerciseName.IsVisible = false;
        try
        {


            GetUserWorkoutLogAverageForExerciseRequest request = (int)period.TotalDays == 0 ? new GetUserWorkoutLogAverageForExerciseRequest() { ExerciseId = exerciseId } : new GetUserWorkoutLogAverageForExerciseRequest() { ExerciseId = exerciseId, PeriodSinceToday = period };
            GetUserWorkoutLogAverageResponse workoutLogAverage = (int)period.TotalDays == 0 ? await DrMuscleRestClient.Instance.GetUserWorkoutLogAverageForExercise(request) : await DrMuscleRestClient.Instance.GetUserWorkoutLogAverageForExerciseForPeriod(request);

            if (workoutLogAverage == null || !workoutLogAverage.Averages.Any())
            {
                //NoDataLabel.IsVisible = true;
                plotView.IsVisible = true;
                plotViewVolume.IsVisible = true;
                // uncomment code please
                //plotScroll1.IsVisible = true;
                //plotScroll2.IsVisible = true;
                var columnCollection = new ColumnDefinitionCollection();
                //GridChart.HeightRequest = 175;
                var columnDefinition = new ColumnDefinition();
                var columnDefinition1 = new ColumnDefinition();
                //columnDefinition.Width = new GridLength(GridChart.Width / 2, GridUnitType.Star);
                //columnDefinition1.Width = new GridLength(GridChart.Width / 2, GridUnitType.Star);
                columnCollection.Add(columnDefinition);
                columnCollection.Add(columnDefinition1);
                //GridChart.Children.Add(plotScroll1, 0, 0);
                //GridChart.Children.Add(plotScroll2, 1, 0);
                //plotView.WidthRequest = GridChart.Width / 2;
                //plotViewVolume.WidthRequest = GridChart.Width / 2;
                plotView.HorizontalOptions = LayoutOptions.FillAndExpand;
                plotViewVolume.HorizontalOptions = LayoutOptions.FillAndExpand;

                plotView.IsVisible = true;
                var plotModel = new PlotModel
                {
                    Title = AppResources.TryaWorkoutToSee,
                    TitleFontSize = 13,
                    //TitleFontWeight = FontWeights.Bold,
                    TitleColor = OxyColors.Blue,
                    Background = OxyColors.Transparent,
                    PlotAreaBackground = OxyColors.Transparent,
                    PlotAreaBorderColor = OxyColors.Blue,
                    //LegendTextColor = OxyColors.Blue,
                    //LegendPosition = LegendPosition.RightMiddle,
                    //LegendOrientation = LegendOrientation.Vertical,
                    //LegendPadding = 20,
                    //LegendLineSpacing = 20,
                    //LegendItemSpacing = 20,
                };

                LinearAxis yAxis = new LinearAxis { Position = AxisPosition.Left, Minimum = 10, Maximum = 20, AxislineColor = OxyColors.Blue, ExtraGridlineColor = OxyColors.Blue, MajorGridlineColor = OxyColors.Blue, MinorGridlineColor = OxyColors.Blue, TextColor = OxyColors.Blue, TicklineColor = OxyColors.Blue, TitleColor = OxyColors.Blue, TickStyle = TickStyle.None };
                yAxis.IsAxisVisible = false;
                LinearAxis xAxis = new LinearAxis { Position = AxisPosition.Bottom, AxislineColor = OxyColor.Parse("#23253A"), ExtraGridlineColor = OxyColors.Transparent, MajorGridlineColor = OxyColors.Transparent, MinorGridlineColor = OxyColors.Transparent, TextColor = OxyColor.Parse("#23253A"), TicklineColor = OxyColor.Parse("#23253A"), TitleColor = OxyColor.Parse("#23253A") };

                xAxis.LabelFormatter = _formatter;
                xAxis.MinimumPadding = 1;
                xAxis.IsPanEnabled = false;
                xAxis.IsZoomEnabled = false;
                xAxis.Minimum = 0.5;
                xAxis.Maximum = 3.5;

                IndexToDateLabel.Clear();
                IndexToDateLabel.Add(xAxis.Minimum, "");
                IndexToDateLabel.Add(xAxis.Maximum, "");

                yAxis.IsPanEnabled = false;
                yAxis.IsZoomEnabled = false;
                plotModel.Axes.Add(yAxis);
                plotModel.Axes.Add(xAxis);
                plotView.Model.InvalidatePlot(true);
                plotView.Model = plotModel;

                plotViewVolume.IsVisible = true;
                var plotModel2 = new PlotModel
                {
                    Title = AppResources.YourProgressInThisChart,//AppResources.TryAWorkoutToSeeYourProgressInThisChart,
                    TitleFontSize = 13,
                    //TitleFontWeight = FontWeights.Bold,
                    TitleColor = OxyColors.Blue,
                    Background = OxyColors.Transparent,
                    PlotAreaBackground = OxyColors.Transparent,
                    PlotAreaBorderColor = OxyColors.Blue,
                    //LegendTextColor = OxyColors.Blue,
                    //LegendPosition = LegendPosition.RightMiddle,
                    //LegendOrientation = LegendOrientation.Vertical,
                    //LegendPadding = 20,
                    //LegendLineSpacing = 20,
                    //LegendItemSpacing = 20,
                };

                LinearAxis yAxis2 = new LinearAxis { Position = AxisPosition.Left, Minimum = 10, Maximum = 20, AxislineColor = OxyColors.Blue, ExtraGridlineColor = OxyColors.Blue, MajorGridlineColor = OxyColors.Blue, MinorGridlineColor = OxyColors.Blue, TextColor = OxyColors.Blue, TicklineColor = OxyColors.Blue, TitleColor = OxyColors.Blue, TickStyle = TickStyle.None };
                yAxis2.IsAxisVisible = false;
                LinearAxis xAxis2 = new LinearAxis { Position = AxisPosition.Bottom, AxislineColor = OxyColors.Blue, ExtraGridlineColor = OxyColors.Transparent, MajorGridlineColor = OxyColors.Transparent, MinorGridlineColor = OxyColors.Transparent, TextColor = OxyColors.Blue, TicklineColor = OxyColors.Blue, TitleColor = OxyColors.Blue };

                xAxis2.LabelFormatter = _formatter;
                xAxis2.MinimumPadding = 1;
                xAxis2.IsPanEnabled = false;
                xAxis2.IsZoomEnabled = false;
                xAxis2.Minimum = 0.5;
                xAxis2.Maximum = 3.5;

                IndexToDateLabel.Clear();
                IndexToDateLabel.Add(xAxis2.Minimum, "");
                IndexToDateLabel.Add(xAxis2.Maximum, "");

                yAxis2.IsPanEnabled = false;
                yAxis2.IsZoomEnabled = false;
                plotModel2.Axes.Add(yAxis2);
                plotModel2.Axes.Add(xAxis2);
                plotViewVolume.Model = plotModel2;
                LblSetsProgress.Text = "";
                LblProgress.Text = "";

            }
            else
            {
                plotView.IsVisible = true;
                plotViewVolume.IsVisible = true;
                //plotScroll1.IsVisible = true;
                //plotScroll2.IsVisible = true;
                var plotModel = new PlotModel
                {

                    Title = AppResources.MaxStrengthCapital,
                    TitleFontSize = 13,
                    //TitleFontWeight = FontWeights.Bold,
                    TitleColor = OxyColor.Parse("#23253A"),
                    Background = OxyColors.Transparent,
                    PlotAreaBackground = OxyColors.Transparent,
                    PlotAreaBorderColor = OxyColor.Parse("#23253A"),
                    //LegendPlacement = LegendPlacement.Outside,
                    //LegendTextColor = OxyColor.Parse("#23253A"),
                    //LegendPosition = LegendPosition.BottomCenter,
                    //LegendOrientation = LegendOrientation.Horizontal,
                    //LegendLineSpacing = 5,
                    IsLegendVisible = true
                };
                bool inKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg";
                LinearAxis xAxis1 = null;
                try
                {
                    var min = (double)workoutLogAverage.Averages.Min(a => inKg ? a.Average.Kg : a.Average.Lb) - 100;
                    var max = (double)workoutLogAverage.Averages.Max(a => inKg ? a.Average.Kg : a.Average.Lb) + 100;
                    if (workoutLogAverage.Sets != null && workoutLogAverage.Sets.Count > 0)
                    {
                        min = Math.Min((double)workoutLogAverage.Averages.Min(a => inKg ? a.Average.Kg : a.Average.Lb) - 100, workoutLogAverage.Sets.Min() - 100);
                        max = Math.Max((double)workoutLogAverage.Averages.Max(a => inKg ? a.Average.Kg : a.Average.Lb) + 100, workoutLogAverage.Sets.Max() + 100);
                    }



                    LinearAxis yAxis = new LinearAxis { Position = AxisPosition.Left, Minimum = min, Maximum = max, AxislineColor = OxyColors.Blue, ExtraGridlineColor = OxyColors.Blue, MajorGridlineColor = OxyColors.Blue, MinorGridlineColor = OxyColors.Blue, TextColor = OxyColors.Blue, TicklineColor = OxyColors.Blue, TitleColor = OxyColors.Blue, TickStyle = TickStyle.None };
                    yAxis.IsAxisVisible = false;
                    xAxis1 = new LinearAxis { Position = AxisPosition.Bottom, AxislineColor = OxyColor.Parse("#23253A"), ExtraGridlineColor = OxyColor.Parse("#23253A"), MajorGridlineColor = OxyColor.Parse("#23253A"), MinorGridlineColor = OxyColor.Parse("#23253A"), TextColor = OxyColor.Parse("#23253A"), TicklineColor = OxyColor.Parse("#23253A"), TitleColor = OxyColor.Parse("#23253A"), FontSize = 10, MinimumMajorStep = 0.3, MinorStep = 0.5, MajorStep = 0.5 };

                    xAxis1.LabelFormatter = _formatter;
                    xAxis1.MinimumPadding = 0.05;
                    xAxis1.MaximumPadding = 0.1;
                    xAxis1.IsPanEnabled = false;
                    xAxis1.IsZoomEnabled = false;
                    xAxis1.Minimum = 0.5;
                    xAxis1.Maximum = 3.5;

                    var selectedText = DatePicker.Items[DatePicker.SelectedIndex];
                    if (selectedText.Equals(AppResources.LastMonth) || selectedText.Equals(AppResources.Last3Months) || selectedText.Equals(AppResources.Last6Months) || selectedText.Equals(AppResources.LastYear) || selectedText.Equals(AppResources.AllTime))
                    {
                        xAxis1.IsPanEnabled = true;
                        xAxis1.IsZoomEnabled = true;

                    }
                    else
                    {
                        xAxis1.IsPanEnabled = false;
                        xAxis1.IsZoomEnabled = false;

                    }

                    if ((int)period.TotalDays == 0)
                    {
                        xAxis1.Minimum = 0.5; //DateTimeAxis.ToDouble(workoutLogAverage.Averages.OrderByDescending(a => a.Date).Take(3).Min(a => a.Date).AddHours(-12));
                        xAxis1.Maximum = 3.5; //DateTimeAxis.ToDouble(workoutLogAverage.Averages.OrderByDescending(a => a.Date).Take(3).Max(a => a.Date).AddHours(12));
                    }
                    else
                    {
                        xAxis1.Minimum = 0.5; //DateTimeAxis.ToDouble(workoutLogAverage.Averages.OrderBy(a => a.Date).Min(a => a.Date).AddHours(-12));
                        xAxis1.Maximum = workoutLogAverage.Averages.Count() + 0.5; //DateTimeAxis.ToDouble(workoutLogAverage.Averages.OrderBy(a => a.Date).Max(a => a.Date).AddHours(12));
                    }

                    IndexToDateLabel.Clear();
                    IndexToDateLabel.Add(xAxis1.Minimum, "");
                    IndexToDateLabel.Add(xAxis1.Maximum, "");

                    yAxis.IsPanEnabled = false;
                    yAxis.IsZoomEnabled = false;
                    plotModel.Axes.Add(yAxis);
                    plotModel.Axes.Add(xAxis1);
                }
                catch (Exception)
                {

                }
                var s1 = new LineSeries()
                {
                    Color = OxyColor.Parse("#38418C"),//Blue
                    MarkerType = MarkerType.Circle,
                    MarkerSize = 6,
                    MarkerStroke = OxyColor.Parse("#38418C"),
                    MarkerFill = OxyColor.Parse("#38418C"),
                    MarkerStrokeThickness = 1,
                    LabelFormatString = "{1:0}",
                    FontSize = 15,
                    TextColor = OxyColor.Parse("#38418C")
                };


                var plotModel2 = new PlotModel
                {
                    Title = AppResources.WorkSetsCapital,
                    TitleFontSize = 13,
                    //TitleFontWeight = FontWeights.Bold,
                    TitleColor = OxyColor.Parse("#23253A"),
                    Background = OxyColors.Transparent,
                    PlotAreaBackground = OxyColors.Transparent,
                    PlotAreaBorderColor = OxyColor.Parse("#23253A"),
                    //LegendPlacement = LegendPlacement.Outside,
                    //LegendTextColor = OxyColor.Parse("#23253A"),
                    //LegendPosition = LegendPosition.BottomCenter,
                    //LegendOrientation = LegendOrientation.Horizontal,
                    //LegendLineSpacing = 5,
                    IsLegendVisible = true
                    //LegendItemSpacing = 20,
                };

                //bool inKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg";
                LinearAxis xAxis = null;
                try
                {
                    var min = (double)workoutLogAverage.Averages.Min(a => inKg ? a.Average.Kg : a.Average.Lb) - 100;
                    var max = (double)workoutLogAverage.Averages.Max(a => inKg ? a.Average.Kg : a.Average.Lb) + 100;
                    if (workoutLogAverage.Sets != null && workoutLogAverage.Sets.Count > 0)
                    {
                        min = Math.Min((double)workoutLogAverage.Averages.Min(a => inKg ? a.Average.Kg : a.Average.Lb) - 100, workoutLogAverage.Sets.Min() - 100);
                        max = Math.Max((double)workoutLogAverage.Averages.Max(a => inKg ? a.Average.Kg : a.Average.Lb) + 100, workoutLogAverage.Sets.Max() + 100);
                    }
                    //var minVal = (double)workoutLogAverage.Sets.Min();
                    //var maxVal = (double)workoutLogAverage.Sets.Max();
                    //var change = maxVal * 0.2;
                    //var min = minVal - change;
                    //var max = 28 * maxVal / 19;

                    LinearAxis yAxis = new LinearAxis { Position = AxisPosition.Left, Minimum = min, Maximum = max, AxislineColor = OxyColors.Blue, ExtraGridlineColor = OxyColors.Blue, MajorGridlineColor = OxyColors.Blue, MinorGridlineColor = OxyColors.Blue, TextColor = OxyColors.Blue, TicklineColor = OxyColors.Blue, TitleColor = OxyColors.Blue, TickStyle = TickStyle.None };
                    yAxis.IsAxisVisible = false;
                    xAxis = new LinearAxis { Position = AxisPosition.Bottom, AxislineColor = OxyColor.Parse("#23253A"), ExtraGridlineColor = OxyColor.Parse("#23253A"), MajorGridlineColor = OxyColor.Parse("#23253A"), MinorGridlineColor = OxyColor.Parse("#23253A"), TextColor = OxyColor.Parse("#23253A"), TicklineColor = OxyColor.Parse("#23253A"), TitleColor = OxyColor.Parse("#23253A"), FontSize = 10, MinimumMajorStep = 0.3, MinorStep = 0.5, MajorStep = 0.5 };

                    xAxis.LabelFormatter = _formatter;
                    xAxis.MinimumPadding = 1;
                    xAxis.IsPanEnabled = false;
                    xAxis.IsZoomEnabled = false;

                    xAxis.Minimum = 0.5;
                    xAxis.Maximum = 3.5;

                    var selectedText = DatePicker.Items[DatePicker.SelectedIndex];
                    if (selectedText.Equals(AppResources.Last6Months) || selectedText.Equals(AppResources.LastYear) || selectedText.Equals(AppResources.AllTime))
                    {
                        xAxis.IsPanEnabled = true;
                        xAxis.IsZoomEnabled = true;
                    }
                    else
                    {
                        xAxis.IsPanEnabled = false;
                        xAxis.IsZoomEnabled = false;
                    }

                    if ((int)period.TotalDays == 0)
                    {
                        xAxis.Minimum = 0.5; //DateTimeAxis.ToDouble(workoutLogAverage.Averages.OrderByDescending(a => a.Date).Take(3).Min(a => a.Date).AddHours(-12));
                        xAxis.Maximum = 3.5; //DateTimeAxis.ToDouble(workoutLogAverage.Averages.OrderByDescending(a => a.Date).Take(3).Max(a => a.Date).AddHours(12));
                    }
                    else
                    {
                        xAxis.Minimum = 0.5; //DateTimeAxis.ToDouble(workoutLogAverage.Averages.OrderBy(a => a.Date).Min(a => a.Date).AddHours(-12));
                        xAxis.Maximum = workoutLogAverage.Averages.Count() + 0.5; //DateTimeAxis.ToDouble(workoutLogAverage.Averages.OrderBy(a => a.Date).Max(a => a.Date).AddHours(12));
                    }

                    IndexToDateLabel2.Clear();
                    IndexToDateLabel2.Add(xAxis.Minimum, "");
                    IndexToDateLabel2.Add(xAxis.Maximum, "");

                    yAxis.IsPanEnabled = false;
                    yAxis.IsZoomEnabled = false;
                    plotModel2.Axes.Add(yAxis);
                    plotModel2.Axes.Add(xAxis);
                }
                catch (Exception)
                {

                }

                var s2 = new LineSeries()
                {
                    Color = OxyColor.Parse("#5DD397"),
                    MarkerType = MarkerType.Circle,
                    MarkerSize = 6,
                    MarkerStroke = OxyColor.Parse("#5DD397"),
                    MarkerFill = OxyColor.Parse("#5DD397"),
                    MarkerStrokeThickness = 1,
                    LabelFormatString = "{1:0}",
                    FontSize = 15,
                    TextColor = OxyColor.Parse("#5DD397")
                };

                int index = 1;
                if ((int)period.TotalDays == 0)
                {
                    foreach (var data in workoutLogAverage.Averages.OrderBy(a => a.Date).Take(3))
                    {
                        s1.Points.Add(new DataPoint(index, Convert.ToDouble(inKg ? data.Average.Kg : data.Average.Lb)));
                        IndexToDateLabel.Add(index, data.Date.ToLocalTime().ToString("MM/dd", CultureInfo.InvariantCulture));
                        IndexToDateLabel2.Add(index, data.Date.ToLocalTime().ToString("MM/dd", CultureInfo.InvariantCulture));
                        index++;
                    }
                    index = 1;
                    foreach (var sets in workoutLogAverage.Sets.Take(3))
                    {
                        s2.Points.Add(new DataPoint(index, Convert.ToDouble(sets)));
                        index++;
                    }
                }
                else
                {
                    foreach (var data in workoutLogAverage.Averages.OrderBy(a => a.Date))
                    {
                        s1.Points.Add(new DataPoint(index, Convert.ToDouble(inKg ? data.Average.Kg : data.Average.Lb)));
                        IndexToDateLabel.Add(index, data.Date.ToLocalTime().ToString("MM/dd", CultureInfo.InvariantCulture));
                        IndexToDateLabel2.Add(index, data.Date.ToLocalTime().ToString("MM/dd", CultureInfo.InvariantCulture));
                        index++;
                    }
                    index = 1;
                    try
                    {
                        //if (workoutLogAverage.Sets.Count > workoutLogAverage.Averages.Count  && workoutLogAverage.Sets.Count > 0)
                        //workoutLogAverage.Sets.RemoveAt(0);
                        foreach (var sets in workoutLogAverage.Averages.OrderBy(a => a.Date))
                        {
                            s2.Points.Add(new DataPoint(index, sets.Worksets));
                            index++;
                        }

                    }
                    catch (Exception ex)
                    {

                    }
                }


                if (s1.Points.Count > 7)
                {
                    var rowCollection = new RowDefinitionCollection();
                    //GridChart.ColumnDefinitions = new ColumnDefinitionCollection();
                    //GridChart.HeightRequest = 300;
                    var rowDefinition = new RowDefinition();
                    var rowDefinition1 = new RowDefinition();
                    rowDefinition.Height = new GridLength(150, GridUnitType.Absolute);
                    rowDefinition1.Height = new GridLength(150, GridUnitType.Absolute);
                    rowCollection.Add(rowDefinition);
                    rowCollection.Add(rowDefinition1);
                    //GridChart.Children.Add(plotScroll1, 0, 0);
                    //GridChart.Children.Add(plotScroll2, 0, 1);

                    plotView.HorizontalOptions = LayoutOptions.Start;
                    plotViewVolume.HorizontalOptions = LayoutOptions.Start;
                    //plotView.WidthRequest = s1.Points.Count * 31;
                    //plotViewVolume.WidthRequest = s1.Points.Count * 31;
                    //if (plotView.WidthRequest < GridChart.Width)
                    //{
                    //plotView.WidthRequest = GridChart.Width;
                    //plotViewVolume.WidthRequest = GridChart.Width;
                    //}
                    xAxis1.FontSize = 8;
                    xAxis.FontSize = 8;
                    s1.FontSize = 9;
                    s2.FontSize = 9;

                    plotModel.TitleHorizontalAlignment = TitleHorizontalAlignment.CenteredWithinView;
                    plotModel2.TitleHorizontalAlignment = TitleHorizontalAlignment.CenteredWithinView;
                }
                else
                {
                    //GridChart.RowDefinitions = new RowDefinitionCollection();
                    var columnCollection = new ColumnDefinitionCollection();
                    //GridChart.HeightRequest = 175;
                    var columnDefinition = new ColumnDefinition();
                    var columnDefinition1 = new ColumnDefinition();
                    //columnDefinition.Width = new GridLength(GridChart.Width / 2, GridUnitType.Star);
                    //columnDefinition1.Width = new GridLength(GridChart.Width / 2, GridUnitType.Star);
                    columnCollection.Add(columnDefinition);
                    columnCollection.Add(columnDefinition1);
                    //GridChart.Children.Add(plotScroll1, 0, 0);
                    //GridChart.Children.Add(plotScroll2, 1, 0);
                    //plotView.WidthRequest = GridChart.Width / 2;
                    //plotViewVolume.WidthRequest = GridChart.Width / 2;
                    plotView.HorizontalOptions = LayoutOptions.FillAndExpand;
                    plotViewVolume.HorizontalOptions = LayoutOptions.FillAndExpand;
                }

                plotModel.Series.Add(s1);
                plotView.Model.InvalidatePlot(true);
                plotView.Model = plotModel;

                plotModel2.Series.Add(s2);
                plotViewVolume.Model.InvalidatePlot(true);
                plotViewVolume.Model = plotModel2;
                //ProgressionLabel.Text = "";
            }

        }
        catch (Exception e)
        {
            var properties = new Dictionary<string, string>
                    {
                        { "HistoryPage_ChartPage_ExerciseLevel", $"{e.StackTrace}" }
                    };
            // uncomment code please
            //Crashes.TrackError(e, properties);
        }
    }

    private async void LoadDefaultChartOfflineAvailable()
    {
        var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
        if (mainWorkoutLog == null || (DatePicker.SelectedIndex == 0 && ExericsesPicker.SelectedIndex == 0))
        {

            if (workouts != null && workouts.Sets != null)
            {
                GetDefaultChartAsync(workouts);
                try
                {
                    UpdateProgramStats(workouts);
                    var exerciseModel = workouts.HistoryExerciseModel;
                    if (exerciseModel != null)
                    {
                        bool inKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg";
                        var unit = inKg ? AppResources.Kg.ToLower() : AppResources.Lbs.ToLower();
                        lblWorkoutsDone.IsVisible = true;
                        lblLiftedCount.IsVisible = true;
                        var weightLifted = inKg ? exerciseModel.TotalWeight.Kg : exerciseModel.TotalWeight.Lb;
                        lblWorkoutsDone.Text = exerciseModel.TotalWorkoutCompleted <= 1 ? $"{exerciseModel.TotalWorkoutCompleted} {AppResources.WorkoutDone}" : $"{exerciseModel.TotalWorkoutCompleted} {AppResources.WorkoutsDone}";
                        lblLiftedCount.Text = $"{weightLifted.ToString("N0")} {unit} {AppResources.Lifted}";
                    }
                }
                catch (Exception ex)
                {

                }
            }
        }
        else
        {
            GetDefaultChartAsync(mainWorkoutLog);
        }

        mainWorkoutLog = workouts == null ? await DrMuscleRestClient.Instance.GetUserWorkoutLogAverageWithUserStats() : await DrMuscleRestClient.Instance.GetUserWorkoutLogAverageWithUserStatsWithoutLoader();
        if(mainWorkoutLog != null)
            UpdateProgramStats(mainWorkoutLog);
        try
        {
            var exerciseModel = mainWorkoutLog.HistoryExerciseModel;
            if (exerciseModel != null)
            {
                bool inKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg";
                var unit = inKg ? AppResources.Kg.ToLower() : AppResources.Lbs.ToLower();
                lblWorkoutsDone.IsVisible = true;
                lblLiftedCount.IsVisible = true;
                var weightLifted = inKg ? exerciseModel.TotalWeight.Kg : exerciseModel.TotalWeight.Lb;
                lblWorkoutsDone.Text = exerciseModel.TotalWorkoutCompleted <= 1 ? $"{exerciseModel.TotalWorkoutCompleted} {AppResources.WorkoutDone}" : $"{exerciseModel.TotalWorkoutCompleted} {AppResources.WorkoutsDone}";
                lblLiftedCount.Text = $"{weightLifted.ToString("N0")} {unit} {AppResources.Lifted}";
            }
        }
        catch (Exception ex)
        {

        }
        GetDefaultChartAsync(mainWorkoutLog);
    }

    void UpdateProgramStats(GetUserWorkoutLogAverageResponse getUserWorkoutLogAverage)
    {
        try
        {
            if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                return;
            var upi = getUserWorkoutLogAverage.GetUserProgramInfoResponseModel;
            if (upi != null)
            {
                if (upi.RecommendedProgram != null && upi.NextWorkoutTemplate != null)
                {
                    //lblProgram.Text = $"Program: {upi.RecommendedProgram.Label}";
                    //lblProgram.Text = $"{AppResources.Program}: {upi.RecommendedProgram.Label}";
                    lblLevel.Text = $"{upi.RecommendedProgram.RemainingToLevelUp} workouts before level up";
                    lblWorkout.Text = $"Next workout: {upi.NextWorkoutTemplate.Label}";
                    if (upi.NextWorkoutTemplate.IsSystemExercise || upi.RecommendedProgram.IsFeaturedProgram)
                        lblLevel.IsVisible = true;
                    else
                        lblLevel.IsVisible = false;

                    LocalDBManager.Instance.SetDBSetting("remain", upi.RecommendedProgram.RemainingToLevelUp.ToString());
                }
                else
                {
                    //lblProgram.Text = $"{AppResources.YourProgramNotSetUp}";
                    lblWorkout.Text = $"{AppResources.TodaysWorkoutNotSetUp}";
                    if (LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId") != null &&
                        LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel") != null &&
                        LocalDBManager.Instance.GetDBSetting("recommendedProgramId") != null &&
                        LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel") != null &&
                        LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout") != null)
                    {
                        try
                        {
                            long workoutTemplateId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId").Value);
                            long programId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedProgramId").Value);
                            upi = new GetUserProgramInfoResponseModel()
                            {
                                NextWorkoutTemplate = new WorkoutTemplateModel() { Id = workoutTemplateId, Label = LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel").Value },
                                RecommendedProgram = new WorkoutTemplateGroupModel() { Id = programId, Label = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel").Value, RemainingToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value), RequiredWorkoutToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value) },
                            };
                            // lblProgram.Text = $"{AppResources.Program}: {upi.RecommendedProgram.Label}";
                            lblWorkout.Text = $"Next workout: {upi.NextWorkoutTemplate.Label}";
                            lblLevel.Text = $"{upi.RecommendedProgram.RemainingToLevelUp} workouts before level up";
                        }
                        catch (Exception ex)
                        {

                        }

                    }
                    if (upi.RecommendedProgram == null)
                        return;
                    LocalDBManager.Instance.SetDBSetting("remain", upi.RecommendedProgram.RemainingToLevelUp.ToString());
                }
            }
            else
            {
                //lblProgram.Text = $"{AppResources.YourProgramNotSetUp}";
                lblWorkout.Text = $"{AppResources.TodaysWorkoutNotSetUp}";
                lblLevel.Text = $"";
            }

        }
        catch (Exception ex)
        {

        }
    }
    private async void GetDefaultChartAsync(GetUserWorkoutLogAverageResponse workoutLogAverage)
    {
        try
        {
            LblSetsProgress.Text = "";


            if (chartWidth == -1)
            {
                chartWidth = plotView.Width;
            }
           
            DatePicker.Items.Clear();
            DatePicker.Items.Add(AppResources.Last3Workouts);
            DatePicker.SelectedIndex = 0;
            ExericsesPicker.Items.Clear();
            ExericsesPicker.Items.Add(AppResources.AverageOfAllRecentExercises);
            ExericsesPicker.SelectedIndex = 0;
            //GridChart.RowDefinitions = new RowDefinitionCollection();
            var columnCollection = new ColumnDefinitionCollection();
            //GridChart.HeightRequest = 175;
            var columnDefinition = new ColumnDefinition();
            var columnDefinition1 = new ColumnDefinition();
            //columnDefinition.Width = new GridLength(chartWidth == -1 ? 0 : chartWidth, GridUnitType.Star);
            //columnDefinition1.Width = new GridLength(chartWidth == -1 ? 0 : chartWidth, GridUnitType.Star);
            columnCollection.Add(columnDefinition);
            columnCollection.Add(columnDefinition1);
            //GridChart.Children.Add(plotScroll1, 0, 0);
            //GridChart.Children.Add(plotScroll2, 1, 0);
            if (chartWidth != -1)
            {
                plotView.WidthRequest = chartWidth;
                plotViewVolume.WidthRequest = chartWidth;
            }
            plotView.HorizontalOptions = LayoutOptions.FillAndExpand;
            plotViewVolume.HorizontalOptions = LayoutOptions.FillAndExpand;

            if (workoutLogAverage != null && !workoutLogAverage.Averages.Any())
            {
                //NoDataLabel.IsVisible = true;
                NoDataLabel.IsVisible = true;
                ContentStack.IsVisible = false;

                plotView.IsVisible = true;
                plotViewVolume.IsVisible = true;
                //plotScroll1.IsVisible = true;
                //plotScroll2.IsVisible = true;
                await Task.Delay(300);
                // uncomment code please
                //DrMuscle.Effects.TooltipEffect.SetHasShowTooltip(ToolTipButton, true);
                var plotModel = new PlotModel
                {
                    Title = AppResources.TryaWorkoutToSee,
                    TitleFontSize = 13,
                    //TitleFontWeight = FontWeights.Bold,
                    TitleColor = OxyColor.Parse("#23253A"),
                    Background = OxyColors.Transparent,
                    PlotAreaBackground = OxyColors.Transparent,
                    PlotAreaBorderColor = OxyColor.Parse("#23253A"),
                    //LegendTextColor = OxyColor.Parse("#23253A"),
                    //LegendPosition = LegendPosition.RightMiddle,
                    //LegendOrientation = LegendOrientation.Vertical,
                    //LegendPadding = 20,

                };

                LinearAxis yAxis = new LinearAxis { Position = AxisPosition.Left, Minimum = 10, Maximum = 20, AxislineColor = OxyColors.Blue, ExtraGridlineColor = OxyColors.Blue, MajorGridlineColor = OxyColors.Blue, MinorGridlineColor = OxyColors.Blue, TextColor = OxyColors.Blue, TicklineColor = OxyColors.Blue, TitleColor = OxyColors.Blue, TickStyle = TickStyle.None };
                yAxis.IsAxisVisible = false;
                LinearAxis xAxis = new LinearAxis { Position = AxisPosition.Bottom, AxislineColor = OxyColor.Parse("#23253A"), ExtraGridlineColor = OxyColors.Transparent, MajorGridlineColor = OxyColors.Transparent, MinorGridlineColor = OxyColors.Transparent, TextColor = OxyColor.Parse("#23253A"), TicklineColor = OxyColor.Parse("#23253A"), TitleColor = OxyColor.Parse("#23253A") };

                xAxis.LabelFormatter = _formatter;
                xAxis.MinimumPadding = 1;
                xAxis.IsPanEnabled = false;
                xAxis.IsZoomEnabled = false;
                xAxis.Minimum = 0.5;
                xAxis.Maximum = 3.5;

                IndexToDateLabel.Clear();
                IndexToDateLabel.Add(xAxis.Minimum, "");
                IndexToDateLabel.Add(xAxis.Maximum, "");

                yAxis.IsPanEnabled = false;
                yAxis.IsZoomEnabled = false;
                plotModel.Axes.Add(yAxis);
                plotModel.Axes.Add(xAxis);
                plotView.Model = plotModel;

                plotViewVolume.IsVisible = true;
                var plotModel2 = new PlotModel
                {
                    Title = AppResources.YourProgressInThisChart,
                    TitleFontSize = 13,
                    //TitleFontWeight = FontWeights.Bold,
                    TitleColor = OxyColor.Parse("#23253A"),
                    Background = OxyColors.Transparent,
                    PlotAreaBackground = OxyColors.Transparent,
                    PlotAreaBorderColor = OxyColor.Parse("#23253A"),
                    //LegendTextColor = OxyColor.Parse("#23253A"),
                    //LegendPosition = LegendPosition.RightMiddle,
                    //LegendOrientation = LegendOrientation.Vertical,
                    //LegendPadding = 20,
                };

                LinearAxis yAxis2 = new LinearAxis { Position = AxisPosition.Left, Minimum = 10, Maximum = 20, AxislineColor = OxyColors.Blue, ExtraGridlineColor = OxyColors.Blue, MajorGridlineColor = OxyColors.Blue, MinorGridlineColor = OxyColors.Blue, TextColor = OxyColors.Blue, TicklineColor = OxyColors.Blue, TitleColor = OxyColors.Blue, TickStyle = TickStyle.None };
                yAxis2.IsAxisVisible = false;
                LinearAxis xAxis2 = new LinearAxis { Position = AxisPosition.Bottom, AxislineColor = OxyColor.Parse("#23253A"), ExtraGridlineColor = OxyColors.Transparent, MajorGridlineColor = OxyColors.Transparent, MinorGridlineColor = OxyColors.Transparent, TextColor = OxyColor.Parse("#23253A"), TicklineColor = OxyColor.Parse("#23253A"), TitleColor = OxyColor.Parse("#23253A") };

                xAxis2.LabelFormatter = _formatter;
                xAxis2.MinimumPadding = 1;
                xAxis2.IsPanEnabled = false;
                xAxis2.IsZoomEnabled = false;
                xAxis2.Minimum = 0.5;
                xAxis2.Maximum = 3.5;

                IndexToDateLabel.Clear();
                IndexToDateLabel.Add(xAxis2.Minimum, "");
                IndexToDateLabel.Add(xAxis2.Maximum, "");

                yAxis2.IsPanEnabled = false;
                yAxis2.IsZoomEnabled = false;
                plotModel2.Axes.Add(yAxis2);
                plotModel2.Axes.Add(xAxis2);
                plotViewVolume.Model = plotModel2;
                LblSetsProgress.Text = "";
                LblProgress.Text = "";

                if (workoutLogAverage.AllExercises != null && workoutLogAverage.AllExercises.Any())
                {
                    ExericsesPicker.Items.Clear();
                    ExericsesPicker.Items.Add(AppResources.AverageOfAllRecentExercises);
                    ExericsesPicker.SelectedIndex = 0;
                    try
                    {
                        ExercisesLabelToID = new Dictionary<string, long>();
                        foreach (ExerciceModel e in workoutLogAverage.AllExercises)
                        {
                            if (ExericsesPicker.Items.Count(i => i == e.Label) == 0)
                                ExericsesPicker.Items.Add(e.Label);
                            if (!ExercisesLabelToID.ContainsKey(e.Label))
                                ExercisesLabelToID.Add(e.Label, e.Id);
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                }
                return;
            }
            else
            {
                NoDataLabel.IsVisible = false;
                ContentStack.IsVisible = true;
                plotView.IsVisible = true;
                plotViewVolume.IsVisible = true;
                //plotScroll1.IsVisible = true;
                //plotScroll2.IsVisible = true;


            }
            try
            {

                //if (workoutLogAverage == null)
                //return;
                if (workoutLogAverage == null || !workoutLogAverage.Averages.Any())
                {
                    try
                    {

                        //NoDataLabel.IsVisible = true;
                        plotView.IsVisible = true;
                        var plotModel = new PlotModel
                        {
                            Title = AppResources.TryaWorkoutToSee,
                            TitleFontSize = 13,
                            //TitleFontWeight = FontWeights.Bold,
                            TitleColor = OxyColor.Parse("#23253A"),
                            Background = OxyColors.Transparent,
                            PlotAreaBackground = OxyColors.Transparent,
                            PlotAreaBorderColor = OxyColor.Parse("#23253A"),
                            //LegendTextColor = OxyColor.Parse("#23253A"),
                            //LegendPosition = LegendPosition.RightMiddle,
                            //LegendOrientation = LegendOrientation.Vertical,
                            //LegendPadding = 20,
                            //LegendLineSpacing = 20,
                            //LegendItemSpacing = 20,
                        };

                        LinearAxis yAxis = new LinearAxis { Position = AxisPosition.Left, Minimum = 10, Maximum = 20, AxislineColor = OxyColors.Blue, ExtraGridlineColor = OxyColors.Blue, MajorGridlineColor = OxyColors.Blue, MinorGridlineColor = OxyColors.Blue, TextColor = OxyColors.Blue, TicklineColor = OxyColors.Blue, TitleColor = OxyColors.Blue, TickStyle = TickStyle.None };
                        yAxis.IsAxisVisible = false;
                        LinearAxis xAxis = new LinearAxis { Position = AxisPosition.Bottom, AxislineColor = OxyColor.Parse("#23253A"), ExtraGridlineColor = OxyColor.Parse("#23253A"), MajorGridlineColor = OxyColor.Parse("#23253A"), MinorGridlineColor = OxyColor.Parse("#23253A"), TextColor = OxyColor.Parse("#23253A"), TicklineColor = OxyColor.Parse("#23253A"), TitleColor = OxyColor.Parse("#23253A") };

                        xAxis.LabelFormatter = _formatter;
                        xAxis.MinimumPadding = 1;
                        xAxis.IsPanEnabled = false;
                        xAxis.IsZoomEnabled = false;
                        xAxis.Minimum = 0.5;
                        xAxis.Maximum = 3.5;

                        IndexToDateLabel.Clear();
                        IndexToDateLabel.Add(xAxis.Minimum, "");
                        IndexToDateLabel.Add(xAxis.Maximum, "");

                        yAxis.IsPanEnabled = false;
                        yAxis.IsZoomEnabled = false;
                        plotModel.Axes.Add(yAxis);
                        plotModel.Axes.Add(xAxis);
                        plotView.Model = plotModel;

                        plotViewVolume.IsVisible = true;
                        var plotModel2 = new PlotModel
                        {
                            Title = AppResources.YourProgressInThisChart,//AppResources.TryAWorkoutToSeeYourProgressInThisChart,
                            TitleFontSize = 13,
                            //TitleFontWeight = FontWeights.Bold,
                            TitleColor = OxyColor.Parse("#23253A"),
                            Background = OxyColors.Transparent,
                            PlotAreaBackground = OxyColors.Transparent,
                            PlotAreaBorderColor = OxyColor.Parse("#23253A"),
                            //LegendTextColor = OxyColor.Parse("#23253A"),
                            //LegendPosition = LegendPosition.RightMiddle,
                            //LegendOrientation = LegendOrientation.Vertical,
                            //LegendPadding = 20,
                            //LegendLineSpacing = 20,
                            //LegendItemSpacing = 20,
                        };

                        LinearAxis yAxis2 = new LinearAxis { Position = AxisPosition.Left, Minimum = 10, Maximum = 20, AxislineColor = OxyColors.Blue, ExtraGridlineColor = OxyColors.Blue, MajorGridlineColor = OxyColors.Blue, MinorGridlineColor = OxyColors.Blue, TextColor = OxyColors.Blue, TicklineColor = OxyColors.Blue, TitleColor = OxyColors.Blue, TickStyle = TickStyle.None };
                        yAxis2.IsAxisVisible = false;
                        LinearAxis xAxis2 = new LinearAxis { Position = AxisPosition.Bottom, AxislineColor = OxyColor.Parse("#23253A"), ExtraGridlineColor = OxyColor.Parse("#23253A"), MajorGridlineColor = OxyColor.Parse("#23253A"), MinorGridlineColor = OxyColor.Parse("#23253A"), TextColor = OxyColor.Parse("#23253A"), TicklineColor = OxyColor.Parse("#23253A"), TitleColor = OxyColor.Parse("#23253A") };

                        xAxis2.LabelFormatter = _formatter;
                        xAxis2.MinimumPadding = 1;
                        xAxis2.IsPanEnabled = false;
                        xAxis2.IsZoomEnabled = false;
                        xAxis2.Minimum = 0.5;
                        xAxis2.Maximum = 3.5;

                        IndexToDateLabel.Clear();
                        IndexToDateLabel.Add(xAxis2.Minimum, "");
                        IndexToDateLabel.Add(xAxis2.Maximum, "");

                        yAxis2.IsPanEnabled = false;
                        yAxis2.IsZoomEnabled = false;
                        plotModel2.Axes.Add(yAxis2);
                        plotModel2.Axes.Add(xAxis2);
                        plotViewVolume.Model = plotModel2;

                    }
                    catch (Exception ex)
                    {

                    }

                }
                else
                {
                    // NoDataLabel.IsVisible = false;
                    plotView.IsVisible = true;

                    var plotModel = new PlotModel
                    {
                        Title = "TOTAL STRENGTH",//AppResources.MaxStrengthCapital,
                        TitleFontSize = 13,
                        //TitleFontWeight = FontWeights.Bold,
                        TitleColor = OxyColor.Parse("#23253A"),
                        Background = OxyColors.Transparent,
                        PlotAreaBackground = OxyColors.Transparent,
                        PlotAreaBorderColor = OxyColor.Parse("#23253A"),
                        //LegendPlacement = LegendPlacement.Outside,
                        //LegendTextColor = OxyColor.Parse("#23253A"),
                        //LegendPosition = LegendPosition.BottomCenter,
                        //LegendOrientation = LegendOrientation.Horizontal,
                        //LegendLineSpacing = 5,
                        IsLegendVisible = true
                        //LegendItemSpacing = 20,
                    };

                    bool inKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg";
                    if (workoutLogAverage.AllExercises != null)
                    {

                        ExericsesPicker.Items.Clear();
                        ExericsesPicker.Items.Add(AppResources.AverageOfAllRecentExercises);
                        ExericsesPicker.SelectedIndex = 0;
                        ExercisesLabelToID = new Dictionary<string, long>();
                        try
                        {
                            foreach (ExerciceModel e in workoutLogAverage.AllExercises)
                            {
                                if (ExericsesPicker.Items.Count(i => i == e.Label) == 0)
                                    ExericsesPicker.Items.Add(e.Label);
                                if (!ExercisesLabelToID.ContainsKey(e.Label))
                                    ExercisesLabelToID.Add(e.Label, e.Id);
                            }
                        }
                        catch (Exception ex)
                        {

                        }
                    }
                    else
                    {
                        ExericsesPicker.Items.Clear();
                        ExericsesPicker.Items.Add(AppResources.AverageOfAllRecentExercises);
                        ExericsesPicker.SelectedIndex = 0;
                    }


                    try
                    {
                        //var min = (double)workoutLogAverage.Averages.Min(a => inKg ? a.Average.Kg : a.Average.Lb) - 100;
                        //var max = (double)workoutLogAverage.Averages.Max(a => inKg ? a.Average.Kg : a.Average.Lb) + (inKg ? 600 : 1200);
                        //if (workoutLogAverage.Sets != null && workoutLogAverage.Sets.Count > 0)
                        //{
                        //    min = Math.Min((double)workoutLogAverage.Averages.Min(a => inKg ? a.Average.Kg : a.Average.Lb) - 100, workoutLogAverage.Sets.Min() - 100);
                        //    max = Math.Max((double)workoutLogAverage.Averages.Max(a => inKg ? a.Average.Kg : a.Average.Lb) + (inKg ? 600 : 1200), workoutLogAverage.Sets.Max() + (inKg ? 600 : 1200));
                        //}
                        var minVal = (double)workoutLogAverage.Averages.Min(a => inKg ? a.Average.Kg : a.Average.Lb);
                        var maxVal = (double)workoutLogAverage.Averages.Max(a => inKg ? a.Average.Kg : a.Average.Lb);
                        //var min = minVal - (maxVal * 0.2);
                        //var max = 25 * maxVal / 19;
                        var min = minVal - (maxVal - minVal) * 0.20;
                        var max = maxVal + (maxVal - minVal) * 0.5;

                        LinearAxis yAxis = new LinearAxis { Position = AxisPosition.Left, Minimum = min, Maximum = max, AxislineColor = OxyColors.Blue, ExtraGridlineColor = OxyColors.Blue, MajorGridlineColor = OxyColors.Blue, MinorGridlineColor = OxyColors.Blue, TextColor = OxyColors.Blue, TicklineColor = OxyColors.Blue, TitleColor = OxyColors.Blue, TickStyle = TickStyle.None };
                        yAxis.IsAxisVisible = false;
                        LinearAxis xAxis = new LinearAxis { Position = AxisPosition.Bottom, AxislineColor = OxyColor.Parse("#23253A"), ExtraGridlineColor = OxyColor.Parse("#23253A"), MajorGridlineColor = OxyColor.Parse("#23253A"), MinorGridlineColor = OxyColor.Parse("#23253A"), TextColor = OxyColor.Parse("#23253A"), TicklineColor = OxyColor.Parse("#23253A"), TitleColor = OxyColor.Parse("#23253A"), FontSize = 12, MinimumMajorStep = 0.3, MinorStep = 0.5, MajorStep = 0.5 };

                        xAxis.LabelFormatter = _formatter;
                        xAxis.MinimumPadding = 0.05;
                        xAxis.MaximumPadding = 0.1;
                        xAxis.IsPanEnabled = false;
                        xAxis.IsZoomEnabled = false;
                        xAxis.Minimum = 0.5;
                        xAxis.Maximum = 3.5;

                        IndexToDateLabel.Clear();
                        IndexToDateLabel.Add(xAxis.Minimum, "");
                        IndexToDateLabel.Add(xAxis.Maximum, "");

                        yAxis.IsPanEnabled = false;
                        yAxis.IsZoomEnabled = false;
                        plotModel.Axes.Add(yAxis);
                        plotModel.Axes.Add(xAxis);
                    }
                    catch (Exception)
                    {

                    }
                    var s1 = new LineSeries()
                    {
                        //Title = AppResources.MaxStrength,
                        Color = OxyColor.Parse("#38418C"),
                        TextColor = OxyColor.Parse("#38418C"),
                        LabelFormatString = "{1:0}",
                        //LabelMargin = -26,
                        FontSize = 15,
                        MarkerType = MarkerType.Circle,
                        MarkerSize = 6,
                        MarkerStroke = OxyColor.Parse("#38418C"),
                        MarkerFill = OxyColor.Parse("#38418C"),
                        MarkerStrokeThickness = 1,
                    };
                    int index = 1;
                    DateTime? creationDate = null;
                    bool isSetsestimated = true;
                    try
                    {
                        creationDate = new DateTime(Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("creation_date").Value));
                    }
                    catch (Exception)
                    {

                    }
                    foreach (var sets in workoutLogAverage.Sets)
                    {
                        if (sets != 0)
                            isSetsestimated = false;
                    }
                    if (workoutLogAverage.Averages.Count > 2)
                    {
                        foreach (var data in workoutLogAverage.Averages.Take(3))
                        {
                            if (creationDate != null && data.Date < ((DateTime)creationDate).Date || isSetsestimated)
                            {
                                isEstimated = true;
                            }
                            s1.Points.Add(new DataPoint(index, Convert.ToDouble(inKg ? data.Average.Kg : data.Average.Lb)));
                            //plotModel.Annotations.Add(new TextAnnotation() { Text = "Estimate", TextVerticalAlignment = VerticalAlignment.Top, StrokeThickness = 0, Stroke = OxyColors.Transparent, TextPosition = new DataPoint(index, Convert.ToDouble(inKg ? data.Average.Kg : data.Average.Lb) - 10) });

                            index++;
                        }
                    }
                    else if (workoutLogAverage.Averages.Count == 2)
                    {
                        index = 2;
                        s1.Points.Add(new DataPoint(1, 0));
                        foreach (var data in workoutLogAverage.Averages.Take(2))
                        {
                            if (creationDate != null && data.Date < ((DateTime)creationDate).Date || isSetsestimated)
                            {
                                isEstimated = true;
                            }
                            s1.Points.Add(new DataPoint(index, Convert.ToDouble(inKg ? data.Average.Kg : data.Average.Lb)));
                            //plotModel.Annotations.Add(new TextAnnotation() { Text = "Estimate", TextVerticalAlignment = VerticalAlignment.Top, StrokeThickness = 0, Stroke = OxyColors.Transparent, TextPosition = new DataPoint(index, Convert.ToDouble(inKg ? data.Average.Kg : data.Average.Lb) - 10) });

                            index++;
                        }

                    }
                    else
                    {
                        index = 3;
                        foreach (var data in workoutLogAverage.Averages.Take(3))
                        {
                            if (creationDate != null && data.Date < ((DateTime)creationDate).Date || isSetsestimated)
                            {
                                isEstimated = true;
                            }

                            s1.Points.Add(new DataPoint(index, Convert.ToDouble(inKg ? data.Average.Kg : data.Average.Lb)));
                            //plotModel.Annotations.Add(new TextAnnotation() { Text = "Estimate", TextVerticalAlignment = VerticalAlignment.Top, StrokeThickness = 0, Stroke = OxyColors.Transparent, TextPosition = new DataPoint(index, Convert.ToDouble(inKg ? data.Average.Kg : data.Average.Lb) - 10) });

                            index--;
                        }
                        if (index > 0)
                        {
                            for (int i = index; i > 0; i--)
                            {
                                s1.Points.Add(new DataPoint(i, 0));
                                //IndexToDateLabel.Add(i, "");
                            }
                        }
                    }


                    index = 1;
                    var s2 = new LineSeries()
                    {
                        //Title = AppResources.WorkSetsNoColon,
                        Color = OxyColor.Parse("#5DD397"),
                        LabelFormatString = "{1:0}",
                        FontSize = 15,
                        TextColor = OxyColor.Parse("#5DD397"),
                        LineStyle = LineStyle.Dash,
                        MarkerType = MarkerType.Diamond,
                        MarkerSize = 6,
                        MarkerStroke = OxyColor.Parse("#5DD397"),
                        MarkerFill = OxyColor.Parse("#5DD397"),
                        MarkerStrokeThickness = 1,
                    };

                    if (workoutLogAverage.Sets != null)
                    {
                        workoutLogAverage.Sets.Reverse();
                        workoutLogAverage.SetsDate.Reverse();
                        IndexToDateLabel.Clear();

                        foreach (var sets in workoutLogAverage.Sets)
                        {
                            s2.Points.Add(new DataPoint(index, Convert.ToDouble(sets)));
                            IndexToDateLabel.Add(index, workoutLogAverage.SetsDate[index - 1].ToLocalTime().ToString("MMM dd", CultureInfo.InvariantCulture));
                            index++;
                        }

                    }

                    plotModel.Series.Add(s1);
                    //plotModel.Series.Add(s2);
                    plotView.Model = plotModel;

                    //LblExerciseName.Text = AppResources.RecentExercisesinFourWeek;
                    //LblExerciseName.IsVisible = true;
                    //ProgressionLabel.Text = "";

                    foreach (ExerciceModel e in workoutLogAverage.AverageExercises)
                    {
                        //ProgressionLabel.Text += string.Format("{0}\n", e.Label);
                    }

                    //ProgressionLabel.Text += "\n";

                    //ProgressionLabel.Text = ProgressionLabel.Text.Substring(0, ProgressionLabel.Text.Length - 2);
                    OneRMAverage last = workoutLogAverage.Averages.ToList()[workoutLogAverage.Averages.Count - 1];
                    var rm1 = (inKg ? Math.Round(last.Average.Kg, 2) + " kg" : Math.Round(last.Average.Lb, 2) + " lbs").ReplaceWithDot();
                    //ProgressionLabel.Text += $"{AppResources.AverageMaxStrength}: {rm1}";
                    //ProgressionLabel.Text += string.Format("{0}{1:0}.", AppResources.ForTheseExercisesYourCurrentAverage1RMIs, inKg ? Math.Round(last.Average.Kg, 2) + " kg" : Math.Round(last.Average.Lb, 2) + " lbs").ReplaceWithDot();

                    //if (workoutLogAverage.Averages.Count > 1)
                    //{

                    //    OneRMAverage before = workoutLogAverage.Averages.OrderBy(a => a.Date).ToList()[workoutLogAverage.Averages.Count - 2];
                    //    decimal progresskg = (last.Average.Kg - before.Average.Kg) * 100 / last.Average.Kg;

                    //    LblProgress.Text = String.Format("{0}: {1}{2} ({3}%)", AppResources.MaxStrength, (last.Average.Kg - before.Average.Kg) > 0 ? "+" : "", inKg ? Math.Round(last.Average.Kg - before.Average.Kg, 2) + " kg" : Math.Round(last.Average.Lb - before.Average.Lb, 2) + " lbs", progresskg.ToString("0.00")).ReplaceWithDot();
                    //}



                    //if (workoutLogAverage.Sets.Count > 1)
                    //{

                    //    int firstSets = workoutLogAverage.Sets[workoutLogAverage.Sets.Count - 1];
                    //    int lastSets = workoutLogAverage.Sets[workoutLogAverage.Sets.Count - 2];
                    //    try
                    //    {
                    //        decimal progressSets = (firstSets - lastSets) * 100 / firstSets;
                    //        LblSetsProgress.Text = String.Format("{0}: {1}{2} ({3}%)", AppResources.WorkSetsNoColon, (firstSets - lastSets) > 0 ? "+" : "", firstSets - lastSets, progressSets.ToString("0.00")).ReplaceWithDot();
                    //    }
                    //    catch (Exception ex)
                    //    {
                    //        LblSetsProgress.Text = "";
                    //    }
                    //}
                    LblProgress.Text = "";
                    if (!isEstimated)
                        LblProgress.Text += $"Total strength: {rm1}";
                    if (workoutLogAverage.Averages.Count > 1)
                    {

                        OneRMAverage before = workoutLogAverage.Averages.ToList()[workoutLogAverage.Averages.Count - 2];
                        decimal progress = (last.Average.Kg - before.Average.Kg) * 100 / (before.Average.Kg < 1 ? 1 : before.Average.Kg);
                        if (!isEstimated)
                            LblProgress.Text += string.Format(" ({0}{1:0.00}%)", progress > 0 ? "+" : "", progress.ToString("0.00")).ReplaceWithDot();
                        //ProgressionLabel.Text += string.Format(" {0} {1:0.00}. {2} {3:0.00}%.", AppResources.YourPrevious1RMWas, inKg ? Math.Round(SaveSetPage.RoundDownToNearest(before.Average.Kg, 1), 2) + " kg" : Math.Round(SaveSetPage.RoundDownToNearest(before.Average.Lb, (decimal)2.5), 2) + " lbs", AppResources.ChangeIs, progress.ToString("0.00")).ReplaceWithDot();

                    }
                    if (workoutLogAverage.Sets.Count > 1)
                    {

                        int firstSets = workoutLogAverage.Sets[workoutLogAverage.Sets.Count - 1];
                        int lastSets = workoutLogAverage.Sets[workoutLogAverage.Sets.Count - 2];
                        try
                        {
                            decimal progressSets = (firstSets - lastSets) * 100 / (lastSets == 0 ? 1 : lastSets);
                            //LblSetsProgress.Text = String.Format("{0}: {1}{2} ({3}%)", (firstSets - lastSets) > 0 ? "+" : "", firstSets - lastSets, progressSets.ToString("0.00")).ReplaceWithDot();
                            var sign = progressSets > 0 ? "+" : "";
                            LblSetsProgress.Text += $"{AppResources.WorkSetsLastSevenDays}: {firstSets} ({sign}{progressSets.ToString("0.00")}%)".ReplaceWithDot();
                        }
                        catch (Exception ex)
                        {
                            // LblSetsProgress.Text = "";
                        }
                    }
                    //Second Chart
                    plotViewVolume.IsVisible = true;

                    var plotModel2 = new PlotModel
                    {
                        Title = AppResources.WorkSetsCapital,
                        TitleFontSize = 13,
                        //TitleFontWeight = FontWeights.Bold,
                        TitleColor = OxyColor.Parse("#23253A"),

                        Background = OxyColors.Transparent,
                        PlotAreaBackground = OxyColors.Transparent,
                        PlotAreaBorderColor = OxyColor.Parse("#23253A"),
                        //LegendPlacement = LegendPlacement.Outside,
                        //LegendTextColor = OxyColor.Parse("#23253A"),
                        //LegendPosition = LegendPosition.BottomCenter,
                        //LegendOrientation = LegendOrientation.Horizontal,
                        //LegendLineSpacing = 5,
                        IsLegendVisible = true
                        //LegendItemSpacing = 20,
                    };

                    //bool inKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg";

                    try
                    {
                        //var min = (double)workoutLogAverage.Averages.Min(a => inKg ? a.Average.Kg : a.Average.Lb) - 300;
                        //var max = (double)workoutLogAverage.Averages.Max(a => inKg ? a.Average.Kg : a.Average.Lb) + 300;
                        //if (workoutLogAverage.Sets != null && workoutLogAverage.Sets.Count > 0)
                        //{
                        //    min = Math.Min((double)workoutLogAverage.Averages.Min(a => inKg ? a.Average.Kg : a.Average.Lb) - 300, workoutLogAverage.Sets.Min() - 300);
                        //    max = Math.Max((double)workoutLogAverage.Averages.Max(a => inKg ? a.Average.Kg : a.Average.Lb) + 300, workoutLogAverage.Sets.Max() + 300);
                        //}
                        var minVal = (double)workoutLogAverage.Sets.Min();
                        var maxVal = (double)workoutLogAverage.Sets.Max();
                        var change = maxVal * 0.2;
                        //var min = minVal - change;
                        //var max = 28 * maxVal / 19;
                        var min = minVal - (maxVal - minVal) * 0.20;
                        var max = maxVal + (maxVal - minVal) * 0.5;
                        if (min == 0 && max == 0)
                        {
                            min = -30;
                            max = 50;
                        }
                        LinearAxis yAxis = new LinearAxis { Position = AxisPosition.Left, Minimum = min, Maximum = max, AxislineColor = OxyColors.Blue, ExtraGridlineColor = OxyColors.Blue, MajorGridlineColor = OxyColors.Blue, MinorGridlineColor = OxyColors.Blue, TextColor = OxyColors.Blue, TicklineColor = OxyColors.Blue, TitleColor = OxyColors.Blue, TickStyle = TickStyle.None };
                        yAxis.IsAxisVisible = false;
                        LinearAxis xAxis = new LinearAxis { Position = AxisPosition.Bottom, AxislineColor = OxyColor.Parse("#23253A"), ExtraGridlineColor = OxyColor.Parse("#23253A"), MajorGridlineColor = OxyColor.Parse("#23253A"), MinorGridlineColor = OxyColor.Parse("#23253A"), TextColor = OxyColor.Parse("#23253A"), TicklineColor = OxyColor.Parse("#23253A"), TitleColor = OxyColor.Parse("#23253A"), MinimumMajorStep = 0.3, MinorStep = 0.5, MajorStep = 0.5 };

                        xAxis.LabelFormatter = _formatter;
                        xAxis.MinimumPadding = 1;
                        xAxis.IsPanEnabled = false;
                        xAxis.IsZoomEnabled = false;
                        xAxis.Minimum = 0.5;
                        xAxis.Maximum = 3.5;

                        IndexToDateLabel2.Clear();
                        IndexToDateLabel2.Add(xAxis.Minimum, "");
                        IndexToDateLabel2.Add(xAxis.Maximum, "");

                        yAxis.IsPanEnabled = false;
                        yAxis.IsZoomEnabled = false;
                        plotModel2.Axes.Add(yAxis);
                        plotModel2.Axes.Add(xAxis);
                    }
                    catch (Exception)
                    {

                    }
                    var s12 = new LineSeries()
                    {
                        //Title = AppResources.MaxStrength,
                        Color = OxyColor.Parse("#38418C"),
                        TextColor = OxyColor.Parse("#38418C"),
                        LabelFormatString = "{1:0}",
                        //LabelMargin = -26,
                        FontSize = 15,
                        MarkerType = MarkerType.Circle,
                        MarkerSize = 6,
                        MarkerStroke = OxyColor.Parse("#38418C"),
                        MarkerFill = OxyColor.Parse("#38418C"),
                        MarkerStrokeThickness = 1,
                    };
                    index = 1;
                    if (workoutLogAverage.Averages.Count > 2)
                    {
                        foreach (var data in workoutLogAverage.Averages.Take(3))
                        {
                            s12.Points.Add(new DataPoint(index, Convert.ToDouble(inKg ? data.Average.Kg : data.Average.Lb)));
                            index++;
                        }
                    }
                    else if (workoutLogAverage.Averages.Count == 2)
                    {
                        index = 2;
                        s12.Points.Add(new DataPoint(1, 0));
                        foreach (var data in workoutLogAverage.Averages.Take(2))
                        {
                            s12.Points.Add(new DataPoint(index, Convert.ToDouble(inKg ? data.Average.Kg : data.Average.Lb)));
                            //IndexToDateLabel.Add(index, data.Date.ToLocalTime().ToString("MM/dd", CultureInfo.InvariantCulture));
                            index++;
                        }

                    }
                    else
                    {
                        index = 3;
                        foreach (var data in workoutLogAverage.Averages.OrderBy(a => a.Date).Take(3))
                        {
                            s12.Points.Add(new DataPoint(index, Convert.ToDouble(inKg ? data.Average.Kg : data.Average.Lb)));
                            //IndexToDateLabel.Add(index, data.Date.ToLocalTime().ToString("MM/dd", CultureInfo.InvariantCulture));
                            index--;
                        }
                        if (index > 0)
                        {
                            for (int i = index; i > 0; i--)
                            {
                                s12.Points.Add(new DataPoint(i, 0));
                                //IndexToDateLabel.Add(i, "");
                            }
                        }
                    }


                    index = 1;
                    var s22 = new LineSeries()
                    {
                        //Title = AppResources.WorkSetsNoColon,
                        Color = OxyColor.Parse("#5DD397"),
                        LabelFormatString = "{1:0}",
                        FontSize = 15,
                        TextColor = OxyColor.Parse("#5DD397"),
                        LineStyle = LineStyle.Dash,
                        MarkerType = MarkerType.Diamond,
                        MarkerSize = 6,
                        MarkerStroke = OxyColor.Parse("#5DD397"),
                        MarkerFill = OxyColor.Parse("#5DD397"),
                        MarkerStrokeThickness = 1,
                    };

                    if (workoutLogAverage.Sets != null)
                    {

                        IndexToDateLabel2.Clear();
                        foreach (var sets in workoutLogAverage.Sets)
                        {
                            s22.Points.Add(new DataPoint(index, Convert.ToDouble(sets)));
                            IndexToDateLabel2.Add(index, workoutLogAverage.SetsDate[index - 1].ToLocalTime().ToString("MM/dd", CultureInfo.InvariantCulture));
                            index++;
                        }
                    }

                    //plotModel2.Series.Add(s12);
                    plotModel2.Series.Add(s22);
                    plotViewVolume.Model = plotModel2;

                    if (workoutLogAverage.Sets != null)
                    {
                        workoutLogAverage.Sets.Reverse();
                        workoutLogAverage.SetsDate.Reverse();
                    }


                }
            }
            catch (Exception e)
            {
                var properties = new Dictionary<string, string>
                    {
                        { "HistoryPage_ChartPage", $"{e.StackTrace}" }
                    };
                // uncomment code please
                //Crashes.TrackError(e, properties);
            }
        }
        catch (Exception e)
        {
            var properties = new Dictionary<string, string>
                    {
                        { "HistoryPage_ChartPage", $"{e.StackTrace}" }
                    };
            // uncomment code please
            //Crashes.TrackError(e, properties);
        }
    }

    private void RemoveWorkoutLog(long workoutLogId)
    {
        List<HistoryItem> toDelete = new List<HistoryItem>();
        foreach (HistoryItem hi in historyModel.Where(h => h.WorkoutLogId == workoutLogId))
            toDelete.Add(hi);

        foreach (HistoryItem hi in toDelete)
            historyModel.Remove(hi);
    }

    public async void OnDeleteWorkoutLogClicked(object sender, EventArgs e)
    {
        var mi = ((Button)sender);
        HistoryItem m = (HistoryItem)mi.CommandParameter;

        BooleanModel result = await DrMuscleRestClient.Instance.DeleteWorkoutLog(new DeleteWorkoutLogExerciseModel() { WorkoutLogId = (long)m.WorkoutLogId });
        if (result.Result)
        {
            RemoveWorkoutLog((long)m.WorkoutLogId);
        }
    }

    private void RemoveWorkoutLogExercise(long workoutLogId, long exerciseId)
    {
        List<HistoryItem> toDelete = new List<HistoryItem>();
        foreach (HistoryItem hi in historyModel.Where(h => h.WorkoutLogId == workoutLogId && h.ExerciseId == exerciseId))
            toDelete.Add(hi);

        foreach (HistoryItem hi in toDelete)
            historyModel.Remove(hi);

        if (historyModel.Count(h => h.WorkoutLogId == workoutLogId && h.ExerciseId != null) == 0)
            RemoveWorkoutLog(workoutLogId);
    }

    public async void OnDeleteWorkoutLogExerciseClicked(object sender, EventArgs e)
    {
        var mi = ((Button)sender);
        HistoryItem m = (HistoryItem)mi.CommandParameter;

        BooleanModel result = await DrMuscleRestClient.Instance.DeleteUserWorkoutLogExercise(new DeleteWorkoutLogExerciseModel() { WorkoutLogId = (long)m.WorkoutLogId, WorkoutLogExerciseId = (long)m.ExerciseId });
        if (result.Result)
        {
            RemoveWorkoutLogExercise((long)m.WorkoutLogId, (long)m.ExerciseId);
        }
    }

    private void RemoveWorkoutLogSerie(HistoryItem hi)
    {
        historyModel.Remove(hi);
        int nbSets = historyModel.Count(h => h.WorkoutLogId == hi.WorkoutLogId && h.ExerciseId == hi.ExerciseId && hi.ItemType == HistoryItemType.SetType);

        // Il y a visiblement un bug dans Linq, �a devrait ramener 0 mais �a ram�ne 2 item de type Date et Statistic... Etrange
        if (nbSets == 2)
        {
            HistoryItem statItem = historyModel.FirstOrDefault(s => s.WorkoutLogId == hi.WorkoutLogId && s.ExerciseId == hi.ExerciseId && s.ItemType == HistoryItemType.StatisticType);
            if (statItem != null)
            {
                historyModel.Remove(statItem);
            }
            RemoveWorkoutLogExercise((long)hi.WorkoutLogId, (long)hi.ExerciseId);
        }
    }

    public async void OnDeleteWorkoutLogSerieClicked(object sender, EventArgs e)
    {
        var mi = ((Button)sender);
        HistoryItem m = (HistoryItem)mi.CommandParameter;

        BooleanModel result = await DrMuscleRestClient.Instance.DeleteWorkoutLogSeries((WorkoutLogSerieModel)m.Model);
        if (result.Result)
        {
            RemoveWorkoutLogSerie(m);
        }
    }
    async void ButtonStartWorkout_Clicked(object sender, System.EventArgs e)
    {
        //PagesFactory.PushAsync<ChooseDrMuscleOrCustomPage>();
        try
        {
            if (App.Current.MainPage.Navigation.NavigationStack.First() is MainTabbedPage)
                ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[0];

        }
        catch (Exception ex)
        {

        }

        await Navigation.PopToRootAsync();
        if (Device.RuntimePlatform == Device.iOS)
            await Task.Delay(100);
        Device.BeginInvokeOnMainThread(() =>
        {
            MessagingCenter.Send<StartNormalWorkout>(new StartNormalWorkout() { }, "StartNormalWorkout");
        });
    }

    async void UpdateBodyweightClicked(object sender, System.EventArgs e)
    {
        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // });
            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Try again","");
            return;
        }

        CustomPromptConfig p = new CustomPromptConfig("Update body weight", "Tap to enter your weight",
            AppResources.Ok,AppResources.Cancel,"At least 6 characters",Keyboard.Numeric,"",7);

            p.ActionSelected += async (sender,action) => {
                if(action == PopupAction.OK){
                     
                    try
                    {
                        if (string.IsNullOrWhiteSpace(p.text) || Convert.ToDecimal(p.text, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                        var weightText = p.text.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);

                        LocalDBManager.Instance.SetDBSetting("BodyWeight", new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit")?.Value).Kg.ToString().Replace(",", "."));
                        var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                        var weights = new MultiUnityWeight(value, "kg");
                        App.IsWeightChangeFromOtherScreen = true;
                        App.IsMealPlanChange = false;
                        LocalDBManager.Instance.SetDBSetting("Macros", "");
                        var email = LocalDBManager.Instance.GetDBSetting("email")?.Value;
                        Preferences.Set($"Macros{email}", "");

                        await DrMuscleRestClient.Instance.SetUserBodyWeight(new UserInfosModel()
                        {
                            BodyWeight = new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit")?.Value)
                        });
                        if (Device.RuntimePlatform.Equals(Device.iOS))
                        {
                            IHealthData _healthService = DependencyService.Get<IHealthData>();
                            await _healthService.GetWeightPermissionAsync(async (r) =>
                            {
                                var a = r;
                                if (r)
                                {
                                    _healthService.SetWeight(LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ? (double)Math.Round(weights.Kg, 2) : (double)Math.Round(weights.Lb, 2));
                                }
                            });
                        }
                    }
                    catch (Exception ex)
                    {

                    }

                    LoadSavedWeights(); MessagingCenter.Send<BodyweightUpdateMessage>(new BodyweightUpdateMessage() { }, "BodyweightUpdateMessage");

                    return;
                }
            };
        await Application.Current.MainPage.ShowPopupAsync(p);

        // PromptConfig firsttimeExercisePopup = new PromptConfig()
        // {
        //     InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
        //     IsCancellable = true,
        //     Title = "Update body weight",
        //     MaxLength = 7,


        //     Placeholder = "Tap to enter your weight",
        //     OkText = AppResources.Ok,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = async (weightResponse) =>
        //     {
        //         if (weightResponse.Ok)
        //         {
        //             try
        //             {
        //                 if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
        //                 {
        //                     return;
        //                 }
        //                 var weightText = weightResponse.Value.Replace(",", ".");
        //                 decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);

        //                 LocalDBManager.Instance.SetDBSetting("BodyWeight", new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit")?.Value).Kg.ToString().Replace(",", "."));
        //                 var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
        //                 var weights = new MultiUnityWeight(value, "kg");
        //                 App.IsWeightChangeFromOtherScreen = true;
        //                 App.IsMealPlanChange = false;
        //                 LocalDBManager.Instance.SetDBSetting("Macros", "");
        //                 var email = LocalDBManager.Instance.GetDBSetting("email")?.Value;
        //                 Preferences.Set($"Macros{email}", "");

        //                 await DrMuscleRestClient.Instance.SetUserBodyWeight(new UserInfosModel()
        //                 {
        //                     BodyWeight = new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit")?.Value)
        //                 });
        //                 if (Device.RuntimePlatform.Equals(Device.iOS))
        //                 {
        //                     IHealthData _healthService = DependencyService.Get<IHealthData>();
        //                     await _healthService.GetWeightPermissionAsync(async (r) =>
        //                     {
        //                         var a = r;
        //                         if (r)
        //                         {
        //                             _healthService.SetWeight(LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ? (double)Math.Round(weights.Kg, 2) : (double)Math.Round(weights.Lb, 2));
        //                         }
        //                     });
        //                 }
        //             }
        //             catch (Exception ex)
        //             {

        //             }

        //             LoadSavedWeights(); MessagingCenter.Send<BodyweightUpdateMessage>(new BodyweightUpdateMessage() { }, "BodyweightUpdateMessage");

        //             return;
        //         }

        //     }
        // };

        // firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
        // UserDialogs.Instance.Prompt(firsttimeExercisePopup);
    }
}
