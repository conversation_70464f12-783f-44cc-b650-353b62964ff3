<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.User.InboxPage"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout" 
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls" 
             xmlns:local="clr-namespace:DrMaxMuscle.Cells"
             Title="InboxPage">
    <CollectionView
        BackgroundColor="#f4f4f4" x:Name="lstView" SelectionMode="None">
        <CollectionView.ItemTemplate>
            <DataTemplate>
                <local:InboxCell>
                    <local:InboxCell.GestureRecognizers>
                        <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped"  CommandParameter="{Binding .}"/>
                    </local:InboxCell.GestureRecognizers>
                </local:InboxCell>
            </DataTemplate>
        </CollectionView.ItemTemplate>
    </CollectionView>
    <!--<controls:ExtendedListView BackgroundColor="#f4f4f4" x:Name="lstView" HasUnevenRows="true"
                           ItemAppearing="Handle_ItemAppearing" ItemDisappearing="Handle_ItemDisappearing">
        --><!--<controls:ExtendedListView.GroupHeaderTemplate>
        <DataTemplate>
            <local:HeaderCell />
        </DataTemplate>
    </controls:ExtendedListView.GroupHeaderTemplate>--><!--
        <controls:ExtendedListView.ItemTemplate>
            <DataTemplate>
                --><!--<ViewCell>
                    <StackLayout Spacing="0" Padding="16,4" BackgroundColor="Transparent" Orientation="Vertical">
                        <Label Margin="0,4,8,0" HeightRequest="20" VerticalOptions="Start" VerticalTextAlignment="Center" FontAttributes="Bold" TextColor="#514992" FontSize="15" LineBreakMode="TailTruncation" Text="{Binding Nickname}" />
                        <Label Margin="0,0,8,4" VerticalOptions="Start" VerticalTextAlignment="Start" TextColor="#943634" FontAttributes="Italic" FontSize="14" Text="{Binding Message}" />
                    </StackLayout>
                </ViewCell>--><!--
                <local:InboxCell />
            </DataTemplate>
        </controls:ExtendedListView.ItemTemplate>
    </controls:ExtendedListView>-->
</ContentPage>