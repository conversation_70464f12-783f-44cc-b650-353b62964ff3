<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.User.GroupChatPage"
             xmlns:local="clr-namespace:DrMaxMuscle.Cells"
xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             Title="GroupChatPage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <local:ChatDataTemplateSelector x:Key="MessageTemplateSelector">
            </local:ChatDataTemplateSelector>

        </ResourceDictionary>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid x:Name="ChatMainView" BackgroundColor="Transparent" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" RowSpacing="1" Padding="2,2,2,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="1" />
                <RowDefinition Height="auto" />
            </Grid.RowDefinitions>

            <StackLayout Grid.Row="0" Spacing="2">

                <CollectionView
                Rotation="180"
            BackgroundColor="Transparent"
            ItemTemplate="{StaticResource MessageTemplateSelector}"
            ItemsSource="{Binding messageList}"
            Margin="0"
            FlowDirection="RightToLeft"
            x:Name="lstChats"
            VerticalOptions="FillAndExpand"
            SelectionMode="None"
>

                </CollectionView>
                <!--<controls:ExtendedListView Rotation="180" BackgroundColor="Transparent" ItemTemplate="{StaticResource MessageTemplateSelector}" ItemsSource="{Binding messageList}" Margin="0" ItemTapped="OnListTapped" FlowDirection="RightToLeft" HasUnevenRows="True" x:Name="lstChats" VerticalOptions="StartAndExpand"
                 SeparatorColor="Transparent" ItemAppearing="Handle_ItemAppearing" ItemDisappearing="Handle_ItemDisappearing">
                </controls:ExtendedListView>-->

            </StackLayout>
            <!--            </ScrollView>-->
            <BoxView HorizontalOptions="FillAndExpand" HeightRequest="1" BackgroundColor="LightGray" Grid.Row="1" />
            <controls:ChatInputBarView Grid.Row="2" Margin="0,0,0,0" Tapped="BtnSendTapGestureRecognizer_Tapped" x:Name="chatInput" />
            <t:DrMuscleButton Grid.Row="2" x:Name="NotPurchased" BackgroundColor="Transparent" IsVisible="false" Clicked="NotPurchased_Clicked" />
        </Grid>
    </ContentPage.Content>
</ContentPage>