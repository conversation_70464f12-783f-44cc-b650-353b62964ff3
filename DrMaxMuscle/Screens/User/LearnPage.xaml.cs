using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Acr.UserDialogs;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Layout;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Utility;
using DrMuscleWebApiSharedModel;
using Microsoft.Maui.Networking;
namespace DrMaxMuscle.Screens.User;

public partial class LearnPage : ContentPage
{
    public ObservableCollection<BotModel> BotList = new ObservableCollection<BotModel>();
    public List<ReviewsModel> userReviewList = new List<ReviewsModel>();
    public List<ReviewsModel> expertReviewList = new List<ReviewsModel>();
    GetUserWorkoutLogAverageResponse workoutLogAverage;
    private GetUserProgramInfoResponseModel upi = null;
    bool isSetupRunnig = false;
    public LearnPage()
    {
        InitializeComponent();
        lstChats.ItemsSource = BotList;
        userReviewList = GetReviews();
        expertReviewList = GetExpertsReviews();
        MessagingCenter.Subscribe<Message.BodyweightUpdateMessage>(this, "BodyweightUpdateMessage", (obj) =>
        {
            StartSetup();
        });
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        Title = "Learn";
        StartSetup();

    }
    protected override bool OnBackButtonPressed()
    {
        return false;
        // uncomment code please
        //if (PopupNavigation.Instance.PopupStack.Count > 0)
        //{
        //    PopupNavigation.Instance.PopAllAsync();
        //    return true;
        //}
        //Device.BeginInvokeOnMainThread(async () =>
        //{
        //    ConfirmConfig exitPopUp = new ConfirmConfig()
        //    {
        //        Title = AppResources.Exit,
        //        Message = AppResources.AreYouSureYouWantToExit,
        //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //        OkText = AppResources.Yes,
        //        CancelText = AppResources.No,
        //    };

        //    var result = await UserDialogs.Instance.ConfirmAsync(exitPopUp);
        //    if (result)
        //    {
        //        var kill = DependencyService.Get<IKillAppService>();
        //        kill.ExitApp();
        //    }
        //});
        return true;
    }

    private async void StartSetup()
    {
        try
        {
            if (isSetupRunnig)
                return;
            isSetupRunnig = true;
            BotList.Clear();
            // BtnShare.IsVisible = false;
            if (LocalDBManager.Instance.GetDBSetting("firstname") == null)
                return;
            //var welcomeNote = "";
            string fname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
            //Title = $"Welcome back {fname}!";
            int lowReps = 0;
            int highreps = 0;
            try
            {
                lowReps = int.Parse(LocalDBManager.Instance.GetDBSetting("repsminimum")?.Value);
                highreps = int.Parse(LocalDBManager.Instance.GetDBSetting("repsmaximum")?.Value);
            }
            catch (Exception)
            {

            }
            var result = "";
            if (lowReps >= 5 && highreps <= 12)
                result = "This helps you build muscle and strength.";
            else if (lowReps >= 8 && highreps <= 15)
                result = "This helps you build muscle and burn fat.";
            else if (lowReps >= 5 && highreps <= 15)
                result = "This helps you build muscle.";
            else if (lowReps >= 12 && highreps <= 20)
                result = "This helps you burn fat.";
            else if (highreps >= 16)
                result = "This helps you build muscle and burn fat.";
            else
            {
                if (LocalDBManager.Instance.GetDBSetting("Demoreprange") != null)
                {
                    if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "BuildMuscle")
                    {
                        result = "This helps you build muscle.";
                    }
                    else if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "BuildMuscleBurnFat")
                    {
                        result = "This helps you build muscle and burn fat.";
                    }
                    else if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "FatBurning")
                    {
                        result = "This helps you burn fat.";
                    }
                }
            }
            BotList.Add(new BotModel()
            {
                Type = BotType.Ques,
                Question = $"{fname}, you're doing {lowReps}-{highreps} reps. {result}"
            });

            var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;


            try
            {

                if (workouts.Sets != null)
                {
                    workoutLogAverage = workouts;
                }
                else
                {
                    workoutLogAverage = null;
                    await FetchMainData();
                    workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
                }
                if (workouts.GetUserProgramInfoResponseModel != null)
                {
                    upi = workouts.GetUserProgramInfoResponseModel;
                    if (upi.RecommendedProgram != null && upi.NextWorkoutTemplate != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("remain", upi.RecommendedProgram.RemainingToLevelUp.ToString());
                        var programText = "";
                        if (!string.IsNullOrEmpty(upi.RecommendedProgram.Label))
                            programText = $"You're on the {upi.RecommendedProgram.Label} program. ";

                        if (upi.NextWorkoutTemplate.IsSystemExercise)
                        {
                            try
                            {

                                if (workoutLogAverage.HistoryExerciseModel != null)
                                {
                                    programText += $"That's based on your past experience";
                                    if (workoutLogAverage.HistoryExerciseModel.TotalWorkoutCompleted > 0)
                                    {
                                        var w = workoutLogAverage.HistoryExerciseModel.TotalWorkoutCompleted <= 1 ? "workout" : "workouts";
                                        programText += $" and {workoutLogAverage.HistoryExerciseModel.TotalWorkoutCompleted} {w} recorded.";
                                    }

                                }

                            }
                            catch (Exception ex)
                            {

                            }
                            BotList.Add(
                            new BotModel()
                            {
                                Question = programText,
                                Type = BotType.Ques
                            });

                            //Current program
                            var programName = workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label;
                            var xDays = 0;
                            //If Default and Age
                            int age = 40;
                            bool isApproxAge = true;
                            if (LocalDBManager.Instance.GetDBSetting("Age") != null && LocalDBManager.Instance.GetDBSetting("Age").Value != null)
                            {
                                age = int.Parse(LocalDBManager.Instance.GetDBSetting("Age").Value);
                            }
                            if ((programName.ToLower().Contains("push/pull/legs") && age < 51) || programName.ToLower().Contains("muscle split"))
                            {
                                xDays = 6;
                            }
                            else if (programName.ToLower().Contains("split"))
                            {
                                if (age < 30)
                                    xDays = 4;
                                else if (age >= 30 && age <= 50)
                                    xDays = 4;
                                else
                                    xDays = 3;
                            }
                            else if (programName.ToLower().Contains("bodyweight") ||
programName.ToLower().Contains("mobility") || programName.ToLower().Contains("full-body") || programName.ToLower().Contains("bands") || programName.ToLower().Contains("powerlifting") || programName.ToLower().Contains("abs, legs & glutes"))
                            {
                                if (age < 30)
                                    xDays = 4;
                                else if (age >= 30 && age <= 50)
                                    xDays = 3;
                                else
                                    xDays = 2;
                            }
                            var currentProgram = $"Your program has {xDays} workout days with {upi.NextWorkoutTemplate.Exercises.Count} exercises each. That's optimal for your goals. Learn more:";
                            BotList.Add(new BotModel()
                            {
                                Question = currentProgram,
                                Type = BotType.Ques
                            });

                            BotList.Add(new BotModel()
                            {
                                Question = "https://dr-muscle.com/change-routine-exercises/",
                                Type = BotType.Link
                            });

                            BotList.Add(new BotModel()
                            {
                                Question = "Want more exercises? Change to a higher program level, an Up/low split, or a Push/pull/legs program. To change, simply tag Settings (bottom bar).",
                                Type = BotType.Link
                            });

                            BotList.Add(new BotModel()
                            {
                                Question = "Generally, you should do as many reps as you can with good form and common sense. If that's more than the app recommends, great. Enter what you did, and the app will adjust in 1 workout.",
                                Type = BotType.Link
                            });

                            BotList.Add(new BotModel()
                            {
                                Question = "Tap \"...\" and Challenge next to any exercise to speed that up.",
                                Type = BotType.Link
                            });


                            var features = "";
                            if (upi.RecommendedProgram.Level == null)
                            {
                                try
                                {

                                    var lvl = upi.RecommendedProgram.Level;
                                    if (lvl == null)
                                    {
                                        lvl = 1;
                                    }
                                    upi.RecommendedProgram.Level = int.Parse(lvl + "");

                                }
                                catch (Exception ex)
                                {

                                }
                            }

                            if (upi.RecommendedProgram.Label.ToLower().Contains("bodyweight") && upi.RecommendedProgram.Level == 1 || (upi.RecommendedProgram.Label.ToLower().Contains("bands") && upi.RecommendedProgram.Level == 1))
                            {
                                features = "";
                            }
                            else if (upi.RecommendedProgram.Label.ToLower().Contains("bodyweight") && upi.RecommendedProgram.Level == 2)
                            {
                                features = "Your program now includes new and harder exercises.";
                            }
                            if (upi.RecommendedProgram.Label.ToLower().Contains("bodyweight") && upi.RecommendedProgram.Level == 3)
                            {
                                features = "Your program now includes new and harder exercises.";
                            }
                            if (upi.RecommendedProgram.Label.ToLower().Contains("bodyweight") && upi.RecommendedProgram.Level == 4)
                            {
                                features = "Your program now includes new and harder exercises.";
                            }
                            else if (upi.RecommendedProgram.Level == 1 && !upi.RecommendedProgram.Label.ToLower().Contains("bodyweight"))
                            {
                                features = "Your program includes simple and effective exercises. Learn more: https://dr-muscle.com/change-routine-exercises/.";
                            }
                            else if (upi.RecommendedProgram.Level == 2 && !upi.RecommendedProgram.Label.ToLower().Contains("bodyweight") || (upi.RecommendedProgram.Label.ToLower().Contains("bands") && upi.RecommendedProgram.Level == 2))
                            {
                                features = "Your program now includes A/B workouts with new exercises in rotation. Learn more at https://dr-muscle.com/build-muscle-faster/#3.";
                            }
                            if (upi.RecommendedProgram.Level == 3 && !upi.RecommendedProgram.Label.ToLower().Contains("bodyweight"))
                            {
                                features = "Your program now includes A/B/C workouts with new exercises in rotation. Learn more at https://dr-muscle.com/build-muscle-faster/#3.";
                            }
                            if (upi.RecommendedProgram.Level == 4 && !upi.RecommendedProgram.Label.ToLower().Contains("bodyweight"))
                            {
                                features = "Your program now includes easy workouts to help you recover. Learn more at http://dr-muscle.com/easy.";
                            }
                            if (upi.RecommendedProgram.Level == 5)
                            {
                                features = "Your program now includes A/B easy workouts to help you recover. Learn more at http://dr-muscle.com/easy.";
                            }
                            if (upi.RecommendedProgram.Level == 6)
                            {
                                features = "Your program now includes A/B/C easy workouts to help you recover. Learn more at http://dr-muscle.com/easy.";
                            }

                            if (upi.RecommendedProgram.Level == 7)
                            {
                                features = "Your program now includes A/B/C medium workouts to prep you for new records on your normal workouts. Learn more at http://dr-muscle.com/easy.";
                            }
                            if (!string.IsNullOrEmpty(features))
                                BotList.Add(new BotModel()
                                {
                                    Question = features,
                                    Type = BotType.Link
                                });





                            var ageText = "";


                            if (age < 30)
                            {
                                ageText = " Your recovery is great.";
                            }
                            else if (age >= 30 && age <= 50)
                                ageText = " Your recovery is a bit slower.";
                            else
                                ageText = " Your recovery is slowing down.";

                            //On this program, you should work out [if (full-body), then "2-4x / week"] [if (split body), then "3-5x / week"]. You're [age], so a good starting point is to work out [if (50+), then "lower bound of the range"] [if (30-50), then "middle of the range"] and if (under 30), then "higher bound of the range"]
                            string daysInterval = "";
                            if (xDays == 2)
                                daysInterval = "2-3";
                            else
                                daysInterval = "1-2";
                            //    var daysName = "";
                            //    if (xDays == 2)
                            //    {
                            //        daysName = "Monday, Thursday or Tuesday, Friday or Wednesday, Saturday or Wednesday, Sunday";
                            //    }
                            //    else if (xDays == 3)
                            //        daysName = "Monday, Wednesday, Friday or  Monday, Wednesday, Saturday";
                            //    else if (xDays == 4)
                            //        daysName = "Monday, Tuesday, Thursday, Friday";
                            //else if (xDays == 6)
                            //    daysName = "Monday, Tuesday, Wednesday, Friday, Saturday, Sunday";
                            //else
                            //        daysName = "Monday, Tuesday, Wednesday, Thursday, Friday";

                            var text = $"You're {age} years old.{ageText} So, on this program, train {xDays} days a week for best results. Rest {daysInterval} days between workouts. Like this:";

                            if (!isApproxAge)
                            {
                                BotList.Add(new BotModel()
                                {
                                    Question = text,
                                    Type = BotType.Ques
                                });
                            }
                            else
                            {
                                BotList.Add(new BotModel()
                                {
                                    Type = BotType.AttributedLabelLink,
                                    Question = text,
                                    Part1 = $"You're {age} years old (",
                                    Part2 = "update your age",
                                    Part3 = $").{ageText} So, on this program, train {xDays} days a week for best results. Rest {daysInterval} days between workouts. Like this:"
                                });
                            }
                            if (xDays != 0)
                            {
                                //
                                if (xDays == 2)
                                {
                                    BotList.Add(new BotModel()
                                    {
                                        Question = $"Mon: workout 1\nTues: off\nWed: off\nThurs: workout 2\nFri: off\nSat: off\nSun: off",
                                        Type = BotType.LearnDay,
                                        Part2 = "Customize your workout days"
                                    });
                                }
                                if (xDays == 3)
                                {
                                    BotList.Add(new BotModel()
                                    {
                                        Question = $"Mon: workout 1\nTues: off\nWed: workout 2\nThurs: off\nFri: workout 3\nSat: off\nSun: off",
                                        Type = BotType.LearnDay,
                                        Part2 = "Customize your workout days"
                                    });
                                }
                                if (xDays == 4)
                                {
                                    BotList.Add(new BotModel()
                                    {
                                        Question = $"Mon: workout 1\nTues: workout 2\nWed: off\nThurs: workout 3\nFri: workout 4\nSat: off\nSun: off",
                                        Type = BotType.LearnDay,
                                        Part2 = "Customize your workout days"
                                    });
                                }
                                if (xDays == 5)
                                {
                                    BotList.Add(new BotModel()
                                    {
                                        Question = $"Mon: workout 1\nTues: workout 2\nWed: workout 3\nThurs: workout 4\nFri: workout 5\nSat: off\nSun: off",
                                        Type = BotType.LearnDay,
                                        Part2 = "Customize your workout days"
                                    });
                                }
                                if (xDays == 6)
                                {
                                    BotList.Add(new BotModel()
                                    {
                                        Question = $"Mon: workout 1\nTues: workout 2\nWed: workout 3\nThurs: off\nFri: workout 4\nSat: workout 5\nSun: workout 6",
                                        Type = BotType.LearnDay,
                                        Part2 = "Customize your workout days"
                                    });
                                }
                            }
                        }
                        else
                        {
                            programText += $"This is a custom program.";
                            BotList.Add(
                            new BotModel()
                            {
                                Question = programText,
                                Type = BotType.Ques
                            });
                        }
                    }

                }

                bool? isProgress = null;
                if (workouts != null)
                {
                    if (workouts.Sets != null)
                    {
                        if (workouts.Averages.Count > 1)
                        {
                            OneRMAverage last = workouts.Averages.ToList()[workouts.Averages.Count - 1];
                            OneRMAverage before = workouts.Averages.ToList()[workouts.Averages.Count - 2];
                            decimal progresskg = (last.Average.Kg - before.Average.Kg) * 100 / (before.Average.Kg < 1 ? 1 : before.Average.Kg);

                            bool inKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg";

                            string progress = "";
                            string worksets = "";
                            if ((last.Average.Kg - before.Average.Kg) >= 0)
                            {
                                progress = String.Format("{0}{1}%", (last.Average.Kg - before.Average.Kg) >= 0 ? "+" : "", Math.Round(progresskg)).ReplaceWithDot();
                                isProgress = true;
                            }
                            else
                            {
                                progress = String.Format("{0}{1}%", (last.Average.Kg - before.Average.Kg) >= 0 ? "+" : "", Math.Round(progresskg)).ReplaceWithDot();
                                isProgress = false;

                            }
                            //statsModel.StrengthMessage = AppResources.MaxStrength;
                            workouts.Sets.Reverse();
                            workouts.SetsDate.Reverse();

                            if (workouts.Sets.Count > 1)
                            {

                                int firstSets = workouts.Sets[workouts.Sets.Count - 1];
                                int lastSets = workouts.Sets[workouts.Sets.Count - 2];
                                try
                                {
                                    decimal progressSets = (firstSets - lastSets) * 100 / (lastSets == 0 ? 1 : lastSets);
                                    // strProgress += String.Format("- {0}: {1}{2} ({3}%)\n", AppResources.WorkSetsNoColon, (firstSets - lastSets) >= 0 ? "+" : "", firstSets - lastSets, Math.Round(progressSets)).ReplaceWithDot();
                                    bool isWorksetProgress = false;
                                    if (lastSets == 0)
                                        progressSets = firstSets;
                                    if ((firstSets - lastSets) >= 0)
                                    {
                                        isWorksetProgress = true;
                                        worksets = String.Format("{0}{1}{2}", (firstSets - lastSets) >= 0 ? "+" : "", Math.Round(progressSets), lastSets == 0 ? "" : "%").ReplaceWithDot();
                                    }
                                    else
                                    {
                                        isWorksetProgress = false;
                                        worksets = String.Format("{0}{1}{2}", (firstSets - lastSets) >= 0 ? "+" : "", Math.Round(progressSets), lastSets == 0 ? "" : "%").ReplaceWithDot();
                                    }
                                    BotList.Add(new BotModel()
                                    {
                                        Question = $"Last 3 weeks, your strength went {progress} (on average) and your work sets went {worksets}.",//string.Format("Last 3 weeks, your strength has gone {0} {1} (on average) and your work sets have gone {2} {3}", isProgress == true ? "up" : "down", progress, isWorksetProgress ? "up" : "down", worksets),
                                        Type = BotType.Ques
                                    });
                                }
                                catch (Exception ex)
                                {
                                }
                            }

                            workouts.Sets.Reverse();
                            workouts.SetsDate.Reverse();

                        }
                        else
                        {

                        }

                    }
                }
                var adjusttext = "";
                if (isProgress == null)
                {
                    adjusttext = "You should adjust. If you progress fast and feel fresh, you can add a weekly session. If you progress slowly, try removing a weekly session.";

                }
                else if (isProgress == true)
                {
                    adjusttext = "So if you feel fresh, you can add a workout this week.";
                }
                else
                {
                    adjusttext = "So if you feel tired, you can skip a workout this week.";
                }
                BotList.Add(new BotModel()
                {
                    Question = adjusttext,
                    Type = BotType.Ques
                });


                if (workouts.GetUserProgramInfoResponseModel != null)
                {
                    upi = workouts.GetUserProgramInfoResponseModel;
                    if (upi.RecommendedProgram != null && upi.NextWorkoutTemplate != null)
                    {
                        var newFeatures = "";
                        if (upi.NextWorkoutTemplate.IsSystemExercise)
                        {
                            if (upi.RecommendedProgram.Label.ToLower().Contains("bodyweight") && upi.RecommendedProgram.Level + 1 == 2)
                            {
                                newFeatures = "You will get new and harder exercises. Learn more at https://dr-muscle.com/building-muscle";
                            }
                            if (upi.RecommendedProgram.Label.ToLower().Contains("bodyweight") && upi.RecommendedProgram.Level + 1 == 3)
                            {
                                newFeatures = "You will get new and harder exercises.Learn more at https://dr-muscle.com/building-muscle";
                            }
                            if (upi.RecommendedProgram.Label.ToLower().Contains("bodyweight") && upi.RecommendedProgram.Level + 1 == 4)
                            {
                                newFeatures = "You will get new and harder exercises. Learn more at https://dr-muscle.com/building-muscle";
                            }
                            else if (upi.RecommendedProgram.Level + 1 == 2 && !upi.RecommendedProgram.Label.ToLower().Contains("bodyweight"))
                            {
                                newFeatures = "You will get A/B workouts with new exercises in rotation. Learn more at https://dr-muscle.com/build-muscle-faster/#3";
                            }
                            if (upi.RecommendedProgram.Level + 1 == 3 && !upi.RecommendedProgram.Label.ToLower().Contains("bodyweight"))
                            {
                                newFeatures = "You will get A/B/C workouts with new exercises in rotation. Learn more at https://dr-muscle.com/build-muscle-faster/#3";
                            }
                            if (upi.RecommendedProgram.Level + 1 == 4 && !upi.RecommendedProgram.Label.ToLower().Contains("bodyweight"))
                            {
                                newFeatures = "You will get easy workouts to help you recover. Learn more at http://dr-muscle.com/easy";
                            }
                            if (upi.RecommendedProgram.Level + 1 == 5)
                            {
                                newFeatures = "You will get A/B easy workouts to help you recover. Learn more at http://dr-muscle.com/easy";
                            }
                            if (upi.RecommendedProgram.Level + 1 == 6)
                            {
                                newFeatures = "You will get A/B/C easy workouts to help you recover. Learn more at http://dr-muscle.com/easy";
                            }

                            if (upi.RecommendedProgram.Level + 1 == 7)
                            {
                                newFeatures = "You will get A/B/C medium workouts to prep you for new records on your normal workouts. Learn more at http://dr-muscle.com/easy";
                            }
                            BotList.Add(new BotModel()
                            {
                                Question = $"Your program will level up in {upi.RecommendedProgram.RemainingToLevelUp} workouts. {newFeatures}.",
                                Type = BotType.Link
                            });
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                await FetchMainData();
                workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
            }

            //BotList.Add(new BotModel()
            //{
            //    Question = "If you enjoyed that experience�share a free month?",
            //    Type = BotType.Ques
            //});
            //BtnShare.IsVisible = false;
            Device.BeginInvokeOnMainThread(() =>
            {
                lstChats.ScrollTo(BotList.First(), ScrollToPosition.Start, animate:false);
            });
            //
            decimal weight1 = 0;
            string link = "https://www.eve.fit/";
            if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
            {
                weight1 = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture);
                //grams = Math.Round(weight1 * (decimal)2.4);
            }

            BotList.Add(new BotModel()
            {
                Question = "Learn more about your smart, custom program.",
                Type = BotType.LinkGesture
            });
            if (result.Contains("build muscle and burn fat"))
            {
                BotList.Add(new BotModel()
                {
                    Type = BotType.Ques,
                    Question = $"With this program, you can build muscle and burn fat fast. To support that, your nutrition is important. You should eat just enough calories, and lots of protein."
                });

                BotList.Add(new BotModel()
                {
                    Type = BotType.Ques,
                    Question = $"To know if you're eating just enough calories, track your waist circumference. You can measure it with a tape, or just see how your jeans fit. Check your fit every week. Your jeans should be easier to get in while your weight stays more or less the same. This means you're burning fat, and building muscle. If you're gaining weight, eat a bit less. If you're losing weight, eat more."
                });
                //https://app.startinfinity.com/b/7N8FXx54wWE/sDxhY6Rxbjh/156cff09-d364-4130-9f38-0991b672e66e?view=c956a7e0-0553-42e6-af24-7b6c915d3c44&t=attributes
                var protein = Math.Round(new MultiUnityWeight((decimal)weight1, "kg").Kg * (decimal)1.8) + "-" + Math.Round(new MultiUnityWeight((decimal)weight1, "kg").Kg * (decimal)2.5);

                BotList.Add(new BotModel()
                {
                    Type = BotType.AttributedLabelLink,
                    Question = $"Now, protein. For this one, aim for {protein} g a day. Good sources are beef, chicken, fish, and eggs. Vegetarian options include tofu, beans, and nuts. Fatty fish (e.g. salmon) and tofu are especially good for health, with their omega-3s.",
                    Part1 = $"Now, protein. For this one, aim for {protein} g a day.",
                    Part2 = "",
                    Part3 = " Good sources are beef, chicken, fish, and eggs. Vegetarian options include tofu, beans, and nuts. Fatty fish (e.g. salmon) and tofu are especially good for health, with their omega-3s."
                });


                //BotList.Add(new BotModel()
                //{
                //    Type = BotType.AttributedLabelLink,
                //    Question = $"For more help with your nutrition, check out our smart diet coach. It integrates with Dr. Muscle to adjust your diet automatically.",
                //    Part1 = "For more help with your nutrition, check out our ",
                //    Part2 = "smart diet coach",
                //    Part3 = ". It integrates with Dr. Muscle to adjust your diet automatically."

                //});
            }
            else if (result.Contains("build muscle"))
            {
                BotList.Add(new BotModel()
                {
                    Type = BotType.Ques,
                    Question = $"With this program, you can build muscle fast. To support that, your nutrition is important. You should eat lots of calories and protein."
                });

                bool IsKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg";
                decimal weeks = 0;
                string Gender = LocalDBManager.Instance.GetDBSetting("gender").Value.Trim();
                var creationDate = new DateTime(Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("creation_date").Value));
                if (creationDate != null)
                {
                    weeks = (int)(DateTime.Now - creationDate).TotalDays / 7;
                }
                decimal gainDouble = 0;
                if (Gender == "Man")
                {
                    if (weeks <= 18)
                        gainDouble = ((decimal)0.015 - (decimal)0.000096899 * weeks) * weight1;
                    else if (weeks > 18 && weeks <= 42)
                        gainDouble = ((decimal)0.011101 - (decimal)0.000053368 * weeks) * weight1;
                    else if (weeks > 42)
                        gainDouble = (decimal)0.00188 * weight1;
                }
                else
                {
                    if (weeks <= 18)
                        gainDouble = (((decimal)0.015 - (decimal)0.000096899 * weeks) * weight1) / 2;
                    else if (weeks > 18 && weeks <= 42)
                        gainDouble = (((decimal)0.011101 - (decimal)0.000053368 * weeks) * weight1) / 2;
                    else if (weeks > 42)
                        gainDouble = ((decimal)0.00188 * weight1) / 2;
                }
                string gain = IsKg ? $"{Math.Round(gainDouble, 2)} kg" : $"{Math.Round(new MultiUnityWeight(gainDouble, WeightUnities.kg).Lb, 2)} lbs";
                var weekText = weeks <= 1 ? "week" : "weeks";
                BotList.Add(new BotModel()
                {
                    Type = BotType.Ques,
                    Question = $"To know if you're eating enough calories, track your weight. Since you have been working out with the app for {weeks} {weekText}, I estimate you should gain about {gain} a month. If you're gaining less than that, you're leaving muscle on the table. Eat more. If you're gaining more than that, you probably gaining fat. Eat less."
                });
                //https://app.startinfinity.com/b/7N8FXx54wWE/sDxhY6Rxbjh/156cff09-d364-4130-9f38-0991b672e66e?view=c956a7e0-0553-42e6-af24-7b6c915d3c44&t=attributes
                var protein = Math.Round(new MultiUnityWeight((decimal)weight1, "kg").Kg * (decimal)1.8) + "-" + Math.Round(new MultiUnityWeight((decimal)weight1, "kg").Kg * (decimal)2.5);

                BotList.Add(new BotModel()
                {
                    Type = BotType.AttributedLabelLink,
                    Question = $"Now, protein. For this one, aim for {protein} g a day. Good sources are beef, chicken, fish, and eggs. Vegetarian options include tofu, beans, and nuts. Fatty fish (e.g. salmon) and tofu are especially good for health.",
                    Part1 = $"Now, protein. For this one, aim for {protein} g a day.",
                    Part2 = "",
                    Part3 = " Good sources are beef, chicken, fish, and eggs. Vegetarian options include tofu, beans, and nuts. Fatty fish (e.g. salmon) and tofu are especially good for health, with their omega-3s."
                });


                BotList.Add(new BotModel()
                {
                    Type = BotType.AnchorLink,
                    Question = $"For more help with your nutrition, check out our [smart diet coach app Eve]({link}). It integrates with Dr. Muscle to adjust your diet automatically."
                });
                //BotList.Add(new BotModel()
                //{
                //    Type = BotType.AttributedLabelLink,
                //    Question = $"For more help with your nutrition, check out our smart diet coach. It integrates with Dr. Muscle to adjust your diet automatically.",
                //    Part1 = "For more help with your nutrition, check out our ",
                //    Part2 = "smart diet coach",
                //    Part3 = ". It integrates with Dr. Muscle to adjust your diet automatically."

                //});
            }
            else if (result.Contains("burn fat"))
            {
                BotList.Add(new BotModel()
                {
                    Type = BotType.Ques,
                    Question = $"With this program, you can burn fat fast. But to make sure you really burn fat, your nutrition is key. You should eat less calories, and enough protein."
                });
                BotList.Add(new BotModel()
                {
                    Type = BotType.Ques,
                    Question = $"To know if you're eating just enough calories, track your weight. You should lose 1-2 lbs a month. If you're losing more than that, chances are, you're also losing muscle. Eat a bit more. If you're losing less than that, you could probably lose faster safely. Eat less."
                });
                //https://app.startinfinity.com/b/7N8FXx54wWE/sDxhY6Rxbjh/156cff09-d364-4130-9f38-0991b672e66e?view=c956a7e0-0553-42e6-af24-7b6c915d3c44&t=attributes
                var protein = Math.Round(new MultiUnityWeight((decimal)weight1, "kg").Kg * (decimal)1.8) + "-" + Math.Round(new MultiUnityWeight((decimal)weight1, "kg").Kg * (decimal)2.5); ;



                BotList.Add(new BotModel()
                {
                    Type = BotType.AttributedLabelLink,
                    Question = $"Now, protein. For this one, aim for {protein} g a day. Good sources are beef, chicken, fish, and eggs. Vegetarian options include tofu, beans, and nuts. Fatty fish (e.g. salmon) and tofu are especially good for health, with their omega-3s.",
                    Part1 = $"Now, protein. For this one, aim for {protein} g a day.",
                    Part2 = "",
                    Part3 = " Good sources are beef, chicken, fish, and eggs. Vegetarian options include tofu, beans, and nuts. Fatty fish (e.g. salmon) and tofu are especially good for health, with their omega-3s."
                });
                BotList.Add(new BotModel()
                {
                    Type = BotType.AnchorLink,
                    Question = $"For more help with your nutrition, check out our [smart diet coach app Eve]({link}). It integrates with Dr. Muscle to adjust your diet automatically."
                });
                //BotList.Add(new BotModel()
                //{
                //    Type = BotType.AttributedLabelLink,
                //    Question = $"For more help with your nutrition, check out our smart diet coach. It integrates with Dr. Muscle to adjust your diet automatically.",
                //    Part1 = "For more help with your nutrition, check out our ",
                //    Part2 = "smart diet coach",
                //    Part3 = ". It integrates with Dr. Muscle to adjust your diet automatically."

                //});

            }
        }
        catch (Exception ex)
        {

        }
        finally
        {
            isSetupRunnig = false;
        }
        try
        {

            if (CurrentLog.Instance.WalkThroughCustomTipsPopup)
            {
                App.IsLearnPopup = false;
                CurrentLog.Instance.WalkThroughCustomTipsPopup = false;

                // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Title = "Get custom tips",
                //     Message = $"Learn about your program and get custom tips on the Learn tab",
                //     OkText = AppResources.GotIt
                // });

                await HelperClass.DisplayCustomPopupForResult("Get custom tips",
                      $"Learn about your program and get custom tips on the Learn tab","OK","");
                //await PagesFactory.PopAsync();
                ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[0];

                return;
            }

        }
        catch (Exception ex)
        {

        }


        // var rndm = new Random();
        //            var review = userReviewList.ElementAt(rndm.Next(0, 9));
        //var expertReview = expertReviewList.ElementAt(rndm.Next(0, 9));

        //BotList.Add(new BotModel()
        //{
        //    Part1 = "User reviews",
        //    Part2 = review.Review,
        //    Part3 = review.ReviewerName,
        //    Type = BotType.Review
        //});


        //BotList.Add(new BotModel()
        //{
        //    Part1 = "Expert reviews",
        //    Part2 = expertReview.Review,
        //    Part3 = expertReview.ReviewerName,
        //    Type = BotType.Review
        //});
    }

    public async Task FetchMainData()
    {
        try
        {
            if (Connectivity.NetworkAccess == NetworkAccess.Internet)
            {
                TimeZoneInfo local = TimeZoneInfo.Local;
                if (workoutLogAverage != null)
                {
                    var userProgram = await DrMuscleRestClient.Instance.GetUserWorkoutProgramTimeZoneInfoWithoutLoader(local);
                    if (userProgram != null)
                    {

                        workoutLogAverage.GetUserProgramInfoResponseModel = userProgram.GetUserProgramInfoResponseModel;
                        workoutLogAverage.LastWorkoutDate = userProgram.LastWorkoutDate;
                        workoutLogAverage.LastConsecutiveWorkoutDays = userProgram.LastConsecutiveWorkoutDays;
                    }
                }
                else
                    workoutLogAverage = await DrMuscleRestClient.Instance.GetUserWorkoutProgramTimeZoneInfo(local);
                if (LocalDBManager.Instance.GetDBSetting("email") == null)
                    return;
                if (workoutLogAverage != null)
                {
                    if (workoutLogAverage.GetUserProgramInfoResponseModel != null)
                    {
                        upi = workoutLogAverage.GetUserProgramInfoResponseModel;
                        if (upi.RecommendedProgram != null && upi.NextWorkoutTemplate != null)
                        {
                            LocalDBManager.Instance.SetDBSetting("remain", upi.RecommendedProgram.RemainingToLevelUp.ToString());
                        }

                    }
                    ((App)Application.Current).UserWorkoutContexts.workouts = workoutLogAverage;

                    ((App)Application.Current).UserWorkoutContexts.SaveContexts();

                    if (workoutLogAverage != null && workoutLogAverage.GetUserProgramInfoResponseModel != null)
                    {
                        if (workoutLogAverage.GetUserProgramInfoResponseModel.RecommendedProgram == null && workoutLogAverage.GetUserProgramInfoResponseModel.NextWorkoutTemplate == null)
                        {
                            if (LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId") != null &&
                                    LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel") != null &&
                                    LocalDBManager.Instance.GetDBSetting("recommendedProgramId") != null &&
                                    LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel") != null &&
                                    LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout") != null)
                            {
                                try
                                {
                                    long workoutTemplateId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId").Value);
                                    long programId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedProgramId").Value);
                                    upi = new GetUserProgramInfoResponseModel()
                                    {
                                        NextWorkoutTemplate = new WorkoutTemplateModel() { Id = workoutTemplateId, Label = LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel").Value, IsSystemExercise = true },
                                        RecommendedProgram = new WorkoutTemplateGroupModel() { Id = programId, Label = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel").Value, RemainingToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value), RequiredWorkoutToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value) },
                                    };
                                    workoutLogAverage.GetUserProgramInfoResponseModel = upi;
                                    ((App)Application.Current).UserWorkoutContexts.workouts = workoutLogAverage;
                                    ((App)Application.Current).UserWorkoutContexts.SaveContexts();
                                    //lblProgram.Text = $"{AppResources.Program}: {upi.RecommendedProgram.Label}";
                                    //lblWorkout.Text = $"{AppResources.UpNext}: {upi.NextWorkoutTemplate.Label}";
                                    //WorkoutNowbutton.Text = $"{AppResources.StartCapitalized} {upi.NextWorkoutTemplate.Label}";
                                    LocalDBManager.Instance.SetDBSetting("remain", upi.RecommendedProgram.RemainingToLevelUp.ToString());
                                }
                                catch (Exception ex)
                                {

                                }

                            }
                        }
                    }
                }
                if (!App.IsV1User)
                {
                    CanGoFurtherWithoughtLoader();
                }

                //WorkoutLogSets();
            }


        }
        catch (Exception ex)
        {

        }
    }
    public async Task<bool> CanGoFurtherWithoughtLoader()
    {
        var _drMuscleSubcription = (IDrMuscleSubcription)MauiProgram.ServiceProvider.GetService(typeof(IDrMuscleSubcription));
        if (LocalDBManager.Instance.GetDBSetting("creation_date") == null)
            return false;
        if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("creation_date").Value))
        {
            DateTime setDate = DateTime.Now.ToUniversalTime();
            LocalDBManager.Instance.SetDBSetting("creation_date", setDate.Ticks.ToString());
            DrMuscleRestClient.Instance.SetUserCreationDate(setDate);
        }
        try
        {
            DateTime creationDate = new DateTime(Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("creation_date").Value));
            if ((DateTime.Now.ToUniversalTime() - creationDate).TotalDays < 14)
            {

                App.IsV1UserTrial = true;
                App.IsFreePlan = false;
                LocalDBManager.Instance.SetDBSetting("IsPurchased", "true");
                // Uncomment code please
                //SetTrialUserNotifications();
            }
        }
        catch (Exception ex)
        {

        }
        try
        {


            BooleanModel isV1User = await DrMuscleRestClient.Instance.IsV1UserWithoutLoaderQuick();

            if (isV1User != null)
            {

                bool isTrail = false;
                DateTime creationDate = new DateTime(Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("creation_date").Value));
                App.IsMealPlan = isV1User.IsMealPlan;
                LocalDBManager.Instance.SetDBSetting("IsMealPlanPurchased", isV1User.IsMealPlan ? "true" : "false");
                if ((DateTime.Now.ToUniversalTime() - creationDate).TotalDays < 14)
                {
                    isTrail = true;
                    App.IsV1UserTrial = true;
                    App.IsMealPlan = isV1User.IsMealPlan;
                }
                else if (!isV1User.IsTraining)
                {
                    App.IsFreePlan = true;
                    App.IsV1UserTrial = false;
                    App.IsV1User = false;
                    App.IsTraining = isV1User.IsTraining;
                    App.IsMealPlan = isV1User.IsMealPlan;

                    LocalDBManager.Instance.SetDBSetting("IsPurchased", "false");
                }
                if (isV1User.Result)
                {
                    App.IsV1UserTrial = isV1User.IsTraining;
                    //App.IsV1User = isV1User.IsTraining;
                    App.IsV1User = isV1User.IsTraining;
                    App.IsMealPlan = isV1User.IsMealPlan;
                    App.IsFreePlan = !isV1User.IsTraining;

                    LocalDBManager.Instance.SetDBSetting("IsPurchased", "true");
                    return true;
                }

                if (_drMuscleSubcription.IsActiveSubscriptions())
                {
                    App.IsV1UserTrial = true;
                    App.IsV1User = true;
                    App.IsFreePlan = false;
                    App.IsTraining = true;
                    LocalDBManager.Instance.SetDBSetting("IsPurchased", "true");
                    if (Device.RuntimePlatform.Equals(Device.Android) && _drMuscleSubcription.IsActiveMealPlan())
                        App.IsMealPlan = true;
                    return true;
                }
                if (Device.RuntimePlatform.Equals(Device.Android) && _drMuscleSubcription.IsActiveMealPlan())
                {
                    App.IsMealPlan = true;
                    LocalDBManager.Instance.SetDBSetting("IsMealPlanPurchased", "true");
                }

                if (!isTrail)
                {
                    LocalDBManager.Instance.SetDBSetting("IsPurchased", "false");
                    App.IsV1UserTrial = false;
                    App.IsFreePlan = true;
                }
            }

        }
        catch (Exception ex)
        {

        }
        finally
        {
            if (_drMuscleSubcription.IsActiveSubscriptions())
            {
                App.IsV1UserTrial = true;
                App.IsV1User = true;
                App.IsFreePlan = false;
                App.IsTraining = true;
                LocalDBManager.Instance.SetDBSetting("IsPurchased", "true");
                if (_drMuscleSubcription.IsActiveMealPlan())
                    App.IsMealPlan = true;
            }
            if (_drMuscleSubcription.IsActiveMealPlan())
            {
                App.IsMealPlan = true;
                LocalDBManager.Instance.SetDBSetting("IsMealPlanPurchased", "true");
            }
        }
        return false;
    }
    private List<ReviewsModel> GetReviews()
    {
        List<ReviewsModel> reviews = new List<ReviewsModel>();
        reviews.Add(new ReviewsModel()
        {
            Review = "For basic strength training this app out performs the many methods/apps I have tried in my 30+ years of body/strength training. What I like the most is that it take the brain work out of weights, reps, and sets (if you follow a structured workout). What I like even more is the exceptional customer engagement.",
            ReviewerName = "TijFamily916"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "Let me just say, I was thinking of being an online personal trainer but after using and seeing the power of this app, I sincerely can�t charge people the rates I had in mind when this app does it at a fraction of the cost. The man behind it, Dr. Juneau is the real deal too.",
            ReviewerName = "Rajib Ghosh"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "love seeing my progress on my 1 RM while varying my weight and rep count. Also feel like I am getting more results in a shorter time utilizing the rest pause method. Loving the workouts and the feedback from the app",
            ReviewerName = "Randall Duke"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "Maximizing the time in the gym takes preparation. This app eliminates that and does a better job then I did with hours of preparation. I've seen amazing gains with less work.",
            ReviewerName = "Raymond Backers"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "Great alternative to an actual human personal trainer if your schedule is always dynamic. The charts and graphs and many various options are outstanding.",
            ReviewerName = "Daniel Quick"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "Dr Carl has used science and experience to create an app that will continually push you to the limits. I've been using this app for about a month now, and am moving weight that I didn't think was possible in this short amount of time. I've been lifting for years, but this app would be just as affective for a beginner. One of the best things about it, is Dr Carl listens to the users and their feedback, and is constantly making improvements.",
            ReviewerName = "DeeBee78"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "This app is absolutely amazing. I have been in and out of the gym for a few years with some light progress every time and modest gains, however, the implementation of this app helped me gain 10 lbs and become significantly more defined in the first 6 weeks. Very easy to use, and the customer service is incredible. This app is really great for anyone from beginners to experts.",
            ReviewerName = "Potero2122"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "When I first trialed the app, I wasn�t sure I�d like it, but after having stuck with it for a couple of months, I�m sold. The AI is great and makes it very easy for me to know how many reps to do and how much weight to lift. No more guessing. He brings all the science of lifting to this app, and I�d been lifting regularly for two years. This really is something different than any other app out there.",
            ReviewerName = "MKJ&MKJ"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "This is a very good app to invest in. It's already a good design and has great workouts that will help you continually build muscle and break through plateaus, but they are constantly working to improve it based on customer feedback. The most important thing about this app is the customer service. Christelle and Carl are always available to assist you in anyway they can in a very timely manner, most of the time within an hour of submitting your question or issue. I would recommend this app to everyone serious about building muscle.",
            ReviewerName = "David Fechter"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "I have been using Dr. Muscle for two years now and this app gives me confidence and provides structure to my workouts. I love that the app adapts to you and is quite \"forgiving\" when you do fail while encouraging you to push harder each time. It has really demystified all the elements of training for hypertrophy so I can get straight to lifting after a hard day at work without having to think about everything! I look forward to the analysis of my \"performance\" after every exercise and love to see those green check marks indicating progress. I have recently subscribed to \"Eve\" the dietary equivalent to this app and while it's in its early stages of development I'm looking forward to similarly great things.",
            ReviewerName = "Remone Mundle"
        });

        return reviews;
    }

    private List<ReviewsModel> GetExpertsReviews()
    {
        List<ReviewsModel> reviews = new List<ReviewsModel>();
        reviews.Add(new ReviewsModel()
        {
            Review = "It's like a personal trainer in your pocket that tells you exactly what to do... I can't believe how easy it is.",
            ReviewerName = "Jon Benson"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "This app could get a whole lot of people on track integrating the modern science-based approach to building muscle mass and strength",
            ReviewerName = "Will Brink"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "Skeptical at first but I gave it a shot. In a few short weeks, not only did I make progress but I hit some new PRs. Now I recommend this app to anyone who will hear me.",
            ReviewerName = "Marc David"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "This app can help you achieve sustained muscle growth and strength development. It's based on rock-solid resistance training SCIENCE that has been proven to work in countless studies.",
            ReviewerName = "Nick Nilsson"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "This program creates new workouts with proven principles to help you build muscle like never before",
            ReviewerName = "Alex Rogers"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "Building muscle and getting into the best shape of my life has NEVER been easier. I dare you to try and find another muscle building app like it.",
            ReviewerName = "Tim Ernst"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "Whoa, I wish I had this app when I started out. In 5 minutes, I had my workout set up and the app is already telling me what to do next. How much to lift, what exercises to do... the whole nine yards.",
            ReviewerName = "Dave Ruel"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "That's why I like this app so much. When the weights get too heavy, it deloads your weights automatically for you. That way, in the long run, you can build more muscle and keep your joints healthy.",
            ReviewerName = "Lee Hayward"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "I interviewed Dr. Carl Juneau and I can tell you this is REAL science with cutting-edge strategies you can use right now increase both strength AND muscle in less time.",
            ReviewerName = "Pete Genot"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "Love the app's straight forward interface. Easy to log your weight training. What's truly unique though is that it is up to date and science-based. It helps you choose the right format of your workouts based on your training background.",
            ReviewerName = "Dr. Nick Monastiriotis"
        });
        return reviews;
    }
    private async void BtnShare_Clicked(object sender, EventArgs e)
    {
        /*
        var firstname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
        if (Device.RuntimePlatform.Equals(Device.Android))
        {

            await Share.RequestAsync(new ShareTextRequest
            {
                Uri = $"https://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign=nux&utm_content={firstname}",
                Subject = $"{firstname} is inviting you to try Dr.Muscle, the app that gets you in shape faster using artificial intelligence"
            });
        }
        else
            Xamarin.Essentials.Share.RequestAsync($"{firstname} is inviting you to try Dr.Muscle, the app that gets you in shape faster using artificial intelligence \nhttps://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign=nux&utm_content={firstname}");
        */
        await HelperClass.ShareApp();
    }

}