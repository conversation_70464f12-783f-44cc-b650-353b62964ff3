<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.User.LearnPage"
              xmlns:t="clr-namespace:DrMaxMuscle.Layout"
 xmlns:local="clr-namespace:DrMaxMuscle.Cells"
             Title="LearnPage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <local:BotDataTemplateSelector
        x:Key="BotTemplateSelector">
            </local:BotDataTemplateSelector>
        </ResourceDictionary>
    </ContentPage.Resources>
    <StackLayout
    Spacing="10" Margin="0,0,-30,0" Padding="0,0,0,15"
             BackgroundColor="#f4f4f4">
        <CollectionView
        BackgroundColor="#f4f4f4"
        ItemTemplate="{StaticResource BotTemplateSelector}"
        x:Name="lstChats"
        VerticalOptions="FillAndExpand"
        FlowDirection="LeftToRight"/>
    </StackLayout>
</ContentPage>