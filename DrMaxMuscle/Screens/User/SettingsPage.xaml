﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.User.SettingsPage"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
            xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             Title="SettingsPage">
        <!--<StackLayout
        Orientation="Horizontal"
        VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand"
        Padding="20,0,20,0">-->
            <ScrollView
                Padding="20,0"
                VerticalOptions="FillAndExpand"
                HorizontalOptions="FillAndExpand"
            x:Name="SettingStack">
                <StackLayout
                Orientation="Vertical"
                VerticalOptions="FillAndExpand"
                HorizontalOptions="FillAndExpand">
                    <StackLayout
                    Margin="20,20,20,0">
                        <Label
                        x:Name="lblProgra"
                        Text="PROGRAM"
                        Style="{StaticResource BoldLabelStyle}"
                        Margin="0,0,0,0" />
                        <Label
                        x:Name="LblProgramDesc"
                        Style="{StaticResource NormalLabelStyle}" />
                        <Label
                        x:Name="lblWorkoutDoneCount"
                        HorizontalOptions="StartAndExpand"
                        Style="{StaticResource OnBoardingLabelStyle}" />

                        <Label
                        x:Name="lblWorkout"
                        HorizontalOptions="StartAndExpand"
                        Style="{StaticResource OnBoardingLabelStyle}" />

                        <Label
                        x:Name="lblLevel"
                        HorizontalOptions="StartAndExpand"
                        Style="{StaticResource OnBoardingLabelStyle}" />
                        <Label
                        x:Name="lblLiftedCount"
                        IsVisible="false"
                        HorizontalOptions="StartAndExpand"
                        Style="{StaticResource OnBoardingLabelStyle}" />
                        <StackLayout
                        Orientation="Horizontal"
                        HorizontalOptions="Center">

                            <!--<Image Source="edit_plate_blue.png"
                               HeightRequest="50"
                               Aspect="AspectFit" VerticalOptions="Center" />-->
                            <!--<Label
                            Text="View more stats on the Web"
                            HeightRequest="45"
                            VerticalOptions="Center"
                            VerticalTextAlignment="Center"
                            Style="{StaticResource OnBoardingLabelStyle}"
                            TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Tapped="ViewMoreStats_Clicked" />
                        </StackLayout.GestureRecognizers>-->
                        </StackLayout>
                        <Button
                        Margin="0,10,0,0"
                        Text="Learn more"
                        HorizontalOptions="FillAndExpand"
                        VerticalOptions="Center"
                        Clicked="LearnMoreWButtonClicked"
                        Style="{StaticResource buttonStyle}"
                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />

                        <!--<t:DrMuscleButton x:Name="WorkoutReminderButton" Margin="0,4,0,0" Text="Choose workout days" Style="{StaticResource buttonStyle}" />-->
                        <Button
                        Margin="0,4,0,0"
                        Text="Change program"
                        HorizontalOptions="FillAndExpand"
                        VerticalOptions="Center"
                        Clicked="ChangeWorkoutClicked"
                        Style="{StaticResource buttonStyle}"
                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                        <!---->
                        <Button
                        Margin="0,4,0,0"
                        Text="Change equipment"
                        HorizontalOptions="FillAndExpand"
                        VerticalOptions="Center"
                        Clicked="ChangeEquipmentClicked"
                        Style="{StaticResource buttonStyle}"
                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                    </StackLayout>

                    <!--Strength phase-->
                    <StackLayout
                    Margin="20,0,20,5">

                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,15,0,0">
                            <Label
                            Text="Strength phase"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="StrengthSwitch"
                            IsToggled="false"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <Label
                        Text="3 weeks of strength training at the end of each program"
                        Style="{StaticResource NormalLabelStyle}"
                        FontSize="{OnPlatform Android='13', iOS='14'}" />
                    </StackLayout>
                    <!--5th day reminder-->
                    <StackLayout
                    Margin="20,0,20,17">
                        <!--<Label Text="REMINDERS" Style="{StaticResource BoldLabelStyle}" />-->
                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,10,0,0">
                            <Label
                            Text="Stay on track"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="ReminderSwitch"
                            IsToggled="false"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <Label
                        Text="Get a notification to do a quick bodyweight workout when you don't train for 5 days"
                        Style="{StaticResource NormalLabelStyle}"
                        FontSize="{OnPlatform Android='13', iOS='14'}" />
                    </StackLayout>
                    <!--Cardio-->
                    <StackLayout
                    Margin="20,0,20,17">
                        <!--<Label x:Name="LblCardioSettings" Style="{StaticResource BoldLabelStyle}" />-->
                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0">
                            <Label
                            x:Name="LblCardio"
                            Text="Cardio"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="CardioSwitch"
                            IsToggled="False"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <Label
                        Text="Add 10-20 min of cardio to your workouts"
                        Style="{StaticResource NormalLabelStyle}"
                        FontSize="{OnPlatform Android='13', iOS='14'}" />
                    </StackLayout>

                    <!--Flexibility & Mobility-->
                    <StackLayout
                    Margin="20,0,20,17">
                        <!--<Label x:Name="LblFlexiblity" Text="FLEXIBILITY &amp; MOBILITY" Style="{StaticResource BoldLabelStyle}" />-->
                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,0,0,0">
                            <Label
                            x:Name="Lblflexwarm"
                            Text="Mobility warm-ups"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="flexiblitySwitch"
                            IsToggled="False"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <Label
                        Text="Add 3-17 min of mobility exercises to your warm-ups"
                        x:Name="LblFlexibilityExplainer"
                        IsVisible="false"
                        Style="{StaticResource NormalLabelStyle}"
                        FontSize="{OnPlatform Android='13', iOS='14'}" />
                        <StackLayout
                        x:Name="FlexiStack"
                        Margin="0,10,0,0"
                        IsVisible="false">
                            <Frame
                            Margin="0"
                            HasShadow="False"
                            IsClippedToBounds="True"
                            Padding="0"
                            BackgroundColor="Transparent"
                            BorderColor="{x:Static app:AppThemeConstants.BlueColor}"
                            CornerRadius="6">
                                <StackLayout
                                Orientation="Horizontal"
                                HorizontalOptions="FillAndExpand"
                                Spacing="0"
                                BackgroundColor="Transparent">
                                    <StackLayout
                                    Margin="0"
                                    x:Name="BeginnerGradient"
                                    HorizontalOptions="FillAndExpand"
                                    VerticalOptions="End">
                                        <Label
                                        FontSize="14"
                                        Text="Beginner"
                                        x:Name="BtnBeginner"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="FillAndExpand"
                                        VerticalTextAlignment="Center"
                                        HorizontalTextAlignment="Center"
                                        TextColor="White"
                                        BackgroundColor="Transparent"
                                        HeightRequest="40"></Label>
                                        <StackLayout.GestureRecognizers>
                                            <TapGestureRecognizer
                                            Tapped="BtnBeginnerClicked" />
                                        </StackLayout.GestureRecognizers>
                                    </StackLayout>
                                    <BoxView
                                    WidthRequest="1"
                                    BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <StackLayout
                                    Margin="0"
                                    HorizontalOptions="FillAndExpand"
                                    x:Name="IntermediateGradient"
                                    VerticalOptions="End">
                                        <Label
                                        FontSize="14"
                                        Text="Intermediate"
                                        x:Name="BtnIntermediate"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="FillAndExpand"
                                        VerticalTextAlignment="Center"
                                        HorizontalTextAlignment="Center"
                                        TextColor="#0C2432"
                                        BackgroundColor="Transparent"
                                        HeightRequest="40"></Label>
                                        <StackLayout.GestureRecognizers>
                                            <TapGestureRecognizer
                                            Tapped="BtnIntermediateClicked" />
                                        </StackLayout.GestureRecognizers>
                                    </StackLayout>
                                    <BoxView
                                    WidthRequest="1"
                                    x:Name="BxSaperator2"
                                    BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <StackLayout
                                    Margin="0"
                                    HorizontalOptions="FillAndExpand"
                                    x:Name="AdvancedGradient"
                                    VerticalOptions="End">
                                        <Label
                                        FontSize="14"
                                        Text="Advanced"
                                        x:Name="Btnadvanced"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="FillAndExpand"
                                        VerticalTextAlignment="Center"
                                        HorizontalTextAlignment="Center"
                                        TextColor="#0C2432"
                                        BackgroundColor="Transparent"
                                        HeightRequest="40"></Label>
                                        <StackLayout.GestureRecognizers>
                                            <TapGestureRecognizer
                                            Tapped="BtnadvancedClicked" />
                                        </StackLayout.GestureRecognizers>
                                    </StackLayout>
                                </StackLayout>
                            </Frame>
                            <Label
                            x:Name="LblFlexibilityLvl"
                            IsVisible="false"
                            Style="{StaticResource NormalLabelStyle}"
                            FontSize="{OnPlatform Android='13', iOS='14'}" />
                            <StackLayout
                            Orientation="Horizontal"
                            Spacing="0"
                            Margin="0,10,0,0">
                                <Label
                                x:Name="LblMobilityReps"
                                Text="Max mobility reps"
                                HorizontalOptions="StartAndExpand"
                                Style="{StaticResource NormalLabelStyle}" />
                                <controls:DrEntry
                                x:Name="MobilityRepsEntry"
                                HorizontalOptions="End"
                                Placeholder="Tap to set"
                                VerticalOptions="Center"
                                HorizontalTextAlignment="End"
                                Keyboard="Telephone"
                                Style="{StaticResource entryStyle}"
                                TextChanged="MobilityRepsEntry_TextChanged"
                                MaxLength="3" />
                            </StackLayout>
                            <t:DrMuscleButton
                            Margin="0,7,0,0"
                            x:Name="SaveMobilityRep"
                            Text="Save mobility reps"
                            Style="{StaticResource buttonStyle}" />
                        </StackLayout>

                    </StackLayout>

                    <!--Body part-->
                    <StackLayout
                    x:Name="BodypartStack"
                    Margin="20,0,20,17">
                        <Label
                        x:Name="LblBodyPartPriority"
                        Text="Body part priority"
                        Style="{StaticResource NormalLabelStyle}"
                        Margin="0,0,0,0" />
                        <controls:DropDownPicker
                        x:Name="PickerBodyPart"
                            BackgroundColor="{OnPlatform Android='#145477'}"
                        Margin="0,10,0,0"
                        Style="{StaticResource PickerStyle}"
                        Image="{OnPlatform Android='white_down_arrow',iOS='black_down_arrow'}"
                        Unfocused="PickerBodyPart_Unfocused"
                        HeightRequest="40">

                        </controls:DropDownPicker>
                        <Label
                        x:Name="LblBodypart"
                        Text="Choose a body part. Add 1 exercise for that part to every workout."
                        Style="{StaticResource NormalLabelStyle}"
                        FontSize="{OnPlatform Android='13', iOS='14'}" />
                    </StackLayout>

                    <StackLayout
                    IsVisible="True"
                    x:Name="MaxWorkoutDurationStack"
                    Margin="20,0,20,17">
                        <Label
                        x:Name="LblWorkoutDuration"
                        Text="Max workout duration"
                        Style="{StaticResource NormalLabelStyle}"
                        Margin="0,0,0,0" />
                        <controls:DropDownPicker
                            BackgroundColor="{OnPlatform Android='#145477'}"
                        x:Name="PickerWorkoutDuration"
                        Margin="0,10,0,0"
                        Style="{StaticResource PickerStyle}"
                        Image="{OnPlatform Android='white_down_arrow',iOS='black_down_arrow'}"
                        Unfocused="PickerMaxWorkoutDuration_Unfocused"
                        HeightRequest="40">
                        </controls:DropDownPicker>
                        <Label
                        x:Name="LblMaxWorkoutDuration"
                        Text=""
                        Style="{StaticResource NormalLabelStyle}"
                        FontSize="{OnPlatform Android='13', iOS='14'}" />
                    </StackLayout>
                    <StackLayout
                    Margin="20,0,20,0">

                        <Label
                        x:Name="lblWorkoutDays"
                        Text="WORKOUT DAYS"
                        Style="{StaticResource BoldLabelStyle}"
                        Margin="0,0,0,0" />
                        <Label
                        Text="Custom or recommended for your program and age."
                        Style="{StaticResource NormalLabelStyle}" />

                        <Frame
                        Margin="0"
                        HasShadow="False"
                        IsClippedToBounds="True"
                        Padding="0"
                            
                        BackgroundColor="Transparent"
                        BorderColor="{x:Static app:AppThemeConstants.BlueColor}"
                        CornerRadius="6">
                            <Grid
                                Margin="0"
                                Padding="0"
                                HeightRequest="40"
                            HorizontalOptions="FillAndExpand"
                            ColumnSpacing="0"
                                VerticalOptions="Center"
                            BackgroundColor="Transparent">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition
                                    Width="*" />
                                    <ColumnDefinition
                                    Width="1" />
                                    <ColumnDefinition
                                    Width="*" />
                                </Grid.ColumnDefinitions>
                                <Frame
                                Grid.Column="0"
                                Margin="0"
                                   Padding="0"
                                    CornerRadius="0"
                                BorderColor="Transparent"
                                HorizontalOptions="FillAndExpand"
                                x:Name="RecommendedGradient"
                                VerticalOptions="End">

                                    <Label
                                    FontSize="14"
                                    Text="Recommended"
                                        Padding="0"
                                    x:Name="BtnRecommended"
                                    HorizontalOptions="CenterAndExpand"
                                    VerticalOptions="CenterAndExpand"
                                    VerticalTextAlignment="Center"
                                    HorizontalTextAlignment="Center"
                                    TextColor="#0C2432"
                                    BackgroundColor="Transparent"
                                    HeightRequest="40"></Label>
                                    <Frame.GestureRecognizers>
                                        <TapGestureRecognizer
                                        Tapped="BtnRecommended_Clicked" />
                                    </Frame.GestureRecognizers>
                                </Frame>
                                <BoxView
                                Grid.Column="1"
                                Color="Transparent"
                                WidthRequest="1"
                                BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                <Frame
                                Grid.Column="2"
                                Margin="0"
                                    Padding="0"
                                    BorderColor="Transparent"
                                HorizontalOptions="FillAndExpand"
                                x:Name="CustomReminderGradient"
                                VerticalOptions="End"
                                CornerRadius="0">

                                    <Label
                                    FontSize="14"
                                    Text="Custom"
                                        Padding="0"
                                    x:Name="BtnCustomReminder"
                                    HorizontalOptions="CenterAndExpand"
                                    VerticalOptions="CenterAndExpand"
                                    VerticalTextAlignment="Center"
                                    HorizontalTextAlignment="Center"
                                    TextColor="#0C2432"
                                    BackgroundColor="Transparent"
                                    HeightRequest="40"></Label>
                                    <Frame.GestureRecognizers>
                                        <TapGestureRecognizer
                                        Tapped="BtnCustomReminderClicked" />
                                    </Frame.GestureRecognizers>
                                </Frame>

                            </Grid>
                        </Frame>
                        <Label
                        x:Name="LblReminderDesc"
                        Style="{StaticResource NormalLabelStyle}"
                        Text="Workout days based on your program and age."
                        FontSize="{OnPlatform Android='13', iOS='14'}" />

                        <StackLayout>
                            <StackLayout
                            Orientation="Horizontal"
                            Margin="0,10,0,0"
                            HorizontalOptions="FillAndExpand">
                                <Label
                                Text="Workout reminder email "
                                HorizontalOptions="StartAndExpand"
                                Style="{StaticResource NormalLabelStyle}" />
                                <Switch
                                x:Name="emailReminderSwitch"
                                HorizontalOptions="End" />
                            </StackLayout>
                            <StackLayout
                            x:Name="StackEmailReminder"
                            IsVisible="false"
                            Margin="0,0,0,0">
                                <StackLayout
                                Orientation="Horizontal"
                                Spacing="0"
                                Margin="0,8,0,0">
                                    <Label
                                    x:Name="LblHowManyHours"
                                    Margin="0,0,10,0"
                                    Text="How many hours before?"
                                    HorizontalOptions="StartAndExpand"
                                    Style="{StaticResource NormalLabelStyle}"
                                    FontSize="{OnPlatform Android='13', iOS='15'}" />
                                    <controls:DrEntry
                                    x:Name="hoursBeforeEntry"
                                    MaxLength="2"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center"
                                    HorizontalTextAlignment="End"
                                    Placeholder="Tap to set"
                                    Keyboard="Telephone"
                                    Style="{StaticResource entryStyle}"
                                    TextChanged="MobilityRepsEntry_TextChanged"
                                    Unfocused="hoursBeforeEntry_Unfocused" />
                                </StackLayout>
                                <!--<t:DrMuscleButton Margin="0,10,0,0" x:Name="SaveWarmupButton" Style="{StaticResource buttonStyle}" />-->
                            </StackLayout>
                        </StackLayout>
                    </StackLayout>


                    <StackLayout
                    Margin="20,20,20,0">
                        <Label
                        x:Name="LblRepRange"
                        Margin="0"
                        Style="{StaticResource BoldLabelStyle}" />
                        <Label
                        x:Name="LblYourProgressFaster"
                        Style="{StaticResource NormalLabelStyle}" />
                        <Label
                        x:Name="LearnMoreAboutRepFocusLink"
                        Style="{StaticResource LearnMoreText}"
                        Margin="0,8,0,0" />
                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,8,0,0">
                            <Label
                            x:Name="Lbl512Reps"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="BuildMuscle"
                            IsToggled="true"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,15,0,0">
                            <Label
                            x:Name="Lbl815Reps"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="BuildMuscleBurnFat"
                            IsToggled="false"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,15,0,0">
                            <Label
                            x:Name="Lbl1220Reps"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="FatBurning"
                            IsToggled="false"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,15,0,0">
                            <Label
                            x:Name="LblCustom"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="Custom"
                            IsToggled="false"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <StackLayout
                        x:Name="CustomRepsStack">
                            <StackLayout
                            Orientation="Horizontal"
                            Margin="0,10,0,0">
                                <Label
                                x:Name="LblMin"
                                Margin="15,0,0,0"
                                Style="{StaticResource NormalLabelStyle}"
                                FontSize="{OnPlatform Android='12', iOS='14'}" />
                                <t:DrMuscleButton
                                x:Name="RepsMinimumLess"
                                Text="-"
                                WidthRequest="40"
                                HeightRequest="40"
                                Style="{StaticResource buttonStyle}"
                                Margin="5,0,0,0" />
                                <Label
                                x:Name="RepsMinimumLabel"
                                FontSize="14"
                                HorizontalOptions="CenterAndExpand"
                                VerticalOptions="Center"
                                HorizontalTextAlignment="Center"
                                Style="{StaticResource LabelStyle}"></Label>
                                <t:DrMuscleButton
                                x:Name="RepsMinimumMore"
                                Text="+"
                                WidthRequest="40"
                                HeightRequest="40"
                                Style="{StaticResource buttonStyle}"
                                Margin="0,0,0,0" />
                            </StackLayout>
                            <StackLayout
                            Orientation="Horizontal"
                            Margin="0,10,0,0">
                                <Label
                                x:Name="LblMax"
                                VerticalOptions="Center"
                                Margin="15,0,0,0"
                                Style="{StaticResource LabelStyle}"
                                FontSize="{OnPlatform Android='12', iOS='14'}" />
                                <t:DrMuscleButton
                                x:Name="RepsMaximumLess"
                                Text="-"
                                WidthRequest="40"
                                HeightRequest="40"
                                Style="{StaticResource buttonStyle}"
                                Margin="1,0,0,0" />
                                <Label
                                x:Name="RepsMaximumLabel"
                                FontSize="14"
                                HorizontalOptions="CenterAndExpand"
                                HorizontalTextAlignment="Center"
                                Style="{StaticResource NormalLabelStyle}"></Label>
                                <t:DrMuscleButton
                                x:Name="RepsMaximumMore"
                                Text="+"
                                WidthRequest="40"
                                HeightRequest="40"
                                Style="{StaticResource buttonStyle}"
                                Margin="0,0,0,0" />
                            </StackLayout>
                            <StackLayout
                            Margin="15,10,0,0">
                                <t:DrMuscleButton
                                x:Name="SaveCustomRepsButton"
                                Style="{StaticResource buttonStyle}" />
                            </StackLayout>
                        </StackLayout>
                    </StackLayout>
                    <!--<StackLayout Margin="20,20,20,0">
                                <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,0,0,0">
                                    <Label x:Name="LblCardio" Text="Cardio" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                                    <Switch x:Name="CardioSwitch" IsToggled="False" HorizontalOptions="End" VerticalOptions="Center" />
                                </StackLayout>
                                <Label Text="Add 10-20 min of cardio to your workouts automatically." Style="{StaticResource NormalLabelStyle}" FontSize="{OnPlatform Android='13', iOS='14'}"/>
                            </StackLayout>-->
                    <StackLayout
                    x:Name="SetsStack"
                    Margin="20,20,20,0">
                        <Label
                        x:Name="LblSetStyle"
                        Style="{StaticResource BoldLabelStyle}" />
                        <StackLayout
                        x:Name="StackWarmup"
                        Margin="0,0,0,0">
                            <StackLayout
                            Orientation="Horizontal"
                            Spacing="0"
                            Margin="0,10,0,0">
                                <Label
                                x:Name="LblHowManyWarmups"
                                HorizontalOptions="StartAndExpand"
                                Style="{StaticResource NormalLabelStyle}" />
                                <controls:DrEntry
                                x:Name="WarmupEntry"
                                MaxLength="2"
                                HorizontalOptions="End"
                                VerticalOptions="Center"
                                HorizontalTextAlignment="End"
                                Keyboard="Telephone"
                                Style="{StaticResource entryStyle}"
                                TextChanged="UnitEntry_TextChanged" />
                            </StackLayout>
                            <t:DrMuscleButton
                            Margin="0,10,0,0"
                            x:Name="SaveWarmupButton"
                            Style="{StaticResource buttonStyle}" />
                        </StackLayout>
                        <Label
                        x:Name="LblWorkoutMode"
                        Text="Work sets"
                        Style="{StaticResource NormalLabelStyle}"
                        Margin="0,15,0,0" />

                        <Frame
                        Margin="0"
                        HasShadow="False"
                        IsClippedToBounds="True"
                        Padding="0"
                        BackgroundColor="Transparent"
                        BorderColor="{x:Static app:AppThemeConstants.BlueColor}"
                        CornerRadius="6">
                            <StackLayout
                            Orientation="Horizontal"
                                HeightRequest="40"
                            HorizontalOptions="FillAndExpand"
                            Spacing="0"
                            BackgroundColor="Transparent">
                                <Frame
                                    BorderColor="Transparent"
                                Margin="0"
                                    Padding="0"
                                HorizontalOptions="FillAndExpand"
                                x:Name="NormalModeGradient"
                                VerticalOptions="End"
                                CornerRadius="0">

                                    <Label
                                    FontSize="14"
                                    Text="Flexible"
                                    x:Name="BtnNormalMode"
                                    HorizontalOptions="FillAndExpand"
                                    VerticalOptions="FillAndExpand"
                                    VerticalTextAlignment="Center"
                                    HorizontalTextAlignment="Center"
                                    TextColor="#0C2432"
                                    BackgroundColor="Transparent"
                                    HeightRequest="40"></Label>
                                    <Frame.GestureRecognizers>
                                        <TapGestureRecognizer
                                        Tapped="BtnNormalMode_Clicked" />
                                    </Frame.GestureRecognizers>
                                </Frame>
                                <BoxView
                                WidthRequest="1"
                                BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                <Frame
                                Margin="0"
                                    BorderColor="Transparent"
                                    Padding="0"
                                HorizontalOptions="FillAndExpand"
                                x:Name="QuickGradient"
                                VerticalOptions="End"
                                CornerRadius="0">

                                    <Label
                                    FontSize="14"
                                    Text="Custom"
                                    x:Name="BtnQuick"
                                    HorizontalOptions="FillAndExpand"
                                    VerticalOptions="FillAndExpand"
                                    VerticalTextAlignment="Center"
                                    HorizontalTextAlignment="Center"
                                    TextColor="#0C2432"
                                    BackgroundColor="Transparent"
                                    HeightRequest="40"></Label>
                                    <Frame.GestureRecognizers>
                                        <TapGestureRecognizer
                                        Tapped="BtnQuickClicked" />
                                    </Frame.GestureRecognizers>
                                </Frame>

                            </StackLayout>
                        </Frame>
                        <Label
                        x:Name="LblWorkoutType"
                        Style="{StaticResource NormalLabelStyle}"
                        FontSize="{OnPlatform Android='13', iOS='14'}" />

                        <StackLayout
                        Margin="0,15,0,0"
                        x:Name="StackWorkset"
                        HorizontalOptions="FillAndExpand">
                            <StackLayout
                            Orientation="Horizontal"
                            Spacing="0"
                            Margin="0,0,0,0">
                                <Label
                                x:Name="LblSetCount"
                                Text="Work sets"
                                HorizontalOptions="StartAndExpand"
                                Style="{StaticResource NormalLabelStyle}" />
                                <controls:DrEntry
                                x:Name="SetEntry"
                                HorizontalOptions="End"
                                MaxLength="2"
                                Placeholder="Tap to set"
                                VerticalOptions="Center"
                                HorizontalTextAlignment="End"
                                Keyboard="Telephone"
                                Style="{StaticResource entryStyle}"
                                TextChanged="SetEntry_TextChanged" />
                            </StackLayout>
                            <t:DrMuscleButton
                            Margin="0,10,0,0"
                            Text="Save work sets"
                            x:Name="SaveSetCountButton"
                            Style="{StaticResource buttonStyle}" />
                            <Label
                            x:Name="LblCustomWorkset"
                            Style="{StaticResource NormalLabelStyle}"
                            FontSize="{OnPlatform Android='13', iOS='14'}" />
                        </StackLayout>
                        <!--<Picker x:Name="SetStylePicker" Margin="0,10,0,0" Style="{StaticResource PickerStyle}" />-->
                        <!--<Frame Margin="0,20,0,0" HasShadow="False" IsClippedToBounds="True" Padding="0" BackgroundColor="Transparent" BorderColor="{x:Static app:AppThemeConstants.BlueColor}" CornerRadius="6">
                                    <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand" Spacing="0" BackgroundColor="Transparent">
                                        <pancakeView:PancakeView 
                                  Margin="0"
                  x:Name="RestPauseGradient"
                                      HorizontalOptions="FillAndExpand" VerticalOptions="End" OffsetAngle="270" CornerRadius="0" >

                                            <Label FontSize="14" Text="Rest-pause" x:Name="BtnRestPause" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"  TextColor="White" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                                            <pancakeView:PancakeView.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="BtnRestPauseClicked" />
                                            </pancakeView:PancakeView.GestureRecognizers>
                                        </pancakeView:PancakeView>
                                        <BoxView WidthRequest="1" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"/>
                                        <pancakeView:PancakeView 
                                  Margin="0" 
                                      HorizontalOptions="FillAndExpand" x:Name="NormalGradient" VerticalOptions="End" OffsetAngle="270" CornerRadius="0" >

                                            <Label FontSize="14" Text="Normal" x:Name="BtnNormal" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center" TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                                            <pancakeView:PancakeView.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="BtnNormalClicked" />
                                            </pancakeView:PancakeView.GestureRecognizers>
                                        </pancakeView:PancakeView>
                                        <BoxView WidthRequest="1" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"/>
                                        <pancakeView:PancakeView 
                                  Margin="0" 
                                      HorizontalOptions="FillAndExpand" x:Name="RPyramidGradient" VerticalOptions="End" OffsetAngle="270" CornerRadius="0" >

                                            <Label FontSize="14"  Text="Pyramid" x:Name="BtnRPyramid" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"   TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                                            <pancakeView:PancakeView.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="BtnRPyramid_Clicked" />
                                            </pancakeView:PancakeView.GestureRecognizers>
                                        </pancakeView:PancakeView>
                                        <BoxView WidthRequest="1" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"/>
                                        <pancakeView:PancakeView 
                                  Margin="0" 
                                      HorizontalOptions="FillAndExpand" x:Name="PyramidGradient" VerticalOptions="End" OffsetAngle="270" CornerRadius="0" >

                                            <Label FontSize="14"  Text="Reverse&#10;Pyramid" x:Name="BtnPyramid" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"   TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                                            <pancakeView:PancakeView.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="BtnPyramid_Clicked" />
                                            </pancakeView:PancakeView.GestureRecognizers>
                                        </pancakeView:PancakeView>




                                        




                                    </StackLayout>
                                </Frame>-->
                        <Label
                        x:Name="LblStyleSubHead"
                        Text="Set style"
                        Style="{StaticResource NormalLabelStyle}"
                        Margin="0,15,0,0" />
                        <controls:DropDownPicker
                            BackgroundColor="{OnPlatform Android='#145477'}"
                        x:Name="SetStylePicker"
                        Margin="0,0,0,0"
                        Style="{StaticResource PickerStyle}"
                        Unfocused="SetStylePicker_Unfocused"
                        Image="{OnPlatform Android='white_down_arrow',iOS='black_down_arrow'}"
                        HeightRequest="40">
                        </controls:DropDownPicker>
                        <Label
                        x:Name="LblRestPauseSets"
                        Style="{StaticResource NormalLabelStyle}"
                        FontSize="{OnPlatform Android='13', iOS='14'}" />

                        <StackLayout>
                            <StackLayout
                            Orientation="Horizontal"
                            Margin="0,15,0,0"
                            HorizontalOptions="FillAndExpand">
                                <Label
                                Text="Reference set"
                                HorizontalOptions="StartAndExpand"
                                Style="{StaticResource NormalLabelStyle}" />
                                <Switch
                                x:Name="refrenceSetSwitch"
                                HorizontalOptions="End" />
                            </StackLayout>
                            <StackLayout
                            x:Name="StackReferenceSet"
                            IsVisible="false"
                            Margin="0,0,0,0">
                                <StackLayout
                                Orientation="Horizontal"
                                Spacing="0"
                                Margin="0,8,0,0">
                                    <Label
                                    x:Name="LblReferenceSet"
                                    Margin="0,0,10,0"
                                    Text="Minimum reps"
                                    HorizontalOptions="StartAndExpand"
                                    Style="{StaticResource NormalLabelStyle}"
                                    FontSize="{OnPlatform Android='13', iOS='15'}" />
                                    <controls:DrEntry
                                    x:Name="referenseSetEntry"
                                    MaxLength="7"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center"
                                    HorizontalTextAlignment="End"
                                    Placeholder="Tap to set"
                                    Keyboard="Telephone"
                                    Style="{StaticResource entryStyle}"
                                    TextChanged="MobilityRepsEntry_TextChanged"
                                    Unfocused="referenceSetEntry_Unfocused" />
                                </StackLayout>
                                <!--<t:DrMuscleButton Margin="0,10,0,0" x:Name="SaveWarmupButton" Style="{StaticResource buttonStyle}" />-->
                                <Label
                        x:Name="LblReferenseSetExplaination"
                        Text="Autosave in background on value change A recurring set of 8 reps or more to track progress"
                        Style="{StaticResource NormalLabelStyle}"
                        FontSize="{OnPlatform Android='13', iOS='14'}" />

                                <Label
                                Text="Learn more"
                        Style="{StaticResource LearnMoreText}"
                        Margin="0,0,0,0" >
                                    <Label.GestureRecognizers>
                                        <TapGestureRecognizer Tapped="LearnMoreReferenceSetButtonClicked" />
                                    </Label.GestureRecognizers>

                                </Label>
                            </StackLayout>
                        </StackLayout>
                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,15,0,0">
                            <Label
                            x:Name="LblBackOff"
                            Text="Back-off set"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="BackOffSetSwitch"
                            IsToggled="true"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <Label
                        x:Name="LblBackOffSets"
                        Text="Do more reps with less weight on your last set to build muscle faster"
                        Style="{StaticResource NormalLabelStyle}"
                        FontSize="{OnPlatform Android='13', iOS='14'}" />

                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,15,0,0">
                            <Label
                            x:Name="Lbl1By1Side"
                            Text="Train left/right sides separately?"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="Switch1By1Side"
                            IsToggled="true"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <Label
                        x:Name="Lbl1By1sides"
                        Text="Do more reps with less weight on your last set to build muscle faster"
                        Style="{StaticResource NormalLabelStyle}"
                        FontSize="{OnPlatform Android='13', iOS='14'}" />

                    </StackLayout>
                    <!--<StackLayout Margin="20,20,20,0">
                                <Label x:Name="LblFlexiblity" Text="FLEXIBILITY &amp; MOBILITY" Style="{StaticResource BoldLabelStyle}" />
                                <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0">
                                    <Label x:Name="Lblflexwarm" Text="Mobility warm-ups" HorizontalOptions="StartAndExpand"  Style="{StaticResource NormalLabelStyle}"/>
                                    <Switch x:Name="flexiblitySwitch" IsToggled="False" HorizontalOptions="End" VerticalOptions="Center" />
                                </StackLayout>
                                <StackLayout x:Name="FlexiStack" Margin="0,20,0,0" IsVisible="false">
                                    <Frame Margin="0" HasShadow="False" IsClippedToBounds="True" Padding="0" BackgroundColor="Transparent" BorderColor="{x:Static app:AppThemeConstants.BlueColor}" CornerRadius="6">
                                        <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand" Spacing="0" BackgroundColor="Transparent">
                                            <StackLayout Margin="0" x:Name="BeginnerGradient" HorizontalOptions="FillAndExpand" VerticalOptions="End">
                                                <Label FontSize="14" Text="Beginner" x:Name="BtnBeginner" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"  TextColor="White" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                                                <StackLayout.GestureRecognizers>
                                                    <TapGestureRecognizer Tapped="BtnBeginnerClicked" />
                                                </StackLayout.GestureRecognizers>
                                            </StackLayout>
                                            <BoxView WidthRequest="1" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"/>
                                            <StackLayout
                                  Margin="0" 
                                      HorizontalOptions="FillAndExpand" x:Name="IntermediateGradient" VerticalOptions="End" >
                                                <Label FontSize="14" Text="Intermediate" x:Name="BtnIntermediate" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center" TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                                                <StackLayout.GestureRecognizers>
                                                    <TapGestureRecognizer Tapped="BtnIntermediateClicked" />
                                                </StackLayout.GestureRecognizers>
                                            </StackLayout>
                                            <BoxView WidthRequest="1" x:Name="BxSaperator2" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"/>
                                            <StackLayout Margin="0" HorizontalOptions="FillAndExpand" x:Name="AdvancedGradient" VerticalOptions="End" >
                                                <Label FontSize="14"  Text="Advanced" x:Name="Btnadvanced" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center" TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                                                <StackLayout.GestureRecognizers>
                                                    <TapGestureRecognizer Tapped="BtnadvancedClicked" />
                                                </StackLayout.GestureRecognizers>
                                            </StackLayout>
                                        </StackLayout>
                                    </Frame>
                                    <Label x:Name="LblFlexibilityLvl" IsVisible="false"  Style="{StaticResource NormalLabelStyle}" FontSize="{OnPlatform Android='13', iOS='14'}" />
<StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0">
                                    <Label x:Name="LblMobilityReps" Text="Max reps" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                                    <t:DrEntry x:Name="MobilityRepsEntry" HorizontalOptions="End" Placeholder="Tap to set" VerticalOptions="Center" HorizontalTextAlignment="End" Keyboard="Telephone" Style="{StaticResource entryStyle}" TextChanged="MobilityRepsEntry_TextChanged" MaxLength="3"  />
                                </StackLayout>
                                    <t:DrMuscleButton Margin="0,7,0,0" x:Name="SaveMobilityRep" Text="Save reps" Style="{StaticResource buttonStyle}" />
                                </StackLayout>
                                
                            </StackLayout>-->


                    <StackLayout
                    x:Name="UnitsStack"
                    Margin="20,20,20,0">
                        <Label
                        x:Name="LblUnits"
                        Style="{StaticResource BoldLabelStyle}" />
                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,10,0,0">
                            <Label
                            x:Name="LblKg"
                            Text="kg"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="KgSwitch"
                            IsToggled="false"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,15,0,0">
                            <Label
                            x:Name="LblLbs"
                            Text="lbs"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="LbsSwitch"
                            IsToggled="False"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <!--<StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0">
                                    <Label x:Name="LblMinIncrements" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                                    <t:DrEntry x:Name="MinEntry"  HorizontalOptions="End" Placeholder="Tap to set" VerticalOptions="Center" HorizontalTextAlignment="End" Keyboard="Telephone" Style="{StaticResource entryStyle}" TextChanged="UnitEntry_TextChanged"  />
                                </StackLayout>
                                <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0">
                                    <Label x:Name="Increments" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                                    <t:DrEntry x:Name="UnitEntry"  HorizontalOptions="End" Placeholder="Tap to set" VerticalOptions="Center" HorizontalTextAlignment="End" Keyboard="Telephone" Style="{StaticResource entryStyle}" TextChanged="UnitEntry_TextChanged"  />
                                </StackLayout>
                                <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0">
                                    <Label x:Name="LblMaxIncrements" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                                    <t:DrEntry x:Name="MaxEntry"  HorizontalOptions="End" Placeholder="Tap to set" VerticalOptions="Center" HorizontalTextAlignment="End" Keyboard="Telephone" Style="{StaticResource entryStyle}" TextChanged="UnitEntry_TextChanged"  />
                                </StackLayout>
                                <StackLayout Margin="0,10,0,0">
                                    <t:DrMuscleButton x:Name="SaveIncrementsButton" Style="{StaticResource buttonStyle}" />
                                </StackLayout>-->
                        <!--<StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,15,0,0">
                        <Label
                            x:Name="LblBodyWeight"
                            Text="Body weight"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                        <t:DrEntry
                            IsReadOnly="True"
                            x:Name="BodyweightEntry"
                            HorizontalOptions="End"
                            Placeholder="Tap to set"
                            VerticalOptions="Center"
                            HorizontalTextAlignment="End"
                            Keyboard="Telephone"
                            Style="{StaticResource entryStyle}"
                            TextChanged="UnitEntry_TextChanged"
                            MaxLength="7" />
                    </StackLayout>-->
                        <!--<StackLayout
                        Margin="0,10,0,0">
                        <t:DrMuscleButton
                            x:Name="SaveBodyweightButton"
                            Text="Save body weight"
                            Style="{StaticResource buttonStyle}" />
                    </StackLayout>-->
                    </StackLayout>

















                    <StackLayout
                    x:Name="BackgroundStack"
                    Margin="20,20,20,20"
                    IsVisible="false">
                        <Label
                        x:Name="LblBackgroundImage"
                        Style="{StaticResource BoldLabelStyle}" />
                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,10,0,0">
                            <Label
                            x:Name="LblMale"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="MaleSwitch"
                            IsToggled="false"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,15,0,0">
                            <Label
                            x:Name="LblFemale"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="FemaleSwitch"
                            IsToggled="false"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,15,0,0">
                            <Label
                            x:Name="LblDrMuscle"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="MuscleLogoSwitch"
                            IsToggled="false"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0,15,0,0">
                            <Label
                            x:Name="LblNoImage"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="NoImageSwitch"
                            IsToggled="false"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                    </StackLayout>


                    <StackLayout
                    Margin="20,20,20,5">

                        <StackLayout
                        Orientation="Horizontal"
                        Spacing="0"
                        Margin="0">
                            <Label
                            x:Name="LblExerciseQuick"
                            Text="Quick mode"
                            HorizontalOptions="StartAndExpand"
                            Style="{StaticResource NormalLabelStyle}" />
                            <Switch
                            x:Name="ExerciseQuickSwitch"
                            IsToggled="False"
                            HorizontalOptions="End"
                            VerticalOptions="Center" />
                        </StackLayout>
                        <Label
                        Text="Skip exercise summary"
                        Style="{StaticResource NormalLabelStyle}"
                        FontSize="{OnPlatform Android='13', iOS='14'}" />
                    </StackLayout>


                    <StackLayout
                    x:Name="LanguageStack">
                        <StackLayout
                        x:Name="AllLanguageStack"
                        Margin="20,20,20,5">
                            <Label
                            x:Name="LblLanguage"
                            Style="{StaticResource BoldLabelStyle}" />
                            <StackLayout
                            Orientation="Horizontal"
                            Spacing="0"
                            Margin="0,10,0,0">
                                <Label
                                x:Name="LblEnglish"
                                Text="English"
                                HorizontalOptions="StartAndExpand"
                                Style="{StaticResource NormalLabelStyle}" />
                                <Switch
                                x:Name="EnglishSwitch"
                                IsToggled="True"
                                HorizontalOptions="End"
                                VerticalOptions="Center" />
                            </StackLayout>
                            <StackLayout
                            Orientation="Horizontal"
                            Spacing="0"
                            Margin="0,15,0,0">
                                <Label
                                Text="French"
                                HorizontalOptions="StartAndExpand"
                                Style="{StaticResource NormalLabelStyle}" />
                                <Switch
                                x:Name="FrenchSwitch"
                                IsToggled="false"
                                HorizontalOptions="End"
                                VerticalOptions="Center" />
                            </StackLayout>
                            <StackLayout
                            Orientation="Horizontal"
                            Spacing="0"
                            Margin="0,15,0,0">
                                <Label
                                Text="Swedish"
                                HorizontalOptions="StartAndExpand"
                                Style="{StaticResource NormalLabelStyle}" />
                                <Switch
                                x:Name="SwedishSwitch"
                                IsToggled="false"
                                HorizontalOptions="End"
                                VerticalOptions="Center" />
                            </StackLayout>
                            <StackLayout
                            Orientation="Horizontal"
                            Spacing="0"
                            Margin="0,15,0,0">
                                <Label
                                Text="German"
                                HorizontalOptions="StartAndExpand"
                                Style="{StaticResource NormalLabelStyle}" />
                                <Switch
                                x:Name="GermanSwitch"
                                IsToggled="false"
                                HorizontalOptions="End"
                                VerticalOptions="Center" />
                            </StackLayout>
                        </StackLayout>
                    </StackLayout>


                    <StackLayout
                    Margin="20,20,20,20" >
                        <Label
                        x:Name="LblAccount"
                        Text="ACCOUNT"
                        Style="{StaticResource BoldLabelStyle}" />
                        <Frame
                        Style="{StaticResource GradientFrameStyleBlue}"
                        Padding="0"
                        Margin="0,10,0,0"
                        IsClippedToBounds="true"
                        CornerRadius="0"
                        HorizontalOptions="FillAndExpand"
                            IsVisible="False"
                        HeightRequest="60">

                            <t:DrMuscleButton
                            Text="Reconfigure account"
                            x:Name="ReconfigureButton"
                            Clicked="ReconfigureButton_Clicked"
                            VerticalOptions="EndAndExpand"
                            HeightRequest="60"
                            HorizontalOptions="FillAndExpand"
                            IsVisible="true"
                            Style="{StaticResource highEmphasisButtonStyle}"
                            BackgroundColor="Transparent"
                            BorderColor="Transparent"
                            TextColor="White" />
                        </Frame>
                        <Label
                        x:Name="LblReconfigure"
                            IsVisible="False"
                        Text="Updates programs, goals, and settings. No effect on exercise history and records."
                        Style="{StaticResource NormalLabelStyle}" />


                        <!--<t:DrMuscleButton Margin="0,10,0,0" x:Name="ChangeEmailButton" Clicked="ChangeEmailButton_Clicked" Text="Change login email" Style="{StaticResource buttonStyle}" />
                                <Label Text="Currently:" x:Name="LblEmail" Style="{StaticResource NormalLabelStyle}" />-->

                        <t:DrMuscleButton
                        x:Name="ResetButton"
                        Margin="0,10,0,0"
                        Text="Reset all exercises"
                        Clicked="ResetButton_Clicked"
                        HorizontalOptions="FillAndExpand"
                        Style="{StaticResource buttonStyle}"
                        BackgroundColor="Red"
                        BorderColor="Red"
                        TextColor="White" />
                        <Label
                        Text="Resets all exercise history. No effect on other settings."
                        Style="{StaticResource NormalLabelStyle}" />
                        <t:DrMuscleButton
                        x:Name="deleteButton"
                        Margin="0,10,0,0"
                        Text="Delete account"
                        Clicked="DeleteButton_Clicked"
                        HorizontalOptions="FillAndExpand"
                        Style="{StaticResource buttonStyle}"
                        BackgroundColor="Red"
                        BorderColor="Red"
                        TextColor="White" />
                        <Label
                        x:Name="lblDeleteDesc"
                        Text="Deletes everything."
                        Style="{StaticResource NormalLabelStyle}" />
                    </StackLayout>
                </StackLayout>
            </ScrollView>
        <!--</StackLayout>-->
</ContentPage>