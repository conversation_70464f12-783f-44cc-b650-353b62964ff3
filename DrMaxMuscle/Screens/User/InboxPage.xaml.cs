using Acr.UserDialogs;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Resx;
using Microsoft.Maui.Networking;
using System.Collections.ObjectModel;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Cells;
using DrMaxMuscle.Utility;
namespace DrMaxMuscle.Screens.User;

public partial class InboxPage : ContentPage, IActiveAware
{
    public event EventHandler IsActiveChanged;

    bool _isActive;
    public bool IsActive
    {
        get => _isActive;
        set
        {
            if (_isActive != value)
            {
                _isActive = value;
                IsActiveChanged?.Invoke(this, EventArgs.Empty);
            }
        }
    }
    bool IsAdmin = false;
    string supportUrl = "";
    public ObservableCollection<DrMaxMuscle.Helpers.Messages> messageList = new ObservableCollection<DrMaxMuscle.Helpers.Messages>();

    public InboxPage()
    {
        InitializeComponent();
        RefreshLocalized();
        lstView.ItemsSource = messageList;
        //Test
        //SendBirdClient.Init("05F82C36-1159-4179-8C49-5910C7F51D7D");
        //lstView.ItemTapped += LstView_ItemTapped;
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });

    }
    void RefreshLocalized()
    {
        Title = AppResources.Support;
    }
    //public override void OnBeforeShow()
    //{
    //    if (LocalDBManager.Instance.GetDBSetting("email") == null)
    //        return;
    //}

    protected override void OnAppearing()
    {
        base.OnAppearing();
        if (messageList.Count == 0)
        {
            if (Connectivity.NetworkAccess == NetworkAccess.Internet)
                UserDialogs.Instance.ShowLoading();

        }

        try
        {
            if (Connectivity.NetworkAccess != NetworkAccess.Internet)
            {
                ConnectionErrorPopup();

                return;
            }
            if (LocalDBManager.Instance.GetDBSetting("email") == null)
                return;
            IsAdmin = LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>") || LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>");

            Connect_Handler(false);
            Connect_Handler(true);
        }
        catch (Exception ex)
        {

        }
    }
    private bool isPresented = false;
    protected async Task ConnectionErrorPopup()
    {
        if (isPresented)
            return;
        isPresented = true;
        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        // {
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     Message = AppResources.PleaseCheckInternetConnection,
        //     Title = AppResources.ConnectionError,
        //     OkText = "Try again"
        // });

        await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Try again","");
        isPresented = false;
    }
    protected override void OnDisappearing()
    {
        base.OnDisappearing();


    }


    async Task Connect_Handler(bool isRead)
    {
        try
        {


            if (!IsAdmin)
            {


                DrMaxMuscle.Helpers.Messages messages = new DrMaxMuscle.Helpers.Messages()
                {
                    Message = AppResources.TapHereFor11Chat,
                    Nickname = AppResources.ChatWithSupport,
                    CreatedDate = DateTime.Now,
                    ChatType = ChannelType.Group,
                    UserId = "<EMAIL>"
                };
                messageList.Add(messages);

            }
            else
            {


                // ObservableCollection<DrMaxMuscle.Helpers.Messages> msgList = new ObservableCollection<DrMaxMuscle.Helpers.Messages>();


                var list = await DrMuscleRestClient.Instance.FetchInboxByType(messageList.Count == 0 ? true : false, isRead);

                List<DrMaxMuscle.Helpers.Messages> lst = new List<DrMaxMuscle.Helpers.Messages>();
                foreach (var item in list)
                {
                    try
                    {
                        DrMaxMuscle.Helpers.Messages messages = new DrMaxMuscle.Helpers.Messages()
                        {
                            Message = item.ChatModel.Message,
                            Nickname = item.SenderName,
                            CreatedDate = item.UpdatedAt,
                            ChatType = ChannelType.Group,
                            UserId = item.SenderEmail,
                            AdminId = "<EMAIL>",
                            SupportChannelUrl = item.SenderEmail.ToLower().Equals("<EMAIL>") ? item.ReceiverId : item.SenderId,
                            IsUnread = !item.ChatModel.IsRead,
                            NormalUSerEmail = item.SenderEmail == "<EMAIL>" ? item.ReceiverEmail : item.SenderEmail,
                            NormalUSerName = item.SenderEmail == "<EMAIL>" ? item.ReceiverName : item.SenderName,
                            ChatRoomId = item.Id,
                            IsV1User = item.IsV1user,
                            IsBothReplied = item.IsBothReplied
                        };
                        lst.Add(messages);

                    }
                    catch (Exception)
                    {

                    }
                }
                //var sortedList = lst
                //            .OrderBy(a => a.IsBothReplied) // Orders by IsBothReplied ascending (false first, true later)
                //            .ThenByDescending(a => a.IsUnread) // Within each IsBothReplied group, sorts by IsUnread descending (true first, false later)
                //            .ToList();
                foreach (var item in lst)
                {
                    //if (messageList.FirstOrDefault(x=>x.ChatRoomId == item.ChatRoomId) == null)
                    //    messageList.Add(item);
                    //else
                    //{
                    //    var oldItem = messageList.FirstOrDefault(x => x.ChatRoomId == item.ChatRoomId);
                    //    messageList[messageList.IndexOf(oldItem)] = item;

                    //}
                    var existingItem = messageList.FirstOrDefault(x => x.ChatRoomId == item.ChatRoomId);
                    if (existingItem == null)
                    {
                        messageList.Add(item);
                    }
                    else
                    {
                        var index = messageList.IndexOf(existingItem);
                        messageList[index] = item;
                    }
                }
                //var sortedList = messageList
                //.OrderBy(a => a.IsBothReplied) // Orders by IsBothReplied ascending (false first, true later)
                //.ThenByDescending(a => a.IsUnread) // Within each IsBothReplied group, sorts by IsUnread descending (true first, false later)
                //.ToList();
                Device.BeginInvokeOnMainThread(() =>
                {
                    // messageList = msgList;
                    lstView.ItemsSource = messageList;
                    lstView.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                    //lstView.ScrollToFirst();
                });

            }


        }
        catch (Exception e)
        {
        }

    }
    async void LstView_ItemTapped(object sender, ItemTappedEventArgs e)
    {
        try
        {
            if (!IsAdmin)
            {
                if (((DrMaxMuscle.Helpers.Messages)e.Item).ChatType == ChannelType.Group)
                {
                    CurrentLog.Instance.ReceiverEmail = ((DrMaxMuscle.Helpers.Messages)e.Item).NormalUSerEmail;
                    CurrentLog.Instance.ReceiverName = ((DrMaxMuscle.Helpers.Messages)e.Item).NormalUSerName;
                    CurrentLog.Instance.ChannelUrl = supportUrl;
                    SupportPage page = new SupportPage();
                    page.OnBeforeShow();
                    await Navigation.PushAsync(page);
                    //await PagesFactory.PushAsync<SupportPage>();
                }
                else
                {
                    ChatPage page = new ChatPage();
                    page.OnBeforeShow();
                    await Navigation.PushAsync(page);
                    //await PagesFactory.PushAsync<ChatPage>();
                }
            }
            else
            {
                if (((DrMaxMuscle.Helpers.Messages)e.Item).ChatType == ChannelType.Group)
                {
                    CurrentLog.Instance.ReceiverEmail = ((DrMaxMuscle.Helpers.Messages)e.Item).NormalUSerEmail;
                    CurrentLog.Instance.ReceiverName = ((DrMaxMuscle.Helpers.Messages)e.Item).NormalUSerName;
                    CurrentLog.Instance.RoomId = ((DrMaxMuscle.Helpers.Messages)e.Item).ChatRoomId;
                    CurrentLog.Instance.ChannelUrl = ((DrMaxMuscle.Helpers.Messages)e.Item).SupportChannelUrl;
                    SupportPage page = new SupportPage();
                    page.OnBeforeShow();
                    await Navigation.PushAsync(page);
                    //await PagesFactory.PushAsync<SupportPage>();
                }
                else
                {
                    ChatPage page = new ChatPage();
                    page.OnBeforeShow();
                    await Navigation.PushAsync(page);
                    //await PagesFactory.PushAsync<ChatPage>();
                }
            }
        }
        catch (Exception ex)
        {

        }
    }

    void Handle_ItemAppearing(object sender, ItemVisibilityEventArgs e)
    {
        var itemTypeObject = e.Item as DrMaxMuscle.Helpers.Messages;

    }

    void Handle_ItemDisappearing(object sender, ItemVisibilityEventArgs e)
    {

    }


    private async void TapGestureRecognizer_Tapped(object sender, TappedEventArgs e)
    {
        try
        {
            var inbox = sender as InboxCell;
            var selectedMessage = inbox.BindingContext;

            // Check if the sender is indeed a TapGestureRecognizer

            if (selectedMessage == null)
                return;
            if (!IsAdmin)
            {
                if (((DrMaxMuscle.Helpers.Messages)selectedMessage).ChatType == ChannelType.Group)
                {
                    CurrentLog.Instance.ReceiverEmail = ((DrMaxMuscle.Helpers.Messages)selectedMessage).NormalUSerEmail;
                    CurrentLog.Instance.ReceiverName = ((DrMaxMuscle.Helpers.Messages)selectedMessage).NormalUSerName;
                    CurrentLog.Instance.ChannelUrl = supportUrl;
                    SupportPage page = new SupportPage();
                    page.OnBeforeShow();
                    await Navigation.PushAsync(page);
                    //await PagesFactory.PushAsync<SupportPage>();
                }
                else
                {
                    ChatPage page = new ChatPage();
                    page.OnBeforeShow();
                    await Navigation.PushAsync(page);
                    //await PagesFactory.PushAsync<ChatPage>();
                }
            }
            else
            {
                if (((DrMaxMuscle.Helpers.Messages)selectedMessage).ChatType == ChannelType.Group)
                {
                    CurrentLog.Instance.ReceiverEmail = ((DrMaxMuscle.Helpers.Messages)selectedMessage).NormalUSerEmail;
                    CurrentLog.Instance.ReceiverName = ((DrMaxMuscle.Helpers.Messages)selectedMessage).NormalUSerName;
                    CurrentLog.Instance.RoomId = ((DrMaxMuscle.Helpers.Messages)selectedMessage).ChatRoomId;
                    CurrentLog.Instance.ChannelUrl = ((DrMaxMuscle.Helpers.Messages)selectedMessage).SupportChannelUrl;
                    SupportPage page = new SupportPage();
                    page.OnBeforeShow();
                    await Navigation.PushAsync(page);
                    //await PagesFactory.PushAsync<SupportPage>();
                }
                else
                {
                    ChatPage page = new ChatPage();
                    page.OnBeforeShow();
                    await Navigation.PushAsync(page);
                    //await PagesFactory.PushAsync<ChatPage>();
                }
            }
            lstView.SelectedItem = null;
        }
        catch (Exception ex)
        {

        }
    }
}
