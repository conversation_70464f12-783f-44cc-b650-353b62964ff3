﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:local="clr-namespace:DrMaxMuscle.Cells"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             x:Class="DrMaxMuscle.Screens.User.OnBoarding.MainOnboardingPage"
             Title="MainOnboardingPage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <local:BotDataTemplateSelector
        x:Key="BotTemplateSelector">
            </local:BotDataTemplateSelector>
        </ResourceDictionary>
    </ContentPage.Resources>
    <Grid>
    <Grid
    BackgroundColor="#f4f4f4"
    HorizontalOptions="FillAndExpand"
    VerticalOptions="FillAndExpand"
    x:Name="MainGrid"
    RowSpacing="1"
    Padding="2,10,2,0">
        <Grid.RowDefinitions>
            <RowDefinition
            Height="*" />
            <RowDefinition
            Height="1" />
            <RowDefinition
            x:Name="BottomViewHeight"
            Height="65" />
        </Grid.RowDefinitions>

        <StackLayout x:Name="StackMain" VerticalOptions="Start" BackgroundColor="Transparent" IsVisible="False" Opacity="0" Grid.Row="0" Margin="0,0,0,0">
            
        </StackLayout>
        <CollectionView
        Grid.Row="0"
        BackgroundColor="#f4f4f4"
        ItemTemplate="{StaticResource BotTemplateSelector}"
        x:Name="lstChats"
        VerticalOptions="FillAndExpand"
        FlowDirection="LeftToRight">
        </CollectionView>
        <!--<controls:AutoBotListView
        Grid.Row="0"
        IsOnBoarding="True"
        BackgroundColor="#f4f4f4"
        ItemTemplate="{StaticResource BotTemplateSelector}"
        ItemAppearing="Handle_ItemAppearing"
        HasUnevenRows="True"
        x:Name="lstChats"
        VerticalOptions="FillAndExpand"
        FlowDirection="LeftToRight"
        SeparatorColor="Transparent">

        </controls:AutoBotListView>-->

        <BoxView
        HorizontalOptions="FillAndExpand"
        HeightRequest="0"
        BackgroundColor="Transparent"
        Grid.Row="1" />
        <StackLayout
        Grid.Row="2"
        Margin="0,0,0,10"
        BackgroundColor="Transparent"
        VerticalOptions="EndAndExpand"
        x:Name="stackOptions" />
        <StackLayout x:Name="TermsConditionStack" HorizontalOptions="CenterAndExpand" IsVisible="false" VerticalOptions="EndAndExpand" Orientation="Vertical" >
            <Label x:Name="ByContinueAgree" HorizontalOptions="CenterAndExpand" Text="By continuing, you agree to our " FontSize="12" Style="{StaticResource LabelStyle}"/>
            <StackLayout x:Name="TermsPrivacyPolicy" HorizontalOptions="CenterAndExpand" VerticalOptions="End" Orientation="Horizontal" Spacing="0" >
                <Label x:Name="TermsOfUse" Text="terms of use" Style="{StaticResource LearnMoreText}"/>
                <Label x:Name="LblAnd" Text=" and " FontSize="12" Style="{StaticResource LabelStyle}"/>
                <Label x:Name="PrivacyPolicy" Text="privacy policy." Style="{StaticResource LearnMoreText}" Margin="0,0,0,20"/>
            </StackLayout>
            <StackLayout x:Name="TermsConditionStackBeta" HorizontalOptions="CenterAndExpand" IsVisible="false" VerticalOptions="EndAndExpand" Orientation="Horizontal" Spacing="0" >
                <Label x:Name="LblAgree" Text="You agree to our " FontSize="12" Style="{StaticResource LabelStyle}"/>
                <Label x:Name="TermsOfUseBeta" Text="terms" Style="{StaticResource LearnMoreText}"/>
                <Label x:Name="LblAndBeta" Text=" and " FontSize="12" Style="{StaticResource LabelStyle}"/>
                <Label x:Name="PrivacyPolicyBeta" Text="privacy policy." Style="{StaticResource LearnMoreText}" Margin="0,0,0,10"/>
            </StackLayout>

        </StackLayout>
    </Grid>
        <t:RightSideMasterPage Padding="0" Margin="0" x:Name="SlideMenu" IsVisible="False" HorizontalOptions="EndAndExpand" VerticalOptions="FillAndExpand"/>
    </Grid>
</ContentPage>