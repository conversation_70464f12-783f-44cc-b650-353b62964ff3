﻿using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Controls;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Message;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.Demo;
using DrMaxMuscle.Screens.Workouts;
using DrMaxMuscle.Utility;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using Maui.PancakeView;
using Newtonsoft.Json;
using Microsoft.Maui.Networking;
using RGPopup.Maui.Services;
using System.Collections.ObjectModel;
using System.Globalization;
using static DrMaxMuscle.Views.CustomPromptConfig;
using GradientStop = Microsoft.Maui.Controls.GradientStop;

namespace DrMaxMuscle.Screens.User.OnBoarding;

public partial class MainOnboardingPage : ContentPage
{
    public ObservableCollection<BotModel> BotList = new ObservableCollection<BotModel>();
    public LearnMore learnMore = new LearnMore();
    bool ManMoreMuscle = false;
    bool ManLessFat = false;
    bool ManBetterHealth = false;
    bool ManStorngerSexDrive = false;
    bool FemaleMoreEnergy = false;
    bool FemaleToned = false;
    bool IsHumanSupport = false;
    bool ShouldAnimate = false;
    bool IsBodyweightPopup = false;
    bool IsIncludeCardio = false;
    bool IsIncludeMobility = false;
    bool? IsRecommendedReminder = false;
    bool isDing = false;
    string mobilityLevel;
    bool? SetStyle = false;
    bool IsPyramid = false;
    bool IsDrop = false;
    bool? isBetaExperience = null;
    public static bool? IsRealBetaExperience = null;
    string focusText = "", mainGoal = "";
    private IFirebase _firebase;
    Picker AgePicker, olderAgePicker;
    Picker BodyweightPicker;
    Picker LevelPicker;
    Picker BodyPartPicker;
    public static bool IsMovedToLogin = false;
    MultiUnityWeight IncrementUnit = null;
    bool IsEquipment = false;
    bool IsPully = false;
    bool isDumbbells = false;
    bool IsPlates = false;
    bool IsChinupBar = false;
    bool isProcessing = false;
    bool isBetaFromFirebase = false;
    bool isUpdatePopupShown = false;

    IDisposable emailDisposible;
    IDisposable firstnameDisposible;
    IDisposable passwordDisposible;
    private int workoutDurationLenggth = 0;
    string bodypartName = "";
    int styleId = 0;
    public MainOnboardingPage()
    {
        InitializeComponent();
        lstChats.ItemsSource = BotList;
        NavigationPage.SetHasBackButton(this, false);
        Title = AppResources.DrMuslce;
        _firebase = (IFirebase)MauiProgram.ServiceProvider.GetService(typeof(IFirebase));
        this.ToolbarItems.Clear();
        var generalToolbarItem = new ToolbarItem("Buy", "menu", SlideGeneralBotActionAsync, ToolbarItemOrder.Primary, 0);
        this.ToolbarItems.Add(generalToolbarItem);
        LocalDBManager.Instance.SetDBSetting("BackgroundImage", "dr_muscle_logo.png");
        MessagingCenter.Send(this, "BackgroundImageUpdated");
        if (Device.RuntimePlatform == Device.Android)
            lstChats.VerticalOptions = LayoutOptions.EndAndExpand;
        var tapLinkTermsOfUseGestureRecognizer = new TapGestureRecognizer();
        tapLinkTermsOfUseGestureRecognizer.Tapped += (s, e) =>
        {
            //Device.OpenUri(new Uri("http://drmuscleapp.com/news/terms/"));
            Browser.OpenAsync("http://drmuscleapp.com/news/terms/", BrowserLaunchMode.SystemPreferred);
        };
        TermsOfUse.GestureRecognizers.Add(tapLinkTermsOfUseGestureRecognizer);

        var tapLinkPrivacyPolicyGestureRecognizer = new TapGestureRecognizer();
        tapLinkPrivacyPolicyGestureRecognizer.Tapped += (s, e) =>
        {
            //Device.OpenUri(new Uri("http://drmuscleapp.com/news/privacy/"));
            Browser.OpenAsync("http://drmuscleapp.com/news/privacy/", BrowserLaunchMode.SystemPreferred);
        };
        PrivacyPolicy.GestureRecognizers.Add(tapLinkPrivacyPolicyGestureRecognizer);
        var tapLinkTermsOfUseGestureRecognizerBeta = new TapGestureRecognizer();
        tapLinkTermsOfUseGestureRecognizerBeta.Tapped += (s, e) =>
        {
            //Device.OpenUri(new Uri("http://drmuscleapp.com/news/terms/"));
            Browser.OpenAsync("http://drmuscleapp.com/news/terms/", BrowserLaunchMode.SystemPreferred);
        };
        TermsOfUseBeta.GestureRecognizers.Add(tapLinkTermsOfUseGestureRecognizer);

        var tapLinkPrivacyPolicyGestureRecognizerBeta = new TapGestureRecognizer();
        tapLinkPrivacyPolicyGestureRecognizerBeta.Tapped += (s, e) =>
        {
            //Device.OpenUri(new Uri("http://drmuscleapp.com/news/privacy/"));
            Browser.OpenAsync("http://drmuscleapp.com/news/privacy/", BrowserLaunchMode.SystemPreferred);
        };
        PrivacyPolicyBeta.GestureRecognizers.Add(tapLinkPrivacyPolicyGestureRecognizer);


        // uncomment code please
        //Timer.Instance.OnTimerChange -= OnTimerChange;
        //Timer.Instance.OnTimerDone -= OnTimerDone;
        //Timer.Instance.OnTimerStop -= OnTimerStop;


    }
    private async void SlideGeneralBotActionAsync()
    {
        SlideMenu.ShowAutoBotMenu();
        var isOpen = SlideMenu.ToggleMenu();
        SlideMenu.IsVisible = await isOpen;
    }
    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        MessagingCenter.Unsubscribe<Message.BodyweightMessage>(this, "BodyweightMessage");
        MessagingCenter.Unsubscribe<Message.GoalWeightMessage>(this, "GoalWeightMessage");
        MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "GeneralMessage");
    }
    private async void LoadIntro()
    {

        App.IsIntroBack = false;


        try
        {
            App.IsIntroBack = true;
            ((App)Application.Current).displayCreateNewAccount = true;
            RegistrationPage page = new RegistrationPage();
            page.OnBeforeShow();
            App.IsDemoProgress = true;
            await Navigation.PushAsync(page);

            // Uncomment code please and revert with above

            //IntroPage1 page = new IntroPage1();
            //page.OnBeforeShow();
            //var navigation = new NavigationPage(page);
            //navigation.BackgroundColor = Colors.White;
            //navigation.BackgroundImageSource = "nav.png";
            //navigation.BarTextColor = Colors.White;
            //await Navigation.PushAsync(navigation);
        }
        catch (Exception ex)
        {

        }


    }


    private async void BodyWeightMassUnitMessage(string bodyWeight)
    {
        try
        {

            LocalDBManager.Instance.SetDBSetting("BodyWeight", new MultiUnityWeight(Convert.ToDecimal(bodyWeight, CultureInfo.InvariantCulture), LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg.ToString().ReplaceWithDot());
            await AddAnswer(bodyWeight);
            lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);

            lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);

            //SetupMainGoal();
            await AddQuestion("What is your target weight?");
            await PopupNavigation.Instance.PushAsync(new WeightGoalPopup());

        }
        catch (Exception ex)
        {

        }
    }

    private async void GoalWeightMassUnitMessage(string WeightGoal)
    {
        try
        {

            LocalDBManager.Instance.SetDBSetting("WeightGoal", new MultiUnityWeight(Convert.ToDecimal(WeightGoal, CultureInfo.InvariantCulture), LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg.ToString().ReplaceWithDot());
            await AddAnswer(WeightGoal);

            lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
            lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);


            if (new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value.Replace(",", ".").Replace("٫", "."), CultureInfo.InvariantCulture), "kg").Lb > 150)
            {
                await AddQuestion("How tall are you?");
                await PopupNavigation.Instance.PushAsync(new UserHeightView());

            }
            else
                SetupMainGoal();


        }
        catch (Exception ex)
        {

        }
    }

    private async void HandleGeneralMessage(GeneralMessage general)
    {
        try
        {
            if (!App.IsNUX)
                return;
            //App.IsAskingForMeal = true;
            if (general.PopupEnum == Enums.GeneralPopupEnum.UserHeight)
            {
                //AllergyText = general.GeneralText;
                await AddAnswer(general.GeneralText);
                LocalDBManager.Instance.SetDBSetting("Height", Config.UserHeight.ToString());
                await Task.Delay(200);
                SetupMainGoal();
            }
        }
        catch (Exception ex)
        {

        }



    }
    private async void BodyweightPicker_Unfocused(object sender, FocusEventArgs e)
    {
        try
        {
            int age = Convert.ToInt32(BodyweightPicker.SelectedItem, CultureInfo.InvariantCulture);
            LocalDBManager.Instance.SetDBSetting("BodyWeight", Convert.ToString(age));
            await AddAnswer(Convert.ToString(age));

            if (LocalDBManager.Instance.GetDBSetting("ExLevel").Value == "Exp")
            {
                LocalDBManager.Instance.SetDBSetting("workout_place", "gym");
                LocalDBManager.Instance.SetDBSetting("experience", "more3years");

                if (LocalDBManager.Instance.GetDBSetting("experience").Value == "beginner")
                    AddCardio();
                else
                    workoutPlace();
                return;
            }
            LocalDBManager.Instance.SetDBSetting("experience", "less1year");
            // await AddQuestion("Are you training at home with no equipment?");
            Device.BeginInvokeOnMainThread(() =>
            {
                lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
                lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
            });
            BeginnerSetup();

        }
        catch (Exception ex)
        {

        }
    }

    private async void AgePicker_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            // Safely convert the selected item to an integer
            if (AgePicker.SelectedItem == null)
            {
                Console.WriteLine("AgePicker selected item is null");
                return;
            }

            int age = Convert.ToInt32(AgePicker.SelectedItem, CultureInfo.InvariantCulture);
            var dbManager = LocalDBManager.Instance;

            if (dbManager == null)
            {
                Console.WriteLine("LocalDBManager instance is null");
                return;
            }

            dbManager.SetDBSetting("Age", Convert.ToString(age));
            await AddAnswer($"{age}");

            // Ensure learnMore object is not null
            if (learnMore != null)
            {
                if (age > 50)
                    learnMore.AgeDesc = $"Recovery is slower at {age}. So, I added easy days to your program.";
                else if (age > 30)
                    learnMore.AgeDesc = $"Recovery is a bit slower at {age}. So, I'm updating your program to make sure you train each muscle max 2x a week.";
                else
                    learnMore.AgeDesc = "Recovery is optimal at your age. You can train each muscle as often as 3x a week.";

                if (dbManager.GetDBSetting("MainProgram").Value.Contains("PPL"))
                {
                    if (age > 50)
                        learnMore.AgeDesc = $"Recovery is slower at {age}. So, I'm updating your program to make sure you train each muscle max 6x a week.";
                    else if (age > 30)
                        learnMore.AgeDesc = $"Recovery is a bit slower at {age}. So, I'm updating your program to make sure you train each muscle max 6x a week.";
                    else
                        learnMore.AgeDesc = "Recovery is optimal at your age. You can train each muscle as often as 6x a week.";
                }
            }

            var genderSetting = dbManager.GetDBSetting("gender");
            var gender = genderSetting?.Value == "Man" ? "Man" : "Woman";

            var xDays = 0;

            var mainProgramSetting = dbManager.GetDBSetting("MainProgram");
            if (mainProgramSetting != null)
            {
                if (mainProgramSetting.Value.Contains("PPL"))
                {
                    xDays = 6;
                }
                else if (mainProgramSetting.Value.Contains("Split"))
                {
                    if (age < 30)
                        xDays = 4;
                    else if (age >= 30 && age <= 50)
                        xDays = 4;
                    else
                        xDays = 3;
                }
                else
                {
                    if (age < 30)
                        xDays = 4;
                    else if (age >= 30 && age <= 50)
                        xDays = 3;
                    else
                        xDays = 2;
                }
            }
            else
            {
                Console.WriteLine("MainProgram setting is null");
                return;
            }

            await AddQuestion($"{gender} aged {age}? I recommend {xDays} workouts a week.");
            RecommendationProgram(xDays);
        }
        catch (Exception ex)
        {
            // Catch any unexpected exceptions and log them
            Console.WriteLine($"An error occurred: {ex.Message}");
        }
    }


    private async void BodyPartPicker_SelectedIndexChanged(object sender, EventArgs e)
    {
        bodypartName = (string)(sender as Picker).SelectedItem;
        if ((sender as Picker).SelectedIndex != -1)
        {
            await AddAnswer((string)(sender as Picker).SelectedItem);
            LocalDBManager.Instance.SetDBSetting("BodypartPriority", bodypartName);

        }

        else
            await AddAnswer("No thanks");
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        await ClearOptions();
        AddCardio();
    }

    private async void LevelPicker_Unfocused(object sender, FocusEventArgs e)
    {
        try
        {
            if (LevelPicker.SelectedIndex == -1)
                LevelPicker.SelectedIndex = 0;
            int level = LevelPicker.SelectedIndex + 1;
            LocalDBManager.Instance.SetDBSetting("MainLevel", level.ToString());
            LocalDBManager.Instance.SetDBSetting("CustomMainLevel", level.ToString());
            await AddAnswer($"Level {level}");
            //SetupMassUnit();

            AskSetStyle();

        }
        catch (Exception ex)
        {

        }
    }

    private async void AgePicker_Unfocused(object sender, FocusEventArgs e)
    {
        try
        {
            int age = Convert.ToInt32(AgePicker.SelectedItem, CultureInfo.InvariantCulture);
            LocalDBManager.Instance.SetDBSetting("Age", Convert.ToString(age));
            //        await AddAnswer(Convert.ToString(age));

            await AddAnswer($"{age}");
            if (age > 50)
                learnMore.AgeDesc = $"Recovery is slower at {age}. So, I added easy days to your program.";
            else if (age > 30)
                learnMore.AgeDesc = $"Recovery is a bit slower at {age}. So, I'm updating your program to make sure you train each muscle max 2x a week.";
            else
                learnMore.AgeDesc = "Recovery is optimal at your age. You can train each muscle as often as 3x a week.";

            if (LocalDBManager.Instance.GetDBSetting("MainProgram").Value.Contains("PPL"))
            {
                if (age > 50)
                    learnMore.AgeDesc = $"Recovery is slower at {age}. So, I'm updating your program to make sure you train each muscle max 6x a week.";
                else if (age > 30)
                    learnMore.AgeDesc = $"Recovery is a bit slower at {age}. So, I'm updating your program to make sure you train each muscle max 6x a week.";
                else
                    learnMore.AgeDesc = "Recovery is optimal at your age. You can train each muscle as often as 6x a week.";
            }
            var gender = LocalDBManager.Instance.GetDBSetting("gender").Value == "Man" ? "Man" : "Woman";
            var xDays = 0;
            if (LocalDBManager.Instance.GetDBSetting("MainProgram").Value.Contains("PPL"))
            {
                xDays = 6;
            }
            else if (LocalDBManager.Instance.GetDBSetting("MainProgram").Value.Contains("Split")) //|| 
            {
                if (age < 30)
                    xDays = 4;
                else if (age >= 30 && age <= 50)
                    xDays = 4;
                else
                    xDays = 3;
            }
            else
            {
                if (age < 30)
                    xDays = 4;
                else if (age >= 30 && age <= 50)
                    xDays = 3;
                else
                    xDays = 2;
            }
            await AddQuestion($"{gender} aged {age}? I recommend {xDays} workouts a week.");



            RecommendationProgram(xDays);

        }
        catch (Exception ex)
        {

        }
    }

    private async void ExerciseVariety()
    {
        await AddQuestion("Exercise variety?");
        var btn1 = new Button()
        {
            Text = "More exercises (can be more fun)",
            TextColor = Color.FromHex("#195377"),
            BackgroundColor = Colors.Transparent,
            HeightRequest = 55,
            BorderWidth = 2,
            BorderColor = AppThemeConstants.BlueColor,
            Margin = new Thickness(25, 2),
            CornerRadius = 0
        };
        btn1.Clicked += (o, ev) => {
            AddAnswer("More exercises (can be more fun)");
            AskSetStyle();
            LocalDBManager.Instance.SetDBSetting("ExerciseVariety", "More");
        };
        stackOptions.Children.Add(btn1);
        await AddOptions($"Fewer exercises (faster progress)", (o, ev) => {
            AddAnswer($"Fewer exercises (faster progress)");
            LocalDBManager.Instance.SetDBSetting("ExerciseVariety", "Less");
            AskSetStyle();
        });


    }

    private async void RecommendationProgram(int xDays)
    {
        var btn1 = new Button()
        {
            Text = "Another schedule",
            TextColor = Color.FromHex("#195377"),
            BackgroundColor = Colors.Transparent,
            HeightRequest = 55,
            BorderWidth = 2,
            BorderColor = AppThemeConstants.BlueColor,
            Margin = new Thickness(25, 2),
            CornerRadius = 0
        };
        btn1.Clicked += (o, ev) => {
            AddAnswer("Another schedule");
            ShowWorkoutReminder(true);
        };
        stackOptions.Children.Add(btn1);

        await AddOptions($"Recommended {xDays}x/week", (o, ev) => {
            AddAnswer($"Recommended {xDays}x/week");
            ShowWorkoutReminder(false);
            IsRecommendedReminder = true;
        });
    }

    public void OnBeforeShow()
    {
        try
        {
            if (Device.RuntimePlatform == Device.Android)
            {

                var styles = (IStyles)MauiProgram.ServiceProvider.GetService(typeof(IStyles));
                styleId = styles.GetStyleId(EAlertStyles.AlertDialogCustomGray);
            }

            App.IsNUX = true;
            CurrentLog.Instance.IsMovingOnBording = false;
            try
            {
                this.ToolbarItems.Clear();
                var generalToolbarItem = new ToolbarItem("Buy", "menu", SlideGeneralBotActionAsync, ToolbarItemOrder.Primary, 0);
                this.ToolbarItems.Add(generalToolbarItem);
            }
            catch (Exception ex)
            {

            }
            IsMovedToLogin = false;

            var age = Enumerable.Range(10, 115).Select(i => i.ToString()).ToList();
            var level = Enumerable.Range(1, 7).Select(i => i.ToString()).ToList();

            List<string> bodyweight = new List<string>();
            
            if (AgePicker != null)
            {
                AgePicker.Unfocused -= AgePicker_Unfocused;
                olderAgePicker = AgePicker;
                if (Device.RuntimePlatform == Device.Android)
                    AgePicker.SelectedIndexChanged -= AgePicker_SelectedIndexChanged;
            }

            AgePicker = new Picker()
            {

                Title = "Age?"
            };
            AgePicker.VerticalOptions = LayoutOptions.Center;
            AgePicker.HorizontalOptions = LayoutOptions.Center;
            AgePicker.BackgroundColor = Colors.Transparent;
            AgePicker.Margin = new Thickness(20);
            //AgePicker.HeightRequest = 1;
            AgePicker.ItemsSource = age;
            AgePicker.SelectedItem = "35";
            AgePicker.Unfocused += AgePicker_Unfocused;
            if (Device.RuntimePlatform == Device.Android)
                AgePicker.SelectedIndexChanged += AgePicker_SelectedIndexChanged;


            //if (LevelPicker != null)
            //    LevelPicker.Unfocused -= LevelPicker_Unfocused;

            //LevelPicker = new Picker()
            //{
            //    Title = "Select level"
            //};
            //LevelPicker.ItemsSource = level;
            //LevelPicker.SelectedIndex = 0;
            //LevelPicker.Unfocused += LevelPicker_Unfocused;



            if (BodyweightPicker != null)
                BodyweightPicker.Unfocused -= BodyweightPicker_Unfocused;
            BodyweightPicker = new Picker()
            {
                Title = "what is your body weight?"
            };
            BodyweightPicker.ItemsSource = bodyweight;
            BodyweightPicker.SelectedItem = "160";
            BodyweightPicker.Unfocused += BodyweightPicker_Unfocused;
            StackMain.Children.Insert(0, AgePicker);
            //StackMain.Children.Insert(0, BodyweightPicker);
            //StackMain.Children.Insert(0, LevelPicker);
            //AgePicker.IsVisible = true;
            //BodyweightPicker.IsVisible = true;
            //LevelPicker.IsVisible = true;
            if (Device.RuntimePlatform == Device.iOS)
                StackMain.IsVisible = false;
            if (App.IsIntro)
            {
                App.IsIntro = false;
                App.IsIntroBack = false;
                Features_Clicked();
            }
            else if (!App.IsDemoProgress)
                StartSetup();
            else
            {
                if (BotList.Count == 1)
                    BotList.Clear();
                SetUpRestOnboarding();
            }

        }
        catch (Exception ex)
        {

        }
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        MessagingCenter.Subscribe<Message.BodyweightMessage>(this, "BodyweightMessage", async (obj) =>
        {
            IsBodyweightPopup = false;
            BodyWeightMassUnitMessage(obj.BodyWeight);

        });
        MessagingCenter.Subscribe<Message.GoalWeightMessage>(this, "GoalWeightMessage", async (obj) =>
        {
            IsBodyweightPopup = false;
            GoalWeightMassUnitMessage(obj.WeightGoal);

        });
        MessagingCenter.Subscribe<Message.GeneralMessage>(this, "GeneralMessage", (obj) =>
        {
            IsBodyweightPopup = false;
            HandleGeneralMessage(obj);
        });
        try
        {
            _firebase.SetScreenName("onboarding_account");
            if (Device.RuntimePlatform.Equals(Device.Android))
            {
                DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1351);
                var dt = DateTime.Now.AddMinutes(1);
                var timeSpan = new TimeSpan(0, dt.Hour, dt.Minute, 0);// DateTime.Now.AddMinutes(2) - DateTime.Now;////
                DependencyService.Get<IAlarmAndNotificationService>().ScheduleNotification("Dr. Muscle", "Oops! You're 12 seconds away from custom, smart workouts", timeSpan, 1351, NotificationInterval.Week);
                // Uncomment code please
                //DependencyService.Get<INotificationRequestService>().RequestNotificationRequest();
            }

            DBSetting dbOnBoardingSeen = LocalDBManager.Instance?.GetDBSetting("onboarding_features");
            if ((dbOnBoardingSeen == null || dbOnBoardingSeen?.Value == "false") && !isUpdatePopupShown)
            {
                isUpdatePopupShown = true;
                var waitHandle2 = new EventWaitHandle(false, EventResetMode.AutoReset);
                var modalPage2 = new Views.RGGeneralPopup("truestate.png", "July update 2!", "✓ Timer between exercises\n✓ More stable app\n✓ Muscle Machine program\n⮕ Soon: Autostart timer\n", "Continue", null, false, false, "false", "false", "false", "false", "false", "true");                // uncomment code please
                modalPage2.Closed += (sender2, e2) =>
                {
                    waitHandle2.Set();
                };
                await Application.Current.MainPage.ShowPopupAsync(modalPage2);

                await Task.Run(() => waitHandle2.WaitOne());
            }
            LocalDBManager.Instance.SetDBSetting("onboarding_features", "true");
        }
        catch (Exception ex)
        {

        }
    }


    protected override bool OnBackButtonPressed()
    {
        // uncomment code please
        if (IsBodyweightPopup)
            return true;
        ((App)Application.Current).displayCreateNewAccount = true;
        //PagesFactory.PushAsync<WelcomePage>();
        return true;

    }
    private async Task ClearOptions()
    {
        //if (Device.RuntimePlatform.Equals(Device.iOS))
        //{
        //    stackOptions.Children.Clear();
        //    return;
        //}
        var count = stackOptions.Children?.Count;
        for (var i = 0; i < count; i++)
        {
            stackOptions.Children.RemoveAt(0);
        }
        BottomViewHeight.Height = 65;
    }
    async Task StartSetup()
    {
        try
        {
            //StackSignupMenu.IsVisible = false;
            try
            {

                if (olderAgePicker != null && olderAgePicker.IsFocused)
                    Device.BeginInvokeOnMainThread(() => { olderAgePicker?.Unfocus(); });

            }
            catch (Exception ex)
            {

            }
            BotList.Clear();
            await ClearOptions();

            //#if DEBUG
            //                SetTimerOption();
            //                return;
            //#endif

            IsPlates = false;
            isDumbbells = false;
            IsPully = false;
            IsEquipment = false;
            IsChinupBar = false;
            SetStyle = false;
            IncrementUnit = null;
            IsPyramid = false;
            IsDrop = false;
            bodypartName = "";
            var welcomeNote = "";
            var time = DateTime.Now.Hour;
            if (time < 12)
                welcomeNote = AppResources.GoodMorning;
            else if (time < 18)
                welcomeNote = AppResources.GoodAfternoon;
            else
                welcomeNote = AppResources.GoodEvening;

            if (BotList.Count == 0)
            {
                BotList.Add(new BotModel()
                {
                    Question = "Welcome to Dr. Muscle! I'm Dr. Carl Juneau and I'll help you get in shape fast with smart, custom workouts.",
                    Type = BotType.Ques
                });
            }
            else
                return;
            ////


            await Task.Delay(2500);
            if (BotList.Count == 1)
            {
                BotList.Add(new BotModel()
                {
                    Question = "Why trust me? I've been a coach for 17 years and a trainer for the Canadian Forces.",
                    Type = BotType.Ques
                });
            }
            else
                return;
            if (BotList.Count == 1 || BotList.Count > 2)
                return;

            await Task.Delay(2500);
            if (BotList.Count == 2)
            {
                BotList.Add(new BotModel()
                {
                    Question = $"",
                    Answer = "",
                    Type = BotType.Photo
                });
            }
            else
                return;

            await ClearOptions();

            SetNotifications();
            await Task.Delay(2500);

            if (IsMovedToLogin)
                return;
            if (BotList.Count == 3)
            {
                BotList.Add(new BotModel()
                {
                    Question = "With this app, you get me as your coach—without the expensive fees. Are you...",
                    Type = BotType.Ques
                });
            }
            else
                return;
            if (Application.Current.MainPage.Navigation.ModalStack?.Count == 0)
            {
                lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
                lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
            }
            else
            {
                lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
                lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
                await Task.Delay(700);
                lstChats.ScrollTo(BotList.First(), position: ScrollToPosition.MakeVisible, animate: false);
                lstChats.ScrollTo(BotList.First(), position: ScrollToPosition.Start, animate: false);
            }

            await Task.Delay(1000);
            if (BotList.Count < 3)
                return;
            await ClearOptions();
            var btn = await AddOptionsWithMediumEmphase("New to lifting weights", async (ss, ee) =>
            {
                SetNotifications();
                ShouldAnimate = false;
                _firebase.LogEvent("start_onboarding", "new_to_training");
                _firebase.LogEvent("new_to_training", "");
                LocalDBManager.Instance.SetDBSetting("CustomExperience", "new to training");
                await AddAnswer("New to lifting weights");
                //if (Device.RuntimePlatform.Equals(Device.Android))
                //    await Task.Delay(300);
                //await ClearOptions();



                //await AddQuestion("Congrats! New users like you get 34% stronger in 30 days. This app makes it easy:");

                //BotList.Add(new BotModel() { Type = BotType.Empty });
                //lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
                //lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);

                LocalDBManager.Instance.SetDBSetting("ExLevel", "New");
                LocalDBManager.Instance.SetDBSetting("NewLevel", "All");
                if (LocalDBManager.Instance.GetDBSetting("experience")?.Value != "")
                    LocalDBManager.Instance.SetDBSetting("experience", "");
                //await Task.Delay(2000);
                //BotList.Remove(BotList.Last());
                if (BotList.Count == 5)
                {
                    if (LocalDBManager.Instance.GetDBSetting("FirstStepCompleted")?.Value != "true")
                    {
                        LoadIntro();
                    }
                    else
                    {
                        SetUpRestOnboarding();
                    }

                    //lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
                    //lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);


                    //await Task.Delay(2000);
                    //await AddOptions(AppResources.GotIt, GotItAfterExperienceLevel);

                }
            });
            Device.BeginInvokeOnMainThread(async () =>
            {
                Grid grid = (Grid)btn.Parent;
                ShouldAnimate = true;
                animate(grid);

            });
            var btn1 = await AddOptionsWithMediumEmphase("Returning after a break", async (ss, ee) =>
            {
                SetNotifications();
                ShouldAnimate = false;
                LocalDBManager.Instance.SetDBSetting("CustomExperience", "returning from a break");

                _firebase.LogEvent("start_onboarding", "returning_after_a_break");
                _firebase.LogEvent("returning_after_a_break", "");
                if (Device.RuntimePlatform.Equals(Device.Android))
                    await Task.Delay(300);
                await AddAnswer("Returning after a break");
                //if (Device.RuntimePlatform.Equals(Device.Android))
                //    await Task.Delay(300);
                if (LocalDBManager.Instance.GetDBSetting("experience")?.Value != "")
                    LocalDBManager.Instance.SetDBSetting("experience", "");
                //ClearOptions();
                //await AddQuestion("This app can help you get back in shape and stay on track:");
                //BotList.Add(new BotModel() { Type = BotType.Empty });
                //lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
                //lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
                LocalDBManager.Instance.SetDBSetting("ExLevel", "Return");

                // await AddOptions(AppResources.GotIt, GotItAfterExperienceLevel);

                //await Task.Delay(2000);
                //BotList.Remove(BotList.Last());
                if (BotList.Count == 5)
                {
                    if (LocalDBManager.Instance.GetDBSetting("FirstStepCompleted")?.Value != "true")
                    {



                        //lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
                        //lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
                        //await Task.Delay(2000);

                        LoadIntro();
                    }
                    else
                        SetUpRestOnboarding();
                }
            });
            Device.BeginInvokeOnMainThread(async () =>
            {
                Grid grid = (Grid)btn1.Parent;
                ShouldAnimate = true;
                animate(grid);

            });

            var btn2 = await AddOptions("Already lifting weights", async (ss, ee) =>
            {
                SetNotifications();
                ShouldAnimate = false;
                LocalDBManager.Instance.SetDBSetting("CustomExperience", "an active, experienced lifter");
                _firebase.LogEvent("start_onboarding", "Active_experienced_lifter");
                _firebase.LogEvent("Active_experienced_lifter", "");
                if (Device.RuntimePlatform.Equals(Device.Android))
                    await Task.Delay(300);
                await AddAnswer("Already lifting weights");


                LocalDBManager.Instance.SetDBSetting("ExLevel", "Exp");
                if (BotList.Count == 5)
                {
                    if (LocalDBManager.Instance.GetDBSetting("FirstStepCompleted")?.Value != "true")
                    {
                        LoadIntro();
                    }
                    else
                        SetUpRestOnboarding();
                }
            });
            Device.BeginInvokeOnMainThread(async () =>
            {
                Grid grid = (Grid)btn2.Parent;
                ShouldAnimate = true;
                animate(grid);

            });
            //SetupMassUnit();
        }
        catch (Exception ex)
        {

        }
    }



    private async void Features_Clicked()
    {
        try
        {

            ClearOptions();

            string cweight = "", todayweight = "", liftedweight = "";
            LocalDBManager.Instance.SetDBSetting("massunit", "lb");

            if (LocalDBManager.Instance.GetDBSetting("FirstStepCompleted")?.Value != "true")
                SetMenu();
            else
                SetUpRestOnboarding();
            if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
            {
                cweight = "95 kg";
                todayweight = "120 kg";
                liftedweight = "242 kg";
            }
            else
            {
                cweight = "210 lbs";
                todayweight = "265 lbs";
                liftedweight = "535 lbs";
            }
            if (LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value == "new to training")
            {
                if (BotList.Count < 4)
                    return;

                BotList.Add(new BotModel()
                {
                    Part1 = "User reviews",
                    Part2 = "\"AI is great and makes it very easy\"",
                    Part3 = "\"Easy for me to know how many reps to do and how much weight to lift. No more guessing. This really is something different.\"",
                    Answer = "MKJ&MKJ",
                    Source = "",
                    Type = BotType.ReviewFullCell
                });
                if (Device.RuntimePlatform.Equals(Device.Android))
                    await Task.Delay(300);
                lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);

            }
            else if (LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value == "returning from a break")
            {
                if (BotList.Count < 4)
                    return;
                BotList.Add(new BotModel()
                {
                    Part1 = "User reviews",
                    Part2 = "\"Gained 10 lbs\"",
                    Part3 = "\"Have been in and out of the gym for a few years with modest gains, however, this app helped me gain 10 lbs and become significantly more defined. Very easy to use.\"",
                    Answer = "Potero2122",
                    Source = "",
                    Type = BotType.ReviewFullCell
                });
                if (Device.RuntimePlatform.Equals(Device.Android))
                    await Task.Delay(300);
                lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);

            }
            else
            {
                if (BotList.Count < 4)
                    return;
                BotList.Add(new BotModel()
                {
                    Part1 = "Expert reviews",
                    Part2 = $"\"I was never that heavy ({cweight})\"",
                    Part3 = $"\"My strength on hip trusts exploded from something like {todayweight} to {liftedweight}. The app has scientific algorithms... simple stupid and effective wether its raining or sunshine, just follow the app and don't overthink to much.\"",
                    Answer = "Jonas Notter, World Natural Bodybuilding Champion",
                    Source = "jonus.png",
                    Type = BotType.ReviewFullCell
                });
                if (Device.RuntimePlatform.Equals(Device.Android))
                    await Task.Delay(300);
                lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);

            }

            if (Device.RuntimePlatform.Equals(Device.Android))
                await Task.Delay(300);
            if (BotList.Count < 4)
                return;
            var botModel = new BotModel()
            {
                Question = "Sign in to customize your program:\n+ Save plan & get by email\n+ Never lose your progress\n+ Train on any device",
                Type = BotType.Ques
            };
            BotList.Add(botModel);


        }
        catch (Exception ex)
        {

        }
    }
    async Task AskforProgramsWithABExperience()
    {
        string val = LocalDBManager.Instance.GetDBSetting("BetaVersion")?.Value;
        if (!string.IsNullOrEmpty(val))
        {
            //if (val == "Beta")
            //{
            RecommendedProgram_clicked(new Button(), EventArgs.Empty);
            //}
            //else
            //{
            //    if (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "less1year")
            //        await AddQuestion("Less than 1 year? I recommend a full-body program.");
            //    else if (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "1-3years")
            //        await AddQuestion("1-3 years? I recommend an upper/lower-body split program.");
            //    else
            //        await AddQuestion("3+ years? I recommend an upper/lower-body split program.");

            //    AskForProgram();
            //}
        }
        else
        {
            //if (isBetaExperience == true)
            //{

            RecommendedProgram_clicked(new Button(), EventArgs.Empty);

            //}
            //else
            //{                    
            //    if (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "less1year")
            //        await AddQuestion("Less than 1 year? I recommend a full-body program.");
            //    else if (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "1-3years")
            //        await AddQuestion("1-3 years? I recommend an upper/lower-body split program.");
            //    else
            //        await AddQuestion("3+ years? I recommend an upper/lower-body split program.");
            //    AskForProgram();
            //}
        }


    }
    async Task AskforProgramsWithAB()
    {
        RecommendedProgram_clicked(new Button(), EventArgs.Empty);

    }
    private async void SetUpRestOnboarding()
    {
        try
        {


            ClearOptions();

            //BotList.Add(new BotModel()
            //{
            //    Question = "Congrats—you smashed the demo!",
            //    Type = BotType.Ques
            //});
            //lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
            //lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
            //await Task.Delay(1000);


            if (LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value == "new to training")
            {
                //Since you are [new to training / returning after a break], I recommend you train your full body 2-4 times a week for best results.

                LocalDBManager.Instance.SetDBSetting("MainProgram", "Full body");
                AskforProgramsWithAB();
            }
            else if (LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value == "returning from a break")
            {
                //Since you are [new to training / returning after a break], I recommend you train your full body 2-4 times a week for best results.

                LocalDBManager.Instance.SetDBSetting("MainProgram", "Split body");
                AskforProgramsWithAB();
            }
            else
            {
                NoAdvancedClicked(new Button(), EventArgs.Empty);
            }
            //    BotList.Add(new BotModel()
            //{
            //    Question = "This app is new. Features like smart watch integration and calendar view are not yet available. But if you’re an early adopter who wants to get in shape fast, you'll love your new custom workouts. Give us a shot: we release new features every month and we'll treat your feedback like gold.",
            //    Type = BotType.Ques
            //});
            //lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
            //lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
            //await Task.Delay(2500);

            //GotItAfterImage(new Button(), EventArgs.Empty);
        }
        catch (Exception e)
        {

        }
    }



    async void GotItAfterExperienceLevel(object sender, EventArgs e)
    {

        await AddAnswer("Start demo");
        ShouldAnimate = false;
        ClearOptions();
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);

        //SetupMassUnit();
        //Move to demo


    }

    private void SetNotifications()
    {
        if (Device.RuntimePlatform.Equals(Device.iOS))
        {
            CancelNotification();
            var dt = DateTime.Now.AddMinutes(5);
            var timeSpan = new TimeSpan(DateTime.Now.AddMinutes(5).Day - DateTime.Now.Day, dt.Hour, dt.Minute, 0);// DateTime.Now.AddMinutes(2) - DateTime.Now;////
            DependencyService.Get<IAlarmAndNotificationService>().ScheduleNotification("Dr. Muscle", "Oops! You're 12 seconds away from custom, smart workouts", timeSpan, 1351, NotificationInterval.Week);
        }
        else
        {
            // Uncomment code please
            //DependencyService.Get<INotificationRequestService>().RequestNotificationRequest();
        }

    }

    private void SetTrialUserNotifications()
    {
        try
        {
            CancelNotification();
            var fName = LocalDBManager.Instance.GetDBSetting("firstname")?.Value??"";
            var dt = DateTime.Now.AddDays(2);
            var timeSpan = new TimeSpan(2, dt.Hour, dt.Minute, 0);// new TimeSpan(DateTime.Now.AddMinutes(10).Day - DateTime.Now.Day, dt.Hour, dt.Minute, 0);////
            DependencyService.Get<IAlarmAndNotificationService>().ScheduleOnceNotification("Dr. Muscle", $"{fName}, you can do this!", timeSpan, 1451);

            var dt1 = DateTime.Now.AddDays(4);
            var timeSpan1 = new TimeSpan(4, dt1.Hour, dt1.Minute, 0);// new TimeSpan(DateTime.Now.AddMinutes(15).Day - DateTime.Now.Day, dt1.Hour, dt1.Minute, 0);////
            DependencyService.Get<IAlarmAndNotificationService>().ScheduleOnceNotification("Dr. Muscle", Device.RuntimePlatform.Equals(Device.Android) ? $"New users like you improve 34% in 30 days" : $"New users like you improve 34%% in 30 days", timeSpan1, 1551);

            var dt2 = DateTime.Now.AddDays(10);
            var timeSpan2 = new TimeSpan(10, dt2.Hour, dt2.Minute, 0);// new TimeSpan(DateTime.Now.AddMinutes(20).Day - DateTime.Now.Day, dt2.Hour, dt2.Minute, 0);////
            DependencyService.Get<IAlarmAndNotificationService>().ScheduleOnceNotification("Dr. Muscle", $"You're 12 seconds away from custom, smart workouts", timeSpan2, 1651);

        }
        catch (Exception ex)
        {

        }
    }

    private void CancelNotification()
    {
        DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1051);
        DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1151);
        DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1251);
        DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1351);
        DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1451);
        DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1551);
        DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1651);

    }
    async void animate(Grid grid)
    {
        try
        {
            if (Battery.EnergySaverStatus == EnergySaverStatus.On && Device.RuntimePlatform.Equals(Device.Android))
                return;
            var a = new Animation();
            a.Add(0, 0.5, new Animation((v) =>
            {
                grid.Scale = v;
            }, 1.0, 0.8, Easing.CubicInOut, () => { System.Diagnostics.Debug.WriteLine("ANIMATION A"); }));
            a.Add(0.5, 1, new Animation((v) =>
            {
                grid.Scale = v;
            }, 0.8, 1.0, Easing.CubicInOut, () => { System.Diagnostics.Debug.WriteLine("ANIMATION B"); }));
            a.Commit(grid, "animation", 16, 2000, null, (d, f) =>
            {
                grid.Scale = 1.0;
                if (ShouldAnimate)
                {
                    ShouldAnimate = false; // Optional: To prevent continuous looping
                    
                    animate(grid);
                }
            });

        }
        catch (Exception ex)
        {
            ShouldAnimate = false;
        }
    }

    async void GotItAfterImage(object sender, EventArgs e)
    {
        //await AddAnswer("Hi Carl");
        await AddQuestion("Man or woman?");
        await Task.Delay(300);
        SetupGender();

    }
    void SetDefaultButtonStyle(Button btn)
    {
        btn.BackgroundColor = Colors.Transparent;
        btn.BorderWidth = 0;
        btn.CornerRadius = 0;
        btn.Margin = new Thickness(25, 2, 25, 2);
        btn.FontAttributes = FontAttributes.Bold;
        btn.BorderColor = Colors.Transparent;
        btn.TextColor = Colors.White;
        btn.HeightRequest = 55;

    }

    void SetEmphasisButtonStyle(Button btn)
    {
        btn.TextColor = Colors.White;
        btn.BackgroundColor = Colors.Transparent;
        btn.Margin = new Thickness(25, 2, 25, 2);
        btn.HeightRequest = 55;
        btn.BorderWidth = 6;
        btn.CornerRadius = 0;
        btn.FontAttributes = FontAttributes.Bold;
    }

    async void YesButton_Clicked(object sender, EventArgs e)
    {

        await AddAnswer(AppResources.Yes);

        await AddQuestion(AppResources.DoYouUseLbsOrKgs, false);

        //Yes-No Button
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        await AddOptions(AppResources.Man, ManButton_Clicked);
        await AddOptions(AppResources.Woman, WomanButton_Clicked);


    }

    async void NoButton_Clicked(object sender, EventArgs e)
    {
        //Move back
        ((App)Application.Current).displayCreateNewAccount = true;
        App.Current.MainPage = new NavigationPage(new WelcomePage());
        //await PagesFactory.PushAsync<WelcomePage>();
    }

    async void ManButton_Clicked(object sender, EventArgs e)
    {

        BotList.Add(new BotModel()
        {
            Answer = AppResources.Man,
            Type = BotType.Ans
        });

        await ClearOptions();


        LocalDBManager.Instance.SetDBSetting("gender", "Man");
        //await AddQuestion("Men often want (tap all that apply):");
        //await Task.Delay(300);

        //await AddCheckbox("More muscle", Man_MoreMuscle_Clicked);
        //await AddCheckbox("Less fat", Man_LessFat_Clicked);
        //await AddCheckbox("Better health", Man_BetterHealth_Clicked);
        //await AddCheckbox("Stronger sex drive", Man_StorngerSexDrive_Clicked);

        //await AddOptions("Continue", ManTakeActionBasedOnInput);
        //SetupMassUnit();
        //SetupMainGoal();
        await AddQuestion("Age?");
        //if (Device.RuntimePlatform.Equals(Device.Android))
        //    await Task.Delay(300);
        GetAge();
        return;

    }

    private async void SetupMainGoal()
    {

        try
        {



            ManLessFat = false;
            ManBetterHealth = false;
            FemaleMoreEnergy = false;
            FemaleToned = false;
            ManMoreMuscle = false;
            ManStorngerSexDrive = false;

            var IsWoman = LocalDBManager.Instance.GetDBSetting("gender").Value == "Woman";
            if (IsWoman)
            {

                await AddQuestion("What are your goals?");

                await AddCheckbox("Less fat", Man_LessFat_Clicked);
                await AddCheckbox("Better health", Man_BetterHealth_Clicked);
                await AddCheckbox("More energy", WoMan_MoreEnergy_Clicked);
                await AddCheckbox("Toned muscles", WoMan_FemaleToned_Clicked);

                await AddOptions("Continue", WomanTakeActionBasedOnInput);
            }
            else
            {
                await AddQuestion("What are your goals?");
                await Task.Delay(300);

                await AddCheckbox("More muscle", Man_MoreMuscle_Clicked);
                await AddCheckbox("Less fat", Man_LessFat_Clicked);
                await AddCheckbox("Better health", Man_BetterHealth_Clicked);
                await AddCheckbox("Stronger sex drive", Man_StorngerSexDrive_Clicked);

                await AddOptions("Continue", ManTakeActionBasedOnInput);
            }
        }
        catch (Exception ex)
        {

        }
    }
    private async void GetMainGoalAction(PromptResult response)
    {
        try
        {

            mainGoal = null;

            if (string.IsNullOrEmpty(response.Text))
            {
                SetupMainGoal();
                return;
            }
            else
            {
                mainGoal = response.Text.ToLower();
                await AddAnswer(response.Text);
                LocalDBManager.Instance.SetDBSetting("PopupMainGoal", mainGoal);
            }
            //GotItAfterImage(new Button(), EventArgs.Empty);
            var IsWoman = LocalDBManager.Instance.GetDBSetting("gender").Value == "Woman";
            if (IsWoman)
            {

                await AddQuestion("What are your goals?");

                await AddCheckbox("Less fat", Man_LessFat_Clicked);
                await AddCheckbox("Better health", Man_BetterHealth_Clicked);
                await AddCheckbox("More energy", WoMan_MoreEnergy_Clicked);
                await AddCheckbox("Toned muscles", WoMan_FemaleToned_Clicked);

                await AddOptions("Continue", WomanTakeActionBasedOnInput);
            }
            else
            {
                await AddQuestion("What are your goals?");
                await Task.Delay(300);

                await AddCheckbox("More muscle", Man_MoreMuscle_Clicked);
                await AddCheckbox("Less fat", Man_LessFat_Clicked);
                await AddCheckbox("Better health", Man_BetterHealth_Clicked);
                await AddCheckbox("Stronger sex drive", Man_StorngerSexDrive_Clicked);

                await AddOptions("Continue", ManTakeActionBasedOnInput);
            }

        }
        catch (Exception ex)
        {

        }

    }

    void Man_MoreMuscle_Clicked(object sender, EventArgs e)
    {
        ManMoreMuscle = !ManMoreMuscle;
        Image img = (Image)((StackLayout)sender).Children[0];
        img.Source = ManMoreMuscle ? "done.png" : "undone.png";
    }

    void WoMan_MoreEnergy_Clicked(object sender, EventArgs e)
    {
        FemaleMoreEnergy = !FemaleMoreEnergy;
        Image img = (Image)((StackLayout)sender).Children[0];
        img.Source = FemaleMoreEnergy ? "done.png" : "undone.png";
    }

    void WoMan_FemaleToned_Clicked(object sender, EventArgs e)
    {
        FemaleToned = !FemaleToned;
        Image img = (Image)((StackLayout)sender).Children[0];
        img.Source = FemaleToned ? "done.png" : "undone.png";
    }

    void Man_LessFat_Clicked(object sender, EventArgs e)
    {
        ManLessFat = !ManLessFat;
        Image img = (Image)((StackLayout)sender).Children[0];
        img.Source = ManLessFat ? "done.png" : "undone.png";
    }

    void Man_BetterHealth_Clicked(object sender, EventArgs e)
    {
        ManBetterHealth = !ManBetterHealth;
        Image img = (Image)((StackLayout)sender).Children[0];
        img.Source = ManBetterHealth ? "done.png" : "undone.png";
    }

    void Man_StorngerSexDrive_Clicked(object sender, EventArgs e)
    {
        ManStorngerSexDrive = !ManStorngerSexDrive;
        Image img = (Image)((StackLayout)sender).Children[0];
        img.Source = ManStorngerSexDrive ? "done.png" : "undone.png";
    }
    async void ManTakeActionBasedOnInput(object sender, EventArgs e)
    {
        try
        {
            var count = 0;
            count += ManMoreMuscle ? 1 : 0;
            count += ManLessFat ? 1 : 0;
            count += ManBetterHealth ? 1 : 0;
            count += ManStorngerSexDrive ? 1 : 0;
            var responseText = "";
            if (ManMoreMuscle)
                responseText = "More muscle";
            if (ManLessFat)
                responseText += responseText == "" ? "Less fat" : "\nLess fat";
            if (ManBetterHealth)
                responseText += responseText == "" ? "Better health" : "\nBetter health";
            if (ManStorngerSexDrive)
                responseText += responseText == "" ? "Stronger sex drive" : "\nStronger sex drive";
            if (responseText != "")
                await AddAnswer(responseText);
            focusText = responseText;
            LocalDBManager.Instance.SetDBSetting("focusText", focusText);
            _firebase.LogEvent("chose_goals", focusText);
            if (ManMoreMuscle && ManLessFat)//&& count > 2
            {
                LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscleBurnFat");
                LocalDBManager.Instance.SetDBSetting("Demoreprange", "BuildMuscleBurnFat");
                LocalDBManager.Instance.SetDBSetting("repsminimum", "8");
                LocalDBManager.Instance.SetDBSetting("repsmaximum", "15");
                //await AddQuestion("Got it. You can build muscle 59% faster with rest-pause sets. I'm adding them to your program. High reps burn more fat.");


            }
            else if (ManMoreMuscle)
            {
                LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscle");
                LocalDBManager.Instance.SetDBSetting("Demoreprange", "BuildMuscle");
                LocalDBManager.Instance.SetDBSetting("repsminimum", "5");
                LocalDBManager.Instance.SetDBSetting("repsmaximum", "12");
                // await AddQuestion("Got it. You can build muscle 59% faster with rest-pause sets. Adding them to your program...");

            }
            else if (ManLessFat)
            {
                LocalDBManager.Instance.SetDBSetting("reprange", "FatBurning");
                LocalDBManager.Instance.SetDBSetting("Demoreprange", "FatBurning");
                LocalDBManager.Instance.SetDBSetting("repsminimum", "12");
                LocalDBManager.Instance.SetDBSetting("repsmaximum", "20");

                // await AddQuestion("OK. High reps burn more fat. I'm setting yours at 12-20.");

            }
            else if (ManBetterHealth || ManStorngerSexDrive)
            {
                LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscleBurnFat");
                LocalDBManager.Instance.SetDBSetting("Demoreprange", "BuildMuscleBurnFat");

                LocalDBManager.Instance.SetDBSetting("repsminimum", "8");
                LocalDBManager.Instance.SetDBSetting("repsmaximum", "15");
                //await AddQuestion("Got it.");

            }
            else
                return;
            if (ManLessFat && ManMoreMuscle)
                LocalDBManager.Instance.SetDBSetting("Demoreprange", "BuildMuscleBurnFat");

            //if (ManLessFat)
            //    FatLossOption();
            //else
            AskForHumanSupport();

        }
        catch (Exception ex)
        {

        }

    }

    async void TakeBodyWeight()
    {

        try
        {
            //BotList.Add(new BotModel()
            //{ Type = BotType.Empty });
            await AddQuestion("What is your body weight?");
            var IsWoman = LocalDBManager.Instance.GetDBSetting("gender").Value == "Woman";

            if (IsWoman)
            {

                BodyweightPicker.SelectedItem = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "140" : "65";
            }
            else
                BodyweightPicker.SelectedItem = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "180" : "80";
            BodyweightPicker.IsVisible = true;
            Device.BeginInvokeOnMainThread(() =>
            {
                lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
                lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
            });
            try
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    BodyweightPicker.Focus();
                });
            }
            catch (Exception ex)
            {

            }

        }
        catch (Exception ex)
        {

        }

    }

    async void WomanTakeActionBasedOnInput(object sender, EventArgs e)
    {
        try
        {
            var count = 0;
            count += FemaleMoreEnergy ? 1 : 0;
            count += ManLessFat ? 1 : 0;
            count += ManBetterHealth ? 1 : 0;
            count += FemaleToned ? 1 : 0;

            var responseText = "";
            if (ManLessFat)
                responseText = "Less fat";
            if (ManBetterHealth)
                responseText += responseText == "" ? "Better health" : "\nBetter health";
            if (FemaleMoreEnergy)
                responseText += responseText == "" ? "More energy" : "\nMore energy";
            if (FemaleToned)
                responseText += responseText == "" ? "Toned muscles" : "\nToned muscles";
            if (responseText != "")
                await AddAnswer(responseText);
            focusText = responseText;
            LocalDBManager.Instance.SetDBSetting("focusText", focusText);
            _firebase.LogEvent("chose_goals", focusText);
            if (FemaleToned && ManLessFat) //&& count > 2
            {
                LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscleBurnFat");
                LocalDBManager.Instance.SetDBSetting("Demoreprange", "BuildMuscleBurnFat");
                LocalDBManager.Instance.SetDBSetting("repsminimum", "8");
                LocalDBManager.Instance.SetDBSetting("repsmaximum", "15");

                // await AddQuestion("Got it. You can build muscle 59% faster with rest-pause sets. I'm adding them to your program. High reps burn more fat.");

            }
            else if (FemaleToned)
            {
                LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscle");
                LocalDBManager.Instance.SetDBSetting("Demoreprange", "BuildMuscle");
                LocalDBManager.Instance.SetDBSetting("repsminimum", "5");
                LocalDBManager.Instance.SetDBSetting("repsmaximum", "12");
                //await AddQuestion("Got it. You can tone up 59% faster with rest-pause sets. Adding them to your program...");


            }
            else if (ManLessFat)
            {
                LocalDBManager.Instance.SetDBSetting("reprange", "FatBurning");
                LocalDBManager.Instance.SetDBSetting("Demoreprange", "FatBurning");
                LocalDBManager.Instance.SetDBSetting("repsminimum", "12");
                LocalDBManager.Instance.SetDBSetting("repsmaximum", "20");
                //await AddQuestion("OK. High reps burn more fat. I'm setting yours at 12-20.");

            }
            else if (ManBetterHealth || FemaleMoreEnergy)
            {
                LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscleBurnFat");
                LocalDBManager.Instance.SetDBSetting("Demoreprange", "BuildMuscleBurnFat");
                LocalDBManager.Instance.SetDBSetting("repsminimum", "8");
                LocalDBManager.Instance.SetDBSetting("repsmaximum", "15");
                //await AddQuestion("Got it.");

            }
            else
                return;
            if (ManLessFat && FemaleToned)
                LocalDBManager.Instance.SetDBSetting("Demoreprange", "BuildMuscleBurnFat");
            //if (ManLessFat)
            //    FatLossOption();
            //else
            AskForHumanSupport();

        }
        catch (Exception ex)
        {

        }
    }

    async void FatLossOption()
    {
        try
        {
            await ClearOptions();
            if (DeviceInfo.Platform == DevicePlatform.Android)
                await Task.Delay(300);
            await AddOptions("Yes", async (o, ee) =>
            {
                await AddAnswer("Yes");
                if (DeviceInfo.Platform == DevicePlatform.Android)
                    await Task.Delay(300);

                //await AddQuestion("According to the International Society of Sports Nutrition, \"A wide range of dietary approaches can be similarly effective for improving body composition.\" In other words, you don’t need to follow a specific diet (e.g. low fat or low carb). Instead, the key is finding the one that works best for you, and that you can stick to long-term.");
                await AddQuestion("Great! Look for an email from my assistant Victoria Kingsley within one business day.");
                await AddOptions(AppResources.GotIt, async (oo, eee) =>
                {
                    await AddAnswer(AppResources.GotIt);
                    if (DeviceInfo.Platform == DevicePlatform.Android)
                        await Task.Delay(300);
                    IsHumanSupport = true;
                    SetupEpeBegginer();
                });
            });

            await AddOptions("No", async (o, ee) =>
            {
                await AddAnswer("No");
                if (DeviceInfo.Platform == DevicePlatform.Android)
                    await Task.Delay(300);
                if (ManLessFat)
                    SetupEpeBegginer();
                else
                    AskForHumanSupport();
            });

        }
        catch (Exception ex)
        {

        }
    }
    async void AskForHumanSupport()
    {
        IsHumanSupport = false;
        SetupEpeBegginer();
    }

    async void SetupEpeBegginer()
    {
        try
        {
            if (LocalDBManager.Instance.GetDBSetting("ExLevel")?.Value == "Exp")
            {
                LocalDBManager.Instance.SetDBSetting("workout_place", "gym");
                if (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "beginner")
                    AddCardio();// 
                else
                    workoutPlace();
                return;
            }
            LocalDBManager.Instance.SetDBSetting("experience", "less1year");
            // await AddQuestion("Are you training at home with no equipment?");
            MainThread.BeginInvokeOnMainThread(() =>
            {
                lstChats?.ScrollTo(BotList?.LastOrDefault(), position: ScrollToPosition.MakeVisible, animate: false);
                lstChats?.ScrollTo(BotList?.LastOrDefault(), position: ScrollToPosition.End, animate: false);
            });
        }
        catch (Exception ex)
        {

        }
        BeginnerSetup();
    }
    async void MenNext_Clicked(object sender, EventArgs e)
    {

    }

    async void WomanButton_Clicked(object sender, EventArgs e)
    {
        BotList.Add(new BotModel()
        {
            Answer = AppResources.Woman,
            Type = BotType.Ans
        });
        await Task.Delay(300);
        await ClearOptions();
        LocalDBManager.Instance.SetDBSetting("gender", "Woman");

        //await AddQuestion("Woman often want (tap all that apply):");

        //await AddCheckbox("Less fat", Man_LessFat_Clicked);
        //await AddCheckbox("Better health", Man_BetterHealth_Clicked);
        //await AddCheckbox("More energy", WoMan_MoreEnergy_Clicked);
        //await AddCheckbox("Toned muscles", WoMan_FemaleToned_Clicked);

        //await AddOptions("Continue", WomanTakeActionBasedOnInput);
        //SetupMassUnit();
        await AddQuestion("Age?");
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        GetAge();
        //SetupMainGoal();

    }

    async void WomanThinClicked(object sender, System.EventArgs e)
    {

        await AddAnswer(((Button)sender).Text);

        await AddQuestion(AppResources.ThinWomenOftenSay);
        await AddQuestion(AppResources.TheyWantToGetFitAndStrongWhileMaintaingLeanPhysiqueAddSizeToLegsBootyDenseLookingMuscleOverall, false);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        await AddOptions(AppResources.Yes, WomanYesSkinnyClicked);
        await AddOptions(AppResources.NoChooseOtherGoal, WomanOtherGoalClicked);
    }

    async void WomanYesSkinnyClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscle");
        LocalDBManager.Instance.SetDBSetting("repsminimum", "5");
        LocalDBManager.Instance.SetDBSetting("repsmaximum", "12");
        await AddAnswer(((Button)sender).Text);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        await AddQuestion(AppResources.GotItAreYouABeginnerWithNoEquipment);

        BeginnerSetup();
    }

    async void WomanOtherGoalClicked(object sender, System.EventArgs e)
    {

        await AddAnswer(((Button)sender).Text);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        await AddQuestion(AppResources.PleaseChooseAGoal);
        await AddQuestion(AppResources.DontWorryLiftingWightsWontMakeyouBulky);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        await AddOptions(AppResources.FocusOnToningUp, WomanFocusMuscleClicked);
        await AddOptions(AppResources.ToneUpAndSlimDown, WomanFocusBothClicked);
        await AddOptions(AppResources.FocusOnSlimmingDown, WomanFocusBurnFatClicked);
    }

    async void WomanFocusMuscleClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscle");
        LocalDBManager.Instance.SetDBSetting("repsminimum", "5");
        LocalDBManager.Instance.SetDBSetting("repsmaximum", "12");
        await AddAnswer(((Button)sender).Text);

        BeginnerSetup();
    }

    async void WomanFocusBothClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscleBurnFat");
        LocalDBManager.Instance.SetDBSetting("repsminimum", "8");
        LocalDBManager.Instance.SetDBSetting("repsmaximum", "15");
        await AddAnswer(((Button)sender).Text);
        BeginnerSetup();
    }

    async void WomanFocusBurnFatClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("reprange", "FatBurning");
        LocalDBManager.Instance.SetDBSetting("repsminimum", "12");
        LocalDBManager.Instance.SetDBSetting("repsmaximum", "20");
        await AddAnswer(((Button)sender).Text);
        BeginnerSetup();
    }

    async void WomanMidsizeClicked(object sender, System.EventArgs e)
    {

        await AddAnswer(((Button)sender).Text);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        await AddQuestion(AppResources.MidsizeWomenOftenSay);
        await AddQuestion(AppResources.TheyWantToGetFitAndStrongLeanerAndComfortableInMyBody);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        await AddOptions(AppResources.Yes, WomanYesMidsizeClicked);
        await AddOptions(AppResources.NoChooseOtherGoal, WomanOtherGoalClicked);
    }

    //Start from here
    async void WomanYesMidsizeClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscleBurnFat");
        LocalDBManager.Instance.SetDBSetting("repsminimum", "8");
        LocalDBManager.Instance.SetDBSetting("repsmaximum", "15");
        await AddAnswer(((Button)sender).Text);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        await AddQuestion(AppResources.GotItAreYouABeginnerWithNoEquipment);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        BeginnerSetup();
    }


    async void WomanFullFClicked(object sender, System.EventArgs e)
    {
        await AddAnswer(((Button)sender).Text);

        await AddQuestion(AppResources.FullFiguredOften);
        await AddQuestion(AppResources.HaveAHardTimeLosingWeightGetFatLookingAtFood);

        await AddOptions(AppResources.YesICanGainWeightEasily, FullFClicked);
        await AddOptions(AppResources.NoIDontGainWeightThatEasily, FullFClicked);
    }

    async void FullFClicked(object sender, System.EventArgs e)
    {
        await AddAnswer(((Button)sender).Text);

        await AddQuestion(AppResources.ThankYouTitle);
        await AddQuestion(AppResources.FullFiguredWomenAlsoOftenSay);
        await AddQuestion(AppResources.TheyWantToGetFitAndStrongWhileDroppingBodyFatShapeArms);

        await AddOptions(AppResources.Yes, WomanYesBigClicked);
        await AddOptions(AppResources.NoChooseOtherGoal, WomanOtherGoalClicked);
    }

    async void WomanYesBigClicked(object sender, System.EventArgs e)
    {
        await AddAnswer(((Button)sender).Text);
        LocalDBManager.Instance.SetDBSetting("reprange", "FatBurning");
        LocalDBManager.Instance.SetDBSetting("repsminimum", "12");
        LocalDBManager.Instance.SetDBSetting("repsmaximum", "20");

        await AddQuestion(AppResources.BurningFatGotItAreYouBegginerWithNoEquipment);
        BeginnerSetup();
    }

    async void SkinnyManButton_Clicked(object sender, EventArgs e)
    {
        await AddAnswer(((Button)sender).Text);

        await AddQuestion(AppResources.SkinnyMenOften);
        await AddQuestion(AppResources.HaveAHardTimeGainingWeightSomeSayIEatConstantlyAndWorkMyButtOff);

        await ClearOptions();

        await AddOptions(AppResources.YesIHaveAHardTimeGaining, ManBodyTypeClicked);
        await AddOptions(AppResources.NoIDontHaveAHardTime, ManBodyTypeClicked);
        await AddOptions(AppResources.NotSureIveNeverLiftedBefore, ManBodyTypeClicked);

    }
    async void BigManButton_Clicked(object sender, EventArgs e)
    {

        await AddAnswer(((Button)sender).Text);

        await AddQuestion(AppResources.BigMenOftenSay);
        await AddQuestion(AppResources.TheyWantToGetRidOfThisBodyFatAndLoseMyGut);

        await AddOptions(AppResources.Yes, ManYesBigClicked);
        await AddOptions(AppResources.NoChooseOtherGoal, ManOtherGoalClicked);

    }

    async void ManYesBigClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("reprange", "FatBurning");
        LocalDBManager.Instance.SetDBSetting("repsminimum", "12");
        LocalDBManager.Instance.SetDBSetting("repsmaximum", "20");
        await AddAnswer(((Button)sender).Text);

        await AddQuestion(AppResources.BurningFatGotItAreYouBegginerWithNoEquipment);
        BeginnerSetup();
    }
    async void MidsizeManButton_Clicked(object sender, EventArgs e)
    {

        await AddAnswer(((Button)sender).Text);
        await AddQuestion(AppResources.MidsizeMenOftenSay);
        await AddQuestion(AppResources.TheyWantToGetFitStrongAndMoreMuscularGainLeanMassAndHaveAVisibleSetOf);

        await AddOptions(AppResources.Yes, ManYesMidsizeClicked);
        await AddOptions(AppResources.NoChooseOtherGoal, ManOtherGoalClicked);
    }

    async void ManYesMidsizeClicked(object sender, System.EventArgs e)
    {
        await AddAnswer(((Button)sender).Text);
        LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscleBurnFat");
        LocalDBManager.Instance.SetDBSetting("repsminimum", "8");
        LocalDBManager.Instance.SetDBSetting("repsmaximum", "15");
        await AddQuestion(AppResources.BuildingMuscleBuriningFatGotItAreYouBeginner);
        BeginnerSetup();
    }


    async void ManBodyTypeClicked(object sender, System.EventArgs e)
    {

        await AddAnswer(((Button)sender).Text);

        await AddQuestion(AppResources.GotIt);
        await AddQuestion(AppResources.SkinnyMenAlsoOftenSay);
        await AddQuestion(AppResources.TheyWantToPutOnLeanMassWhileKeepingmyAbsDefinedGainHealthy);

        await AddOptions(AppResources.Yes, ManYesSkinnyClicked);
        await AddOptions(AppResources.NoChooseOtherGoal, ManOtherGoalClicked);

    }

    async void ManYesSkinnyClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscle");
        LocalDBManager.Instance.SetDBSetting("repsminimum", "5");
        LocalDBManager.Instance.SetDBSetting("repsmaximum", "12");

        await AddAnswer(((Button)sender).Text);
        await AddQuestion(AppResources.BuildingMuscleGotItAreYouABeginnerWithNoEquipment);
        BeginnerSetup();
    }

    [Obsolete]
    async void ManOtherGoalClicked(object sender, System.EventArgs e)
    {
        try
        {
            await AddAnswer(((Button)sender).Text);

            BotList.Add(new BotModel()
            {
                Question = AppResources.DontWorryYouCanCustomizeLater,
                Type = BotType.Ques
            });
            lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
            await Task.Delay(300);
            BotList.Add(new BotModel()
            {
                Question = AppResources.PleaseChooseAGoal,
                Type = BotType.Ques
            });

            await Task.Delay(300);

            await ClearOptions();
            var manBurnFatButton = new Button()
            {
                Text = AppResources.FocusOnBurningFat,
                TextColor = Colors.White
            };
            manBurnFatButton.Clicked += ManFocusBurnFatClicked;
            SetDefaultButtonStyle(manBurnFatButton);
            var grid = new Grid();

            var gradientBrush = new LinearGradientBrush();
            gradientBrush.GradientStops.Add(new GradientStop(Color.FromHex("#0C2432"), (float)0.0));
            gradientBrush.GradientStops.Add(new GradientStop(Color.FromHex("#195276"), (float)1.0));

            var frame = new Frame();
            frame.Background = gradientBrush;
            frame.HeightRequest = 50;
            frame.Margin = new Thickness(25, 2);



            //var pancakeView = new PancakeView() { HeightRequest = 50, Margin = new Thickness(25, 2) };
            //pancakeView.OffsetAngle = 45;
            //pancakeView.BackgroundGradientStops.Add(new PancakeView.GradientStop { Color = Color.FromHex("#0C2432"), Offset = 0 });
            //pancakeView.BackgroundGradientStops.Add(new PancakeView.GradientStop { Color = Color.FromHex("#195276"), Offset = 1 });
            grid.Children.Add(frame);
            grid.Children.Add(manBurnFatButton);

            stackOptions.Children.Add(grid);
            lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
            await Task.Delay(300);

            var manBuildMuscleBurnFatButton = new Button()
            {
                Text = AppResources.BuildMuscleAndBurnFat,
                TextColor = Colors.White
            };
            manBuildMuscleBurnFatButton.Clicked += ManFocusBothClicked;
            SetDefaultButtonStyle(manBuildMuscleBurnFatButton);
            stackOptions.Children.Add(manBuildMuscleBurnFatButton);
            lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
            await Task.Delay(300);

            var manBuildMuscleButton = new Button()
            {
                Text = AppResources.FocusOnBuildingMuscle,
                TextColor = Colors.White,
            };
            manBuildMuscleButton.Clicked += ManFocusMuscleClicked;
            SetDefaultButtonStyle(manBuildMuscleButton);

            stackOptions.Children.Add(manBuildMuscleButton);
            await Task.Delay(300);
            lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);

        }
        catch (Exception ex)
        {

        }
    }

    async void BeginnerSetup()
    {
        try
        {
            await ClearOptions();

            if (DeviceInfo.Platform == DevicePlatform.Android)
                await Task.Delay(300);
            //await AddOptions("At home, no equipment", YesBeginnerClicked);
            //await AddOptions("Gym or home gym", async (sender, e) => {

            LocalDBManager.Instance.SetDBSetting("experience", "less1year");

            //await AddAnswer(((Button)sender).Text);
            if (DeviceInfo.Platform == DevicePlatform.Android)
                await Task.Delay(300);
            if (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "beginner")
                AddCardio();// 
            else
                workoutPlace();
            //});

        }
        catch (Exception ex)
        {

        }
    }

    async void ManFocusMuscleClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscle");
        LocalDBManager.Instance.SetDBSetting("repsminimum", "5");
        LocalDBManager.Instance.SetDBSetting("repsmaximum", "12");

        await AddAnswer(AppResources.FocusOnBuildingMuscle);

        BeginnerSetup();

    }

    async void ManFocusBothClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscleBurnFat");
        LocalDBManager.Instance.SetDBSetting("repsminimum", "8");
        LocalDBManager.Instance.SetDBSetting("repsmaximum", "15");

        await AddAnswer(AppResources.BuildMuscleAndBurnFat);
        BeginnerSetup();

    }

    async void ManFocusBurnFatClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("reprange", "FatBurning");
        LocalDBManager.Instance.SetDBSetting("repsminimum", "12");
        LocalDBManager.Instance.SetDBSetting("repsmaximum", "20");

        await AddAnswer(AppResources.FocusOnBurningFat);
        BeginnerSetup();
    }

    async void YesBeginnerClicked(object sender, System.EventArgs e)
    {
        await ClearOptions();
        LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscleBurnFat");
        LocalDBManager.Instance.SetDBSetting("repsminimum", "8");
        LocalDBManager.Instance.SetDBSetting("repsmaximum", "15");
        LocalDBManager.Instance.SetDBSetting("experience", "beginner");
        LocalDBManager.Instance.SetDBSetting("workout_place", "homeBodyweightOnly");


        await AddAnswer(((Button)sender).Text);

        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        await AddQuestion("Age?");
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        GetAge();
    }


    async void AddGotItBeforeAge()
    {
        try
        {
            await ClearOptions();
            await AddOptions(AppResources.GotIt, GotIt_Clicked);
            async void GotIt_Clicked(object sender, EventArgs e)
            {

                await AddAnswer(((Button)sender).Text);
                if (Device.RuntimePlatform.Equals(Device.Android))
                    await Task.Delay(300);
                await AddQuestion("Age?");
                if (Device.RuntimePlatform.Equals(Device.Android))
                    await Task.Delay(300);
                GetAge();
            }

        }
        catch (Exception ex)
        {

        }
    }
    async void NoAdvancedClicked(object sender, System.EventArgs e)
    {
        //await AddAnswer(((Button)sender).Text);
        //if (Device.RuntimePlatform.Equals(Device.Android))

        //    await Task.Delay(300);
        await AddQuestion("Experience lifting weights?");
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        await ClearOptions();
        await AddOptions(AppResources.LessThan1Year, LessOneYearClicked);
        await AddOptions(AppResources.OneToThreeYears, OneThreeYearsClicked);
        await AddOptions(AppResources.MoreThan3Years, More3YearsClicked);
    }

    async void AskForProgram()
    {
        ClearOptions();
        var btn1 = new Button()
        {
            Text = "Another program",
            TextColor = Color.FromHex("#195377"),
            BackgroundColor = Colors.Transparent,
            HeightRequest = 55,
            BorderWidth = 2,
            BorderColor = AppThemeConstants.BlueColor,
            Margin = new Thickness(25, 2),
            CornerRadius = 0
        };
        btn1.Clicked += AnotherProgram_clicked;
        stackOptions.Children.Add(btn1);

        await AddOptions("Recommended program", RecommendedProgram_clicked);
    }
    async void AnotherProgram_clicked(object sender, EventArgs args)
    {
        //[Full body, 2-4x/week]
        //[Split body, 3-5x/week]
        await AddAnswer("Another program");
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        ClearOptions();
        await AddOptions("Full-body (2-4 workouts / week)", (o, e) => {
            LocalDBManager.Instance.SetDBSetting("MainProgram", "Full body");
            LocalDBManager.Instance.SetDBSetting("CustomProgramName", "Full-body");
            GetGender();
        });

        await AddOptions("Upper/lower (3-4 / week)", (o, e) => {
            LocalDBManager.Instance.SetDBSetting("MainProgram", "Split body");
            LocalDBManager.Instance.SetDBSetting("CustomProgramName", "Up/Low Split");
            GetGender();
        });

        await AddOptions("Push/pull/legs (6 / week)", (o, e) =>
        {
            LocalDBManager.Instance.SetDBSetting("MainProgram", "PPL");
            LocalDBManager.Instance.SetDBSetting("CustomProgramName", "Push/Pull/Legs");
            GetGender();
        });

        await AddOptions("Powerlifting (2-4 / week)", (o, e) =>
        {
            LocalDBManager.Instance.SetDBSetting("MainProgram", "Powerlifting");
            LocalDBManager.Instance.SetDBSetting("CustomProgramName", "Powerlifting");
            GetGender();
        });

        await AddOptions("Bands only (2-4 / week)", (o, e) =>
        {
            LocalDBManager.Instance.SetDBSetting("MainProgram", "Bands only");
            LocalDBManager.Instance.SetDBSetting("CustomProgramName", "Buffed w/ Bands");
            GetGender();
        });

        await AddOptions("Bodyweight only (2-4 / week)", (o, e) =>
        {
            LocalDBManager.Instance.SetDBSetting("MainProgram", "Bodyweight");
            LocalDBManager.Instance.SetDBSetting("CustomProgramName", "Bodyweight");
            var level = 3;
            if (LocalDBManager.Instance.GetDBSetting("CustomExperience").Value == "new to training" || LocalDBManager.Instance.GetDBSetting("CustomExperience").Value == "returning from a break")
            {
                level = 2;
            }

            else
            {
                level = 3;
            }
            LocalDBManager.Instance.SetDBSetting("MainLevel", level.ToString());
            GetGender();
        });

    }

    async void ShowWorkoutReminder(bool isShowReminder)
    {
        LocalDBManager.Instance.SetDBSetting("Registring", "true");
        // uncomment code please
        if (isShowReminder)
            await PopupNavigation.Instance.PushAsync(new ReminderPopup());
        ClearOptions();




        string ProgramLabel = "";
        string programInfo = "";
        int level = 2;
        int age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age").Value);


        if (LocalDBManager.Instance.GetDBSetting("MainProgram").Value.Contains("Split"))
        {

            if (age > 50)
            {
                ProgramLabel = "Up/Low Split Level 6";
                programInfo = "This level includes A/B/C easy workouts to help you recover.";
                level = 6;
            }
            else
            //if (age > 30)
            {
                if (LocalDBManager.Instance.GetDBSetting("experience") != null && (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "more3years"))
                {
                    ProgramLabel = "Up/Low Split Level 3";
                    programInfo = "This level includes new and harder exercises.";
                    level = 3;
                }
                else if (LocalDBManager.Instance.GetDBSetting("experience") != null && (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "1-3years"))
                {
                    ProgramLabel = "Up/Low Split Level 2";
                    programInfo = "This level includes simple and effective exercises.";
                    level = 2;
                }
                else
                {
                    ProgramLabel = "Full-Body Level 2";
                    programInfo = "This level includes new and harder exercises.";
                    level = 2;
                }

            }
        }
        else if (LocalDBManager.Instance.GetDBSetting("MainProgram").Value.Contains("PPL"))
        {
            ProgramLabel = "Push/Pull/Legs Level 2";
            programInfo = "This level includes simple and effective exercises.";
            level = 2;
        }
        else if (LocalDBManager.Instance.GetDBSetting("MainProgram").Value.Contains("Powerlifting"))
        {

            if (age > 50)
            {
                ProgramLabel = "Powerlifting Level 2";
                programInfo = "This level includes simple and effective exercises.";
                level = 2;
            }
            else
            //if (age > 30)
            {
                if (LocalDBManager.Instance.GetDBSetting("experience") != null && (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "more3years"))
                {
                    ProgramLabel = "Powerlifting Level 3";
                    programInfo = "This level includes new and harder exercises.";
                    level = 3;
                }
                else if (LocalDBManager.Instance.GetDBSetting("experience") != null && (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "1-3years"))
                {
                    ProgramLabel = "Powerlifting Level 2";
                    programInfo = "This level includes simple and effective exercises.";
                    level = 2;
                }
                else
                {
                    ProgramLabel = "Powerlifting Level 3";
                    programInfo = "This level includes new and harder exercises.";
                    level = 3;
                }

            }
        }
        else if (LocalDBManager.Instance.GetDBSetting("MainProgram").Value.Contains("Bands only"))
        {

            if (age > 50)
            {
                ProgramLabel = "Buffed w/ Bands Level 3";
                programInfo = "This level includes simple and effective exercises.";
                level = 3;
            }
            else
            {
                if (LocalDBManager.Instance.GetDBSetting("experience") != null && (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "more3years"))
                {
                    ProgramLabel = "Buffed w/ Bands Level 3";
                    programInfo = "This level includes A/B workouts to help you recover.";
                    level = 3;
                }
                else if (LocalDBManager.Instance.GetDBSetting("experience") != null && (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "1-3years"))
                {
                    ProgramLabel = "Buffed w/ Bands Level 2";
                    programInfo = "This level includes simple and effective exercises.";
                    level = 2;
                }
                else
                {
                    ProgramLabel = "Buffed w/ Bands Level 3";
                    programInfo = "This level includes A/B workouts to help you recover.";
                    level = 3;
                }

            }

        }
        else if (LocalDBManager.Instance.GetDBSetting("MainProgram").Value.Contains("Bodyweight"))
        {

            //if (age > 50)
            //{
            //    ProgramLabel = "Bodyweight Level 1";
            //    programInfo = "This level includes simple and effective exercises.";
            //    level = 2;
            //}
            //else
            ////if (age > 30)
            //{
            if (LocalDBManager.Instance.GetDBSetting("experience") != null && (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "more3years") || (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "1-3years"))
            {
                ProgramLabel = "Bodyweight Level 3";
                programInfo = "This level includes new and harder exercises.";
                level = 3;
            }

            else
            {
                ProgramLabel = "Bodyweight Level 2";
                programInfo = "This level includes simple and effective exercises.";
                level = 2;
            }

            if (LocalDBManager.Instance.GetDBSetting("CustomExperience").Value == "new to training" || LocalDBManager.Instance.GetDBSetting("CustomExperience").Value == "returning from a break")
            {
                level = 2;
            }

            else
            {
                level = 3;
            }
            LocalDBManager.Instance.SetDBSetting("MainLevel", level.ToString());

            //}
        }
        else
        {
            if (age > 50)
            {
                ProgramLabel = "Full-Body Level 6";
                programInfo = "This level includes A/B/C easy workouts to help you recover.";
                level = 6;
            }
            else
            //if (age > 30)
            {
                ProgramLabel = "Full-Body Level 1";
                programInfo = "This level includes simple and effective exercises.";
                level = 2;
            }
        }


        LocalDBManager.Instance.SetDBSetting("MainLevel", level.ToString());
        LocalDBManager.Instance.SetDBSetting("Registring", "");

        ExerciseVariety();
    }

    private async void AskSetStyle()
    {
        try
        {


            await ClearOptions();
            await AddQuestion("Try rest-pause sets? They're harder, but make your workouts 59% faster.");


            var btn1 = new Button()
            {
                Text = "Normal sets",
                TextColor = Color.FromHex("#195377"),
                BackgroundColor = Colors.Transparent,
                HeightRequest = 55,
                BorderWidth = 2,
                BorderColor = AppThemeConstants.BlueColor,
                Margin = new Thickness(25, 2),
                CornerRadius = 0
            };
            btn1.Clicked += (o, ev) => {
                AddAnswer("Normal sets");
                SetStyle = true;
                SetupMassUnit();
            };
            stackOptions.Children.Add(btn1);
            if (LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value == "new to training" || LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value == "returning from a break")
            { }
            else
            {


                var btnPyramid = new Button()
                {
                    Text = "Pyramid sets",
                    TextColor = Color.FromHex("#195377"),
                    BackgroundColor = Colors.Transparent,
                    HeightRequest = 55,
                    BorderWidth = 2,
                    BorderColor = AppThemeConstants.BlueColor,
                    Margin = new Thickness(25, 2),
                    CornerRadius = 0
                };
                btnPyramid.Clicked += (o, ev) => {
                    AddAnswer("Pyramid sets");
                    SetStyle = false;
                    IsPyramid = true;
                    SetupMassUnit();
                };
                stackOptions.Children.Add(btnPyramid);

                var btn2 = new Button()
                {
                    Text = "Reverse pyramid sets",
                    TextColor = Color.FromHex("#195377"),
                    BackgroundColor = Colors.Transparent,
                    HeightRequest = 55,
                    BorderWidth = 2,
                    BorderColor = AppThemeConstants.BlueColor,
                    Margin = new Thickness(25, 2),
                    CornerRadius = 0
                };
                btn2.Clicked += (o, ev) => {
                    AddAnswer("Reverse pyramid sets");
                    SetStyle = null;
                    SetupMassUnit();
                };
                stackOptions.Children.Add(btn2);
            }
            await AddOptions("Rest-pause sets", (ss, ee) =>
            {
                AddAnswer("Rest-pause sets");
                SetStyle = false;
                SetupMassUnit();
            });

        }
        catch (Exception ex)
        {

        }
    }

    async void GetGender()
    {

        ClearOptions();
        try
        {

            if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("firstname")?.Value))
            {
                await AddQuestion("Enter first name");
                if (Device.RuntimePlatform.Equals(Device.iOS))
                {
                    lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
                    lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
                }
                GetFirstName();
            }
            else
            {
                GotItAfterImage(new Button(), EventArgs.Empty);
            }
        }
        catch (Exception ex)
        {


        }
    }

    async void RecommendedProgram_clicked(object sender, EventArgs args)
    {

        try
        {

            if ((LocalDBManager.Instance.GetDBSetting("experience") != null && (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "more3years") || (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "1-3years")) || (LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value == "returning from a break"))//
            {
                LocalDBManager.Instance.SetDBSetting("MainProgram", "Split body");
                LocalDBManager.Instance.SetDBSetting("CustomProgramName", "Split body");
            }
            else
            {
                LocalDBManager.Instance.SetDBSetting("CustomProgramName", "Full-body");
                LocalDBManager.Instance.SetDBSetting("MainProgram", "Full body");
            }

        }
        catch (Exception ex)
        {
            LocalDBManager.Instance.SetDBSetting("CustomProgramName", "Full-body");
            LocalDBManager.Instance.SetDBSetting("MainProgram", "Full body");
        }
        string email = LocalDBManager.Instance.GetDBSetting("email")?.Value;
        string loginType = LocalDBManager.Instance.GetDBSetting("LoginType")?.Value;
        if (loginType == "Email")
        {
            bool isEmailExist = await checkEmailExist(email);
            if (isEmailExist == false)
            {
                RegisterAccount();
                GetGender();
            }
            else if (isEmailExist == true && LocalDBManager.Instance.GetDBSetting("isAccountCreatedInBackground")?.Value == "true")
            {
                GetGender();
            }
            else
            {
                CustomPromptConfig customPromptConfig = new CustomPromptConfig("Email already in use", "", "Use another email", AppResources.LogIn, "Use another email or log into your existing account.");
                await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);
                customPromptConfig.ActionSelected += async (sender1, action) =>
                {
                    try
                    {
                        if (action == PopupAction.OK)
                        {
                            GetEmail();
                        }
                        else
                        {
                            ((App)Application.Current).displayCreateNewAccount = true;

                            WelcomePage page = new WelcomePage();
                            page.OnBeforeShow();
                            await Navigation.PushAsync(page);
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                };
                //ConfirmConfig ShowAlertPopUp = new ConfirmConfig()
                //{
                //    Title = "Email already in use",
                //    Message = "Use another email or log into your existing account.",
                //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //    OkText = "Use another email",
                //    CancelText = AppResources.LogIn,

                //};
                //var actionOk = await UserDialogs.Instance.ConfirmAsync(ShowAlertPopUp);
                //if (actionOk)
                //{
                //    GetEmail();
                //}
                //else
                //{
                //    ((App)Application.Current).displayCreateNewAccount = true;

                //    WelcomePage page = new WelcomePage();
                //    page.OnBeforeShow();
                //    await Navigation.PushAsync(page);
                //    // await PagesFactory.PushAsync<WelcomePage>();
                //}
            }
        }
        else
        {
            GetGender();
        }
    }
    public async void RegisterAccount()
    {
        int? workoutId = null;
        int? programId = null;
        int? remainingWorkout = null;
        var WorkoutInfo2 = "";

        //SignUp here
        RegisterModel registerModel = new RegisterModel();

        //Revert it
        string FirstName = "";
        registerModel.Firstname = FirstName;
        registerModel.EmailAddress = LocalDBManager.Instance.GetDBSetting("email")?.Value;
        registerModel.MassUnit = "lb";
        registerModel.BodyWeight = new MultiUnityWeight(150, "lb");
        registerModel.Password = LocalDBManager.Instance.GetDBSetting("password")?.Value;
        registerModel.ConfirmPassword = LocalDBManager.Instance.GetDBSetting("password")?.Value;

        // API call 1
        try
        {
            BooleanModel registerResponse = await DrMuscleRestClient.Instance.RegisterUserBeforeDemo(registerModel);
            if (registerResponse.Result)
            {
                _firebase.LogEvent("account_created", "");
            }
        }
        catch (Exception ex)
        {
            // Handle exceptions
        }

        // API call 2
        try
        {
            LoginSuccessResult lr = await DrMuscleRestClient.Instance.LoginWithoutLoader(new LoginModel()
            {
                Username = registerModel.EmailAddress,
                Password = registerModel.Password
            });
            LocalDBManager.Instance.SetDBSetting("isAccountCreatedInBackground", "true");
            if (lr != null && !string.IsNullOrEmpty(lr.access_token))
            {


                DateTime current = DateTime.Now;

                UserInfosModel uim = await DrMuscleRestClient.Instance.GetUserInfoWithoutLoader(); ;

                LocalDBManager.Instance.SetDBSetting("email", uim.Email);
                LocalDBManager.Instance.SetDBSetting("firstname", !string.IsNullOrEmpty(uim.Firstname) ? ((uim.Firstname.Contains(' ')) ? uim.Firstname.Split(' ')[0] : uim.Firstname) : uim.Firstname);
                LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
                //LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
                LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
                LocalDBManager.Instance.SetDBSetting("password", registerModel.Password);
                LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
                LocalDBManager.Instance.SetDBSetting("token_expires_date", current.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
                LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
                LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
                LocalDBManager.Instance.SetDBSetting("reprangeType", uim.ReprangeType.ToString());
                LocalDBManager.Instance.SetDBSetting("repsminimum", Convert.ToString(uim.RepsMinimum));
                LocalDBManager.Instance.SetDBSetting("repsmaximum", Convert.ToString(uim.RepsMaximum));
                LocalDBManager.Instance.SetDBSetting("onboarding_seen", "true");
                LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                LocalDBManager.Instance.SetDBSetting("timer_vibrate", uim.IsVibrate ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_sound", uim.IsSound ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_123_sound", uim.IsTimer321 ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_reps_sound", uim.IsRepsSound ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_autostart", uim.IsAutoStart ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_autoset", uim.IsAutomatchReps ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_fullscreen", uim.IsFullscreen ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("QuickMode", uim.IsQuickMode == true ? "true" : uim.IsQuickMode == null ? "null" : "false");                //if (uim.ReminderTime != null)

                if (uim.WarmupsValue != null)
                {
                    LocalDBManager.Instance.SetDBSetting("warmups", Convert.ToString(uim.WarmupsValue));
                }
                if (uim.Increments != null)
                    LocalDBManager.Instance.SetDBSetting("workout_increments", uim.Increments.Kg.ToString().ReplaceWithDot());
                if (uim.Max != null)
                    LocalDBManager.Instance.SetDBSetting("workout_max", uim.Max.Kg.ToString().ReplaceWithDot());
                if (uim.Min != null)
                    LocalDBManager.Instance.SetDBSetting("workout_min", uim.Min.Kg.ToString().ReplaceWithDot());
                if (uim.BodyWeight != null)
                {
                    LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString().ReplaceWithDot());
                }
                if (uim.WeightGoal != null)
                {
                    LocalDBManager.Instance.SetDBSetting("WeightGoal", uim.WeightGoal.Kg.ToString().ReplaceWithDot());
                }
            }
            else
            {
            }
        }
        catch (Exception ex)
        {
        }

    }
    async void LessOneYearClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("experience", "less1year");

        await AddAnswer(((Button)sender).Text);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);

        //await AddQuestion("Less than 1 year? I recommend a full-body program.");
        AskforProgramsWithABExperience();
        LocalDBManager.Instance.SetDBSetting("MainProgram", "Full body");
        //RecommendedProgram_clicked(sender, e);

    }

    async void OneThreeYearsClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("experience", "1-3years");

        await AddAnswer(((Button)sender).Text);
        //await AddQuestion("1-3 years? I recommend an upper/lower-body split program.");
        LocalDBManager.Instance.SetDBSetting("MainProgram", "Split body");
        AskforProgramsWithABExperience();
        //RecommendedProgram_clicked(sender, e);
        //await AddQuestion("How old are you?");
        //if (Device.RuntimePlatform.Equals(Device.Android))
        //    await Task.Delay(300);
        //GetAge();
    }

    async void More3YearsClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("experience", "more3years");

        await AddAnswer(((Button)sender).Text);
        //await AddQuestion("3+ years? I recommend an upper/lower-body split program.");
        LocalDBManager.Instance.SetDBSetting("MainProgram", "Split body");
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        AskforProgramsWithABExperience();
        //RecommendedProgram_clicked(sender, e);
        //await AddQuestion("How old are you?");
        //if (Device.RuntimePlatform.Equals(Device.Android))
        //    await Task.Delay(300);
        //GetAge();
    }

    async void workoutPlace()
    {
        try
        {
            await AddQuestion("Where do you train?");
            if (DeviceInfo.Platform == DevicePlatform.Android)
                await Task.Delay(300);

            var btn1 = new Button()
            {
                Text = AppResources.HomeBodtweightOnly,
                TextColor = Color.FromHex("#195377"),
                BackgroundColor = Colors.Transparent,
                HeightRequest = 55,
                BorderWidth = 2,
                BorderColor = AppThemeConstants.BlueColor,
                Margin = new Thickness(25, 4),
                CornerRadius = 0
            };
            btn1.Clicked += BodyweightClicked;
            stackOptions.Children.Add(btn1);
            //
            var btnBands = new Button()
            {
                Text = "Home (bodyweight & bands)",
                TextColor = Color.FromHex("#195377"),
                BackgroundColor = Colors.Transparent,
                HeightRequest = 55,
                BorderWidth = 2,
                BorderColor = AppThemeConstants.BlueColor,
                Margin = new Thickness(25, 4),
                CornerRadius = 0
            };
            btnBands.Clicked += BodyweightBandsClicked;
            stackOptions.Children.Add(btnBands);
            var btn2 = new Button()
            {
                Text = AppResources.HomeGymBasicEqipment,
                TextColor = Color.FromHex("#195377"),
                BackgroundColor = Colors.Transparent,
                HeightRequest = 55,
                BorderWidth = 2,
                BorderColor = AppThemeConstants.BlueColor,
                Margin = new Thickness(25, 4, 25, 2),
                CornerRadius = 0
            };
            btn2.Clicked += HomeClicked;
            stackOptions.Children.Add(btn2);

            //await AddOptions(AppResources.HomeBodtweightOnly, BodyweightClicked);
            //await AddOptions(AppResources.HomeGymBasicEqipment, HomeClicked);
            await AddOptions(AppResources.Gym, GymClicked);

        }
        catch (Exception ex)
        {

        }
    }

    async void GymClicked(object senders, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("workout_place", "gym");

        await AddAnswer(((Button)senders).Text);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        await AddQuestion("Confirm equipment");
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        IsPully = true;
        await AddCheckbox("Pulley", (sender, ev) =>
        {
            IsPully = !IsPully;
            Image img = (Image)((StackLayout)sender).Children[0];
            img.Source = IsPully ? "done.png" : "undone.png";
        }, true);
        IsPlates = true;
        await AddCheckbox("Plates", (sender, ev) =>
        {
            IsPlates = !IsPlates;
            Image img = (Image)((StackLayout)sender).Children[0];
            img.Source = IsPlates ? "done.png" : "undone.png";
        }, true);
        IsChinupBar = true;
        await AddCheckbox("Chin-up bar", (sender, ev) =>
        {
            IsChinupBar = !IsChinupBar;
            Image img = (Image)((StackLayout)sender).Children[0];
            img.Source = IsChinupBar ? "done.png" : "undone.png";
        }, true);

        isDumbbells = true;
        await AddCheckbox("Dumbbells", (sender, ev) =>
        {
            isDumbbells = !isDumbbells;
            Image img = (Image)((StackLayout)sender).Children[0];
            img.Source = isDumbbells ? "done.png" : "undone.png";
        }, true);
        IsEquipment = true;

        await AddOptions("Continue", async (sender, ee) =>
        {
            await AddAnswer("Continue");
            if (Device.RuntimePlatform.Equals(Device.Android))
                await Task.Delay(300);
            //AskforBodypartPriority();
            AskForIncrements();
        });
    }

    async void HomeClicked(object senders, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("workout_place", "home");

        await AddAnswer(((Button)senders).Text);
        ClearOptions();
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        await AddQuestion("What equipment do you have?");

        await AddCheckbox("Pulley", (sender, ev) =>
        {
            IsPully = !IsPully;
            Image img = (Image)((StackLayout)sender).Children[0];
            img.Source = IsPully ? "done.png" : "undone.png";
        });

        await AddCheckbox("Plates", (sender, ev) =>
        {
            IsPlates = !IsPlates;
            Image img = (Image)((StackLayout)sender).Children[0];
            img.Source = IsPlates ? "done.png" : "undone.png";
        });
        await AddCheckbox("Chin-up bar", (sender, ev) =>
        {
            IsChinupBar = !IsChinupBar;
            Image img = (Image)((StackLayout)sender).Children[0];
            img.Source = IsChinupBar ? "done.png" : "undone.png";
        });

        isDumbbells = true;
        await AddCheckbox("Dumbbells", (sender, ev) =>
        {
            isDumbbells = !isDumbbells;
            Image img = (Image)((StackLayout)sender).Children[0];
            img.Source = isDumbbells ? "done.png" : "undone.png";
        }, true);

        IsEquipment = true;

        await AddOptions("Continue", async (sender, ee) =>
        {
            await AddAnswer("Continue");
            if (Device.RuntimePlatform.Equals(Device.Android))
                await Task.Delay(300);
            //AskforBodypartPriority();
            AskForIncrements();

        });
    }

    async void AskForIncrements()
    {
        ClearOptions();
        await AddQuestion("Your weights go up in what increments? Customize detailed dumbbells, plates, and more later in Settings.");
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        var isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg";


        var btn2 = new Button()
        {
            Text = "Not sure (set up later in Settings)",
            TextColor = Color.FromHex("#195377"),
            BackgroundColor = Colors.Transparent,
            HeightRequest = 55,
            BorderWidth = 2,
            BorderColor = AppThemeConstants.BlueColor,
            Margin = new Thickness(25, 4),
            CornerRadius = 0
        };
        btn2.Clicked += async (s, e) => {
            await AddAnswer("Not sure (set up later in Settings)");
            IncrementUnit = null;
            AskforBodypartPriority();

        };
        stackOptions.Children.Add(btn2);

        var btn1 = new Button()
        {
            Text = isKg ? "1 kg" : "2.5 lbs",
            TextColor = Color.FromHex("#195377"),
            BackgroundColor = Colors.Transparent,
            HeightRequest = 55,
            BorderWidth = 2,
            BorderColor = AppThemeConstants.BlueColor,
            Margin = new Thickness(25, 4, 25, 2),
            CornerRadius = 0
        };
        btn1.Clicked += async (s, e) => {
            await AddAnswer(isKg ? "1 kg" : "2.5 lbs");
            IncrementUnit = new MultiUnityWeight(isKg ? (decimal)1 : (decimal)2.5, isKg ? "kg" : "lb");
            AskforBodypartPriority();
        };
        stackOptions.Children.Add(btn1);

        await AddOptions(isKg ? "2.5 kg" : "5 lbs", async (s, e) => {
            await AddAnswer(isKg ? "2.5 kg" : "5 lbs");
            IncrementUnit = new MultiUnityWeight(isKg ? (decimal)2.5 : (decimal)5, isKg ? "kg" : "lb");
            AskforBodypartPriority();
        });
    }

    async void BodyweightClicked(object sender, System.EventArgs e)
    {
        try
        {
            // Ensure LocalDBManager.Instance is not null
            var dbManager = LocalDBManager.Instance;
            if (dbManager == null)
            {
                // Log the issue or notify the user appropriately
                Console.WriteLine("Database manager is unavailable.");
                return;
            }
            // Attempt to set the workout place, safely
            dbManager.SetDBSetting("workout_place", "homeBodyweightOnly");

            await AddAnswer(((Button)sender).Text);
            if (Device.RuntimePlatform.Equals(Device.Android))
                await Task.Delay(300);
            var level = 0;
            if (LocalDBManager.Instance.GetDBSetting("CustomExperience").Value == "new to training" || LocalDBManager.Instance.GetDBSetting("CustomExperience").Value == "returning from a break")
            {
                level = 2;
            }

            else
            {
                level = 3;
            }
            LocalDBManager.Instance.SetDBSetting("MainLevel", level.ToString());
            AskforBodypartPriority();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }

    async void BodyweightBandsClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("workout_place", "homeBodyweightBandsOnly");

        await AddAnswer(((Button)sender).Text);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        //await AddQuestion("OK, bodyweight exercises only. No problem.");
        //if (Device.RuntimePlatform.Equals(Device.Android))
        //    await Task.Delay(300);
        //AddCardio();
        AskforBodypartPriority();
    }
    private async void BodypartPicker_Unfocused(object sender, FocusEventArgs e)
    {
        //bodypartName = (sender as Picker).SelectedIndex == -1 || (sender as Picker).SelectedIndex == 0 ? "" : (string)(sender as Picker).SelectedItem;
        bodypartName = (string)(sender as Picker).SelectedItem;
        if ((sender as Picker).SelectedIndex != -1)
        {
            await AddAnswer((string)(sender as Picker).SelectedItem);
            LocalDBManager.Instance.SetDBSetting("BodypartPriority", bodypartName);

        }

        else
            await AddAnswer("No thanks");
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        await ClearOptions();
        AddCardio();
    }

    async void AskforBodypartPriority()
    {
        bodypartName = "";
        await AddQuestion("Prioritize a body part?");
        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");



        await AddOptions("Yes, select part", async (sender, e) =>
        {
            try
            {


                if (BodyPartPicker != null)
                {
                    BodyPartPicker.Unfocused -= BodypartPicker_Unfocused;
                }
                BodyPartPicker = new Picker();
                List<string> bodyParts = new List<string>();
                //bodyParts.Add("");
                //bodyParts.Add("No thanks");
                //bodyParts.Add("Biceps");
                //bodyParts.Add("Chest");
                //bodyParts.Add("Abs");
                //bodyParts.Add("Legs");
                //bodyParts.Add("Glutes");

                bodyParts.Add("Abs");
                bodyParts.Add("Biceps");
                bodyParts.Add("Calves");
                bodyParts.Add("Chest");
                bodyParts.Add("Glutes");
                bodyParts.Add("Legs");
                bodyParts.Add("Shoulders");
                bodyParts.Add("Traps");
                bodyParts.Add("Triceps");
                bodyParts.Add("Upper back");

                BodyPartPicker.ItemsSource = bodyParts;
                StackMain.Children.Insert(0, BodyPartPicker);
                BodyPartPicker.Unfocused += BodypartPicker_Unfocused;
                if (Device.RuntimePlatform == Device.Android)
                    BodyPartPicker.SelectedIndexChanged += BodyPartPicker_SelectedIndexChanged;
                BodyPartPicker.Focus();

            }
            catch (Exception exception)
            {

            }
        });

        await AddOptions("No thanks", async (sender, e) =>
        {
            bodypartName = "";
            await AddAnswer("No thanks");
            if (Device.RuntimePlatform.Equals(Device.Android))
                await Task.Delay(300);
            AddCardio();
        });

    }



    async void AddCardio()
    {
        try
        {
            await AddQuestion("Cardio?", false);
            if (DeviceInfo.Platform == DevicePlatform.Android)
                await Task.Delay(300);

            await AddOptions("Include cardio", async (sender, e) =>
            {
                await AddAnswer("Include cardio");
                if (DeviceInfo.Platform == DevicePlatform.Android)
                    await Task.Delay(300);
                IsIncludeCardio = true;
                AskForMobility();
            });

            await AddOptions("No cardio", async (sender, e) =>
            {
                IsIncludeCardio = false;
                await AddAnswer("No cardio");
                if (DeviceInfo.Platform == DevicePlatform.Android)
                    await Task.Delay(300);
                AskForMobility();
            });
        }
        catch (Exception ex)
        {

        }
    }

    async void AskForMobility()
    {
        try
        {
            await AddQuestion("Guided warm-ups? Prevent injuries and boost flexibility, mobility, and performance.", false);
            if (DeviceInfo.Platform == DevicePlatform.Android)
                await Task.Delay(300);
            if (LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value == "new to training" || LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value == "returning from a break")
                mobilityLevel = "Beginner";
            else
            {
                mobilityLevel = "Intermediate";
                if (LocalDBManager.Instance.GetDBSetting("experience") != null && (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "more3years") || (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "1-3years"))
                    mobilityLevel = "Advanced";
            }
            await AddOptions("No mobility warm-ups", async (sender, e) =>
            {
                IsIncludeMobility = false;
                await AddAnswer("No mobility warm-ups");
                if (DeviceInfo.Platform == DevicePlatform.Android)
                    await Task.Delay(300);
                if (DeviceInfo.Platform == DevicePlatform.iOS)
                    SetupAppleSync();
                else
                    SetTimerOption();
            });

            await AddOptions("Include mobility warm-ups", async (sender, e) =>
            {
                await AddAnswer("Include mobility warm-ups");
                if (Device.RuntimePlatform.Equals(Device.Android))
                    await Task.Delay(300);
                IsIncludeMobility = true;
                if (Device.RuntimePlatform == Device.iOS)
                    SetupAppleSync();
                else
                    SetTimerOption();
            });


        }
        catch (Exception ex)
        {

        }
    }

    async void SetupAppleSync()
    {
        await AddQuestion("Sync with Apple Health?", false);
        if (DeviceInfo.Platform == DevicePlatform.Android)
            await Task.Delay(300);
        //await AddOptions("30 min", QuickmodeOnClicked);
        //await AddOptions("45 min", QuickmodeMediumClicked);

        var btn1 = new Button()
        {
            Text = "No",
            TextColor = Color.FromHex("#195377"),
            BackgroundColor = Colors.Transparent,
            HeightRequest = 55,
            BorderWidth = 2,
            BorderColor = AppThemeConstants.BlueColor,
            Margin = new Thickness(25, 2),
            CornerRadius = 0
        };
        btn1.Clicked += async (sender, e) => {
            await AddAnswer("No");
            LocalDBManager.Instance.SetDBSetting("AppleSync", "false");
            SetTimerOption();
        };
        stackOptions.Children.Add(btn1);

        //var btn2 = new Button()
        //{
        //    Text = "45 min",
        //    TextColor = Color.FromHex("#195377"),
        //    BackgroundColor = Colors.Transparent,
        //    HeightRequest = 55,
        //    BorderWidth = 2,
        //    BorderColor = AppThemeConstants.BlueColor,
        //    Margin = new Thickness(25, 0)
        //};
        //btn2.Clicked += QuickmodeMediumClicked;
        //stackOptions.Children.Add(btn2);


        await AddOptions("Yes, sync", async (sender, e) =>
        {
            LocalDBManager.Instance.SetDBSetting("AppleSync", "true");
            await AddAnswer("Yes, sync");
            IHealthData _healthService = DependencyService.Get<IHealthData>();
            await _healthService.GetHealthPermissionAsync(async (r) =>
            {
                SetTimerOption();
            });
        });
    }

    async void SetTimerOption()
    {
        try
        {
            await AddQuestion("Timer sound?", false);

            if (Device.RuntimePlatform.Equals(Device.Android))
                await Task.Delay(300);

            var dingButton = new Button()
            {
                Text = "Ding",
                ImageSource = "play_dark_blue.png",
                ContentLayout = new Button.ButtonContentLayout(Button.ButtonContentLayout.ImagePosition.Left, 10),
                TextColor = Color.FromHex("#195377"),
                BackgroundColor = Color.FromHex("#e1e1e1"),
                HeightRequest = 50,
                BorderWidth = 0,
                Padding = new Thickness(10, 0),
                WidthRequest = 180,
                HorizontalOptions = LayoutOptions.Center,
                Margin = new Thickness(25, 2),
                CornerRadius = 25
            };
            dingButton.Clicked += dingSoundClicked;
            stackOptions.Children.Add(dingButton);

            var repsToDoButton = new Button()
            {
                Text = "Reps to do",
                ImageSource = "play_dark_blue.png",
                ContentLayout = new Button.ButtonContentLayout(Button.ButtonContentLayout.ImagePosition.Left, 10),
                TextColor = Color.FromHex("#195377"),
                BackgroundColor = Color.FromHex("#e1e1e1"),
                HeightRequest = 50,
                BorderWidth = 0,
                Padding = new Thickness(10, 0),
                HorizontalOptions = LayoutOptions.Center,
                WidthRequest = 180,
                Margin = new Thickness(25, 3),
                CornerRadius = 25
            };
            repsToDoButton.Clicked += repsToDoSoundClicked;
            stackOptions.Children.Add(repsToDoButton);

            var btn1 = new Button()
            {
                Text = "Ding",
                TextColor = Color.FromHex("#195377"),
                BackgroundColor = Colors.Transparent,
                HeightRequest = 55,
                BorderWidth = 2,
                BorderColor = AppThemeConstants.BlueColor,
                Margin = new Thickness(25, 3),
                CornerRadius = 0
            };
            btn1.Clicked += dingClicked;
            stackOptions.Children.Add(btn1);

            await AddOptions("Reps to do", repsToDoClicked);
        }
        catch(Exception ex)
        {

        }
    }
    async void dingSoundClicked(object sender, System.EventArgs e)
    {
        Timer.Instance.NextRepsCount = 0;
        try
        {
            if (Device.RuntimePlatform == Device.Android)
            {
                var fileName = "alarma.mp3";
                DependencyService.Get<IAudio>().PlayAudioFile(fileName, true, false);
            }
            else if (Device.RuntimePlatform == Device.iOS)
            {
                var message = new PlayAudioFileMessage();
                MessagingCenter.Send(message, "PlayAudioFileMessage");
            }
        }
        catch (Exception ex)
        {

        }
        //Device.OnPlatform(
        //       Android: async () =>
        //       {

        //           var fileName = "alarma.mp3";
        //           DependencyService.Get<IAudio>().PlayAudioFile(fileName, true, false);
        //       }, iOS: async () =>
        //       {

        //           var message = new PlayAudioFileMessage();
        //           MessagingCenter.Send(message, "PlayAudioFileMessage");
        //       });
    }
    async void repsToDoSoundClicked(object sender, System.EventArgs e)
    {
        Timer.Instance.NextRepsCount = 5;
        try
        {
            if (Device.RuntimePlatform == Device.Android)
            {
                var fileName = "alarma.mp3";
                fileName = $"reps5.mp3";

                DependencyService.Get<IAudio>().PlayAudioFile(fileName, true, false);
            }
            else if (Device.RuntimePlatform == Device.iOS)
            {
                var message = new PlayAudioFileMessage();
                MessagingCenter.Send(message, "PlayAudioFileMessage");
            }
            //Device.OnPlatform(
            //       Android: async () => {

            //           var fileName = "alarma.mp3";
            //           fileName = $"reps5.mp3";

            //           DependencyService.Get<IAudio>().PlayAudioFile(fileName, true, false);
            //       }, iOS: async () => {

            //           var message = new PlayAudioFileMessage();
            //           MessagingCenter.Send(message, "PlayAudioFileMessage");
            //       });
        }
        catch (Exception ex)
        {

        }
    }

    async void dingClicked(object sender, System.EventArgs e)
    {
        isDing = true;
        await AddAnswer(((Button)sender).Text);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);

        SetupQuickMode();

    }

    async void repsToDoClicked(object sender, System.EventArgs e)
    {
        isDing = false;
        await AddAnswer(((Button)sender).Text);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);

        SetupQuickMode();

    }

    async void SetupQuickMode()
    {

        // if (LocalDBManager.Instance.GetDBSetting("NewLevel") != null && LocalDBManager.Instance.GetDBSetting("ExLevel") != null && LocalDBManager.Instance.GetDBSetting("NewLevel").Value == "Streamline" && LocalDBManager.Instance.GetDBSetting("ExLevel").Value == "New")
        // {
        //     LocalDBManager.Instance.SetDBSetting("QuickMode", "false");
        //     ProgramReadyInstruction();
        //     return;
        // }
        LocalDBManager.Instance.SetDBSetting("QuickMode", "false");
        await AddQuestion("Workout length?", false);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);

        var btn1 = new Button()
        {
            Text = "30 min",
            TextColor = Color.FromHex("#195377"),
            BackgroundColor = Colors.Transparent,
            HeightRequest = 55,
            BorderWidth = 2,
            BorderColor = AppThemeConstants.BlueColor,
            Margin = new Thickness(25, 4),
            CornerRadius = 0
        };
        btn1.Clicked += QuickmodeOnClicked;
        stackOptions.Children.Add(btn1);

        var btn2 = new Button()
        {
            Text = "45 min",
            TextColor = Color.FromHex("#195377"),
            BackgroundColor = Colors.Transparent,
            HeightRequest = 55,
            BorderWidth = 2,
            BorderColor = AppThemeConstants.BlueColor,
            Margin = new Thickness(25, 4),
            CornerRadius = 0
        };
        btn2.Clicked += QuickmodeOnClicked;
        stackOptions.Children.Add(btn2);

        var btn3 = new Button()
        {
            Text = "1 hour",
            TextColor = Color.FromHex("#195377"),
            BackgroundColor = Colors.Transparent,
            HeightRequest = 55,
            BorderWidth = 2,
            BorderColor = AppThemeConstants.BlueColor,
            Margin = new Thickness(25, 4, 25, 2),
            CornerRadius = 0
        };
        btn3.Clicked += QuickmodeOnClicked;
        stackOptions.Children.Add(btn3);

        await AddOptions("Flexible (adapts to you)", QuickmodeOffClicked);
    }
    async void QuickmodeOnClicked(object sender, System.EventArgs e)
    {
        if (((Button)sender).Text == "30 min")
        {
            workoutDurationLenggth = 1;
        }
        if (((Button)sender).Text == "45 min")
        {
            workoutDurationLenggth = 2;
        }
        if (((Button)sender).Text == "1 hour")
        {
            workoutDurationLenggth = 3;
        }
        await AddAnswer(((Button)sender).Text);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        LearnMoreTimeline();


    }

    async void LearnMoreTimeline()
    {
        //BotList.Add(new BotModel()
        //{
        //    Question = "Features like smart watch integration and calendar view are not yet available. But if you’re an early adopter who wants to get in shape fast, you'll love your new custom workouts. Give us a shot: we'll treat your feedback like gold. Got a suggestion? Get in touch. We release new features every month.",
        //    Type = BotType.Ques
        //});
        //lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
        //lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);

        //await AddQuestion("More features are coming! Got a suggestion? Get in touch. We release new features every week.", false);

        ProgramReadyInstruction();
    }

    async void QuickmodeOffClicked(object sender, System.EventArgs e)
    {
        workoutDurationLenggth = 0;
        await AddAnswer(((Button)sender).Text);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        LearnMoreTimeline();

    }

    async void QuickmodeMediumClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("QuickMode", "null");

        await AddAnswer(((Button)sender).Text);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        LearnMoreTimeline();

    }

    async void SetupMassUnit()
    {
        ClearOptions();
        IsBodyweightPopup = true;
        await AddQuestion("What is your body weight?");
        await PopupNavigation.Instance.PushAsync(new BodyweightPopup());
        //await AddQuestion(AppResources.DoYouUseLbsOrKgs, false);

        //await AddOptions(AppResources.Lbs, LbsClicked);
        //await AddOptions(AppResources.Kg, KgClicked);


    }

    async void SetupGender()
    {
        ManBetterHealth = false;
        ManLessFat = false;
        ManStorngerSexDrive = false;
        ManMoreMuscle = false;
        FemaleMoreEnergy = false;
        FemaleToned = false;

        await AddOptions(AppResources.Woman, WomanButton_Clicked);
        await AddOptions(AppResources.Man, ManButton_Clicked);


    }

    async void GetAge()
    {
        BotList.Add(new BotModel()
        { Type = BotType.Empty });
        StackMain.IsVisible = true;
        AgePicker.IsVisible = true;
        Device.BeginInvokeOnMainThread(() =>
        {
            lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
            lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
        });

        ShowAgePicker();

    }

    private void ShowAgePicker()
    {
        try
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                //Android picker doesn't have a solution to handle Cancel button/Hardware back button/Selection of same already selected item
#if ANDROID
                var result = await DisplayActionSheet("Age?", string.Empty, null, AgePicker.Items.ToArray());
                if (string.IsNullOrEmpty(result))
                    ShowAgePicker();
                else
                {
                    AgePicker.SelectedIndex = -1;
                    AgePicker.SelectedItem = result;
                }
#else
                AgePicker.Focus();
#endif
            });
        }
        catch (Exception ex)
        {

        }
    }

    async void LbsClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("massunit", "lb");

        await AddAnswer(((Button)sender).Text);
        TakeBodyWeight();
    }

    async void KgClicked(object sender, System.EventArgs e)
    {
        LocalDBManager.Instance.SetDBSetting("massunit", "kg");

        await AddAnswer(((Button)sender).Text);


        TakeBodyWeight();

    }

    async void GotoLevelUp()
    {


        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Try again","");
            GotoLevelUp();
            return;
        }
        //await AddQuestion($"All right! Your custom program is ready. Learn more?");
        //await AddOptions(AppResources.LearnMore, LearnMoreButton_Clicked);
        //await AddOptions(AppResources.Skip, LearnMoreSkipButton_Clicked);
        LearnMoreSkipButton_Clicked(new Button(), EventArgs.Empty);

    }
    private void GetFirstName()
    {
        //StackSignupMenu.IsVisible = false;
        try
        {
            // if (Device.RuntimePlatform == Device.Android)
            {
                CustomPromptConfig customPromptConfig = new CustomPromptConfig("Your first name", "Enter first name", AppResources.Continue, "", "");
                Application.Current.MainPage.ShowPopup(customPromptConfig);
                customPromptConfig.ActionSelected += (sender1, action) =>
                {
                    if (action == PopupAction.OK)
                    {
                        PromptResult result = new PromptResult(true, customPromptConfig.text);
                        GetFirstNameAction(result);
                    }
                };
            }
            // else
            // {
            //     PromptConfig p = new PromptConfig()
            //     {
            //         InputType = InputType.Default,
            //         IsCancellable = false,
            //         Title = "Your first name",
            //         Placeholder = "Enter first name",
            //         OkText = AppResources.Continue,
            //         AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //         OnAction = new Action<PromptResult>(GetFirstNameAction)
            //     };
            //     p.OnTextChanged += Name_OnTextChanged;

            //     firstnameDisposible = UserDialogs.Instance.Prompt(p);
            // }

        }
        catch (Exception ex)
        {

        }
    }
    protected void Name_OnTextChanged(PromptTextChangedArgs obj)
    {

        if (!string.IsNullOrEmpty(obj.Value))
        {
            if (obj.Value.Length == 1)
                obj.Value = char.ToUpper(obj.Value[0]) + "";
            else if (obj.Value.Length > 1)
                obj.Value = char.ToUpper(obj.Value[0]) + obj.Value.Substring(1);
        }
    }
    private async void GetFirstNameAction(PromptResult response)
    {
        if (response.Ok)
        {
            string text = response.Text;
            if (!string.IsNullOrEmpty(text))
            {
                text = text.Replace("<", "");
                text = text.Replace(">", "");
                text = text.Replace("/", "");
            }
            if (string.IsNullOrEmpty(text))
            {
                GetFirstName();
                return;
            }
            await AddAnswer(text);
            if (Device.RuntimePlatform.Equals(Device.iOS))
            {
                lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
                lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
            }
            LocalDBManager.Instance.SetDBSetting("firstname", !string.IsNullOrEmpty(text) ? ((text.Contains(' ')) ? text.Split(' ')[0] : text) : text);
            LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
            CurrentLog.Instance.ShowWelcomePopUp = true;
            ((App)Application.Current).displayCreateNewAccount = true;

            var result = await DrMuscleRestClient.Instance.SetUserFirstname(new UserInfosModel() { Firstname = response.Text });


            GetGender();
        }
    }

    private void GetEmail()
    {
        //if(Device.RuntimePlatform == Device.Android)
        //{
            CustomPromptConfig customPromptConfig = new CustomPromptConfig("Your email", "Enter your email", AppResources.Continue, "","",Keyboard.Email);
            Application.Current.MainPage.ShowPopup(customPromptConfig);
            customPromptConfig.ActionSelected += (sender1, action) =>
            {
                if (action == PopupAction.OK)
                {
                    PromptResult result = new PromptResult(true, customPromptConfig.text);
                    GetEmailAction(result);
                }
            };
        //}
        //else
        //{
        //    PromptConfig p = new PromptConfig()
        //    {
        //        InputType = InputType.Email,
        //        IsCancellable = false,
        //        Title = "Your email",
        //        Placeholder = "Enter your email",
        //        OkText = AppResources.Continue,
        //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //        OnAction = new Action<PromptResult>(GetEmailAction)
        //    };

        //    UserDialogs.Instance.Prompt(p);
        //}

    }
    private async void GetEmailAction(PromptResult response)
    {

        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            CustomPromptConfig customPromptConfig = new CustomPromptConfig(AppResources.ConnectionError, "", "OK", "", AppResources.PleaseCheckInternetConnection);
            await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);
            customPromptConfig.ActionSelected += (sender1, action) =>
            {
                if (action == PopupAction.OK)
                {
                    GetEmailAction(response);
                }
            };
            //await UserDialogs.Instance.AlertAsync(new AlertConfig()
            //{
            //    Message = AppResources.PleaseCheckInternetConnection,
            //    Title = AppResources.ConnectionError,
            //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            //});
            ////await UserDialogs.Instance.AlertAsync(new AlertConfig()
            ////{
            ////    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            ////    Message = AppResources.PleaseCheckInternetConnection,
            ////    Title = AppResources.ConnectionError
            ////});
            //GetEmailAction(response);
            return;
        }
        if (response.Ok)
        {
            var text = response.Text;
            if (!string.IsNullOrEmpty(response.Text) && text.Contains("@"))
            {
                var email = response.Text.Substring(0, response.Text.IndexOf('@'));
                if (email.Length == 1)
                    text = $"a{text}";
            }
            if (!Emails.ValidateEmail(text.ToLower()))
            {
                CustomPromptConfig customPromptConfig = new CustomPromptConfig(AppResources.InvalidEmailAddress, "", "OK", "", AppResources.InvalidEmailError);
                await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);
                customPromptConfig.ActionSelected += (sender1, action) =>
                {
                    if (action == PopupAction.OK)
                    {
                        GetEmail();
                    }
                };
                //await UserDialogs.Instance.AlertAsync(new AlertConfig()
                //{
                //    Message = AppResources.InvalidEmailError,
                //    Title = AppResources.InvalidEmailAddress,
                //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                //});
                //GetEmail();
                return;
            }
            if (response.Text.Contains("#") || response.Text.Contains("%") || response.Text.Contains("{") || response.Text.Contains("}") || response.Text.Contains("(") || response.Text.Contains("}") || response.Text.Contains("$") || response.Text.Contains("^") || response.Text.Contains("&") || response.Text.Contains("=") || response.Text.Contains("`") || response.Text.Contains("'") || response.Text.Contains("\"") || response.Text.Contains(",") || response.Text.Contains("?") || response.Text.Contains("/") || response.Text.Contains("\\") || response.Text.Contains("<") || response.Text.Contains(">") || response.Text.Contains(":") || response.Text.Contains(";") || response.Text.Contains("|") || response.Text.Contains("[") || response.Text.Contains("]") || response.Text.Contains("*") || response.Text.Contains("*") || response.Text.Contains("!") || response.Text.Contains("~") || response.Text.Count(t => t == '@') > 1)
            {
                CustomPromptConfig customPromptConfig = new CustomPromptConfig(AppResources.InvalidEmailAddress, "", "OK", "", AppResources.InvalidEmailError);
                await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);
                customPromptConfig.ActionSelected += (sender1, action) =>
                {
                    if (action == PopupAction.OK)
                    {
                        GetEmail();
                    }
                };
                //await UserDialogs.Instance.AlertAsync(new AlertConfig()
                //{
                //    Message = AppResources.InvalidEmailError,
                //    Title = AppResources.InvalidEmailAddress,
                //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                //});
                //GetEmail();
                return;
            }

            try
            {
                var domain = response.Text.Substring(response.Text.IndexOf('@'));
                var extension = response.Text.Substring(response.Text.IndexOf('.') + 1).ToUpper();
                if (domain.Contains("gnail") || domain.Contains("gmaill") || domain.Contains(".cam"))
                {
                    CustomPromptConfig customPromptConfig = new CustomPromptConfig(AppResources.InvalidEmailAddress, "", "OK", "", AppResources.InvalidEmailError);
                    await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);
                    customPromptConfig.ActionSelected += (sender1, action) =>
                    {
                        if (action == PopupAction.OK)
                        {
                            GetEmail();
                        }
                    };
                    //await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    //{
                    //    Message = AppResources.InvalidEmailError,
                    //    Title = AppResources.InvalidEmailAddress,
                    //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                    //});
                    //GetEmail();
                    return;
                }
            }
            catch (Exception ex)
            {

            }

            bool isEmailExist = await checkEmailExist(response.Text);
            if (isEmailExist)
            {
                CustomPromptConfig customPromptConfig = new CustomPromptConfig("Email already in use", "", "Use another email", AppResources.LogIn, "Use another email or log into your existing account.");
                await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);
                customPromptConfig.ActionSelected += async (sender1, action) =>
                {
                    try
                    {
                        if (action == PopupAction.OK)
                        {
                            GetEmail();
                        }
                        else
                        {
                            ((App)Application.Current).displayCreateNewAccount = true;

                            WelcomePage page = new WelcomePage();
                            page.OnBeforeShow();
                            await Navigation.PushAsync(page);
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                };
                //ConfirmConfig ShowAlertPopUp = new ConfirmConfig()
                //{
                //    Title = "Email already in use",
                //    Message = "Use another email or log into your existing account.",
                //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //    OkText = "Use another email",
                //    CancelText = AppResources.LogIn,

                //};
                //var actionOk = await UserDialogs.Instance.ConfirmAsync(ShowAlertPopUp);
                //if (actionOk)
                //{
                //    GetEmail();
                //}
                //else
                //{
                //    ((App)Application.Current).displayCreateNewAccount = true;

                //    WelcomePage page = new WelcomePage();
                //    page.OnBeforeShow();
                //    await Navigation.PushAsync(page);
                //    // await PagesFactory.PushAsync<WelcomePage>();
                //}
            }
            else
            {
                //GetEmailAction(response);

                App.IsNewUser = true;

                LocalDBManager.Instance.SetDBSetting("email", response.Text);
                await AddAnswer(response.Text);
                await AddQuestion("Enter first name");
                if (Device.RuntimePlatform.Equals(Device.iOS))
                {
                    lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
                    lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
                }
                if (LocalDBManager.Instance.GetDBSetting("LoginType")?.Value == "Email" && LocalDBManager.Instance.GetDBSetting("isAccountCreatedInBackground")?.Value == "false")
                {
                    RegisterAccount();
                }
                GetFirstName();
            }

        }
    }

    private async Task<bool> checkEmailExist(string email)
    {
        BooleanModel existingUser = await DrMuscleRestClient.Instance.IsEmailAlreadyExistWithoutLoader(new IsEmailAlreadyExistModel() { email = email });
        if (existingUser != null)
        {
            if (existingUser.Result)
            {
                //try
                //{
                //    if (firstnameDisposible != null)
                //        firstnameDisposible.Dispose();
                //    if (passwordDisposible != null)
                //        passwordDisposible.Dispose();

                //}
                //catch (Exception ex)
                //{

                //} finally
                //{
                //    await Task.Delay(500);
                //}



                return true;
            }
            else
            {
                return false;
            }

        }
        else
        {
            return false;
        }

    }
    private async void GetPassword()
    {
        CustomPromptConfig p = new CustomPromptConfig("Create password","Create password",
            AppResources.Create,"","At least 6 characters",Keyboard.Default,"",0,false);

            p.ActionSelected += async (sender,action) => {
                if(action == PopupAction.OK){
                     if (string.IsNullOrWhiteSpace(p.text) )
                    {
                        return;
                    }
                    GetPasswordAction(action,p.text);
                }
            };
        await Application.Current.MainPage.ShowPopupAsync(p);

        // PromptConfig p = new PromptConfig()
        // {
        //     InputType = InputType.Password,
        //     IsCancellable = false,
        //     Title = "Create password",
        //     Message = "At least 6 characters",
        //     Placeholder = "Create password",
        //     OkText = AppResources.Create,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = new Action<PromptResult>(GetPasswordAction)
        // };

        // passwordDisposible = UserDialogs.Instance.Prompt(p);
    }
    private async void GetPasswordAction(PopupAction response,string Text)
    {
        if (response == PopupAction.OK)
        {

            if (Text.Length < 6)
            {
                // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     Message = AppResources.PasswordLengthError,
                //     Title = AppResources.Error,
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                // });

                await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
                       AppResources.PasswordLengthError,"Ok","");
                GetPassword();
                return;
            }
            await AddAnswer(Text);
            LocalDBManager.Instance.SetDBSetting("password", Text);
            await Task.Delay(100);
            //CreateAccountBeforeDemoButton_Clicked();
            CreateAccountBeforeDemoButton_Clicked();
            //await AccountCreatedPopup();
            SetUpRestOnboarding();
        }

    }
    async void CreateAccountButton_Clicked()
    {
        DBSetting experienceSetting = LocalDBManager.Instance.GetDBSetting("experience");
        DBSetting workoutPlaceSetting = LocalDBManager.Instance.GetDBSetting("workout_place");
        int? workoutId = null;
        int? programId = null;
        int? remainingWorkout = null;
        var WorkoutInfo2 = "";


        string ProgramLabel = AppResources.NotSetUp;
        int age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age").Value);


        var level = 2;
        if (LocalDBManager.Instance.GetDBSetting("MainLevel") != null)
            level = int.Parse(LocalDBManager.Instance.GetDBSetting("MainLevel").Value);
        if (LocalDBManager.Instance.GetDBSetting("ExerciseVariety")?.Value == "More")
            level += 1;
        bool isSplit = LocalDBManager.Instance.GetDBSetting("MainProgram").Value.Contains("Split");
        bool isGym = workoutPlaceSetting?.Value == "gym";
        var mo = AppThemeConstants.GetLevelProgram(level, isGym, !isSplit, LocalDBManager.Instance.GetDBSetting("MainProgram").Value);

        if (workoutPlaceSetting?.Value == "homeBodyweightOnly")
        {
            if (LocalDBManager.Instance.GetDBSetting("CustomMainLevel") != null && LocalDBManager.Instance.GetDBSetting("CustomMainLevel")?.Value == "1" && LocalDBManager.Instance.GetDBSetting("ExerciseVariety")?.Value == "Less")
            {

                mo.workoutName = "Bodyweight 1";
                mo.workoutid = 12645;
                mo.programid = 487;
                mo.reqWorkout = 12;
                mo.programName = "Bodyweight Level 1";
            }
            else if (level <= 1)
            {
                mo.workoutName = "Bodyweight 2";
                mo.workoutid = 12646;
                mo.programid = 488;
                mo.reqWorkout = 12;
                mo.programName = "Bodyweight Level 2";
            }
            else if (level == 2)
            {
                mo.workoutName = "Bodyweight 2";
                mo.workoutid = 12646;
                mo.programid = 488;
                mo.reqWorkout = 12;
                mo.programName = "Bodyweight Level 2";
            }
            else if (level == 3)
            {
                mo.workoutName = "Bodyweight 3";
                mo.workoutid = 14017;
                mo.programid = 923;
                mo.reqWorkout = 15;
                mo.programName = "Bodyweight Level 3";
            }
            else if (level >= 4)
            {
                mo.workoutName = "Bodyweight 4";
                mo.workoutid = 14019;
                mo.programid = 924;
                mo.reqWorkout = 15;
                mo.programName = "Bodyweight Level 4";
            }


        }


        LocalDBManager.Instance.SetDBSetting("recommendedWorkoutId", mo.workoutid.ToString());
        LocalDBManager.Instance.SetDBSetting("recommendedWorkoutLabel", mo.workoutName);
        LocalDBManager.Instance.SetDBSetting("recommendedProgramId", mo.programid.ToString());
        LocalDBManager.Instance.SetDBSetting("recommendedRemainingWorkout", mo.reqWorkout.ToString());
        LocalDBManager.Instance.SetDBSetting("recommendedProgramLabel", mo.programName);
        //}
        //SignUp here

        workoutId = mo.workoutid;
        WorkoutInfo2 = mo.workoutName;
        programId = mo.programid;
        ProgramLabel = mo.programName;
        remainingWorkout = mo.reqWorkout;


        RegisterModel registerModel = new RegisterModel();

        registerModel.Firstname = LocalDBManager.Instance.GetDBSetting("firstname").Value;
        registerModel.EmailAddress = LocalDBManager.Instance.GetDBSetting("email").Value;
        registerModel.SelectedGender = LocalDBManager.Instance.GetDBSetting("gender").Value;
        registerModel.MassUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value;
        if (LocalDBManager.Instance.GetDBSetting("QuickMode") == null)
            registerModel.IsQuickMode = false;
        else
        {
            if (LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "null")
                registerModel.IsQuickMode = null;
            else
                registerModel.IsQuickMode = LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "true" ? true : false;
        }
        if (LocalDBManager.Instance.GetDBSetting("Age") != null)
            registerModel.Age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age").Value);
        registerModel.RepsMinimum = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("repsminimum").Value);
        registerModel.RepsMaximum = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("repsmaximum").Value);
        if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
            registerModel.BodyWeight = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.Replace(",", ".").Replace("٫", "."), CultureInfo.InvariantCulture), "kg");
        if (LocalDBManager.Instance.GetDBSetting("WeightGoal") != null)
            registerModel.WeightGoal = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal").Value.Replace(",", ".").Replace("٫", "."), CultureInfo.InvariantCulture), "kg");

        registerModel.Password = LocalDBManager.Instance.GetDBSetting("password").Value;
        registerModel.ConfirmPassword = LocalDBManager.Instance.GetDBSetting("password").Value;
        registerModel.LearnMoreDetails = learnMore;
        registerModel.IsHumanSupport = IsHumanSupport;
        registerModel.IsCardio = IsIncludeCardio;
        registerModel.BodyPartPrioriy = bodypartName;
        registerModel.SetStyle = SetStyle.Value;
        registerModel.IsPyramid = IsPyramid;
        registerModel.isDing = isDing;
        registerModel.WorkoutDuration = workoutDurationLenggth;
        if (LocalDBManager.Instance.GetDBSetting("Height")?.Value != null && Config.UserHeight != 0)
        {
            registerModel.Height = Config.UserHeight;
        }
        if (LocalDBManager.Instance.GetDBSetting("email") != null && LocalDBManager.Instance.GetDBSetting("email").Value.Contains("yopmail") || (LocalDBManager.Instance.GetDBSetting("Environment") != null && LocalDBManager.Instance.GetDBSetting("Environment").Value != "Production"))
        { registerModel.MainGoal = ""; }
        else
            registerModel.MainGoal = isBetaExperience == true ? "Beta" : isBetaExperience == false ? "Normal" : "";

        if (LocalDBManager.Instance.GetDBSetting("email") != null && LocalDBManager.Instance.GetDBSetting("email").Value.Contains("yopmail") || (LocalDBManager.Instance.GetDBSetting("Environment") != null && LocalDBManager.Instance.GetDBSetting("Environment").Value != "Production"))
        { registerModel.MainGoal = ""; }
        else
            registerModel.MainGoal = isBetaExperience == true ? "Beta" : isBetaExperience == false ? "Normal" : "";

        if (LocalDBManager.Instance.GetDBSetting("ReminderDays") != null && LocalDBManager.Instance.GetDBSetting("ReminderTime") != null)
        {
            try
            {
                registerModel.ReminderTime = TimeSpan.Parse(LocalDBManager.Instance.GetDBSetting("ReminderTime").Value);
                registerModel.ReminderDays = LocalDBManager.Instance.GetDBSetting("ReminderDays")?.Value;
            }
            catch (Exception ex)
            {

            }

        }
        if (IsEquipment)
        {
            var model = new EquipmentModel();

            if (LocalDBManager.Instance.GetDBSetting("workout_place")?.Value == "gym")
            {
                model.IsEquipmentEnabled = true;
                model.IsDumbbellEnabled = isDumbbells;
                model.IsPlateEnabled = IsPlates;
                model.IsPullyEnabled = IsPully;
                model.IsChinUpBarEnabled = IsChinupBar;
                model.IsBands = true;
                model.Active = "gym";

                model.IsHomeEquipmentEnabled = false;
                model.IsHomeDumbbell = true;
                model.IsHomePlate = true;
                model.IsHomePully = true;
                model.IsHomeChinupBar = true;
                model.IsHomeBands = true;
            }
            else
            {
                model.IsEquipmentEnabled = false;
                model.IsDumbbellEnabled = true;
                model.IsPlateEnabled = true;
                model.IsPullyEnabled = true;
                model.IsChinUpBarEnabled = true;
                model.IsBands = true;
                model.IsOtherEquipmentEnabled = false;
                model.IsOtherDumbbell = true;
                model.IsOtherPlate = true;
                model.IsOtherPully = true;
                model.IsOtherChinupBar = true;
                model.IsOtherBands = true;
                model.IsHomeEquipmentEnabled = true;
                model.IsHomeDumbbell = isDumbbells;
                model.IsHomePlate = IsPlates;
                model.IsHomePully = IsPully;
                model.IsHomeChinupBar = IsChinupBar;
                model.IsHomeBands = true;
                model.Active = "home";
            }
            var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True";
            model.AvilablePlate = kgString;
            model.AvilableHomePlate = kgString;
            model.AvilableOtherPlate = kgString;
            var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
            model.AvilableLbPlate = lbString;
            model.AvilableHomeLbPlate = lbString;
            model.AvilableOtherLbPlate = lbString;


            registerModel.EquipmentModel = model;
        }
        if (IncrementUnit != null)
        {
            registerModel.Increments = IncrementUnit.Kg;
        }
        try
        {


            BooleanModel registerResponse = await DrMuscleRestClient.Instance.RegisterUser(registerModel);
            if (registerResponse.Result)
            {
                _firebase.LogEvent("account_created", "");
            }
        }
        catch (Exception ex)
        {

        }
        //Login
        LoginSuccessResult lr = await DrMuscleRestClient.Instance.Login(new LoginModel()
        {
            Username = registerModel.EmailAddress,
            Password = registerModel.Password
        });

        if (lr != null && !string.IsNullOrEmpty(lr.access_token))
        {
            DateTime current = DateTime.Now;

            UserInfosModel uim = await DrMuscleRestClient.Instance.GetUserInfo();

            LocalDBManager.Instance.SetDBSetting("email", uim.Email);
            LocalDBManager.Instance.SetDBSetting("firstname", !string.IsNullOrEmpty(uim.Firstname) ? ((uim.Firstname.Contains(' ')) ? uim.Firstname.Split(' ')[0] : uim.Firstname) : uim.Firstname);
            LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
            LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
            LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
            LocalDBManager.Instance.SetDBSetting("password", registerModel.Password);
            LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
            LocalDBManager.Instance.SetDBSetting("token_expires_date", current.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
            LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());

            LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
            LocalDBManager.Instance.SetDBSetting("reprangeType", uim.ReprangeType.ToString());
            LocalDBManager.Instance.SetDBSetting("repsminimum", Convert.ToString(uim.RepsMinimum));
            LocalDBManager.Instance.SetDBSetting("repsmaximum", Convert.ToString(uim.RepsMaximum));
            LocalDBManager.Instance.SetDBSetting("onboarding_seen", "true");

            LocalDBManager.Instance.SetDBSetting("timer_vibrate", uim.IsVibrate ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_sound", uim.IsSound ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_123_sound", uim.IsTimer321 ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_reps_sound", uim.IsRepsSound ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_autostart", uim.IsAutoStart ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_autoset", uim.IsAutomatchReps ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_fullscreen", uim.IsFullscreen ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("QuickMode", uim.IsQuickMode == true ? "true" : uim.IsQuickMode == null ? "null" : "false"); if (uim.Age != null)
                LocalDBManager.Instance.SetDBSetting("Age", Convert.ToString(uim.Age));

            if (uim.TargetIntake != null && uim.TargetIntake != 0)
                LocalDBManager.Instance.SetDBSetting("TargetIntake", uim.TargetIntake.ToString());
            SetupEquipment(uim);
            if (uim.IsPyramid)
            {
                LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                LocalDBManager.Instance.SetDBSetting("IsRPyramid", "true");
                LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
            }
            else if (uim.IsNormalSet == null || uim.IsNormalSet == true)
            {
                LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
                LocalDBManager.Instance.SetDBSetting("IsPyramid", uim.IsNormalSet == null ? "true" : "false");
            }
            else
            {
                LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
            }
            if (uim.WarmupsValue != null)
            {
                LocalDBManager.Instance.SetDBSetting("warmups", Convert.ToString(uim.WarmupsValue));
            }
            if (uim.Increments != null)
                LocalDBManager.Instance.SetDBSetting("workout_increments", uim.Increments.Kg.ToString().ReplaceWithDot());
            if (uim.Max != null)
                LocalDBManager.Instance.SetDBSetting("workout_max", uim.Max.Kg.ToString().ReplaceWithDot());
            if (uim.Min != null)
                LocalDBManager.Instance.SetDBSetting("workout_min", uim.Min.Kg.ToString().ReplaceWithDot());
            if (uim.BodyWeight != null)
            {
                LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString().ReplaceWithDot());
            }
            if (uim.WeightGoal != null)
            {
                LocalDBManager.Instance.SetDBSetting("WeightGoal", uim.WeightGoal.Kg.ToString().ReplaceWithDot());
            }
            //if (uim.EquipmentModel != null)
            //{
            //    LocalDBManager.Instance.SetDBSetting("Equipment", uim.EquipmentModel.IsEquipmentEnabled ? "true" : "false");
            //    LocalDBManager.Instance.SetDBSetting("ChinUp", uim.EquipmentModel.IsChinUpBarEnabled ? "true" : "false");
            //    LocalDBManager.Instance.SetDBSetting("Dumbbell", uim.EquipmentModel.IsDumbbellEnabled ? "true" : "false");
            //    LocalDBManager.Instance.SetDBSetting("Plate", uim.EquipmentModel.IsPlateEnabled ? "true" : "false");
            //    LocalDBManager.Instance.SetDBSetting("Pully", uim.EquipmentModel.IsPullyEnabled ? "true" : "false");
            //}
            //else
            //{
            //    LocalDBManager.Instance.SetDBSetting("Equipment", "false");
            //    LocalDBManager.Instance.SetDBSetting("ChinUp", "true");
            //    LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");
            //    LocalDBManager.Instance.SetDBSetting("Plate", "true");
            //    LocalDBManager.Instance.SetDBSetting("Pully", "true");
            //}
            SetupEquipment(uim);

            if (string.IsNullOrEmpty(uim.BodyPartPrioriy))
                LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
            else
                LocalDBManager.Instance.SetDBSetting("BodypartPriority", uim.BodyPartPrioriy.Trim());

            LocalDBManager.Instance.SetDBSetting("Cardio", uim.IsCardio ? "true" : "false");

            LocalDBManager.Instance.SetDBSetting("BackOffSet", uim.IsBackOffSet ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("1By1Side", uim.Is1By1Side ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("StrengthPhase", uim.IsStrength ? "true" : "false");
            ((App)Application.Current).displayCreateNewAccount = true;

            if (uim.Gender.Trim().ToLowerInvariant().Equals("man"))
                LocalDBManager.Instance.SetDBSetting("BackgroundImage", "Background2.png");
            else
                LocalDBManager.Instance.SetDBSetting("BackgroundImage", "BackgroundFemale.png");
            App.IsNewFirstTime = false;
            if (LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId") != null &&
                    LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel") != null &&
                    LocalDBManager.Instance.GetDBSetting("recommendedProgramId") != null &&
                    LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel") != null &&
                    LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout") != null)
            {
                try
                {
                    long workoutTemplateId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId").Value);
                    long pId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedProgramId").Value);
                    var upi = new GetUserProgramInfoResponseModel()
                    {
                        NextWorkoutTemplate = new WorkoutTemplateModel() { Id = workoutTemplateId, Label = LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel").Value },
                        RecommendedProgram = new WorkoutTemplateGroupModel() { Id = pId, Label = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel").Value, RemainingToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value), RequiredWorkoutToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value) },
                    };
                    if (upi != null)
                    {
                        WorkoutTemplateModel nextWorkout = upi.NextWorkoutTemplate;
                        if (upi.NextWorkoutTemplate.Exercises == null || upi.NextWorkoutTemplate.Exercises.Count() == 0)
                        {
                            try
                            {
                                nextWorkout = await DrMuscleRestClient.Instance.GetUserCustomizedCurrentWorkout(workoutTemplateId);
                            }
                            catch (Exception ex)
                            {
                                // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                                // {
                                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                //     Message = AppResources.PleaseCheckInternetConnection,
                                //     Title = AppResources.ConnectionError
                                // });

                                await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Ok","");

                                return;
                            }

                        }
                        App.IsNUX = false;
                        if (nextWorkout != null)
                        {
                            try
                            {
                                CurrentLog.Instance.CurrentWorkoutTemplate = nextWorkout;
                                CurrentLog.Instance.WorkoutTemplateCurrentExercise = nextWorkout.Exercises.First();
                                CurrentLog.Instance.WorkoutStarted = true;
                                if (Device.RuntimePlatform.Equals(Device.Android))
                                {
                                    await Navigation.PushAsync(new KenkoDemoWorkoutExercisePage());
                                    //await PagesFactory.PopToRootThenPushAsync<KenkoDemoWorkoutExercisePage>(true);
                                    App.IsDemoProgress = false;
                                    App.IsWelcomeBack = true;
                                    App.IsNewUser = true;
                                    LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
                                    CurrentLog.Instance.Exercise1RM.Clear();
                                    //await PopupNavigation.Instance.PushAsync(new ReminderPopup());
                                    Device.BeginInvokeOnMainThread(async () =>
                                    {
                                        await Navigation.PopToRootAsync();
                                        //await PagesFactory.PopToRootAsync(true);
                                    });
                                    MessagingCenter.Send<SignupFinishMessage>(new SignupFinishMessage(), "SignupFinishMessage");
                                }
                                else
                                {

                                    App.IsDemoProgress = false;
                                    App.IsWelcomeBack = true;
                                    App.IsNewUser = true;
                                    LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
                                    CurrentLog.Instance.Exercise1RM.Clear();

                                    await Navigation.PushAsync(new KenkoDemoWorkoutExercisePage());
                                    //await PagesFactory.PopToRootMoveAsync(true);
                                    //await PagesFactory.PushMoveAsync<KenkoDemoWorkoutExercisePage>();
                                    MessagingCenter.Send<SignupFinishMessage>(new SignupFinishMessage(), "SignupFinishMessage");
                                }
                            }
                            catch (Exception ex)
                            {

                            }
                        }
                        else
                        {
                            await Navigation.PopToRootAsync();
                            //await PagesFactory.PopToRootAsync(true);
                            App.IsDemoProgress = false;
                            App.IsWelcomeBack = true;
                            App.IsNewUser = true;
                            LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");

                        }

                    }
                }
                catch (Exception ex)
                {

                }

            }

            try
            {
                OpenPreview(registerModel);
            }
            catch (Exception ex)
            {

            }




        }
        else
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     Message = AppResources.EmailAndPasswordDoNotMatch,
            //     Title = AppResources.UnableToLogIn,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.UnableToLogIn,
                       AppResources.EmailAndPasswordDoNotMatch,"Ok","");
        }
    }
    async void CreateAccountBeforeDemoButton_Clicked()
    {
        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError
            // });

             await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Ok","");
            return;
        }
        LocalDBManager.Instance.SetDBSetting("LoginType", "Email");

        int? workoutId = null;
        int? programId = null;
        int? remainingWorkout = null;
        var WorkoutInfo2 = "";
        //Setup Program

        //SignUp here
        RegisterModel registerModel = new RegisterModel();

        registerModel.Firstname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
        registerModel.EmailAddress = LocalDBManager.Instance.GetDBSetting("email")?.Value;
        registerModel.MassUnit = "lb";
        registerModel.BodyWeight = new MultiUnityWeight(150, "lb");
        registerModel.Password = LocalDBManager.Instance.GetDBSetting("password")?.Value;
        registerModel.ConfirmPassword = LocalDBManager.Instance.GetDBSetting("password")?.Value;

        try
        {

            BooleanModel registerResponse = await DrMuscleRestClient.Instance.RegisterUserBeforeDemo(registerModel);
            if (registerResponse.Result)
            {
                _firebase.LogEvent("account_created", "");
            }
        }
        catch (Exception ex)
        {

        }
        //Login
        LoginSuccessResult lr = await DrMuscleRestClient.Instance.LoginWithoutLoader(new LoginModel()
        {
            Username = registerModel.EmailAddress,
            Password = registerModel.Password
        });

        if (lr != null && !string.IsNullOrEmpty(lr.access_token))
        {
            DateTime current = DateTime.Now;

            UserInfosModel uim = await DrMuscleRestClient.Instance.GetUserInfoWithoutLoader();

            LocalDBManager.Instance.SetDBSetting("email", uim.Email);
            LocalDBManager.Instance.SetDBSetting("firstname", !string.IsNullOrEmpty(uim.Firstname) ? ((uim.Firstname.Contains(' ')) ? uim.Firstname.Split(' ')[0] : uim.Firstname) : uim.Firstname);
            LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
            //LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
            LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
            LocalDBManager.Instance.SetDBSetting("password", registerModel.Password);
            LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
            LocalDBManager.Instance.SetDBSetting("token_expires_date", current.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
            LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
            LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
            LocalDBManager.Instance.SetDBSetting("reprangeType", uim.ReprangeType.ToString());
            LocalDBManager.Instance.SetDBSetting("repsminimum", Convert.ToString(uim.RepsMinimum));
            LocalDBManager.Instance.SetDBSetting("repsmaximum", Convert.ToString(uim.RepsMaximum));
            LocalDBManager.Instance.SetDBSetting("onboarding_seen", "true");
            LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
            LocalDBManager.Instance.SetDBSetting("timer_vibrate", uim.IsVibrate ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_sound", uim.IsSound ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_123_sound", uim.IsTimer321 ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_reps_sound", uim.IsRepsSound ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_autostart", uim.IsAutoStart ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_autoset", uim.IsAutomatchReps ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_fullscreen", uim.IsFullscreen ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("QuickMode", uim.IsQuickMode == true ? "true" : uim.IsQuickMode == null ? "null" : "false");                //if (uim.ReminderTime != null)

            if (uim.WarmupsValue != null)
            {
                LocalDBManager.Instance.SetDBSetting("warmups", Convert.ToString(uim.WarmupsValue));
            }
            if (uim.Increments != null)
                LocalDBManager.Instance.SetDBSetting("workout_increments", uim.Increments.Kg.ToString().ReplaceWithDot());
            if (uim.Max != null)
                LocalDBManager.Instance.SetDBSetting("workout_max", uim.Max.Kg.ToString().ReplaceWithDot());
            if (uim.Min != null)
                LocalDBManager.Instance.SetDBSetting("workout_min", uim.Min.Kg.ToString().ReplaceWithDot());
            if (uim.BodyWeight != null)
            {
                LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString().ReplaceWithDot());
            }
            if (uim.WeightGoal != null)
            {
                LocalDBManager.Instance.SetDBSetting("WeightGoal", uim.WeightGoal.Kg.ToString().ReplaceWithDot());
            }
            LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "true");
            //await AccountCreatedPopup();
            //SetUpRestOnboarding();
        }
        else
        {
            // UserDialogs.Instance.Alert(new AlertConfig()
            // {
            //     Message = AppResources.EmailAndPasswordDoNotMatch,
            //     Title = AppResources.UnableToLogIn,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // });

             await HelperClass.DisplayCustomPopupForResult(AppResources.UnableToLogIn,
                       AppResources.EmailAndPasswordDoNotMatch,"Ok","");
        }
    }

    private async Task AccountCreatedPopup()
    {
        var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
        var modalPage = new Views.GeneralPopup("truestate.png", "Success!", "Account created", "Customize program");
        modalPage.Closed += (sender2, e2) =>
        {
            waitHandle.Set();
        };
        await Application.Current.MainPage.ShowPopupAsync(modalPage);
        await Task.Run(() => waitHandle.WaitOne());

    }

    private void SetupEquipment(UserInfosModel uim)
    {
        LocalDBManager.Instance.SetDBSetting("KgBarWeight", uim.KgBarWeight == null ? "20" : Convert.ToString(uim.KgBarWeight).ReplaceWithDot());
        LocalDBManager.Instance.SetDBSetting("LBBarWeight", uim.LbBarWeight == null ? "45" : Convert.ToString(uim.LbBarWeight).ReplaceWithDot());

        if (uim.EquipmentModel != null)
        {
            LocalDBManager.Instance.SetDBSetting("Equipment", uim.EquipmentModel.IsEquipmentEnabled ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("ChinUp", uim.EquipmentModel.IsChinUpBarEnabled ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("Dumbbell", uim.EquipmentModel.IsDumbbellEnabled ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("Plate", uim.EquipmentModel.IsPlateEnabled ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("Pully", uim.EquipmentModel.IsPullyEnabled ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("Bands", uim.EquipmentModel.IsBands ? "true" : "false");

            LocalDBManager.Instance.SetDBSetting("HomeMainEquipment", uim.EquipmentModel.IsHomeEquipmentEnabled ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("HomeChinUp", uim.EquipmentModel.IsHomeChinupBar ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("HomeDumbbell", uim.EquipmentModel.IsHomeDumbbell ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("HomePlate", uim.EquipmentModel.IsHomePlate ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("HomePully", uim.EquipmentModel.IsHomePully ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("HomeBands", uim.EquipmentModel.IsHomeBands ? "true" : "false");


            LocalDBManager.Instance.SetDBSetting("OtherMainEquipment", uim.EquipmentModel.IsOtherEquipmentEnabled ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("OtherChinUp", uim.EquipmentModel.IsOtherChinupBar ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("OtherDumbbell", uim.EquipmentModel.IsOtherDumbbell ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("OtherPlate", uim.EquipmentModel.IsOtherPlate ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("OtherPully", uim.EquipmentModel.IsOtherPully ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("OtherBands", uim.EquipmentModel.IsOtherBands ? "true" : "false");


            if (uim.EquipmentModel.Active == "gym")
                LocalDBManager.Instance.SetDBSetting("GymEquipment", "true");
            if (uim.EquipmentModel.Active == "home")
                LocalDBManager.Instance.SetDBSetting("HomeEquipment", "true");
            if (uim.EquipmentModel.Active == "other")
                LocalDBManager.Instance.SetDBSetting("OtherEquipment", "true");
            if (!string.IsNullOrEmpty(uim.EquipmentModel.AvilablePlate))
            {
                LocalDBManager.Instance.SetDBSetting("PlatesKg", uim.EquipmentModel.AvilablePlate);
                LocalDBManager.Instance.SetDBSetting("HomePlatesKg", uim.EquipmentModel.AvilableHomePlate);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesKg", uim.EquipmentModel.AvilableOtherPlate);

                LocalDBManager.Instance.SetDBSetting("PlatesLb", uim.EquipmentModel.AvilableLbPlate);
                LocalDBManager.Instance.SetDBSetting("HomePlatesLb", uim.EquipmentModel.AvilableHomeLbPlate);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesLb", uim.EquipmentModel.AvilableHomeLbPlate);
            }
            else
            {
                var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True";
                LocalDBManager.Instance.SetDBSetting("PlatesKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomePlatesKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesKg", kgString);

                var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
                LocalDBManager.Instance.SetDBSetting("PlatesLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomePlatesLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesLb", lbString);
            }
            if (!string.IsNullOrEmpty(uim.EquipmentModel.AvilableDumbbell))
            {
                LocalDBManager.Instance.SetDBSetting("DumbbellKg", uim.EquipmentModel.AvilableDumbbell);
                LocalDBManager.Instance.SetDBSetting("HomeDumbbellKg", uim.EquipmentModel.AvilableHomeDumbbell);
                LocalDBManager.Instance.SetDBSetting("OtherDumbbellKg", uim.EquipmentModel.AvilableOtherDumbbell);

                LocalDBManager.Instance.SetDBSetting("DumbbellLb", uim.EquipmentModel.AvilableLbDumbbell);
                LocalDBManager.Instance.SetDBSetting("HomeDumbbellLb", uim.EquipmentModel.AvilableHomeLbDumbbell);
                LocalDBManager.Instance.SetDBSetting("OtherDumbbellLb", uim.EquipmentModel.AvilableHomeLbDumbbell);
            }
            else
            {
                var kgString = "50_2_True|47.5_2_True|45_2_True|42.5_2_True|40_2_True|37.5_2_True|35_2_True|32.5_2_True|30_2_True|27.5_2_True|25_2_True|22.5_2_True|20_2_True|17.5_2_True|15_2_True|12.5_2_True|10_2_True|7.5_2_True|5_2_True|2.5_2_True|1_2_True";
                LocalDBManager.Instance.SetDBSetting("DumbbellKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomeDumbbellKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherDumbbellKg", kgString);

                var lbString = "90_2_True|85_2_True|80_2_True|75_2_True|70_2_True|65_2_True|60_2_True|55_2_True|50_2_True|45_2_True|40_2_True|35_2_True|30_2_True|25_2_True|20_2_True|15_2_True|12_2_True|10_2_True|8_2_True|5_2_True|3_2_True|2_2_True";
                LocalDBManager.Instance.SetDBSetting("DumbbellLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomeDumbbellLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherDumbbellLb", lbString);
            }

            if (!string.IsNullOrEmpty(uim.EquipmentModel.AvilablePulley))
            {

                LocalDBManager.Instance.SetDBSetting("PulleyKg", uim.EquipmentModel.AvilablePulley);
                LocalDBManager.Instance.SetDBSetting("HomePulleyKg", uim.EquipmentModel.AvilableHomePulley);
                LocalDBManager.Instance.SetDBSetting("OtherPulleyKg", uim.EquipmentModel.AvilableOtherPulley);


                LocalDBManager.Instance.SetDBSetting("PulleyLb", uim.EquipmentModel.AvilableLbPulley);
                LocalDBManager.Instance.SetDBSetting("HomePulleyLb", uim.EquipmentModel.AvilableHomeLbPulley);
                LocalDBManager.Instance.SetDBSetting("OtherPulleyLb", uim.EquipmentModel.AvilableOtherLbPulley);
            }
            else
            {
                var kgString = "5_20_True|1.5_2_True";
                var lbString = "10_20_True|5_2_True|2.5_2_True";
                LocalDBManager.Instance.SetDBSetting("PulleyKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomePulleyKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherPulleyKg", kgString);


                LocalDBManager.Instance.SetDBSetting("PulleyLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomePulleyLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherPulleyLb", lbString);
            }

            if (!string.IsNullOrEmpty(uim.EquipmentModel.AvilableBands))
            {

                LocalDBManager.Instance.SetDBSetting("BandsKg", uim.EquipmentModel.AvilableBands);
                LocalDBManager.Instance.SetDBSetting("HomeBandsKg", uim.EquipmentModel.AvilableHomeBands);
                LocalDBManager.Instance.SetDBSetting("OtherBandsKg", uim.EquipmentModel.AvilableOtherBands);


                LocalDBManager.Instance.SetDBSetting("BandsLb", uim.EquipmentModel.AvilableLbBands);
                LocalDBManager.Instance.SetDBSetting("HomeBandsLb", uim.EquipmentModel.AvilableHomeLbBands);
                LocalDBManager.Instance.SetDBSetting("OtherBandsLb", uim.EquipmentModel.AvilableOtherLbBands);
            }
            else
            {

                var kgString = "Black_40_2_True|Blue_30_2_True|Green_20_2_True|Red_10_2_True|Yellow_4_2_True";
                var lbString = "Black_90_2_True|Blue_65_2_True|Green_45_2_True|Red_25_2_True|Yellow_10_2_True";

                LocalDBManager.Instance.SetDBSetting("BandsKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomeBandsKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherBandsKg", kgString);

                LocalDBManager.Instance.SetDBSetting("BandsLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomeBandsLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherBandsLb", lbString);
            }
        }
        else
        {
            LocalDBManager.Instance.SetDBSetting("Equipment", "false");
            LocalDBManager.Instance.SetDBSetting("ChinUp", "true");
            LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");
            LocalDBManager.Instance.SetDBSetting("Plate", "true");
            LocalDBManager.Instance.SetDBSetting("Pully", "true");
            LocalDBManager.Instance.SetDBSetting("Bands", "true");

            LocalDBManager.Instance.SetDBSetting("HomeMainEquipment", "false");
            LocalDBManager.Instance.SetDBSetting("HomeChinUp", "true");
            LocalDBManager.Instance.SetDBSetting("HomeDumbbell", "true");
            LocalDBManager.Instance.SetDBSetting("HomePlate", "true");
            LocalDBManager.Instance.SetDBSetting("HomePully", "true");
            LocalDBManager.Instance.SetDBSetting("HomeBands", "true");

            LocalDBManager.Instance.SetDBSetting("OtherEquipment", "false");
            LocalDBManager.Instance.SetDBSetting("OtherChinUp", "true");
            LocalDBManager.Instance.SetDBSetting("OtherDumbbell", "true");
            LocalDBManager.Instance.SetDBSetting("OtherPlate", "true");
            LocalDBManager.Instance.SetDBSetting("OtherPully", "true");
            LocalDBManager.Instance.SetDBSetting("OtherBands", "true");

        }
    }

    async void CreateAccountAfterDemoButton_Clicked()
    {
        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {

            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // });

             await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Ok","");
            return;
        }
        if (isProcessing)
            return;
        isProcessing = true;
        //LocalDBManager.Instance.SetDBSetting("ExerciseVariety", "Less");
        LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
        DBSetting experienceSetting = LocalDBManager.Instance.GetDBSetting("experience");
        DBSetting workoutPlaceSetting = LocalDBManager.Instance.GetDBSetting("workout_place");
        int? workoutId = null;
        int? programId = null;
        int? remainingWorkout = null;
        var WorkoutInfo2 = "";



        string ProgramLabel = AppResources.NotSetUp;
        int age = 35;
        if(LocalDBManager.Instance.GetDBSetting("Age")?.Value != null)
             age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age")?.Value);

        var level = 0;
        if (LocalDBManager.Instance.GetDBSetting("MainLevel") != null)
            level = int.Parse(LocalDBManager.Instance.GetDBSetting("MainLevel").Value);
        if (LocalDBManager.Instance.GetDBSetting("ExerciseVariety")?.Value == "More")
            level += 1;
        bool isSplit = LocalDBManager.Instance.GetDBSetting("MainProgram")?.Value?.Contains("Split") ?? false;
        bool isGym = workoutPlaceSetting?.Value == "gym";
        var mo = AppThemeConstants.GetLevelProgram(level, isGym, !isSplit, LocalDBManager.Instance.GetDBSetting("MainProgram")?.Value);

        if (workoutPlaceSetting?.Value == "homeBodyweightOnly")
        {


            if (level <= 1)
            {
                mo.workoutName = "Bodyweight 1";
                mo.workoutid = 12645;
                mo.programid = 487;
                mo.reqWorkout = 12;
                mo.programName = "Bodyweight Level 1";
            }
            else if (level <= 2)
            {
                mo.workoutName = "Bodyweight 2";
                mo.workoutid = 12646;
                mo.programid = 488;
                mo.reqWorkout = 12;
                mo.programName = "Bodyweight Level 2";
            }

            else if (level == 3)
            {
                mo.workoutName = "Bodyweight 3";
                mo.workoutid = 14017;
                mo.programid = 923;
                mo.reqWorkout = 15;
                mo.programName = "Bodyweight Level 3";
            }
            else if (level >= 4)
            {
                mo.workoutName = "Bodyweight 4";
                mo.workoutid = 14019;
                mo.programid = 924;
                mo.reqWorkout = 15;
                mo.programName = "Bodyweight Level 4";
            }

        }
        else if (workoutPlaceSetting?.Value == "homeBodyweightBandsOnly")
        {
            //mo.workoutName = "[Home] Buffed w/ Bands";
            //mo.programName = "[Home] Buffed w/ Bands Level 1";
            //mo.workoutid = 15377;
            //mo.programid = 1339;
            //mo.reqWorkout = 15;

            //if (LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value == "an active, experienced lifter" )
            //{
            mo.workoutName = "[Home] Buffed w/ Bands 2A";
            mo.programName = "[Home] Buffed w/ Bands Level 2";
            mo.workoutid = 15376;
            mo.programid = 1338;
            mo.reqWorkout = 18;
            if (LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value == "an active, experienced lifter" && LocalDBManager.Instance.GetDBSetting("ExerciseVariety")?.Value == "More")
            {
                mo.workoutName = "[Home] Buffed w/ Bands 3A";
                mo.programName = "[Home] Buffed w/ Bands Level 3";
                mo.workoutid = 17323;
                mo.programid = 2032;
                mo.reqWorkout = 24;
            }
        }
        string gender = LocalDBManager.Instance.GetDBSetting("gender")?.Value;
        string customExperience = LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value;
        string workplace = LocalDBManager.Instance.GetDBSetting("workout_place")?.Value;
        string exeVariety = LocalDBManager.Instance.GetDBSetting("ExerciseVariety")?.Value;
        //if (gender == "Woman" && (customExperience == "new to training" || customExperience == "returning from a break"))
        //{
        //    if (workplace == "gym")
        //    {
        //        mo.workoutid = 21785;
        //        mo.programid = 3034;
        //        mo.workoutName = "[Gym] Abs, Legs & Glutes Focus";
        //        mo.programName = "[Gym] Abs, Legs & Glutes Focus Level 1";
        //        mo.reqWorkout = 18;

        //    }
        //    else if (workplace == "home")
        //    {
        //        mo.workoutid = 21939;
        //        mo.programid = 3069;
        //        mo.workoutName = "[Home] Abs, Legs & Glutes Focus";
        //        mo.programName = "[Home] Abs, Legs & Glutes Focus Level 1";
        //        mo.reqWorkout = 18;
        //    }
        //}
        //else
        if (gender == "Woman" && (customExperience == "an active, experienced lifter" || exeVariety == "More"))
        {
            if (workplace == "gym")
            {
                //mo.workoutid = 21783; Actuall workout Id
                mo.workoutid = 21782;
                mo.programid = 3032;
                mo.workoutName = "[Gym] Abs, Legs & Glutes Focus 3A";
                mo.programName = "[Gym] Abs, Legs & Glutes Focus Level 3";
                mo.reqWorkout = 24;

            }
            else if (workplace == "home")
            {
                //mo.workoutid = 21937; Actuall workout Id
                mo.workoutid = 21936;
                mo.programid = 3067;
                mo.workoutName = "[Home] Abs, Legs & Glutes Focus 3A";
                mo.programName = "[Home] Abs, Legs & Glutes Focus Level 3";
                mo.reqWorkout = 24;
            }
        }
        else if (gender == "Woman")
        {
            if (workplace == "gym")
            {
                //mo.workoutid = 21783; Actuall workout Id
                mo.workoutid = 21784;
                mo.programid = 3033;
                mo.workoutName = "[Gym] Abs, Legs & Glutes Focus 2A";
                mo.programName = "[Gym] Abs, Legs & Glutes Focus Level 2";
                mo.reqWorkout = 24;

            }
            else if (workplace == "home")
            {
                //mo.workoutid = 21937; Actuall workout Id
                mo.workoutid = 21938;
                mo.programid = 3068;
                mo.workoutName = "[Home] Abs, Legs & Glutes Focus 2A";
                mo.programName = "[Home] Abs, Legs & Glutes Focus Level 2";
                mo.reqWorkout = 24;
            }
        }



        try
        {
            LocalDBManager.Instance.SetDBSetting("recommendedWorkoutId", mo.workoutid.ToString());
            LocalDBManager.Instance.SetDBSetting("recommendedWorkoutLabel", mo.workoutName);
            LocalDBManager.Instance.SetDBSetting("recommendedProgramId", mo.programid.ToString());
            LocalDBManager.Instance.SetDBSetting("recommendedRemainingWorkout", mo.reqWorkout.ToString());

            LocalDBManager.Instance.SetDBSetting("recommendedProgramLabel", mo.programName);
            //}
            //SignUp here

            workoutId = mo.workoutid;
            WorkoutInfo2 = mo.workoutName;
            programId = mo.programid;
            ProgramLabel = mo.programName;
            remainingWorkout = mo.reqWorkout;
        }
        catch (Exception ex)
        {

        }

        RegisterModel registerModel = new RegisterModel();

        registerModel.Firstname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
        registerModel.EmailAddress = LocalDBManager.Instance.GetDBSetting("email")?.Value;
        registerModel.SelectedGender = LocalDBManager.Instance.GetDBSetting("gender")?.Value;
        registerModel.MassUnit = LocalDBManager.Instance.GetDBSetting("massunit")?.Value;
        registerModel.IsMobility = IsIncludeMobility;
        registerModel.MobilityLevel = mobilityLevel;
        registerModel.IsReminderEmail = true;
        registerModel.TimeBeforeWorkout = 5;
        if (LocalDBManager.Instance.GetDBSetting("QuickMode") == null)
            registerModel.IsQuickMode = false;
        else
        {
            if (LocalDBManager.Instance.GetDBSetting("QuickMode")?.Value == "null")
                registerModel.IsQuickMode = null;
            else
                registerModel.IsQuickMode = LocalDBManager.Instance.GetDBSetting("QuickMode")?.Value == "true" ? true : false;
        }
        if (LocalDBManager.Instance.GetDBSetting("Age") != null)
            registerModel.Age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age")?.Value);
        registerModel.RepsMinimum = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("repsminimum")?.Value);
        registerModel.RepsMaximum = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("repsmaximum")?.Value);
        if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
            registerModel.BodyWeight = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.Replace(",", ".").Replace("٫", "."), CultureInfo.InvariantCulture), "kg");
        if (LocalDBManager.Instance.GetDBSetting("WeightGoal") != null)
            registerModel.WeightGoal = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal").Value.Replace(",", ".").Replace("٫", "."), CultureInfo.InvariantCulture), "kg");

        registerModel.Password = LocalDBManager.Instance.GetDBSetting("password") != null ? LocalDBManager.Instance.GetDBSetting("password").Value : "";
        registerModel.ConfirmPassword = LocalDBManager.Instance.GetDBSetting("password") != null ? LocalDBManager.Instance.GetDBSetting("password").Value : "";
        registerModel.LearnMoreDetails = learnMore;
        registerModel.IsHumanSupport = IsHumanSupport;
        registerModel.IsCardio = IsIncludeCardio;
        registerModel.BodyPartPrioriy = bodypartName;
        registerModel.SetStyle = SetStyle;
        registerModel.IsPyramid = IsPyramid;
        registerModel.isDing = isDing;
        registerModel.WorkoutDuration = workoutDurationLenggth;
        if (LocalDBManager.Instance.GetDBSetting("Height")?.Value != null && Config.UserHeight != 0)
        {
            registerModel.Height = Config.UserHeight;
        }
        if (LocalDBManager.Instance.GetDBSetting("email") != null && LocalDBManager.Instance.GetDBSetting("email").Value.Contains("yopmail") || (LocalDBManager.Instance.GetDBSetting("Environment") != null && LocalDBManager.Instance.GetDBSetting("Environment").Value != "Production"))
        { registerModel.MainGoal = ""; }
        else
            registerModel.MainGoal = isBetaExperience == true ? "Beta" : isBetaExperience == false ? "Normal" : "";
        registerModel.ProgramId = programId;
        if (LocalDBManager.Instance.GetDBSetting("ReminderDays") != null && LocalDBManager.Instance.GetDBSetting("ReminderTime") != null)
        {
            try
            {
                registerModel.ReminderTime = TimeSpan.Parse(LocalDBManager.Instance.GetDBSetting("ReminderTime")?.Value);
                registerModel.ReminderDays = LocalDBManager.Instance.GetDBSetting("ReminderDays")?.Value;
                if (registerModel.ReminderDays.Contains("1"))
                    registerModel.IsRecommendedReminder = false;

            }
            catch (Exception ex)
            {

            }

        }
        else
            registerModel.IsRecommendedReminder = IsRecommendedReminder;
        if (IsEquipment)
        {
            var model = new EquipmentModel();

            if (LocalDBManager.Instance.GetDBSetting("workout_place")?.Value == "gym")
            {
                model.IsEquipmentEnabled = true;
                model.IsDumbbellEnabled = isDumbbells;
                model.IsPlateEnabled = IsPlates;
                model.IsPullyEnabled = IsPully;
                model.IsChinUpBarEnabled = IsChinupBar;
                model.IsBands = true;
                model.Active = "gym";

                model.IsHomeEquipmentEnabled = false;
                model.IsHomeDumbbell = true;
                model.IsHomePlate = true;
                model.IsHomePully = true;
                model.IsHomeChinupBar = true;
                model.IsHomeBands = true;
            }
            else
            {
                model.IsEquipmentEnabled = false;
                model.IsDumbbellEnabled = true;
                model.IsPlateEnabled = true;
                model.IsPullyEnabled = true;
                model.IsChinUpBarEnabled = true;
                model.IsBands = true;
                model.IsOtherEquipmentEnabled = false;
                model.IsOtherDumbbell = true;                    //every thing was opposite only ishomebands was true
                model.IsOtherPlate = true;
                model.IsOtherPully = true;
                model.IsOtherChinupBar = true;
                model.IsOtherBands = true;
                model.IsHomeEquipmentEnabled = true;
                model.IsHomeDumbbell = isDumbbells;
                model.IsHomePlate = IsPlates;
                model.IsHomePully = IsPully;
                model.IsHomeChinupBar = IsChinupBar;
                model.IsHomeBands = true;
                model.Active = "home";
            }
            var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True";
            model.AvilablePlate = kgString;
            model.AvilableHomePlate = kgString;
            model.AvilableOtherPlate = kgString;
            var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
            model.AvilableLbPlate = lbString;
            model.AvilableHomeLbPlate = lbString;
            model.AvilableOtherLbPlate = lbString;

            var kgString1 = "50_2_True|47.5_2_True|45_2_True|42.5_2_True|40_2_True|37.5_2_True|35_2_True|32.5_2_True|30_2_True|27.5_2_True|25_2_True|22.5_2_True|20_2_True|17.5_2_True|15_2_True|12.5_2_True|10_2_True|7.5_2_True|5_2_True|2.5_2_True|1_2_True";
            model.AvilableDumbbell = kgString1;
            model.AvilableHomeDumbbell = kgString1;
            model.AvilableOtherDumbbell = kgString1;
            var lbString1 = "90_2_True|85_2_True|80_2_True|75_2_True|70_2_True|65_2_True|60_2_True|55_2_True|50_2_True|45_2_True|40_2_True|35_2_True|30_2_True|25_2_True|20_2_True|15_2_True|12_2_True|10_2_True|8_2_True|5_2_True|3_2_True|2_2_True";
            model.AvilableLbDumbbell = lbString1;
            model.AvilableHomeLbDumbbell = lbString1;
            model.AvilableOtherLbDumbbell = lbString1;

            var kgString2 = "5_20_True|1.5_2_True";
            var lbString2 = "10_20_True|5_2_True|2.5_2_True";
            model.AvilablePulley = kgString2;
            model.AvilableHomePulley = kgString2;
            model.AvilableOtherPulley = kgString2;

            model.AvilableLbPulley = lbString2;
            model.AvilableHomeLbPulley = lbString2;
            model.AvilableOtherLbPulley = lbString2;

            var kgString3 = "Black_40_2_True|Blue_30_2_True|Green_20_2_True|Red_10_2_True|Yellow_4_2_True";
            var lbString3 = "Black_90_2_True|Blue_65_2_True|Green_45_2_True|Red_25_2_True|Yellow_10_2_True";
            model.AvilableBands = kgString3;
            model.AvilableHomeBands = kgString3;
            model.AvilableOtherBands = kgString3;

            model.AvilableLbBands = lbString3;
            model.AvilableHomeLbBands = lbString3;
            model.AvilableOtherLbBands = lbString3;


            registerModel.EquipmentModel = model;
        }
        if (IncrementUnit != null)
        {
            registerModel.Increments = IncrementUnit.Kg;
        }
        //if (LocalDBManager.Instance.GetDBSetting("workout_increments") != null)
        //{
        //    var increments = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_increments").Value, System.Globalization.CultureInfo.InvariantCulture);
        //    var incrementsWeight = new MultiUnityWeight(increments, LocalDBManager.Instance.GetDBSetting("massunit").Value);
        //    registerModel.Increments = incrementsWeight.Kg;
        //}

        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Ok","");
            return;
        }
        OpenPreview(registerModel);
        UserInfosModel registerResponse = await DrMuscleRestClient.Instance.RegisterUserAfterDemo(registerModel);

        if (registerResponse != null)
        {


            CancelNotification();
            SetTrialUserNotifications();
            DateTime current = DateTime.Now;

            UserInfosModel uim = registerResponse;

            LocalDBManager.Instance.SetDBSetting("email", uim.Email);
            LocalDBManager.Instance.SetDBSetting("firstname", !string.IsNullOrEmpty(uim.Firstname) ? ((uim.Firstname.Contains(' ')) ? uim.Firstname.Split(' ')[0] : uim.Firstname) : uim.Firstname);
            LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
            LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
            LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
            LocalDBManager.Instance.SetDBSetting("password", registerModel.Password);
            //LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
            //LocalDBManager.Instance.SetDBSetting("token_expires_date", current.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
            LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
            LocalDBManager.Instance.SetDBSetting("IsMobility", uim.IsMobility == null ? null : uim.IsMobility == false ? "false" : "true");
            LocalDBManager.Instance.SetDBSetting("MaxWorkoutDuration", uim.WorkoutDuration.ToString());
            LocalDBManager.Instance.SetDBSetting("IsExerciseQuickMode", uim.IsExerciseQuickMode == null ? null : uim.IsExerciseQuickMode == false ? "false" : "true");
            LocalDBManager.Instance.SetDBSetting("MobilityLevel", uim.MobilityLevel);
            LocalDBManager.Instance.SetDBSetting("MobilityRep", uim.MobilityRep == null ? "" : Convert.ToString(uim.MobilityRep));
            LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
            LocalDBManager.Instance.SetDBSetting("reprangeType", uim.ReprangeType.ToString());
            LocalDBManager.Instance.SetDBSetting("repsminimum", Convert.ToString(uim.RepsMinimum));
            LocalDBManager.Instance.SetDBSetting("repsmaximum", Convert.ToString(uim.RepsMaximum));
            LocalDBManager.Instance.SetDBSetting("onboarding_seen", "true");
            if (uim.IsPyramid)
            {
                LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                LocalDBManager.Instance.SetDBSetting("IsRPyramid", "true");
                LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
            }
            else if (uim.IsNormalSet == null || uim.IsNormalSet == true)
            {
                LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
                LocalDBManager.Instance.SetDBSetting("IsPyramid", uim.IsNormalSet == null ? "true" : "false");
            }
            else
            {
                LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
            }
            LocalDBManager.Instance.SetDBSetting("timer_vibrate", uim.IsVibrate ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_sound", uim.IsSound ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_123_sound", uim.IsTimer321 ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_reps_sound", uim.IsRepsSound ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_autostart", uim.IsAutoStart ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_autoset", uim.IsAutomatchReps ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("timer_fullscreen", uim.IsFullscreen ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("QuickMode", uim.IsQuickMode == true ? "true" : uim.IsQuickMode == null ? "null" : "false"); if (uim.Age != null)
                LocalDBManager.Instance.SetDBSetting("Age", Convert.ToString(uim.Age));
            if (uim.TargetIntake != null && uim.TargetIntake != 0)
                LocalDBManager.Instance.SetDBSetting("TargetIntake", uim.TargetIntake.ToString());
            LocalDBManager.Instance.SetDBSetting("RecommendedReminder", uim.IsRecommendedReminder == true ? "true" : uim.IsRecommendedReminder == null ? "null" : "false");
            LocalDBManager.Instance.SetDBSetting("IsReferenceSetReps", uim.IsReferenseSet ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("IsEmailReminder", uim.IsReminderEmail ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("ReminderHours", uim.ReminderBeforeHours.ToString());
            if (uim.IsRecommendedReminder == true)
            {
                var timeSpan = new TimeSpan(0, 22, 0, 0);
                // uncomment code please
                //IAlarmAndNotificationService alarmAndNotificationService = new AlarmAndNotificationService();
                //alarmAndNotificationService.CancelNotification(101);
                //alarmAndNotificationService.CancelNotification(102);
                //alarmAndNotificationService.CancelNotification(103);
                //alarmAndNotificationService.CancelNotification(104);
                //alarmAndNotificationService.CancelNotification(105);
                //alarmAndNotificationService.CancelNotification(106);
                //alarmAndNotificationService.CancelNotification(107);
                //alarmAndNotificationService.CancelNotification(108); alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 1111, NotificationInterval.Week);
            }
            if (uim.WarmupsValue != null)
            {
                LocalDBManager.Instance.SetDBSetting("warmups", Convert.ToString(uim.WarmupsValue));
            }
            if (uim.Increments != null)
                LocalDBManager.Instance.SetDBSetting("workout_increments", uim.Increments.Kg.ToString().ReplaceWithDot());
            if (uim.Max != null)
                LocalDBManager.Instance.SetDBSetting("workout_max", uim.Max.Kg.ToString().ReplaceWithDot());
            if (uim.Min != null)
                LocalDBManager.Instance.SetDBSetting("workout_min", uim.Min.Kg.ToString().ReplaceWithDot());
            if (uim.BodyWeight != null)
            {
                LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString().ReplaceWithDot());
            }
            if (uim.WeightGoal != null)
            {
                LocalDBManager.Instance.SetDBSetting("WeightGoal", uim.WeightGoal.Kg.ToString().ReplaceWithDot());
            }
            SetupEquipment(uim);

            if (string.IsNullOrEmpty(uim.BodyPartPrioriy))
                LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
            else
                LocalDBManager.Instance.SetDBSetting("BodypartPriority", uim.BodyPartPrioriy.Trim());

            LocalDBManager.Instance.SetDBSetting("Cardio", uim.IsCardio ? "true" : "false");

            LocalDBManager.Instance.SetDBSetting("BackOffSet", uim.IsBackOffSet ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("1By1Side", uim.Is1By1Side ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("StrengthPhase", uim.IsStrength ? "true" : "false");

            ((App)Application.Current).displayCreateNewAccount = true;

            if (uim.Gender.Trim().ToLowerInvariant().Equals("man"))
                LocalDBManager.Instance.SetDBSetting("BackgroundImage", "Background2.png");
            else
                LocalDBManager.Instance.SetDBSetting("BackgroundImage", "BackgroundFemale.png");
            App.IsNewFirstTime = false;

            long workoutTemplateId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId").Value);
            long pId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedProgramId").Value);




            var upi = new GetUserProgramInfoResponseModel()
            {
                NextWorkoutTemplate = new WorkoutTemplateModel() { Id = workoutTemplateId, Label = LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel").Value },
                RecommendedProgram = new WorkoutTemplateGroupModel() { Id = pId, Label = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel").Value, RemainingToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value), RequiredWorkoutToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value) },
            };
            var ms = "Welcome! I'm Dr. Muscle, your AI coach. Trained on the latest exercise science by Dr. Carl Juneau, PhD. Ask me anything for a quick reply. A human will also reply in 1 business day.";

            DrMuscleRestClient.Instance.SendAdminMessageWithoutLoader(new ChatModel()
            {
                ReceiverId = AppThemeConstants.ChatReceiverId,
                Message = ms,
                IsFromAI = true
            });
            LocalDBManager.Instance.SetDBSetting("ReadyRegisterModel", JsonConvert.SerializeObject(registerModel));
            DrMuscleRestClient.Instance.SaveWorkoutV3WithoutLoader(new SaveWorkoutModel() { WorkoutId = workoutTemplateId });
            if (upi != null)
            {
                WorkoutTemplateModel nextWorkout = upi.NextWorkoutTemplate;
                if (upi.NextWorkoutTemplate.Exercises == null || upi.NextWorkoutTemplate.Exercises.Count() == 0)
                {
                    try
                    {
                        nextWorkout = await DrMuscleRestClient.Instance.GetUserCustomizedCurrentWorkoutWithoutLoader(workoutTemplateId);
                        //nextWorkout = w.Workouts.First(ww => ww.Id == upi.NextWorkoutTemplate.Id);
                    }
                    catch (Exception ex)
                    {
                        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                        // {
                        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        //     Message = AppResources.PleaseCheckInternetConnection,
                        //     Title = AppResources.ConnectionError
                        // });

                        await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Ok","");
                        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                        //{
                        //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        //    Message = AppResources.PleaseCheckInternetConnection,
                        //    Title = AppResources.ConnectionError
                        //});
                        return;
                    }

                }
                LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "");


            }


        }
        else
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     Message = "Something went wrong, please try again.",
            //     Title = AppResources.Error,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
                       "Something went wrong, please try again.","Ok","");
        }
        isProcessing = false;
    }

    async void OpenDemo()
    {
        try
        {
            CurrentLog.Instance.CurrentExercise = new ExerciceModel()
            {
                BodyPartId = 7,
                VideoUrl = "https://youtu.be/Plh1CyiPE_Y",
                IsBodyweight = true,
                IsEasy = false,
                IsFinished = false,
                IsMedium = false,
                IsNextExercise = false,
                IsNormalSets = false,
                IsSwapTarget = false,
                IsSystemExercise = true,
                IsTimeBased = false,
                IsUnilateral = false,
                Label = "Crunch",
                RepsMaxValue = null,
                RepsMinValue = null,
                Timer = null,
                Id = 864
            };
            App.IsDemoProgress = true;
            LocalDBManager.Instance.SetDBSetting("DemoProgress", "true");
            await Navigation.PushAsync(new NewDemoPage());
            //await PagesFactory.PushAsync<NewDemoPage>();
        }
        catch (Exception ex)
        {

        }
    }
    async Task OpenPreview(RegisterModel registerModel)
    {


        try
        {
            var waitHandle1 = new EventWaitHandle(false, EventResetMode.AutoReset);
            var modalPage1 = new Views.PreviewOverlay();
            //modalPage1.Disappearing += (sender2, e2) =>
            //{
            //    waitHandle1.Set();
            //};
            if(modalPage1 != null)
            {
                modalPage1.OnBeforeShow();
                this.ShowPopup(modalPage1);
                //await PopupNavigation.Instance.PushAsync(modalPage1);

                await Task.Run(() => waitHandle1.WaitOne());
            }
            


            //    }
            //    else
            //        OpenDemo();
            //}
        }
        catch (Exception ex)
        {
        }

        try
        {
            App.RegisterDeviceToken();
        }
        catch (Exception ex)
        {

        }
    }


    async void LearnMoreButton_Clicked(object sender, EventArgs e)
    {
        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                      AppResources.PleaseCheckInternetConnection,"Ok","");
            return;
        }
        await AddAnswer(((Button)sender).Text);
        lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
        await ClearOptions();
        await Task.Delay(300);



        DBSetting experienceSetting = LocalDBManager.Instance.GetDBSetting("experience");
        learnMore.Exp = "";
        learnMore.ExpDesc = "";
        if (experienceSetting != null)
        {
            if (experienceSetting.Value == "less1year")
            {
                learnMore.Exp = $"Less than 1 year";
                learnMore.ExpDesc = "You're new to lifting, so I recommend you train each muscle 3x a week on a full-body program. You will progress faster that way.";
            }
            if (experienceSetting.Value == "1-3years")
            {
                learnMore.Exp = $"1-3 years";
                learnMore.ExpDesc = "You have been lifting for over a year, so I recommend you train each muscle 2x a week on a split-body program. This gives you more time to recover between workouts for each muscle.";
            }
            if (experienceSetting.Value == "more3years")
            {
                learnMore.Exp = $"More than 3 years";
                learnMore.ExpDesc = "You have been lifting for 3+ years, so I recommend you train each muscle 2x a week on a split-body program with A and B days. This gives you more time to recover between workouts for each muscle and different exercises on A and B days. At your level, this is almost always needed to continue making progress.";
            }
            if (!string.IsNullOrEmpty(learnMore.Exp))
            {
                await AddQuestion($"Your experience: {learnMore.Exp}");
                await AddQuestion(learnMore.ExpDesc);
                await AddOptions(AppResources.GotIt, LearnMoreSteps2);
                return;
            }
        }

        LearnMoreSteps2(sender, e);

    }

    async void LearnMoreSteps2(object sender, EventArgs e)
    {
        await ClearOptions();
        var IsWoman = LocalDBManager.Instance.GetDBSetting("gender").Value == "Woman";
        if (LocalDBManager.Instance.GetDBSetting("reprange").Value == "BuildMuscle")
        {
            learnMore.Focus = IsWoman ? "Getting stronger" : "Building muscle";
            learnMore.FocusDesc = IsWoman ? "To get stronger, I recommend you repeat each exercise 5-12 times. You will also get stronger by lifting in that range." : "To build muscle, I recommend you repeat each exercise 5-12 times. You will also get stronger by lifting in that range.";
        }
        else if (LocalDBManager.Instance.GetDBSetting("reprange").Value == "BuildMuscleBurnFat")
        {
            learnMore.Focus = IsWoman ? "Overall fitness" : "Building muscle and burning fat";
            learnMore.FocusDesc = IsWoman ? "For overall fitness, I recommend you repeat each exercise 8-15 times." : "To build muscle and burn fat, I recommend you repeat each exercise 8-15 times.";
        }
        else if (LocalDBManager.Instance.GetDBSetting("reprange").Value == "FatBurning")
        {
            learnMore.Focus = "Burning fat";
            learnMore.FocusDesc = "To burn fat, I recommend you repeat each exercise 12-20 times. You will burn more calories by lifting in that range.";
        }
        await AddQuestion($"Your focus: {learnMore.Focus}");
        await AddQuestion(learnMore.FocusDesc);
        await AddOptions(AppResources.GotIt, LearnMoreSteps3);

    }
    async void LearnMoreSteps3(object sender, EventArgs e)
    {
        await ClearOptions();
        int age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age").Value);
        learnMore.Age = age;
        await AddQuestion($"Your age: {age}");
        if (age > 50)
            learnMore.AgeDesc = $"Recovery is slower at {age}. So, I added easy days to your program.";
        else if (age > 30)
            learnMore.AgeDesc = $"Recovery is a bit slower at {age}. So, I'm updating your program to make sure you train each muscle max 2x a week.";
        else
            learnMore.AgeDesc = "Recovery is optimal at your age. You can train each muscle as often as 3x a week.";
        //await AddQuestion(learnMore.AgeDesc);
        //await Task.Delay(100);
        if (Device.RuntimePlatform.Equals(Device.iOS))
        {
            lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
            lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
        }
        //await AddOptions(AppResources.GotIt, LearnMoreComplete
        LearnMoreComplete(sender, e);
    }

    async void LearnMoreComplete(object sender, EventArgs e)
    {
        await ClearOptions();
        await ProgramReadyInstruction();

        await AddOptions(AppResources.GotIt, GotItButton_Clicked);

        stackOptions.Children.Add(TermsConditionStack);
        TermsConditionStack.IsVisible = true;

    }
    async Task ProgramReadyInstruction()
    {


        string goalLabel = "";
        try
        {
            
            if (LocalDBManager.Instance.GetDBSetting("reprange")?.Value == "BuildMuscle")
            {
                goalLabel = AppResources.IUpdateItEveryTimeYouWorkOutBuild;
            }
            else if (LocalDBManager.Instance.GetDBSetting("reprange")?.Value == "BuildMuscleBurnFat")
            {
                goalLabel = AppResources.IUpdateItEveryTimeYouWorkOutBuildNBuildFat;
            }
            else if (LocalDBManager.Instance.GetDBSetting("reprange")?.Value == "FatBurning")
            {
                goalLabel = AppResources.IUpdateItEveryTimeYouWorkOutBurnFatFaster;
            }


        }
        catch (Exception ex)
        { }

        try
        {
            await ClearOptions();
            DBSetting experienceSetting = LocalDBManager.Instance.GetDBSetting("experience");
            DBSetting workoutPlaceSetting = LocalDBManager.Instance.GetDBSetting("workout_place");
            var programId = 0;
            if (experienceSetting != null && workoutPlaceSetting != null)
            {
                if (workoutPlaceSetting?.Value == "gym")
                {

                    if (experienceSetting?.Value == "less1year")
                    {
                        programId = 10;
                    }
                    if (experienceSetting?.Value == "1-3years")
                    {
                        programId = 15;
                    }
                    if (experienceSetting?.Value == "more3years")
                    {
                        programId = 16;
                    }
                }
                else if (workoutPlaceSetting?.Value == "home")
                {
                    if (experienceSetting?.Value == "less1year")
                    {
                        programId = 17;
                    }
                    if (experienceSetting?.Value == "1-3years")
                    {
                        programId = 21;
                    }
                    if (experienceSetting?.Value == "more3years")
                    {
                        programId = 22;
                    }
                }
                else if (workoutPlaceSetting?.Value == "homeBodyweightOnly")
                {
                    programId = 487;
                }
                else if (workoutPlaceSetting?.Value == "homeBodyweightBandsOnly")
                {
                    programId = 1339;
                    if (experienceSetting?.Value == "more3years")
                    {
                        programId = 1338;
                    }
                }

                if (experienceSetting?.Value == "beginner")
                {
                    programId = 488;
                }
                var ProgramLabel = "";
                int age = 35;
                if (LocalDBManager.Instance.GetDBSetting("Age")?.Value != null)
                {
                    age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age")?.Value);
                }
                 
                learnMore.Age = age;
                switch (programId)
                {
                    case 10:
                        ProgramLabel = "[Gym] Full-Body Level 1";
                        if (age > 50)
                        {

                            ProgramLabel = "[Gym] Full-Body Level 6";
                            programId = 395;
                        }
                        else if (age > 30)
                        {
                            ProgramLabel = "[Gym] Up/Low Split Level 1";
                            programId = 15;
                        }

                        break;
                    case 15:
                        ProgramLabel = "[Gym] Up/Low Split Level 1";
                        if (age > 50)
                        {

                            ProgramLabel = "[Gym] Up/Low Split Level 6";
                            programId = 401;
                        }
                        break;
                    case 16:
                        ProgramLabel = "[Gym] Up/Low Split Level 2";
                        if (age > 50)
                        {

                            ProgramLabel = "[Gym] Up/Low Split Level 6";
                            programId = 401;
                        }
                        break;
                    case 17:
                        ProgramLabel = "[Home] Full-Body Level 1";

                        if (age > 50)
                        {
                            ProgramLabel = "[Home] Full-Body Level 6";
                            programId = 398;
                        }
                        else if (age > 30)
                        {
                            ProgramLabel = "[Home] Up/Low Split Level 1";
                            programId = 21;
                        }
                        break;
                    case 21:
                        ProgramLabel = "[Home] Up/Low Split Level 1";

                        if (age > 50)
                        {
                            ProgramLabel = "[Home] Up/Low Split Level 6";
                            programId = 404;
                        }
                        break;
                    case 22:
                        ProgramLabel = "[Home] Up/Low Split Level 2";
                        if (age > 50)
                        {
                            ProgramLabel = "[Home] Up/Low Split Level 6";
                            programId = 404;
                        }
                        break;
                    case 487:
                        ProgramLabel = "Bodyweight Level 2";
                        break;
                    case 488:
                        ProgramLabel = "Bodyweight Level 1";
                        break;
                    case 1339:
                        ProgramLabel = "[Home] Buffed w/ Bands Level 1";
                        break;
                    case 1338:
                        ProgramLabel = "[Home] Buffed w/ Bands Level 2";
                        break;
                }

                if (age > 51)
                {

                }
                var weekX = 0;
                var dayText = "";
                var instructionText = "";
                instructionText += "- This template is flexible\n";
                instructionText += AppResources.YouCanChangeWorkoutDays + "\n";
                if (LocalDBManager.Instance.GetDBSetting("experience")?.Value == "more3years" || LocalDBManager.Instance.GetDBSetting("experience")?.Value == "1-3years" || ProgramLabel.ToLower().Contains("split"))
                {
                    weekX = 4;
                    dayText += "Your week should look like this:" + "\n";
                    dayText += AppResources.MondayUpperBody1More1Year + "\n";
                    dayText += AppResources.TuesdayLowerBodyMore1Year + "\n";
                    dayText += AppResources.WednesdayOffMore1Year + "\n";
                    dayText += AppResources.ThursdayUpperBodyMore1Year + "\n";
                    dayText += AppResources.FridayOrSaturdayLowerBodyMore1Year + "\n";
                    dayText += AppResources.SundayOffMore1Year;
                    instructionText += AppResources.WorkOutYourUpperAndYourLowerBody2xWeekForBestResultsMore1Year + "\n";
                    instructionText += "- Don't worry: you can change everything later.";
                }
                else
                {
                    weekX = 3;
                    dayText += "Your week should look like this:" + "\n";
                    dayText += AppResources.MondayFullBody + "\n";
                    dayText += AppResources.TuesdayOff + "\n";
                    dayText += AppResources.WednesdayFullBody + "\n";
                    dayText += AppResources.ThursdayOff + "\n";
                    dayText += AppResources.FridayOrSaturdayFullBody + "\n";
                    dayText += AppResources.SundayOff;
                    instructionText += AppResources.WorkOutYourFullBody3xWeekForBestResults + "\n";
                    instructionText += "- Don't worry: you can change everything later.";
                }

                if (Device.RuntimePlatform.Equals(Device.Android))
                    await Task.Delay(300);


                LocalDBManager.Instance.SetDBSetting("ReadyToSignup", "true");
                try
                {

                    int? workoutId = null;

                    int? remainingWorkout = null;
                    var WorkoutInfo2 = "";
                    //Setup Program
                    if (experienceSetting != null && workoutPlaceSetting != null)
                    {
                        if (workoutPlaceSetting.Value == "gym")
                        {

                            if (experienceSetting.Value == "less1year")
                            {
                                WorkoutInfo2 = "[Gym] Full-Body";
                                workoutId = 104;
                                programId = 10;
                                remainingWorkout = 18;
                            }
                            if (experienceSetting.Value == "1-3years")
                            {
                                WorkoutInfo2 = "[Gym] Lower-Body";
                                workoutId = 106;
                                programId = 15;
                                remainingWorkout = 32;
                            }
                            if (experienceSetting.Value == "more3years")
                            {
                                WorkoutInfo2 = "[Gym] Upper-Body Level 2";
                                workoutId = 424;
                                programId = 16;
                                remainingWorkout = 40;
                            }
                        }
                        else if (workoutPlaceSetting.Value == "home")
                        {
                            if (experienceSetting.Value == "less1year")
                            {
                                WorkoutInfo2 = "[Home] Full-Body";
                                workoutId = 108;
                                programId = 17;
                                remainingWorkout = 18;
                            }
                            if (experienceSetting.Value == "1-3years")
                            {
                                WorkoutInfo2 = "[Home] Upper-Body";
                                workoutId = 109;
                                programId = 21;
                                remainingWorkout = 24;
                            }
                            if (experienceSetting.Value == "more3years")
                            {
                                WorkoutInfo2 = "[Home] Upper-Body Level 2";
                                workoutId = 428;
                                programId = 22;
                                remainingWorkout = 40;
                            }
                        }
                        else if (workoutPlaceSetting.Value == "homeBodyweightOnly")
                        {
                            WorkoutInfo2 = "Bodyweight Level 2";
                            workoutId = 12646;
                            programId = 487;
                            remainingWorkout = 12;
                        }
                        else if (workoutPlaceSetting.Value == "homeBodyweightBandsOnly")
                        {
                            WorkoutInfo2 = "[Home] Buffed w/ Bands";
                            workoutId = 15377;
                            programId = 1339;
                            remainingWorkout = 15;

                            if (experienceSetting?.Value == "more3years")
                            {
                                WorkoutInfo2 = "[Home] Buffed w/ Bands 2A";
                                workoutId = 15376;
                                programId = 1338;
                                remainingWorkout = 18;
                            }
                        }
                        if (experienceSetting.Value == "beginner")
                        {
                            WorkoutInfo2 = "Bodyweight Level 1";
                            workoutId = 12645;
                            programId = 488;
                            remainingWorkout = 6;
                        }


                        switch (programId)
                        {
                            case 10:
                                ProgramLabel = "[Gym] Full-Body Level 1";
                                if (age > 50)
                                {
                                    ProgramLabel = "[Gym] Full-Body Level 6";
                                    programId = 395;
                                    WorkoutInfo2 = "[Gym] Full-Body 6A (easy)";
                                    workoutId = 2312;
                                }
                                else if (age > 30)
                                {
                                    ProgramLabel = "[Gym] Up/Low Split Level 1";
                                    programId = 15;
                                    WorkoutInfo2 = "[Gym] Lower Body";
                                    workoutId = 107;
                                }
                                break;
                            case 15:
                                ProgramLabel = "[Gym] Up/Low Split Level 1";
                                if (age > 50)
                                {
                                    ProgramLabel = "[Gym] Up/Low Split Level 6";
                                    programId = 401;
                                    WorkoutInfo2 = "[Gym] Lower Body 6A (easy)";
                                    workoutId = 2337;
                                }
                                break;
                            case 16:
                                ProgramLabel = "[Gym] Up/Low Split Level 2";
                                if (age > 50)
                                {
                                    ProgramLabel = "[Gym] Up/Low Split Level 6";
                                    programId = 401;
                                    WorkoutInfo2 = "[Gym] Lower Body 6A (easy)";
                                    workoutId = 2337;
                                }
                                break;
                            case 17:
                                ProgramLabel = "[Home] Full-Body Level 1";
                                if (age > 50)
                                {
                                    ProgramLabel = "[Home] Full-Body Level 6";
                                    programId = 398;
                                    WorkoutInfo2 = "[Home] Full-Body 6A (easy)";
                                    workoutId = 2325;
                                }
                                else if (age > 30)
                                {
                                    ProgramLabel = "[Home] Up/Low Split Level 1";
                                    programId = 21;
                                    WorkoutInfo2 = "[Home] Lower Body";
                                    workoutId = 110;
                                }
                                break;
                            case 21:
                                ProgramLabel = "[Home] Up/Low Split Level 1";
                                if (age > 50)
                                {
                                    ProgramLabel = "[Home] Up/Low Split Level 6";
                                    programId = 404;
                                    WorkoutInfo2 = "[Home] Lower Body 6A (easy)";
                                    workoutId = 2361;
                                }
                                break;
                            case 22:
                                ProgramLabel = "[Home] Up/Low Split Level 2";
                                if (age > 50)
                                {
                                    ProgramLabel = "[Home] Up/Low Split Level 6";
                                    programId = 404;
                                    WorkoutInfo2 = "[Home] Lower Body 6A (easy)";
                                    workoutId = 2361;
                                }
                                break;
                            case 487:
                                ProgramLabel = "Bodyweight Level 2";
                                break;
                            case 488:
                                ProgramLabel = "Bodyweight Level 1";
                                break;
                        }
                        LocalDBManager.Instance.SetDBSetting("recommendedWorkoutId", workoutId.ToString());
                        LocalDBManager.Instance.SetDBSetting("recommendedWorkoutLabel", WorkoutInfo2);
                        LocalDBManager.Instance.SetDBSetting("recommendedProgramId", programId.ToString());
                        LocalDBManager.Instance.SetDBSetting("recommendedRemainingWorkout", remainingWorkout.ToString());

                        LocalDBManager.Instance.SetDBSetting("recommendedProgramLabel", ProgramLabel);
                    }
                    //SignUp here
                    RegisterModel registerModel = new RegisterModel();

                    registerModel.Firstname = "";
                    registerModel.EmailAddress = "";
                    registerModel.SelectedGender = LocalDBManager.Instance.GetDBSetting("gender")?.Value;
                    registerModel.MassUnit = LocalDBManager.Instance.GetDBSetting("massunit")?.Value;
                    if (LocalDBManager.Instance.GetDBSetting("QuickMode") == null)
                        registerModel.IsQuickMode = false;
                    else
                    {
                        if (LocalDBManager.Instance.GetDBSetting("QuickMode")?.Value == "null")
                            registerModel.IsQuickMode = null;
                        else
                            registerModel.IsQuickMode = LocalDBManager.Instance.GetDBSetting("QuickMode")?.Value == "true" ? true : false;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("Age") != null)
                        registerModel.Age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age").Value);
                    registerModel.RepsMinimum = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("repsminimum").Value);
                    registerModel.RepsMaximum = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("repsmaximum").Value);
                    if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
                        registerModel.BodyWeight = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.Replace(",", ".").Replace("٫", "."), CultureInfo.InvariantCulture), "kg");
                    if (LocalDBManager.Instance.GetDBSetting("WeightGoal") != null)
                        registerModel.WeightGoal = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal").Value.Replace(",", ".").Replace("٫", "."), CultureInfo.InvariantCulture), "kg");

                    registerModel.Password = "";
                    registerModel.ConfirmPassword = "";
                    registerModel.LearnMoreDetails = learnMore;
                    registerModel.IsHumanSupport = IsHumanSupport;
                    registerModel.IsCardio = IsIncludeCardio;
                    registerModel.BodyPartPrioriy = bodypartName;
                    registerModel.IsMobility = IsIncludeMobility;
                    registerModel.MobilityLevel = mobilityLevel;
                    registerModel.IsReminderEmail = true;
                    registerModel.TimeBeforeWorkout = 5;

                    if (LocalDBManager.Instance.GetDBSetting("email") != null && LocalDBManager.Instance.GetDBSetting("email").Value.Contains("yopmail") || (LocalDBManager.Instance.GetDBSetting("Environment") != null && LocalDBManager.Instance.GetDBSetting("Environment").Value != "Production"))
                    { registerModel.MainGoal = ""; }
                    else
                        registerModel.MainGoal = isBetaExperience == true ? "Beta" : isBetaExperience == false ? "Normal" : "";
                    if (IsEquipment)
                    {
                        var model = new EquipmentModel();

                        if (LocalDBManager.Instance.GetDBSetting("workout_place")?.Value == "gym")
                        {
                            model.IsEquipmentEnabled = true;
                            model.IsDumbbellEnabled = isDumbbells;
                            model.IsPlateEnabled = IsPlates;
                            model.IsPullyEnabled = IsPully;
                            model.IsChinUpBarEnabled = IsChinupBar;
                            model.IsBands = true;
                            model.Active = "gym";
                        }
                        else
                        {
                            model.IsHomeEquipmentEnabled = true;
                            model.IsHomeDumbbell = isDumbbells;
                            model.IsHomePlate = IsPlates;
                            model.IsHomePully = IsPully;
                            model.IsHomeChinupBar = IsChinupBar;
                            model.IsHomeBands = true;
                            model.Active = "home";
                        }
                        registerModel.EquipmentModel = model;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("workout_increments") != null)
                    {
                        var increments = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_increments").Value, System.Globalization.CultureInfo.InvariantCulture);
                        var incrementsWeight = new MultiUnityWeight(increments, LocalDBManager.Instance.GetDBSetting("massunit").Value);
                        registerModel.Increments = incrementsWeight.Kg;
                    }
                    LocalDBManager.Instance.SetDBSetting("ReadyRegisterModel", JsonConvert.SerializeObject(registerModel));
                }
                catch (Exception ex)
                {

                }
                _firebase.LogEvent("got_program", ProgramLabel);

                LearnMoreSkipButton_Clicked(new Button(), EventArgs.Empty);
                //await AddQuestion("Congratulations! Your custom, smart program is ready. Learn more?");
                //await AddOptions("Learn more", async (sender, esc) =>
                //{
                //    await AddAnswer(((Button)sender).Text, false);

                //    await AddQuestion($"Based on your age, experience, and preferences, I recommend you work out {weekX} times a week on this program: '{ProgramLabel}'.");
                //    _firebase.LogEvent("got_program", ProgramLabel);
                //    await ClearOptions();
                //    await AddOptions(AppResources.GotIt, BtnGotItProgram);
                //    //Day

                //    if (Device.RuntimePlatform.Equals(Device.iOS))
                //    {
                //        lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
                //        lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
                //    }
                //    async void BtnGotItProgram(object sse, EventArgs evr)
                //    {
                //        //await AddAnswer(((Button)sse).Text, false);

                //        //    ((Button)sse).Clicked -= BtnGotItProgram;
                //        //    ((Button)sse).Clicked += BtnGotItNext;
                //        //    await AddQuestion($"Your smart program:\n- Updates in real time\n- Matches your progress\n- Speeds up future progress");

                //        //    if (Device.RuntimePlatform.Equals(Device.iOS))
                //        //    {
                //        //        lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
                //        //        lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
                //        //    }

                //        //    async void BtnGotItNext(object s, EventArgs e)
                //        //{
                //        //    _firebase.LogEvent("gotten_program", ProgramLabel);
                //        //    if (Device.RuntimePlatform.Equals(Device.Android))
                //        //        await Task.Delay(300);
                //        LearnMoreSkipButton_Clicked(sse, evr);
                //        //await AddQuestion(AppResources.WarningIMNOtLikeOtherAppsIGuideYouInRealTimeBased, false);
                //        //if (Device.RuntimePlatform.Equals(Device.iOS))
                //        //{
                //        //    lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
                //        //    lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.End, animate: false);
                //        //}
                //        //    ((Button)s).Clicked -= BtnGotItNext;
                //        //((Button)s).Clicked += btnGotIt4_Clicked;
                //        //async void btnGotIt4_Clicked(object ob, EventArgs ar)
                //        //{
                //        //    await AddAnswer(((Button)ob).Text, false);
                //        //    await ClearOptions();
                //        //    //
                //        //    await AddQuestion("This app uses the latest science, but it can't correct your form, or allow for a medical condition. It may be wrong at times. When in doubt, trust your judgment. Features like smart watch integration and calendar view are not yet available. But if you’re an early adopter who wants to get in shape fast, you'll love your new custom workouts. Give us a shot: we'll treat your feedback like gold. Got a suggestion? Get in touch. We release new features every month.");
                //        //    if (Device.RuntimePlatform.Equals(Device.Android))
                //        //        await Task.Delay(300);
                //        //    await AddOptions("View latest features", async (sand, ees) =>
                //        //    {
                //        //        if (Device.RuntimePlatform.Equals(Device.Android))
                //        //            await Task.Delay(300);
                //        //        //Device.OpenUri(new Uri("https://dr-muscle.com/timeline"));
                //        //        await Browser.OpenAsync("https://dr-muscle.com/timeline", BrowserLaunchMode.SystemPreferred);
                //        //        LearnMoreSkipButton_Clicked(sand, ees);
                //        //    });
                //        //    await AddOptions("Continue", async (sende, eee) =>
                //        //    {
                //        //        if (Device.RuntimePlatform.Equals(Device.Android))
                //        //            await Task.Delay(300);
                //        //        LearnMoreSkipButton_Clicked(sende, eee);
                //        //    });
                //        //    stackOptions.Children.Add(TermsConditionStack);
                //        //    TermsConditionStack.IsVisible = true;
                //        //}
                //    }
                //    //}
                //});
                //await AddOptions("Skip", async (sender, esc) => {
                //    //await AddAnswer(((Button)sender).Text, false);
                //    _firebase.LogEvent("got_program", ProgramLabel);

                //    LearnMoreSkipButton_Clicked(sender, esc);
                //});

            }
        }
        catch (Exception ex)
        {

        }

    }

    async void LearnMoreSkipButton_Clicked(object sender, EventArgs e)
    {
        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                      AppResources.PleaseCheckInternetConnection,"Ok","");
            return;
        }
        //await AddAnswer(((Button)sender).Text);
        try
        {
            await ClearOptions();
            int age = 35;
            if (LocalDBManager.Instance.GetDBSetting("Age")?.Value != null)
            {
                age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age").Value);
            }
            learnMore.Age = age;

            if (age > 50)
                learnMore.AgeDesc = $"Recovery is slower at {age}.";
            else if (age > 30)
                learnMore.AgeDesc = $"Recovery is a bit slower at {age}. So, I recommend you train each muscle 2x a week (instead of 3x a week).";
            else
                learnMore.AgeDesc = "Recovery is optimal at your age. You can train each muscle as often as 3x a week.";



            var IsWoman = LocalDBManager.Instance.GetDBSetting("gender")?.Value == "Woman";
            if (!string.IsNullOrEmpty(focusText))
            {
                focusText = focusText.Replace("\nStronger sex drive", "");
                focusText = focusText.Replace("Stronger sex drive", "");
            }
            if (string.IsNullOrEmpty(focusText))
                focusText = "Better health";

            if (LocalDBManager.Instance.GetDBSetting("reprange")?.Value == "BuildMuscle")
            {
                learnMore.Focus = focusText.Replace("\n", ", ").ToLower();
                if (learnMore.Focus.Contains(","))
                {
                    int ind = learnMore.Focus.LastIndexOf(",");
                    var subStr = learnMore.Focus.Substring(ind);
                    var newStr = subStr.Replace(",", " and");
                    learnMore.Focus = learnMore.Focus.Replace(subStr, newStr);
                }

                learnMore.FocusDesc = IsWoman ? "To get stronger, I recommend you repeat each exercise 5-12 times. You will also get stronger by lifting in that range." : "To build muscle, I recommend you repeat each exercise 5-12 times. You will also get stronger by lifting in that range.";
            }
            else if (LocalDBManager.Instance.GetDBSetting("reprange")?.Value == "BuildMuscleBurnFat")
            {
                learnMore.Focus = focusText.Replace("\n", ", ").ToLower();
                if (learnMore.Focus.Contains(","))
                {
                    int ind = learnMore.Focus.LastIndexOf(",");
                    var subStr = learnMore.Focus.Substring(ind);
                    var newStr = subStr.Replace(",", " and");
                    learnMore.Focus = learnMore.Focus.Replace(subStr, newStr);
                }
                learnMore.FocusDesc = IsWoman ? "For overall fitness, I recommend you repeat each exercise 8-15 times." : "To build muscle and burn fat, I recommend you repeat each exercise 8-15 times.";
            }
            else if (LocalDBManager.Instance.GetDBSetting("reprange")?.Value == "FatBurning")
            {
                learnMore.Focus = focusText.Replace("\n", ", ").ToLower();
                if (learnMore.Focus.Contains(","))
                {
                    int ind = learnMore.Focus.LastIndexOf(",");
                    var subStr = learnMore.Focus.Substring(ind);
                    var newStr = subStr.Replace(",", " and");
                    learnMore.Focus = learnMore.Focus.Replace(subStr, newStr);
                }
                learnMore.FocusDesc = "To burn fat, I recommend you repeat each exercise 12-20 times. You will burn more calories by lifting in that range.";
            }
            LocalDBManager.Instance.SetDBSetting("DBFocus", learnMore.Focus);

            DBSetting experienceSetting = LocalDBManager.Instance.GetDBSetting("experience");
            learnMore.Exp = "";
            learnMore.ExpDesc = "";
            if (experienceSetting != null)
            {
                if (experienceSetting.Value == "less1year")
                {
                    learnMore.Exp = $"Less than 1 year";
                    learnMore.ExpDesc = "You're new to lifting, so I recommend you train each muscle 3x a week on a full-body program. You will progress faster that way.";
                }
                if (experienceSetting.Value == "1-3years")
                {
                    learnMore.Exp = $"1-3 years";
                    learnMore.ExpDesc = "You have been lifting for over a year, so I recommend you train each muscle 2x a week on a split-body program. This gives you more time to recover between working out each muscle.";
                }
                if (experienceSetting.Value == "more3years")
                {
                    learnMore.Exp = $"More than 3 years";
                    learnMore.ExpDesc = "You have been lifting for 3+ years, so I recommend you train each muscle 2x a week on a split-body program with A and B days. This gives you more time to recover between working out each muscle and more exercise variation. At your level, it's important. ";
                }

            }


            //SignupCode here:
            await Task.Delay(1000);
        }
        catch (Exception ex)
        {

        }

        if (LocalDBManager.Instance.GetDBSetting("LoginType") != null && LocalDBManager.Instance.GetDBSetting("LoginType")?.Value == "Social")
        {
            GoogleFbLoginAfterDemo();
        }
        else
            CreateAccountAfterDemoButton_Clicked();
        // SetMenu();
    }

    async void SetMenu()
    {

    }
    async void GotItButton_Clicked(object sender, EventArgs e)
    {
        await AddAnswer(((Button)sender).Text);
        if (Device.RuntimePlatform.Equals(Device.Android))
            await Task.Delay(300);
        lstChats.ScrollTo(BotList.Last(), position: ScrollToPosition.MakeVisible, animate: false);
        await ClearOptions();
        await Task.Delay(300);
        GetEmail();
    }

    async void ConnectWithEmail(object sender, EventArgs e)
    {
        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                      AppResources.PleaseCheckInternetConnection,"Ok","");
            //await UserDialogs.Instance.AlertAsync(new AlertConfig()
            //{
            //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //    Message = AppResources.PleaseCheckInternetConnection,
            //    Title = AppResources.ConnectionError
            //});
            return;
        }
        await ClearOptions();
        //GetFirstName();
        GetEmail();
    }


    async Task AddQuestion(string question, bool isAnimated = true)
    {
        try
        {
            BotList.Add(new BotModel()
            {
                Question = question,
                Type = BotType.Ques
            });
            if (isAnimated)
            {
                await Task.Delay(300);
            }
            lstChats.ScrollTo(BotList.LastOrDefault(), position: ScrollToPosition.MakeVisible, animate: false);
            lstChats.ScrollTo(BotList.LastOrDefault(), position: ScrollToPosition.End, animate: false);
        }
        catch (Exception ex)
        {

        }
    }

    async Task AddAnswer(string answer, bool isClearOptions = true)
    {
        BotList.Add(new BotModel()
        {
            Answer = answer,
            Type = BotType.Ans
        });
        if (isClearOptions)
            await ClearOptions();
        lstChats.ScrollTo(BotList.LastOrDefault(), position: ScrollToPosition.MakeVisible, animate: false);
        lstChats.ScrollTo(BotList.LastOrDefault(), position: ScrollToPosition.End, animate: false);

        await Task.Delay(300);
    }

    async Task<CustomImageButton> AddCheckbox(string title, EventHandler handler, bool ischecked = false)
    {
        try
        {

            CustomImageButton imgBtn = new CustomImageButton()
            {
                Text = title,
                Source = ischecked ? "done.png" : "undone.png",
                BackgroundColor = Colors.White,
                TextFontColor = AppThemeConstants.OffBlackColor,
                Margin = new Thickness(25, 3),
                Padding = new Thickness(2)
            };
            imgBtn.Clicked += handler;
            stackOptions.Children.Add(imgBtn);
            return imgBtn;

        }
        catch (Exception ex)
        {

        }
        return new CustomImageButton();
    }

    private void TestBtn(object sender, EventArgs e)
    {
        GetAge();
    }

    async Task<Button> AddOptions(string title, EventHandler handler)
    {
        try
        {

            var grid = new Grid();
            var gradientBrush = new LinearGradientBrush
            {
                EndPoint = new Point(1, 0)
            };
            gradientBrush.GradientStops.Add(new GradientStop(Color.FromHex("#0C2432"), (float)0.0));
            gradientBrush.GradientStops.Add(new GradientStop(Color.FromHex("#195276"), (float)1.0));

            var frame = new Frame();
            frame.Background = gradientBrush;
            frame.HeightRequest = 55;
            frame.Margin = new Thickness(24, 4);
            frame.CornerRadius = 0;
            frame.BorderColor = Colors.Transparent;
            //var pancakeView = new PancakeView() { HeightRequest = 55, Margin = new Thickness(25, 2) };
            //pancakeView.OffsetAngle = Device.RuntimePlatform.Equals(Device.Android) ? 45 : 90;
            //pancakeView.BackgroundGradientStops.Add(new PancakeView.GradientStop { Color = Color.FromHex("#195276"), Offset = 1 });
            //pancakeView.BackgroundGradientStops.Add(new PancakeView.GradientStop { Color = Color.FromHex("#0C2432"), Offset = 0 });
            grid.Children.Add(frame);


            var btn = new Button()
            {
                Text = title,
                TextColor = Colors.Black,
                BackgroundColor = Colors.White,
                FontSize = (DeviceInfo.Platform == DevicePlatform.Android) ? 15 : 17,
                CornerRadius = 0,
                HeightRequest = 55
            };
            btn.Clicked += handler;
            SetDefaultButtonStyle(btn);
            grid.Children.Add(btn);
            stackOptions.Children.Add(grid);

            BottomViewHeight.Height = GridLength.Auto;
            lstChats.ScrollTo(BotList.LastOrDefault(), position: ScrollToPosition.MakeVisible, animate: false);
            lstChats.ScrollTo(BotList.LastOrDefault(), position: ScrollToPosition.End, animate: false);

            return btn;

        }
        catch (Exception ex)
        {

        }
        return new Button();
    }
    async Task<Button> AddOptionsWithMediumEmphase(string title, EventHandler handler)
    {
        var grid = new Grid();

        var btn = new Button()
        {
            Text = title,
            TextColor = AppThemeConstants.BlueColor,
            BackgroundColor = Colors.White,
            FontSize = (DeviceInfo.Platform == DevicePlatform.Android) ? 15 : 17,
            CornerRadius = 0,
            HeightRequest = 55,
            BorderColor = AppThemeConstants.BlueColor,
            BorderWidth = 2,
            FontAttributes = FontAttributes.Bold,
            Margin = new Thickness(25, 4, 25, 4)
        };

        btn.Clicked += handler;
        grid.Children.Add(btn);
        stackOptions.Children.Add(grid);
        BottomViewHeight.Height = GridLength.Auto;
        lstChats.ScrollTo(BotList.LastOrDefault(), position: ScrollToPosition.MakeVisible, animate: false);
        lstChats.ScrollTo(BotList.LastOrDefault(), position: ScrollToPosition.End, animate: false);

        return btn;
    }
    private async void GoogleFbLoginAfterDemo()
    {
        LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
        DBSetting experienceSetting = LocalDBManager.Instance.GetDBSetting("experience");
        DBSetting workoutPlaceSetting = LocalDBManager.Instance.GetDBSetting("workout_place");
        int? workoutId = null;
        int? programId = null;
        int? remainingWorkout = null;
        var WorkoutInfo2 = "";


        string ProgramLabel = AppResources.NotSetUp;
        int age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age")?.Value??"35");


        var level = 1;
        if (LocalDBManager.Instance.GetDBSetting("MainLevel") != null)
            level = int.Parse(LocalDBManager.Instance.GetDBSetting("MainLevel")?.Value ?? "1");
        bool isSplit = LocalDBManager.Instance.GetDBSetting("MainProgram")?.Value?.Contains("Split") ?? false;
        if (LocalDBManager.Instance.GetDBSetting("ExerciseVariety")?.Value == "More")
            level += 1;

        bool isGym = workoutPlaceSetting?.Value == "gym";
        var mo = AppThemeConstants.GetLevelProgram(level, isGym, !isSplit, LocalDBManager.Instance.GetDBSetting("MainProgram")?.Value);

        if (workoutPlaceSetting?.Value == "homeBodyweightOnly")
        {
            if (level <= 1)
            {
                mo.workoutName = "Bodyweight 1";
                mo.workoutid = 12645;
                mo.programid = 487;
                mo.reqWorkout = 12;
                mo.programName = "Bodyweight Level 1";
            }
            else if (level <= 2)
            {
                mo.workoutName = "Bodyweight 2";
                mo.workoutid = 12646;
                mo.programid = 488;
                mo.reqWorkout = 12;
                mo.programName = "Bodyweight Level 2";
            }
            else if (level == 3)
            {
                mo.workoutName = "Bodyweight 3";
                mo.workoutid = 14017;
                mo.programid = 923;
                mo.reqWorkout = 15;
                mo.programName = "Bodyweight Level 3";
            }
            else if (level >= 4)
            {
                mo.workoutName = "Bodyweight 4";
                mo.workoutid = 14019;
                mo.programid = 924;
                mo.reqWorkout = 15;
                mo.programName = "Bodyweight Level 4";
            }

        }
        else if (workoutPlaceSetting?.Value == "homeBodyweightBandsOnly")
        {
            mo.workoutName = "[Home] Buffed w/ Bands";
            mo.programName = "[Home] Buffed w/ Bands Level 1";
            mo.workoutid = 15377;
            mo.programid = 1339;
            mo.reqWorkout = 15;

            if (LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value == "an active, experienced lifter" && LocalDBManager.Instance.GetDBSetting("ExerciseVariety")?.Value == "Less")
            {
                mo.workoutName = "[Home] Buffed w/ Bands 2A";
                mo.programName = "[Home] Buffed w/ Bands Level 2";
                mo.workoutid = 15376;
                mo.programid = 1338;
                mo.reqWorkout = 18;
            }
            else if (LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value == "an active, experienced lifter" && LocalDBManager.Instance.GetDBSetting("ExerciseVariety")?.Value == "More")
            {
                mo.workoutName = "[Home] Buffed w/ Bands 3A";
                mo.programName = "[Home] Buffed w/ Bands Level 3";
                mo.workoutid = 17323;
                mo.programid = 2032;
                mo.reqWorkout = 24;
            }
        }

        try
        {
            LocalDBManager.Instance.SetDBSetting("recommendedWorkoutId", mo?.workoutid.ToString() ?? "");
            LocalDBManager.Instance.SetDBSetting("recommendedWorkoutLabel", mo?.workoutName);
            LocalDBManager.Instance.SetDBSetting("recommendedProgramId", mo?.programid.ToString() ?? "");
            LocalDBManager.Instance.SetDBSetting("recommendedRemainingWorkout", mo?.reqWorkout.ToString());

            LocalDBManager.Instance.SetDBSetting("recommendedProgramLabel", mo?.programName);
        }
        catch (Exception ex)
        {

        }
        //}
        //SignUp here

        workoutId = mo?.workoutid;
        WorkoutInfo2 = mo?.workoutName;
        programId = mo?.programid;
        ProgramLabel = mo?.programName;
        remainingWorkout = mo?.reqWorkout;

        RegisterModel registerModel = new RegisterModel();
        registerModel.Firstname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
        registerModel.EmailAddress = LocalDBManager.Instance.GetDBSetting("email")?.Value;
        registerModel.IsMobility = IsIncludeMobility;
        registerModel.MobilityLevel = mobilityLevel;
        registerModel.IsReminderEmail = true;
        registerModel.TimeBeforeWorkout = 5;

        registerModel.SelectedGender = LocalDBManager.Instance.GetDBSetting("gender")?.Value;
        registerModel.MassUnit = LocalDBManager.Instance.GetDBSetting("massunit")?.Value;
        if (LocalDBManager.Instance.GetDBSetting("QuickMode") == null)
            registerModel.IsQuickMode = false;
        else
        {
            if (LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "null")
                registerModel.IsQuickMode = null;
            else
                registerModel.IsQuickMode = LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "true" ? true : false;
        }
        if (LocalDBManager.Instance.GetDBSetting("Age") != null)
            registerModel.Age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age")?.Value??"35");
        registerModel.RepsMinimum = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("repsminimum")?.Value);
        registerModel.RepsMaximum = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("repsmaximum")?.Value);
        registerModel.Password = "";
        registerModel.ConfirmPassword = "";
        registerModel.LearnMoreDetails = learnMore;
        registerModel.IsHumanSupport = IsHumanSupport;
        registerModel.IsCardio = IsIncludeCardio;
        registerModel.BodyPartPrioriy = bodypartName;
        if(SetStyle.HasValue)
            registerModel.SetStyle = SetStyle.Value;
        registerModel.IsPyramid = IsPyramid;
        registerModel.IsDropSet = IsDrop;
        registerModel.isDing = isDing;
        registerModel.WorkoutDuration = workoutDurationLenggth;
        if (programId.HasValue)
        {
            registerModel.ProgramId = programId.Value;
        }
        registerModel.IsMobility = IsIncludeMobility;
        registerModel.MobilityLevel = mobilityLevel;
        registerModel.IsReminderEmail = true;
        registerModel.TimeBeforeWorkout = 5;

        if (LocalDBManager.Instance.GetDBSetting("Height")?.Value != null && Config.UserHeight != 0)
        {
            registerModel.Height = Config.UserHeight;
        }
        if (LocalDBManager.Instance.GetDBSetting("email") != null && LocalDBManager.Instance.GetDBSetting("email").Value.Contains("yopmail") || (LocalDBManager.Instance.GetDBSetting("Environment") != null && LocalDBManager.Instance.GetDBSetting("Environment").Value != "Production"))
        { registerModel.MainGoal = ""; }
        else
            registerModel.MainGoal = isBetaExperience == true ? "Beta" : isBetaExperience == false ? "Normal" : "";
        if (LocalDBManager.Instance.GetDBSetting("ReminderDays") != null && LocalDBManager.Instance.GetDBSetting("ReminderTime") != null)
        {
            try
            {
                registerModel.ReminderTime = TimeSpan.Parse(LocalDBManager.Instance.GetDBSetting("ReminderTime")?.Value??"0");
                registerModel.ReminderDays = LocalDBManager.Instance.GetDBSetting("ReminderDays")?.Value;
                if (registerModel.ReminderDays.Contains("1"))
                    registerModel.IsRecommendedReminder = true;
                else
                    registerModel.IsRecommendedReminder = null;

            }
            catch (Exception ex)
            {

            }

        }
        else
            registerModel.IsRecommendedReminder = IsRecommendedReminder;
        if (IsEquipment)
        {
            var model = new EquipmentModel();

            if (LocalDBManager.Instance.GetDBSetting("workout_place")?.Value == "gym")
            {
                model.IsEquipmentEnabled = true;
                model.IsDumbbellEnabled = isDumbbells;
                model.IsPlateEnabled = IsPlates;
                model.IsPullyEnabled = IsPully;
                model.IsChinUpBarEnabled = IsChinupBar;
                model.IsBands = true;
                model.Active = "gym";

                model.IsHomeEquipmentEnabled = false;
                model.IsHomeDumbbell = true;
                model.IsHomePlate = true;
                model.IsHomePully = true;
                model.IsHomeChinupBar = true;
                model.IsHomeBands = true;
            }
            else
            {
                model.IsEquipmentEnabled = false;
                model.IsDumbbellEnabled = true;
                model.IsPlateEnabled = true;
                model.IsPullyEnabled = true;
                model.IsChinUpBarEnabled = true;
                model.IsBands = true;
                model.IsOtherEquipmentEnabled = false;
                model.IsOtherDumbbell = true;
                model.IsOtherPlate = true;
                model.IsOtherPully = true;
                model.IsOtherChinupBar = true;
                model.IsOtherBands = true;
                model.IsHomeEquipmentEnabled = true;
                model.IsHomeDumbbell = isDumbbells;
                model.IsHomePlate = IsPlates;
                model.IsHomePully = IsPully;
                model.IsHomeChinupBar = IsChinupBar;
                model.IsHomeBands = true;
                model.Active = "home";
            }
            var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True";
            model.AvilablePlate = kgString;
            model.AvilableHomePlate = kgString;
            model.AvilableOtherPlate = kgString;
            var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
            model.AvilableLbPlate = lbString;
            model.AvilableHomeLbPlate = lbString;
            model.AvilableOtherLbPlate = lbString;
            registerModel.EquipmentModel = model;
        }
        if (IncrementUnit != null)
        {
            registerModel.Increments = IncrementUnit.Kg;
        }
        if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
            registerModel.BodyWeight = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture), "kg");
        if (LocalDBManager.Instance.GetDBSetting("WeightGoal") != null)
            registerModel.WeightGoal = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal").Value, CultureInfo.InvariantCulture), "kg");

        if (string.IsNullOrEmpty(registerModel.Firstname))
            registerModel.Firstname = "";

        try
        {
            OpenPreview(registerModel);
        }
        catch (Exception ex)
        {

        }
        var uim = await DrMuscleRestClient.Instance.RegisterUserAfterDemo(registerModel);

        try
        {
            CancelNotification();
            SetTrialUserNotifications();
            if(uim != null) {
                LocalDBManager.Instance.SetDBSetting("email", uim.Email);
                LocalDBManager.Instance.SetDBSetting("firstname", !string.IsNullOrEmpty(uim.Firstname) ? ((uim.Firstname.Contains(' ')) ? uim.Firstname.Split(' ')[0] : uim.Firstname) : uim.Firstname);
                LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
                LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
                LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
                LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
                LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
                LocalDBManager.Instance.SetDBSetting("reprangeType", uim.ReprangeType.ToString());
                LocalDBManager.Instance.SetDBSetting("repsminimum", Convert.ToString(uim.RepsMinimum));
                LocalDBManager.Instance.SetDBSetting("repsmaximum", Convert.ToString(uim.RepsMaximum));
                LocalDBManager.Instance.SetDBSetting("QuickMode", uim.IsQuickMode == true ? "true" : uim.IsQuickMode == null ? "null" : "false"); LocalDBManager.Instance.SetDBSetting("WorkoutTypeList", "0");
                LocalDBManager.Instance.SetDBSetting("ExerciseTypeList", "0");
                LocalDBManager.Instance.SetDBSetting("onboarding_seen", "true");
                if (uim.Age != null)
                    LocalDBManager.Instance.SetDBSetting("Age", Convert.ToString(uim.Age));
                if (uim.TargetIntake != null && uim.TargetIntake != 0)
                    LocalDBManager.Instance.SetDBSetting("TargetIntake", uim.TargetIntake.ToString());
                //if (uim.ReminderTime != null)
                //    LocalDBManager.Instance.SetDBSetting("ReminderTime", uim.ReminderTime.ToString());
                //if (uim.ReminderDays != null)
                //    LocalDBManager.Instance.SetDBSetting("ReminderDays", uim.ReminderDays);

                LocalDBManager.Instance.SetDBSetting("timer_vibrate", uim.IsVibrate ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_sound", uim.IsSound ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_123_sound", uim.IsTimer321 ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_reps_sound", uim.IsRepsSound ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_autostart", uim.IsAutoStart ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_autoset", uim.IsAutomatchReps ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_fullscreen", uim.IsFullscreen ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_count", uim.TimeCount.ToString());
                LocalDBManager.Instance.SetDBSetting("timer_remaining", uim.TimeCount.ToString());
                LocalDBManager.Instance.SetDBSetting("Cardio", uim.IsCardio ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("IsMobility", uim.IsMobility == null ? null : uim.IsMobility == false ? "false" : "true");
                LocalDBManager.Instance.SetDBSetting("MaxWorkoutDuration", uim.WorkoutDuration.ToString());
                LocalDBManager.Instance.SetDBSetting("IsExerciseQuickMode", uim.IsExerciseQuickMode == null ? null : uim.IsExerciseQuickMode == false ? "false" : "true");
                LocalDBManager.Instance.SetDBSetting("MobilityLevel", uim.MobilityLevel);
                LocalDBManager.Instance.SetDBSetting("MobilityRep", uim.MobilityRep == null ? "" : Convert.ToString(uim.MobilityRep));
                LocalDBManager.Instance.SetDBSetting("BackOffSet", uim.IsBackOffSet ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("1By1Side", uim.Is1By1Side ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("StrengthPhase", uim.IsStrength ? "true" : "false");
                if (uim.IsDropSet)
                {
                    LocalDBManager.Instance.SetDBSetting("SetStyle", "Drop");
                    LocalDBManager.Instance.SetDBSetting("IsRPyramid", "false");
                    LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                }
                else if (uim.IsPyramid)
                {
                    LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                    LocalDBManager.Instance.SetDBSetting("IsRPyramid", "true");
                    LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                }
                else if (uim.IsNormalSet == null || uim.IsNormalSet == true)
                {
                    LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
                    LocalDBManager.Instance.SetDBSetting("IsPyramid", uim.IsNormalSet == null ? "true" : "false");
                }
                else
                {
                    LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                    LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                }
                if (uim.Increments != null)
                    LocalDBManager.Instance.SetDBSetting("workout_increments", uim.Increments.Kg.ToString().ReplaceWithDot());
                if (uim.Max != null)
                    LocalDBManager.Instance.SetDBSetting("workout_max", uim.Max.Kg.ToString().ReplaceWithDot());
                if (uim.Min != null)
                    LocalDBManager.Instance.SetDBSetting("workout_min", uim.Min.Kg.ToString().ReplaceWithDot());
                if (uim.BodyWeight != null)
                {
                    LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString().ReplaceWithDot());
                }
                if (uim.WeightGoal != null)
                {
                    LocalDBManager.Instance.SetDBSetting("WeightGoal", uim.WeightGoal.Kg.ToString().ReplaceWithDot());
                }
                if (uim.WarmupsValue != null)
                {
                    LocalDBManager.Instance.SetDBSetting("warmups", Convert.ToString(uim.WarmupsValue));
                }

                SetupEquipment(uim);
                ((App)Application.Current).displayCreateNewAccount = true;
                if (string.IsNullOrEmpty(uim.BodyPartPrioriy))
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                else
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", uim.BodyPartPrioriy.Trim());
                if (uim.Gender.Trim().ToLowerInvariant().Equals("man"))
                    LocalDBManager.Instance.SetDBSetting("BackgroundImage", "Background2.png");
                else
                    LocalDBManager.Instance.SetDBSetting("BackgroundImage", "BackgroundFemale.png");

                LocalDBManager.Instance.SetDBSetting("IsEmailReminder", uim.IsReminderEmail ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("IsReferenceSetReps", uim.IsReferenseSet ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("ReferenceSetReps", uim.ReferenceSetReps.ToString());
                LocalDBManager.Instance.SetDBSetting("ReminderHours", uim.ReminderBeforeHours.ToString());

                LocalDBManager.Instance.SetDBSetting("RecommendedReminder", uim.IsRecommendedReminder == true ? "true" : uim.IsRecommendedReminder == null ? "null" : "false");
                if (uim.IsRecommendedReminder == true)
                {
                    var timeSpan = new TimeSpan(0, 22, 0, 0);
                    IAlarmAndNotificationService alarmAndNotificationService = new AlarmAndNotificationService();
                    alarmAndNotificationService.CancelNotification(101);
                    alarmAndNotificationService.CancelNotification(102);
                    alarmAndNotificationService.CancelNotification(103);
                    alarmAndNotificationService.CancelNotification(104);
                    alarmAndNotificationService.CancelNotification(105);
                    alarmAndNotificationService.CancelNotification(106);
                    alarmAndNotificationService.CancelNotification(107);
                    alarmAndNotificationService.CancelNotification(108);
                    alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 1111, NotificationInterval.Week);
                }
                LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "");

            }
        }
        catch (Exception ex)
        {

        }
    }


}