using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using Controls.UserDialogs.Maui;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Utility;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using Newtonsoft.Json;
using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Series;
using Microsoft.Maui.Networking;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Text.RegularExpressions;
using static DrMaxMuscle.Views.BodyProgressPopup;
using AlertConfig = Acr.UserDialogs.AlertConfig;
using ConfirmConfig = Acr.UserDialogs.ConfirmConfig;
using IView = Microsoft.Maui.IView;
using UserDialogs = Acr.UserDialogs.UserDialogs;
using DrMaxMuscle.Screens.Subscription;
using DrMaxMuscle.Message;
using DrMaxMuscle.Dependencies;
using RGPopup.Maui.Services;
using OxyPlot.Legends;
using DrMaxMuscle.Screens.Eve;

namespace DrMaxMuscle.Screens.History;

public partial class HistortWeightPage : ContentPage
{
    private List<UserWeight> allWeights = new List<UserWeight>();
    public PlotModel plotModel = null;
    public PlotModel plotFatModel = null;
    public PlotModel plotFFMModel = null;
    private List<YearlyUserWeight> yearlyWeights = new List<YearlyUserWeight>();
    private StackLayout contextMenu = null;
    public UserWeight lastWeight = new UserWeight();
    GetUserWorkoutLogAverageResponse workoutLogAverage;
    private Dictionary<double, string> IndexToDateLabel = new Dictionary<double, string>();
    private Dictionary<double, string> IndexToDateLabel2 = new Dictionary<double, string>();
    private Dictionary<double, string> IndexToDateLabel3 = new Dictionary<double, string>();
    //List<UserWeight> group = new List<UserWeight>();
    private bool IsViewVisible = true;
    ObservableCollection<UserWeight> group = new ObservableCollection<UserWeight>();

    IList<UserWeight> source;
    int step = 10;
    int page = 1;
    public ObservableCollection<UserWeight> UserWeightList { get; private set; }

    public HistortWeightPage()
    {
        InitializeComponent();
        //HistoryListView.HasUnevenRows = true;
        HistoryListView.ItemTemplate = new HistoryWeightDataTemplateSelector
        {
            HistoryDateTemplate = SetHeaderTemplate,
            HistorySetTemplate = SetTemplate,

        };
        //HistoryListView.ItemSelected += HistoryListViewOnItemSelected;
        
        MessagingCenter.Subscribe<Message.LoadHistoryMessage>(this, "LoadHistoryMessage", (obj) =>
        {
            //if (Device.RuntimePlatform == Device.Android)
            //{
            //    WeightBox2.IsVisible = false;
            //    FatBox.IsVisible = false;
            //}
            Refresh();
        });
        
        Title = "Weight progress";
    }
    private PlotModel InitializeFFMPlotModel()
    {
        try
        {
            var ffmSeries = new LineSeries
            {
                MarkerType = MarkerType.Circle,
                MarkerSize = 5,
                Color = OxyColor.Parse("#195377"),
                TextColor = OxyColor.Parse("#195377"),
                LabelFormatString = "{1:#.#;#}",
                Title = "Fat-free-mass",
                MarkerStroke = OxyColor.Parse("#195377"),
                MarkerFill = OxyColor.Parse("#195377"),
                MarkerStrokeThickness = 1,
                FontSize = 10,
                TrackerFormatString = ""
            };
            var ffmTrendSeries = new LineSeries
            {
                MarkerType = MarkerType.Circle,
                MarkerSize = 5,
                MarkerStrokeThickness = 1,
                LabelFormatString = "{1:#.#;#}",
                FontSize = 10,
                LabelMargin = 5,
                TrackerFormatString = "",
                Title = "Trend     " // Legend title
            };

            var ffmPlotModel = new PlotModel
            {
                Background = OxyColors.Transparent,
                PlotAreaBackground = OxyColors.Transparent,
                PlotAreaBorderColor = OxyColor.Parse("#23253A"),
                //LegendPlacement = LegendPlacement.Outside,
                //LegendTextColor = OxyColor.Parse("#23253A"),
                //LegendPosition = LegendPosition.BottomLeft,
                //LegendOrientation = LegendOrientation.Horizontal,
                //LegendMaxWidth = 500,
                //LegendLineSpacing = 10,
                //LegendColumnSpacing = 10,
                //LegendPadding = 2,
                IsLegendVisible = true
            };
            var legend = new Legend
            {
                LegendPosition = LegendPosition.BottomCenter, // Position of the legend
                LegendPlacement = LegendPlacement.Outside, // Place legend outside the plot area
                LegendOrientation = LegendOrientation.Horizontal, // Arrange legend items vertically
                LegendFontSize = 12, // Font size of legend items
                LegendTextColor = OxyColors.Black, // Color of the legend text
                LegendSymbolLength = 24, // Length of the legend symbol (e.g., line or marker)
                LegendSymbolMargin = 8, // Margin between legend symbol and text
            };

            ffmPlotModel.Legends.Add(legend);
            ffmPlotModel.Series.Add(ffmSeries);
            ffmPlotModel.Series.Add(ffmTrendSeries);



            ffmPlotModel.Axes.Add(new DateTimeAxis
            {
                Position = AxisPosition.Bottom,
                //StringFormat = "MMM dd",
                LabelFormatter = _formatter,
                MinimumPadding = 0.05,
                MaximumPadding = 0.05,
                ExtraGridlineColor = OxyColors.Transparent,
                MajorGridlineColor = OxyColors.Transparent,
                MinorGridlineColor = OxyColors.Transparent,
                IsZoomEnabled = false,
                IsPanEnabled = false,
            });
            ffmPlotModel.Axes.Add(new LinearAxis
            {
                Position = AxisPosition.Left,
                Minimum = 30,
                Maximum = 70,
                IsZoomEnabled = false,
                IsPanEnabled = false,
            });

            ffmPlotModel.TitleHorizontalAlignment = TitleHorizontalAlignment.CenteredWithinView;



            return ffmPlotModel;
        }
        catch (Exception ex)
        {
            return new PlotModel();
        }
    }
    private PlotModel InitializePlotModel()
    {
        try
        {
            var series = new LineSeries
            {
                MarkerType = MarkerType.Circle,
                MarkerSize = 5,
                Color = OxyColor.Parse("#195377"),
                TextColor = OxyColor.Parse("#195377"),
                LabelFormatString = "{1:#.#;#}",
                Title = "Weight",
                MarkerStroke = OxyColor.Parse("#195377"),
                MarkerFill = OxyColor.Parse("#195377"),
                MarkerStrokeThickness = 1,
                FontSize = 10,
                TrackerFormatString = ""
            };

            var series1 = new LineSeries
            {
                MarkerType = MarkerType.Circle,
                MarkerSize = 5,
                MarkerStrokeThickness = 1,
                LabelFormatString = "{1:#.#;#}",
                FontSize = 10,
                LabelMargin = 5,
                Title = "Trend     ",
                TrackerFormatString = ""
            };
            //Create the plot model
            var plotModelWeight = new PlotModel
            {
                Background = OxyColors.Transparent,
                PlotAreaBackground = OxyColors.Transparent,
                PlotAreaBorderColor = OxyColor.Parse("#23253A"),
                //LegendPlacement = LegendPlacement.Outside,
                //LegendTextColor = OxyColor.Parse("#23253A"),
                //LegendPosition = LegendPosition.BottomLeft,
                //LegendOrientation = LegendOrientation.Horizontal,
                //LegendMaxWidth = 500,
                //LegendLineSpacing = 10,
                //LegendColumnSpacing = 10,
                //LegendPadding = 2,
                IsLegendVisible = true
            };
            var legend = new Legend
            {
                LegendPosition = LegendPosition.BottomCenter, // Position of the legend
                LegendPlacement = LegendPlacement.Outside, // Place legend outside the plot area
                LegendOrientation = LegendOrientation.Horizontal, // Arrange legend items vertically
                LegendFontSize = 12, // Font size of legend items
                LegendTextColor = OxyColors.Black, // Color of the legend text
                LegendSymbolLength = 24, // Length of the legend symbol (e.g., line or marker)
                LegendSymbolMargin = 8, // Margin between legend symbol and text
            };

            plotModelWeight.Legends.Add(legend);
            plotModelWeight.Series.Add(series);
            plotModelWeight.Series.Add(series1);

            plotModelWeight.Axes.Add(new DateTimeAxis
            {
                Position = AxisPosition.Bottom,
                StringFormat = "MMM dd",
                LabelFormatter = _formatter2,
                MinimumPadding = 0.05,
                MaximumPadding = 0.05,
                ExtraGridlineColor = OxyColors.Transparent,
                MajorGridlineColor = OxyColors.Transparent,
                MinorGridlineColor = OxyColors.Transparent,
                IsZoomEnabled = false,
                IsPanEnabled = false,
            });
            plotModelWeight.Axes.Add(new LinearAxis { Position = AxisPosition.Left, Minimum = 30, Maximum = 70,
                IsZoomEnabled = false,
                IsPanEnabled = false,
            });
            plotModelWeight.TitleHorizontalAlignment = TitleHorizontalAlignment.CenteredWithinView;


            return plotModelWeight;
        }
        catch (Exception ex)
        {
            return new PlotModel();
        }
    }
    private PlotModel InitializeFatPlotModel()
    {
        try
        {
            var fatSeries = new LineSeries
            {
                MarkerType = MarkerType.Circle,
                MarkerSize = 5,
                Color = OxyColor.Parse("#195377"),
                TextColor = OxyColor.Parse("#195377"),
                LabelFormatString = "{1:#.#;#}",
                Title = "Fat",
                MarkerStroke = OxyColor.Parse("#195377"),
                MarkerFill = OxyColor.Parse("#195377"),
                MarkerStrokeThickness = 1,
                FontSize = 10,
                TrackerFormatString = ""
            };
            var fatTrendSeries = new LineSeries
            {
                MarkerType = MarkerType.Circle,
                MarkerSize = 5,
                MarkerStrokeThickness = 1,
                LabelFormatString = "{1:#.#;#}",
                FontSize = 10,
                TrackerFormatString = "",
                LabelMargin = 5,
                Title = "Trend      " // Legend title
            };

            var fatPlotModel = new PlotModel
            {
                Background = OxyColors.Transparent,
                PlotAreaBackground = OxyColors.Transparent,
                PlotAreaBorderColor = OxyColor.Parse("#23253A"),
                //LegendPlacement = LegendPlacement.Outside,
                //LegendTextColor = OxyColor.Parse("#23253A"),
                //LegendPosition = LegendPosition.BottomLeft,
                //LegendOrientation = LegendOrientation.Horizontal,
                //LegendMaxWidth = 500,
                //LegendLineSpacing = 10,
                //LegendColumnSpacing = 10,
                //LegendPadding = 2,
                IsLegendVisible = true,
            };
            var legend = new Legend
            {
                LegendPosition = LegendPosition.BottomCenter, // Position of the legend
                LegendPlacement = LegendPlacement.Outside, // Place legend outside the plot area
                LegendOrientation = LegendOrientation.Horizontal, // Arrange legend items vertically
                LegendFontSize = 12, // Font size of legend items
                LegendTextColor = OxyColors.Black, // Color of the legend text
                LegendSymbolLength = 24, // Length of the legend symbol (e.g., line or marker)
                LegendSymbolMargin = 8, // Margin between legend symbol and text
            };

            fatPlotModel.Legends.Add(legend);
            fatPlotModel.Series.Add(fatSeries);
            fatPlotModel.Series.Add(fatTrendSeries);


            fatPlotModel.Axes.Add(new DateTimeAxis
            {
                Position = AxisPosition.Bottom,
                //StringFormat = "MMM dd",
                LabelFormatter = _formatter3,
                MinimumPadding = 0.05,
                MaximumPadding = 0.05,
                ExtraGridlineColor = OxyColors.Transparent,
                MajorGridlineColor = OxyColors.Transparent,
                MinorGridlineColor = OxyColors.Transparent,
                IsZoomEnabled = false,
                IsPanEnabled = false,
            });
            fatPlotModel.Axes.Add(new LinearAxis
            {
                Position = AxisPosition.Left,
                Minimum = 0,
                Maximum = 50,
                IsZoomEnabled = false,
                IsPanEnabled = false,
            });

            fatPlotModel.TitleHorizontalAlignment = TitleHorizontalAlignment.CenteredWithinView;


            return fatPlotModel;
        }
        catch (Exception ex)
        {
            return new PlotModel();
        }
    }
    private async Task LoadSavedWeightFromServer()
    {
        try
        {
            fatLbl.Text = "FAT %\n(trend)";
            ffmLbl.Text = "FFM\n(trend)";
            // Initialize the plot model and series with placeholder data
            if (App.IsFatExist)
            {
                FatBox.IsVisible = true;
                FFMBox.IsVisible = true;
            }
            else
            {
                FatBox.IsVisible = false;
                FFMBox.IsVisible = false;
            }
            

            List<DataPoint> weightData = new List<DataPoint>();
            List<DataPoint> weightTrendData = new List<DataPoint>();
            List<DataPoint> fatData = new List<DataPoint>();
            List<DataPoint> fatTrendData = new List<DataPoint>();
            List<DataPoint> ffmData = new List<DataPoint>();
            List<DataPoint> ffmTrendData = new List<DataPoint>();



            List<UserWeight> weightList = new List<UserWeight>();
            var data = LocalDBManager.Instance.GetDBSetting("OverallWeights")?.Value;
            if (data != null)
            {
                weightList = JsonConvert.DeserializeObject<List<UserWeight>>(data);
            }
            //var weightList = ((App)Application.Current).weightContext.Weights;
            if (weightList != null && weightList.Count == 0)
            {
                weightList = await GetUserWeightsAsync();
                if(weightList?.Count == 0)
                {
                    return;
                }
            }
            else
                weightList = weightList.Distinct().ToList();
            if (weightList != null && weightList.Any(a => a.Fat > 0))
            {
                App.IsFatExist = true;

                FatBox.IsVisible = true;
                FFMBox.IsVisible = true;
            }
            else
            {
                FatBox.IsVisible = false;
                FFMBox.IsVisible = false;
            }
            lastWeight = weightList?.FirstOrDefault();
            //var weightList = await DrMuscleRestClient.Instance.GetUserWeights();
            if (weightList == null)
                return;
            allWeights = weightList;
            //var newWeightList = new List<UserWeight>();
            decimal latesFatValue = 0;
            //var count = 0;
            if (allWeights != null)
            {
                foreach (var item in allWeights)
                {
                    if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
                    {
                        massUnitType.Text = "KG\n(trend)";
                        item.Weight = Math.Round(new MultiUnityWeight(item.Weight, "kg").Kg, 1);
                        item.Label = string.Format("{0:0.#}", item.Weight);

                        item.TrendWeight = Math.Round(new MultiUnityWeight(item.TrendWeight ?? 0, "kg").Kg, 1);
                        item.TrendWeightLabel = string.Format("{0:0.#}", item.TrendWeight ?? 0);


                    }
                    else
                    {
                        massUnitType.Text = "LBS\n(trend)";

                        item.Weight = Math.Round(new MultiUnityWeight(item.Weight, "kg").Lb, 2);
                        item.Label = string.Format("{0:0.#}", item.Weight);

                        item.TrendWeight = Math.Round(new MultiUnityWeight(item.TrendWeight ?? 0, "kg").Lb, 1);
                        item.TrendWeightLabel = string.Format("{0:0.#}", item.TrendWeight);
                    }
                    if (item.TrendFat >= 0)
                    {
                        item.TrendFatLabel = "(" + string.Format("{0:0.#}", item.TrendFat ?? 0) + ")";

                        decimal fatMassTrend = (decimal)(item.TrendWeight * (item.TrendFat / 100)); // Calculate fat mass trend
                        item.TrendFFMLabel = "(" + string.Format("{0:0.#}", Math.Round(item.TrendWeight ?? 0, 1) - Math.Round(fatMassTrend, 1)) + ")";
                    }
                    else
                    {
                        item.TrendFatLabel = "";
                    }


                    if (item?.Fat > 0)
                    {
                        if (latesFatValue == 0)
                            latesFatValue = item.Fat ?? 0;
                        decimal fatMass = (decimal)(item.Weight * (item.Fat / 100)); // Calculate fat mass
                        item.FFM = Math.Round(item.Weight - Math.Round(fatMass, 1), 1);
                        item.FFMLabel = string.Format("{0:0.#}", item.FFM);
                    }
                    else
                    {
                        item.FFMLabel = "";
                        item.Fat = null;
                    }


                }
            }
            ParseUserWeights();

            if (latesFatValue > 0)
                LocalDBManager.Instance.SetDBSetting("BodyFat", latesFatValue.ToString());


            //((App)Application.Current).weightContext.Weights = weightList;
            //((App)Application.Current).weightContext.SaveContexts();
            SetupWeightTracker(weightList);
            //LblWeightGoal2.Margin = new Thickness(20, 11, 20, 0);
            //LblFatGoal2.Margin = new Thickness(20, 11, 20, 0);
            WeightArrowText.IsVisible = true;

            //if (weightList.Count < 3)
            //{
            //    weightList.Add(weightList.Last());
            //}
            var days = 1;
            var dayStr = "day";

            string massunit = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ? "kg" : "lbs";
            if (weightList?.Count > 1)
            {
                days = (int)((DateTime)weightList[0].CreatedDate.Date - (DateTime)weightList[1].CreatedDate.Date).TotalDays;
                if (days == 0)
                    days = 1;
                dayStr = days > 1 ? "days" : "day";

                //WeightArrowText.Text = $"In the last {days} {dayStr}.";

                //LblTrackin2.IsVisible = false;


                //FFMArrowText.FontSize = 20;
                //FFMArrowText.TextColor = Color.Black;
                //FFMArrowText.FontAttributes = FontAttributes.Bold;
                if (Math.Round(weightList[0].Weight, 2) == Math.Round(weightList[1].Weight, 2))
                {
                    //LblWeightGoal2.Text = "Weight is stable";
                    WeightArrowText.Text = $"Body weight is stable in the last {days} {dayStr}.";
                }
                else if (Math.Round(weightList[0].Weight, 2) >= Math.Round(weightList[1].Weight, 2))
                {
                    var progress = (weightList[0].Weight - weightList[1].Weight) * 100 / weightList[1].Weight;
                    var weightChange = weightList[0].Weight - weightList[1].Weight;
                    //LblWeightGoal2.Text = "Weight +" + String.Format("{0:0.##}%", Math.Round(progress, 2)).ReplaceWithDot();
                    //WeightArrowText.Text = "Body weight +" + String.Format("{0:0.#}", Math.Round(weightChange, 1)) + " " + massunit + " (+" + String.Format("{0:0.#}%)", Math.Round(progress, 1)).ReplaceWithDot() ;
                    WeightArrowText.Text = "Body weight +" + String.Format("{0:0.#}", Math.Round(weightChange, 1)) + " " + massunit;

                    WeightArrowText.Text = $"{WeightArrowText.Text} in the last {days} {dayStr}.";
                }
                else
                {
                    var progress = (weightList[0].Weight - weightList[1].Weight) * 100 / weightList[1].Weight;
                    //LblWeightGoal2.Text = "Weight -" + String.Format("{0:0.##}%", Math.Round(progress, 2)).ReplaceWithDot().Replace("-", ""); ;
                    var weightChange = weightList[0].Weight - weightList[1].Weight;
                    //WeightArrowText.Text = "Body weight " + String.Format("{0:0.#}", Math.Round(weightChange, 1)) + " " + massunit + " (-" + String.Format("{0:0.#}%)", Math.Round(progress, 1)).ReplaceWithDot().Replace("-", ""); ;
                    WeightArrowText.Text = "Body weight " + String.Format("{0:0.#}", Math.Round(weightChange, 1)) + " " + massunit;
                    WeightArrowText.Text = $"{WeightArrowText.Text} in the last {days} {dayStr}.";
                }
                if (Math.Round(weightList[0].TrendWeight ?? 0, 2) == Math.Round(weightList[1].TrendWeight ?? 0, 2))
                {
                    //LblWeightGoal2.Text = LblWeightGoal2.Text + "\u2014" + "Trend is stable";
                    WeightArrowText.Text = $"{WeightArrowText.Text} Trend is stable.";
                }
                else if (Math.Round(weightList[0].TrendWeight ?? 0, 2) >= Math.Round(weightList[1].TrendWeight ?? 0, 2))
                {
                    var progress = (weightList[0].TrendWeight - weightList[1].TrendWeight) * 100 / weightList[1].TrendWeight;
                    var weightTrendChange = weightList[0].TrendWeight - weightList[1].TrendWeight;
                    //LblWeightGoal2.Text = LblWeightGoal2.Text + "\u2014Trend +" + String.Format("{0:0.##}%", Math.Round(progress ?? 0, 2)).ReplaceWithDot();
                    //WeightArrowText.Text =$"{WeightArrowText.Text} Trend +" + String.Format("{0:0.#}", Math.Round(weightTrendChange??0, 1))  + " " + massunit + " (+" + String.Format("{0:0.#}%).", Math.Round(progress ?? 0, 2)).ReplaceWithDot();
                    WeightArrowText.Text = $"{WeightArrowText.Text} Trend +" + String.Format("{0:0.#}", Math.Round(weightTrendChange ?? 0, 1)) + " " + massunit;

                }
                else
                {
                    var progress = (weightList[0].TrendWeight - weightList[1].TrendWeight) * 100 / weightList[1].TrendWeight;
                    //LblWeightGoal2.Text = LblWeightGoal2.Text + "\u2014Trend -" + String.Format("{0:0.##}%", Math.Round(progress ?? 0, 2)).ReplaceWithDot().Replace("-", ""); ;
                    var weightTrendChange = weightList[0].TrendWeight - weightList[1].TrendWeight;
                    //WeightArrowText.Text = $"{WeightArrowText.Text} Trend " + String.Format("{0:0.#}", Math.Round(weightTrendChange ?? 0, 1)) + " " + massunit + " (-" + String.Format("{0:0.#}%).", Math.Round(progress ?? 0, 2)).ReplaceWithDot().Replace("-", ""); ;
                    WeightArrowText.Text = $"{WeightArrowText.Text} Trend " + String.Format("{0:0.#}", Math.Round(weightTrendChange ?? 0, 1)) + " " + massunit;
                }
            }
            else
            {
                //WeightArrowText.Text = "In the last 1 day.";
                //LblWeightGoal2.Text = "Weight is stable"+ "\u2014Trend is stable";
                WeightArrowText.Text = "Body weight and trend is stable.";
            }

            try
            {

                FatArrowText.IsVisible = true;
                if (weightList?.Count > 1)
                {
                    decimal index0Fat = (weightList[0]?.Fat) ?? 0;
                    decimal index0TrendFat = (weightList[0]?.TrendFat) ?? 0;
                    decimal index1Fat = (weightList[1]?.Fat) ?? 0;
                    decimal index1TrendFat = (weightList[1]?.TrendFat) ?? 0;
                    decimal index0FFM = 0;
                    decimal index0TrendFFM = 0;
                    string mass_Unit = (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg") ? "kg" : "lbs";
                    if (index0Fat > 0)
                    {
                        decimal ffmweight = 0;
                        decimal ffmtrendweight = 0;
                        var isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg";
                        if (isKg)
                        {
                            ffmweight = Decimal.Parse(string.Format("{0:0.##}", Math.Round(weightList[0].Weight, 2)));
                            ffmtrendweight = Decimal.Parse(string.Format("{0:0.##}", Math.Round(weightList[0].TrendWeight ?? 0, 2)));

                        }
                        else
                        {
                            var currentWeightKg = (decimal)weightList[0].Weight;
                            var trendWeightKg = (decimal)weightList[0].TrendWeight;

                            var currentWeightLb = new MultiUnityWeight(currentWeightKg, "kg").Lb;
                            var trendWeightLb = new MultiUnityWeight(trendWeightKg, "kg").Lb;

                            var formattedCurrentWeightLb = Decimal.Parse(string.Format("{0:0.##}", currentWeightLb));
                            var formattedTrendWeightLb = Decimal.Parse(string.Format("{0:0.##}", trendWeightLb));

                            ffmweight = formattedCurrentWeightLb;
                            ffmtrendweight = formattedTrendWeightLb;
                            //ffmweight = Decimal.Parse(string.Format("{0:0.##}", new MultiUnityWeight((decimal)weightList[0].Weight, "kg").Lb));
                            //ffmtrendweight = Decimal.Parse(string.Format("{0:0.##}", new MultiUnityWeight((decimal)weightList[0].TrendWeight, "kg").Lb));

                        }

                        decimal fatMass = (decimal)(ffmweight * (index0Fat / 100)); // Calculate fat mass
                        decimal fatMassTrend = (decimal)(ffmtrendweight * (index0TrendFat / 100)); // Calculate fat mass
                        index0FFM = ffmweight - fatMass;
                        index0TrendFFM = ffmtrendweight - fatMassTrend;
                    }
                    decimal index1FFM = 0;
                    decimal index1TrendFFM = 0;
                    if (index1Fat > 0)
                    {
                        decimal ffmweight = 0;
                        decimal ffmtrendweight = 0;
                        var isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg";
                        if (isKg)
                        {
                            ffmweight = Decimal.Parse(string.Format("{0:0.##}", Math.Round(weightList[1].Weight, 2)));
                            ffmtrendweight = Decimal.Parse(string.Format("{0:0.##}", Math.Round(weightList[1].TrendWeight ?? 0, 2)));

                        }
                        else
                        {
                            ffmweight = Decimal.Parse(string.Format("{0:0.##}", new MultiUnityWeight((decimal)weightList[1].Weight, "kg").Lb));
                            ffmtrendweight = Decimal.Parse(string.Format("{0:0.##}", new MultiUnityWeight((decimal)weightList[1].TrendWeight, "kg").Lb));

                        }

                        decimal fatMass = (decimal)(ffmweight * (index1Fat / 100)); // Calculate fat mass
                        decimal fatMassTrend = (decimal)(ffmtrendweight * (index1TrendFat / 100)); // Calculate fat mass
                        index1FFM = ffmweight - fatMass;
                        index1TrendFFM = ffmtrendweight - fatMassTrend;
                    }

                    var fatdays = 0;
                    var fatdayStr = "";
                    if (index0Fat != 0 && index1Fat != 0)
                    {
                        fatdays = (int)((DateTime)weightList[0].CreatedDate.Date - (DateTime)weightList[1].CreatedDate.Date).TotalDays;
                        fatdayStr = fatdays > 1 ? "days" : "day";
                        FatArrowText.Text = $"In the last {fatdays} {fatdayStr}.";
                    }
                    else if (index0Fat != 0 && index1Fat == 0)
                    {
                        fatdays = (int)((DateTime)weightList[0].CreatedDate.Date - DateTime.Now.Date).TotalDays;
                        if (fatdays > 0)
                        {
                            fatdayStr = fatdays > 1 ? "days" : "day";
                            FatArrowText.Text = $"In the last {fatdays} {fatdayStr}.";
                        }
                        else
                        {
                            FatArrowText.Text = $"Fat is stable";
                        }
                    }

                    if (index0FFM != 0 && index1FFM != 0)
                    {
                        fatdays = (int)((DateTime)weightList[0].CreatedDate.Date - (DateTime)weightList[1].CreatedDate.Date).TotalDays;
                        fatdayStr = fatdays > 1 ? "days" : "day";
                        FFMArrowText.Text = $"In the last {fatdays} {fatdayStr}.";
                    }
                    else if (index0FFM != 0 && index1FFM == 0)
                    {
                        fatdays = (int)((DateTime)weightList[0].CreatedDate.Date - DateTime.Now.Date).TotalDays;
                        if (fatdays > 0)
                        {
                            fatdayStr = fatdays > 1 ? "days" : "day";
                            FFMArrowText.Text = $"In the last {fatdays} {fatdayStr}.";
                        }
                        else
                        {
                            FFMArrowText.Text = $"FFM is stable.";
                        }
                    }


                    if (index0Fat == 0 && index1Fat == 0)
                    {
                        //LblFatGoal2.Text = "Fat is stable";
                        FatArrowText.Text = $"Body fat is stable in the last {days} {dayStr}.";
                        //FatArrowText.IsVisible = false;
                    }
                    else if (Math.Round(index0Fat, 2) == Math.Round(index1Fat, 2))
                    {
                        //LblFatGoal2.Text = "Fat is stable";
                        FatArrowText.Text = $"Body fat is stable in the last {days} {dayStr}.";
                        //FatArrowText.Text = "Since last entry.";
                    }
                    else if (index1Fat == 0)
                    {
                        //LblFatGoal2.Text = "Fat is stable";
                        FatArrowText.Text = $"Body fat is stable in the last {days} {dayStr}.";
                    }
                    else if (Math.Round(index0Fat, 2) >= Math.Round(index1Fat, 2))
                    {
                        var progress = (index0Fat - index1Fat) * 100 / index1Fat;
                        //LblFatGoal2.Text = "Fat +" + String.Format("{0:0.##}%", Math.Round((decimal)progress, 2)).ReplaceWithDot();
                        FatArrowText.Text = $"Body fat +" + String.Format("{0:0.#}%", Math.Round((decimal)progress, 1)).ReplaceWithDot() + $" in the last {days} {dayStr}.";
                    }
                    else
                    {
                        var progress = (index0Fat - index1Fat) * 100 / index1Fat;
                        //LblFatGoal2.Text = "Fat -" + String.Format("{0:0.##}%", Math.Round((decimal)progress, 2)).ReplaceWithDot().Replace("-", ""); ;
                        FatArrowText.Text = $"Body fat -" + String.Format("{0:0.#}%", Math.Round((decimal)progress, 1)).ReplaceWithDot().Replace("-", "") + $" in the last {days} {dayStr}.";
                    }
                    FatBox.IsVisible = true;
                    if (Math.Round(weightList[0].TrendFat ?? 0, 2) == Math.Round(weightList[1].TrendFat ?? 0, 2))
                    {
                        //LblFatGoal2.Text = LblFatGoal2.Text + "\u2014" + "Trend is stable";
                        FatArrowText.Text = $"Body fat and trend is stable in the last {days} {dayStr}.";
                    }
                    else if (Math.Round(index0TrendFat, 2) >= Math.Round(index1TrendFat, 2))
                    {
                        if (index1TrendFat > 0)
                        {
                            var progress = (Math.Round(index0TrendFat, 1) - Math.Round(index1TrendFat, 1)) * 100 / Math.Round(index1TrendFat, 1);
                            //LblFatGoal2.Text = LblFatGoal2.Text + "\u2014Trend +" + String.Format("{0:0.##}%", progress).ReplaceWithDot();
                            FatArrowText.Text = FatArrowText.Text + " Trend +" + String.Format("{0:0.#}%.", progress).ReplaceWithDot();
                        }
                        else
                        {
                            FatArrowText.Text = FatArrowText.Text + " Trend is stable.";
                        }
                    }
                    else
                    {
                        if (index1TrendFat > 0)
                        {
                            var progress = (Math.Round(index0TrendFat, 1) - Math.Round(index1TrendFat, 1)) * 100 / Math.Round(index1TrendFat, 1);
                            //LblFatGoal2.Text = LblFatGoal2.Text + "\u2014Trend -" + String.Format("{0:0.##}%", progress).ReplaceWithDot().Replace("-", ""); 
                            FatArrowText.Text = FatArrowText.Text + " Trend -" + String.Format("{0:0.#}%.", progress).ReplaceWithDot().Replace("-", "");
                        }
                        else
                        {
                            FatArrowText.Text = FatArrowText.Text + " Trend is stable.";
                        }
                    }

                    if (index0FFM == 0 && index1FFM == 0)
                    {
                        //LblFFMGoal2.Text = "FFM is stable";
                        FFMArrowText.Text = "FFM is stable";
                        //FFMArrowText.Text = "Fat free mass is stable";
                    }
                    else if (index1FFM == 0)
                    {
                        //LblFFMGoal2.Text ="FFM is stable";
                        FFMArrowText.Text = "FFM is stable";
                    }
                    else if (Math.Round(index0FFM, 2) == Math.Round(index1FFM, 2))
                    {
                        //LblFFMGoal2.Text = "FFM is stable";
                        FFMArrowText.Text = "FFM is stable";
                        //FatArrowText.Text = "Since last entry.";
                    }
                    else if (Math.Round(index0FFM, 2) >= Math.Round(index1FFM, 2))
                    {
                        var ffmprogress = (Math.Round(index0FFM, 1) - Math.Round(index1FFM, 1)) * 100 / Math.Round(index1FFM, 1);
                        //LblFFMGoal2.Text = "FFM +" + String.Format("{0:0.##}%", Math.Round((decimal)ffmprogress, 2)).ReplaceWithDot();
                        var ffmChange = index0FFM - index1FFM;
                        double newValue = 0;
                        if (mass_Unit == "lbs")
                        {
                            newValue = (double)Math.Round(ffmChange, 2) * 0.453592;
                            FFMArrowText.Text = "FFM +" + String.Format("{0:0.#}", Math.Round(newValue, 1)) + " " + massunit + $" in the last {days} {dayStr}.";
                        }
                        else
                        {
                            //FFMArrowText.Text = "FFM +" + String.Format("{0:0.#}", Math.Round(ffmChange, 1)) + " " + massunit + " (+" + String.Format("{0:0.#}%)", Math.Round((decimal)ffmprogress, 1)).ReplaceWithDot() + $" in the last {days} {dayStr}.";
                            FFMArrowText.Text = "FFM +" + String.Format("{0:0.#}", Math.Round(ffmChange, 1)) + " " + massunit + $" in the last {days} {dayStr}.";
                        }

                    }
                    else
                    {
                        var ffmprogress = (Math.Round(index0FFM, 1) - Math.Round(index1FFM, 1)) * 100 / Math.Round(index1FFM, 1);
                        //LblFFMGoal2.Text = "FFM -" + String.Format("{0:0.##}%", Math.Round((decimal)ffmprogress, 2)).ReplaceWithDot().Replace("-", "");
                        var ffmChange = index0FFM - index1FFM;
                        double newValue = 0;
                        if (mass_Unit == "lbs")
                        {
                            newValue = (double)Math.Round(ffmChange, 2) * 0.453592;
                            FFMArrowText.Text = "FFM " + String.Format("{0:0.#}", Math.Round(newValue, 1)) + " " + massunit + $" in the last {days} {dayStr}.";
                        }
                        else
                        {
                            //FFMArrowText.Text = "FFM " + String.Format("{0:0.#}", Math.Round(ffmChange, 1)) + " " + massunit + " (-" + String.Format("{0:0.#}%)", Math.Round((decimal)ffmprogress, 1)).ReplaceWithDot().Replace("-", "") + $" in the last {days} {dayStr}.";
                            FFMArrowText.Text = "FFM " + String.Format("{0:0.#}", Math.Round(ffmChange, 1)) + " " + massunit + $" in the last {days} {dayStr}.";
                        }

                    }
                    if (Math.Round(index0TrendFFM, 2) == Math.Round(index1TrendFFM, 2))
                    {
                        //LblFFMGoal2.Text = LblFFMGoal2.Text + "\u2014" + "Trend is stable";
                        FFMArrowText.Text = LblFFMGoal2.Text + " and " + "trend is stable";
                    }
                    else if (index1TrendFFM == 0 && index1FFM == 0)
                    {
                        FFMArrowText.Text = "FFM and trend is stable";
                    }
                    else if (Math.Round(index0TrendFFM, 2) >= Math.Round(index1TrendFFM, 2))
                    {
                        var progress = (Math.Round(index0TrendFFM, 1) - Math.Round(index1TrendFFM, 1)) * 100 / Math.Round(index1TrendFFM, 1);
                        //LblFFMGoal2.Text = LblFFMGoal2.Text + "\u2014Trend +" + String.Format("{0:0.##}%", progress).ReplaceWithDot();
                        var ffmChange = index0TrendFFM - index1TrendFFM;
                        double newValue = 0;
                        if (mass_Unit == "lbs")
                        {
                            newValue = (double)Math.Round(ffmChange, 2) * 0.453592;
                            FFMArrowText.Text = FFMArrowText.Text + " Trend +" + String.Format("{0:0.#}", Math.Round(newValue, 1)) + " " + massunit;
                        }
                        else
                        {
                            //FFMArrowText.Text = FFMArrowText.Text + " Trend +" + String.Format("{0:0.#}", Math.Round(ffmChange, 1)) + " " + massunit + " (+" + String.Format("{0:0.#}%).", progress).ReplaceWithDot();
                            FFMArrowText.Text = FFMArrowText.Text + " Trend +" + String.Format("{0:0.#}", Math.Round(ffmChange, 1)) + " " + massunit;
                        }

                    }
                    else
                    {
                        var progress = (Math.Round(index0TrendFFM, 1) - Math.Round(index1TrendFFM, 1)) * 100 / Math.Round(index1TrendFFM, 1);
                        //LblFFMGoal2.Text = LblFFMGoal2.Text + "\u2014Trend -" + String.Format("{0:0.##}%", progress).ReplaceWithDot().Replace("-", ""); ;
                        var ffmChange = index0TrendFFM - index1TrendFFM;

                        double newValue = 0;
                        if (mass_Unit == "lbs")
                        {
                            newValue = (double)Math.Round(ffmChange, 2) * 0.453592;
                            FFMArrowText.Text = FFMArrowText.Text + " Trend " + String.Format("{0:0.#}", Math.Round(newValue, 1)) + " " + massunit;
                        }
                        else
                        {
                            //FFMArrowText.Text = FFMArrowText.Text + " Trend " + String.Format("{0:0.#}", Math.Round(ffmChange, 1)) + " " + massunit + " (-" + String.Format("{0:0.#}%).", progress).ReplaceWithDot().Replace("-", ""); ;
                            FFMArrowText.Text = FFMArrowText.Text + " Trend " + String.Format("{0:0.#}", Math.Round(ffmChange, 1)) + " " + massunit;
                        }

                    }


                    if (weightList[0]?.Fat == null && weightList[1]?.Fat == null)
                    {
                        FatBox.IsVisible = false;
                        FFMBox.IsVisible = false;
                    }
                    else
                    {
                        FatBox.IsVisible = true;
                        FFMBox.IsVisible = true;
                    }
                }
                else
                {
                    if (weightList[0]?.Fat > 0)
                    {
                        FatBox.IsVisible = true;
                        FFMBox.IsVisible = true;
                        //LblFatGoal2.Text = "Fat is stable" + "\u2014Trend is stable";
                        FatArrowText.Text = "Body fat and trend is stable.";
                        //LblFFMGoal2.Text = "FFM is stable" + "\u2014Trend is stable";
                        FFMArrowText.Text = "FFM and trend is stable";
                        //FatArrowText.Text = $"In the last 1 day";
                        //FFMArrowText.Text = FatArrowText.Text;
                    }
                    else
                    {
                        FatBox.IsVisible = false;
                        FFMBox.IsVisible = false;
                    }
                }

                //FFMArrowText1.Text = FatArrowText.Text;
            }
            catch (Exception ex)
            {

            }
            //Set Weight data
            try
            {
                if (plotView != null)
                {
                    plotView.Controller = new PlotController();
                    plotView.Controller?.UnbindMouseDown(OxyMouseButton.Left); // Disable default behavior for left click (single tap)
                    plotView.Controller?.UnbindTouchDown(); // Disable touch down events
                    if (plotModel == null)
                    {
                        plotModel = InitializePlotModel();

                        plotView.Model = plotModel;
                        plotView.Model.InvalidatePlot(true);
                    }
                }
                if (plotViewFat != null)
                {
                    plotViewFat.Controller = new PlotController();
                    plotViewFat.Controller?.UnbindMouseDown(OxyMouseButton.Left); // Disable default behavior for left click (single tap)
                    plotViewFat.Controller?.UnbindTouchDown(); // Disable touch down events
                    if (plotFatModel == null)
                    {
                        plotFatModel = InitializeFatPlotModel();

                        plotViewFat.Model = plotFatModel;
                        plotViewFat.Model.InvalidatePlot(true);

                    }
                }
                if (plotViewFFM != null)
                {
                    plotViewFFM.Controller = new PlotController();
                    plotViewFFM.Controller?.UnbindMouseDown(OxyMouseButton.Left); // Disable default behavior for left click (single tap)
                    plotViewFFM.Controller?.UnbindTouchDown(); // Disable touch down events
                    if (plotFFMModel == null)
                    {
                        plotFFMModel = InitializeFFMPlotModel();

                        plotViewFFM.Model = plotFFMModel;
                        plotViewFFM.Model.InvalidatePlot(true);
                    }
                }
            }
            catch (Exception ex)
            {

            }
            CurrentLog.Instance.WeightChangedPercentage = WeightArrowText.Text;
            CurrentLog.Instance.FatChangedPercentage = FatArrowText.Text;
            CurrentLog.Instance.FFMChangedPercentage = FFMArrowText.Text;
            var last3points = weightList != null ? weightList.Take(3).Reverse() : new List<UserWeight>();
            //int count = 0;
            int index = 1;
            int index2 = 1;
            int index3 = 1;
            var minFatVal = 0;
            var maxFatVal = 0;
            var minFatTrendVal = 0;
            var maxFatTrendVal = 0;

            var minFFMVal = 0;
            var maxFFMVal = 0;
            var minFFMTrendVal = 0;
            var maxFFMTrendVal = 0;

            IndexToDateLabel.Clear();
            IndexToDateLabel2.Clear();
            IndexToDateLabel3.Clear();
            foreach (var weight in last3points)
            {

                //weight.Fat = count++;
                if (weight?.Fat > 0)
                {
                    decimal fatval = 0;
                    decimal fatTrendval = 0;
                    fatval = Decimal.Parse(string.Format("{0:0.##}", Math.Round((decimal)weight.Fat, 2)));
                    fatTrendval = Decimal.Parse(string.Format("{0:0.##}", Math.Round((decimal)weight.TrendFat, 2)));
                    //entriesFat.Add(new ChartEntry((float)fatval) { Label = weight.CreatedDate.ToString("MMM dd"), ValueLabel = fatval.ToString() });
                    if (minFatVal != 0 && minFatVal > fatval)
                    {
                        minFatVal = (int)fatval;
                    }
                    else if (minFatVal == 0)
                    {
                        minFatVal = (int)fatval;
                    }
                    if (maxFatVal != 0 && maxFatVal < fatval)
                    {
                        maxFatVal = (int)fatval + 1;
                    }
                    else if (maxFatVal == 0)
                    {
                        maxFatVal = (int)fatval + 1;
                    }

                    if (minFatTrendVal != 0 && minFatTrendVal > fatTrendval)
                    {
                        minFatTrendVal = (int)fatTrendval;
                    }
                    else if (minFatTrendVal == 0)
                    {
                        minFatTrendVal = (int)fatTrendval;
                    }
                    if (maxFatTrendVal != 0 && maxFatTrendVal < fatTrendval)
                    {
                        maxFatTrendVal = (int)fatTrendval + 1;
                    }
                    else if (maxFatTrendVal == 0)
                    {
                        maxFatTrendVal = (int)fatTrendval + 1;
                    }
                    fatData.Add(new DataPoint(index3, (double)fatval));
                    fatTrendData.Add(new DataPoint(index3, (double)fatTrendval));
                    IndexToDateLabel3[index3] = weight.CreatedDate.ToString("MMM dd", CultureInfo.InvariantCulture);
                    index3++;
                }
                else
                { }
                decimal val = 0;
                decimal trendval = 0;
                val = Decimal.Parse(string.Format("{0:0.##}", Math.Round(weight.Weight, 2)));
                trendval = Decimal.Parse(string.Format("{0:0.##}", Math.Round(weight?.TrendWeight ?? 0, 2)));
                //if (isKg)
                //{
                //    val = Decimal.Parse(string.Format("{0:0.##}", Math.Round(weight.Weight, 2)));
                //    trendval = Decimal.Parse(string.Format("{0:0.##}", Math.Round(weight?.TrendWeight ?? 0, 2)));
                //}
                //else
                //{
                //    val = Decimal.Parse(string.Format("{0:0.##}", new MultiUnityWeight((decimal)weight.Weight, "kg").Lb));
                //    trendval = Decimal.Parse(string.Format("{0:0.##}", new MultiUnityWeight(weight?.TrendWeight ?? 0, "kg").Lb));
                //}


                //entries.Add(new ChartEntry((float)val) { Label = weight.CreatedDate.ToString("MMM dd"), ValueLabel = val.ToString(), TextColor = SKColor.Parse("#38418C") });
                //entriesTrend.Add(new ChartEntry((float)trendval) { Label = weight.CreatedDate.ToString("MMM dd"), ValueLabel = trendval.ToString(), TextColor = SKColor.Parse("#5DD397") });
                decimal fatfreemass = 0;
                decimal fatfreemassTrend = 0;
                weightData.Add(new DataPoint(index2, (double)val));
                weightTrendData.Add(new DataPoint(index2, (double)trendval));
                IndexToDateLabel2[index2] = weight.CreatedDate.ToString("MMM dd", CultureInfo.InvariantCulture);
                index2++;
                if (weight?.Fat > 0)
                {
                    decimal ffmweight = 0;
                    decimal ffmTrendweight = 0;
                    decimal ffmTrendFat = 0;
                    ffmweight = Decimal.Parse(string.Format("{0:0.##}", Math.Round(weight.Weight, 2)));

                    ffmTrendweight = Decimal.Parse(string.Format("{0:0.##}", Math.Round((decimal)weight.TrendWeight, 2)));
                    ffmTrendFat = Decimal.Parse(string.Format("{0:0.##}", Math.Round((decimal)weight.TrendFat, 2)));

                    decimal fatMass = (decimal)(ffmweight * (weight.Fat / 100)); // Calculate fat mass
                    decimal fatMassTrend = (decimal)(ffmTrendweight * (weight.TrendFat / 100)); // Calculate fat mass
                    fatfreemass = ffmweight - fatMass;
                    fatfreemassTrend = ffmTrendweight - fatMassTrend;
                    if (fatfreemass % 1 == 0) // Check if fatfreemass is an integer
                    {
                        fatfreemass = (int)fatfreemass;
                    }
                    fatfreemassTrend = Math.Round(fatfreemassTrend, 2);
                    if (fatfreemassTrend % 1 == 0) // Check if fatfreemass is an integer
                    {
                        fatfreemassTrend = (int)fatfreemassTrend;
                    }
                    fatfreemassTrend = Math.Round(fatfreemassTrend, 2);

                    if (minFFMVal != 0 && minFFMVal > fatfreemass)
                    {
                        minFFMVal = (int)fatfreemass;
                    }
                    else if (minFFMVal == 0)
                    {
                        minFFMVal = (int)fatfreemass;
                    }
                    if (maxFFMVal != 0 && maxFFMVal < fatfreemass)
                    {
                        maxFFMVal = (int)fatfreemass + 1;
                    }
                    else if (maxFFMVal == 0)
                    {
                        maxFFMVal = (int)fatfreemass + 1;
                    }

                    if (minFFMTrendVal != 0 && minFFMTrendVal > fatfreemassTrend)
                    {
                        minFFMTrendVal = (int)fatfreemassTrend;
                    }
                    else if (minFFMTrendVal == 0)
                    {
                        minFFMTrendVal = (int)fatfreemassTrend;
                    }
                    if (maxFFMTrendVal != 0 && maxFFMTrendVal < fatfreemassTrend)
                    {
                        maxFFMTrendVal = (int)fatfreemassTrend + 1;
                    }
                    else if (maxFFMTrendVal == 0)
                    {
                        maxFFMTrendVal = (int)fatfreemassTrend + 1;
                    }
                    ffmData.Add(new DataPoint(index, (double)fatfreemass));
                    ffmTrendData.Add(new DataPoint(index, (double)fatfreemassTrend));
                    IndexToDateLabel[index] = weight.CreatedDate.ToString("MMM dd", CultureInfo.InvariantCulture);
                    index++;
                }
                else
                {
                }

            }

            var weightMin = (double)last3points.Min(a => a.Weight);
            var weightMax = (double)last3points.Max(a => a.Weight);
            var trendMin = (double)last3points.Min(a => a.TrendWeight);
            var trendMax = (double)last3points.Max(a => a.TrendWeight);
            var minVal = (weightMin > trendMin) ? trendMin : weightMin;
            var maxVal = (weightMax > trendMax) ? weightMax : trendMax;
            if (minVal == maxVal)
            {
                minVal = minVal - 5;
                maxVal = maxVal + 5;
            }
            var weightDateMin = last3points.Min(a => a.CreatedDate);
            var weightDateMax = last3points.Max(a => a.CreatedDate);
            var min = minVal - (maxVal - minVal) * 0.1;
            var max = maxVal + (maxVal - minVal) * 0.15;

            var minVal2 = (minFatVal > minFatTrendVal) ? minFatTrendVal : minFatVal;
            var maxVal2 = (maxFatVal > maxFatTrendVal) ? maxFatVal : maxFatTrendVal;

            var min2 = minVal2 - (maxVal2 - minVal2) * 0.1;
            var max2 = maxVal2 + (maxVal2 - minVal2) * 0.15;

            ////////FFM

            var minVal3 = (minFFMVal > minFFMTrendVal) ? minFFMTrendVal : minFFMVal;
            var maxVal3 = (maxFFMVal > maxFFMTrendVal) ? maxFFMVal : maxFFMTrendVal;

            var min3 = minVal3 - (maxVal3 - minVal3) * 0.1;
            var max3 = maxVal3 + (maxVal3 - minVal3) * 0.15;
            try
            {

                // Add the series to the plot model
                (plotModel.Series[0] as LineSeries).Points.Clear();
                foreach (var point in weightData)
                {
                    (plotModel.Series[0] as LineSeries).Points.Add(point);
                }

                        (plotModel.Series[1] as LineSeries).Points.Clear();
                foreach (var point in weightTrendData)
                {
                    (plotModel.Series[1] as LineSeries).Points.Add(point);
                }
                await UpdateAxisLimits(plotModel, min, max, "weight");
                plotView.Model.InvalidatePlot(true);

                // Add the series to the plot model
                (plotFatModel.Series[0] as LineSeries).Points.Clear();
                foreach (var point in fatData)
                {
                    (plotFatModel.Series[0] as LineSeries).Points.Add(point);
                }

                        (plotFatModel.Series[1] as LineSeries).Points.Clear();
                foreach (var point in fatTrendData)
                {
                    (plotFatModel.Series[1] as LineSeries).Points.Add(point);
                }
                await UpdateAxisLimits(plotFatModel, min2, max2, "fat");
                plotViewFat.Model.InvalidatePlot(true);

                (plotFFMModel.Series[0] as LineSeries).Points.Clear();
                foreach (var point in ffmData)
                {
                    (plotFFMModel.Series[0] as LineSeries).Points.Add(point);
                }

                        (plotFFMModel.Series[1] as LineSeries).Points.Clear();
                foreach (var point in ffmTrendData)
                {
                    (plotFFMModel.Series[1] as LineSeries).Points.Add(point);
                }
                await UpdateAxisLimits(plotFFMModel, min3, max3, "ffm");
                plotViewFFM.Model.InvalidatePlot(true);
            }
            catch (Exception ex)
            {

            }

            //plotModel.Series.Add(series);
            //plotModel.Series.Add(series1);
            // Add the series to the plot model
            //fatPlotModel.Series.Add(fatSeries);
            //fatPlotModel.Series.Add(fatTrendSeries);
            ////////FFM
            //ffmPlotModel.Series.Add(ffmSeries);
            //ffmPlotModel.Series.Add(ffmTrendSeries);



            //plotModel.Axes.Add(new DateTimeAxis
            //{
            //    Position = AxisPosition.Bottom,
            //    StringFormat = "MMM dd",
            //    LabelFormatter = _formatter2,
            //    MinimumPadding = 0.05,
            //    MaximumPadding = 0.05,
            //    ExtraGridlineColor = OxyColors.Transparent,
            //    MajorGridlineColor = OxyColors.Transparent,
            //    MinorGridlineColor = OxyColors.Transparent
            //});
            //plotModel.Axes.Add(new LinearAxis { Position = AxisPosition.Left, Minimum = min, Maximum = max, });

            //plotModel.TitleHorizontalAlignment = TitleHorizontalAlignment.CenteredWithinView;
            //// Set the model for the PlotView
            //plotView.Model = plotModel;




            WeightBox2.IsVisible = true;
        }
        catch (Exception ex)
        {
        }



    }
    private async Task UpdateAxisLimits(PlotModel plotModel, double minimum, double maximum, string plot = "")
    {
        var yAxis = plotModel.Axes.FirstOrDefault(a => a.Position == AxisPosition.Left) as LinearAxis;
        if (yAxis != null)
        {
            if (maximum - minimum > 2)
            {
                // Optionally add some padding or use specific logic to determine min and max
                yAxis.Minimum = minimum;
                yAxis.Maximum = maximum;
            }
            else
            {
                maximum = maximum + 2;
                yAxis.Minimum = minimum;
                yAxis.Maximum = maximum;
            }

        }
        if (plot == "weight")
        {
            int digitCount = Math.Round(maximum).ToString().Length;
            var newValue = 4 * digitCount;
            var rightMarginValue = 20 + newValue;
            LblWeightGoal2.Margin = new Thickness(rightMarginValue, 0, 0, 0);
            WeightArrowText.Margin = LblWeightGoal2.Margin;
        }
        else if (plot == "fat")
        {
            int digitCount = Math.Round(maximum).ToString().Length;
            var newValue = 4 * digitCount;
            var rightMarginValue = 20 + newValue;
            LblFatGoal2.Margin = new Thickness(rightMarginValue, 0, 0, 0);
            FatArrowText.Margin = LblFatGoal2.Margin;
        }
        else if (plot == "ffm")
        {
            int digitCount = Math.Round(maximum).ToString().Length;
            var newValue = 4 * digitCount;
            var rightMarginValue = 20 + newValue;
            LblFFMGoal2.Margin = new Thickness(rightMarginValue, 0, 0, 0);
            FFMArrowText.Margin = LblFFMGoal2.Margin;
        }
    }
    public async Task<List<UserWeight>> GetUserWeightsAsync()
    {
        // Assuming DrMuscleRestClient.Instance.GetUserWeights() returns a Task<List<Weight>>
        // If not, you need to make GetUserWeights method truly asynchronous
        return await DrMuscleRestClient.Instance.GetUserWeights();
    }
    async void CheckInClicked(object sender, EventArgs e)
    {
        try
        {
            var modalPage1 = new BodyProgressPopup(null, false, lastWeight.Weight, lastWeight.Fat);
            modalPage1.ActionSelected += (sender1, action) =>
            {
                if (action == BodyProgressPopup.PopupAction.OK)
                {
                    Device.BeginInvokeOnMainThread(async () =>
                    {
                        //HistoryListView.IsVisible = false;
                        //getUseeWeights();
                        LocalDBManager.Instance.SetDBSetting("CaloriesUpdated", "False");
                        var weightList = await DrMuscleRestClient.Instance.GetUserWeights();
                        if (weightList == null)
                            return;
                        LocalDBManager.Instance.SetDBSetting("OverallWeights", JsonConvert.SerializeObject(weightList));
                       

                        ((App)Application.Current).weightContext.Weights = weightList;
                        ((App)Application.Current).weightContext.SaveContexts();
                        await UpdateTargetIntake();
                        await LoadSavedWeightFromServer();
                        LoadAnalysis();

                    });
                }
            };
            await this.ShowPopupAsync(modalPage1);
        }
        catch (Exception ex)
        {

        }


    }
    protected async override void OnAppearing()
    {
        base.OnAppearing();
        IsViewVisible = true;
        try
        {
            scrollView.Scrolled += ScrollView_Scrolled;
            MessagingCenter.Subscribe<Message.GeneralMessage>(this, "GeneralMessage", (obj) =>
            {
                //if (Device.RuntimePlatform == Device.Android)
                //{
                //    WeightBox2.IsVisible = false;
                //    FatBox.IsVisible = false;
                //}
                if (obj.PopupEnum == Enums.GeneralPopupEnum.TrendInfo)
                    InfoIcon_Tapped2(new object(), EventArgs.Empty);
            });
            Refresh();
        }
        catch (Exception ex)
        {

        }
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        scrollView.Scrolled -= ScrollView_Scrolled;
        WeightArrowText.Text = "";
        FatArrowText.Text = "";
        FFMArrowText.Text = "";
        IsViewVisible = false;
    }
    async void Refresh()
    {
        //Device.BeginInvokeOnMainThread(async () =>
        //{
        //HistoryListView.IsVisible = false;
        try
        {
            await LoadSavedWeightFromServer();
            //});
            var analysis = LocalDBManager.Instance.GetDBSetting("AnalysisReport")?.Value;
            if (string.IsNullOrEmpty(analysis))
            {
                LblStrengthUpQuestion.Text = "Loading AI analysis...";
                LblStrengthUpTextPart1.Text = "Loading...";
                var name = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;

                FatChartPremiumStack.IsVisible = false;
                plotViewFatGrid.IsVisible = true;
                FatArrowText.IsVisible = true;

                FFMFatChartPremiumStack.IsVisible = false;
                plotViewFFMGrid.IsVisible = true;
                FFMArrowText.IsVisible = true;

                if (!string.IsNullOrEmpty(name))
                    LoadAnalysis();
            }
            else if (App.IsFreePlan)
            {
                plotViewFatGrid.IsVisible = false;
                FatChartPremiumStack.IsVisible = true;
                FatArrowText.IsVisible = false;

                plotViewFFMGrid.IsVisible = false;
                FFMFatChartPremiumStack.IsVisible = true;
                FFMArrowText.IsVisible = false;


                LblStrengthUpQuestion.Text = "Loading AI analysis...";
                LblStrengthUpTextPart1.Text = "Premium feature.";
                gridChatButtons.IsVisible = false;
                //LblStrengthUpTextPart2.Text = " Learn more.";
                LblStrengthUpTextPart2.Text = "";
                AICardLearnMoreGrid.IsVisible = true;
            }
            else
            {
                FatChartPremiumStack.IsVisible = false;
                plotViewFatGrid.IsVisible = true;
                FatArrowText.IsVisible = true;

                FFMFatChartPremiumStack.IsVisible = false;
                plotViewFFMGrid.IsVisible = true;
                FFMArrowText.IsVisible = true;
                AICardLearnMoreGrid.IsVisible = false;

                var AIanalysisHeader = LocalDBManager.Instance.GetDBSetting("AIanalysisHeader")?.Value;
                LblStrengthUpQuestion.Text = !string.IsNullOrEmpty(AIanalysisHeader) ? AIanalysisHeader : AppThemeConstants.AIAnalysisTitle();
                LblStrengthUpTextPart1.Text = analysis;
                if (App.IsWeightChangeFromOtherScreen || App.IsMealPlanChange)
                {
                    LoadAnalysis();
                }
            }
        }
        catch (Exception ex)
        {

        }
    }
    private void LoadAnalysis()
    {

        var queryprogress = $"Give me a progress report. Focus on body weight and fat progress, with tips for faster progress.\r\n\r\nCover the following points, starting each paragraph with \"you\" or \"your\":\r\nBody weight progress according to calories, protein, fat and carbs, also return the values for the each  calories, protein, carbs and fat\r\nBody fat and fat-free mass progress\r\nCompare and contrast my body weight, fat, and FFM progress\r\nDiet tips for faster progress\r\nStrength and volume progress\r\nWorkouts completed total weight lifted\r\nEnd on an inspiring note";

        //LblStrengthUpQuestion.Text = "Loading...";
        //LblStrengthUpTextPart1.Text = "Loading...";
        var modelprogress = new BotModel()
        {
            Question = "Loading...",
            IsNewRecordAvailable = false,
            Part1 = "Loading AI analysis...",
            StrengthImage = "lamp.png",
            LevelUpText = "3",
            Type = BotType.AICard
        };

        if (!App.IsFreePlan)
        {
            plotViewFatGrid.IsVisible = true;
            FatChartPremiumStack.IsVisible = false;
            FatArrowText.IsVisible = true;

            FFMFatChartPremiumStack.IsVisible = false;
            plotViewFFMGrid.IsVisible = true;
            FFMArrowText.IsVisible = true;

            AICardLearnMoreGrid.IsVisible = false;

            LblStrengthUpQuestion.Text = AppThemeConstants.AIAnalysisTitle();
            LocalDBManager.Instance.SetDBSetting("AIanalysisHeader", LblStrengthUpQuestion.Text);
            gridChatButtons.IsVisible = true;
            SendMessagesForAIAnalysis(queryprogress, true, 4, modelprogress);
        }
        else
        {
            plotViewFatGrid.IsVisible = false;
            FatChartPremiumStack.IsVisible = true;
            FatArrowText.IsVisible = false;

            plotViewFFMGrid.IsVisible = false;
            FFMFatChartPremiumStack.IsVisible = true;
            FFMArrowText.IsVisible = false;

            LblStrengthUpQuestion.Text = "Loading AI analysis...";
            LblStrengthUpTextPart1.Text = "Premium feature.";
            gridChatButtons.IsVisible = false;
            //LblStrengthUpTextPart2.Text = " Learn more.";
            //BotList.Add(modelprogress);
            AICardLearnMoreGrid.IsVisible = true;
        }
    }

    private async void SendMessagesForAIAnalysis(string msg, bool isFromUser, int noOfAttempt, BotModel botModel)
    {
        try
        {
            //var url = ;

            string chatBotId = AppThemeConstants.ChatBotId;
            string chatBotSecretKey = AppThemeConstants.ChatBotSecretKey;
            string prompt = "";

            HttpClientHandler clientHandler = new HttpClientHandler();
            clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
            var name = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
            //Integrate previous two messages:
            var msgFromUser = new List<GptMessage>() { new GptMessage() { role = "user", content = msg } };

            //TO SEND INFORMATION ABOUT SOME USER INFORMATION

            try
            {
                bool isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ? true : false;
                decimal _userBodyWeight = 0;
                if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
                {
                    _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
                    Config.CurrentWeight = _userBodyWeight.ToString();
                }

                decimal goalWeight = 0;

                if (LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value != null)
                {
                    goalWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value.ReplaceWithDot(), CultureInfo.InvariantCulture);

                }

                var userstats = "";
                if (isKg)
                {
                    userstats = string.Format("Client name: {0}\nBody weight:  {1:0.##} {2}", name, Math.Round(_userBodyWeight, 2), "kg");
                    if (goalWeight != 0)
                        userstats += string.Format("\nTarget body weight: {0:0.##} {1}", Math.Round(goalWeight, 2), "kg");
                }
                else
                {

                    var truncateWeight = TruncateDecimal(_userBodyWeight, 3);
                    var lbWeight = new MultiUnityWeight(truncateWeight, "kg").Lb;
                    userstats = string.Format("Client name: {0}\nBody weight:  {1:0.##} {2}", name, Math.Round(lbWeight, 2), "lbs");
                    if (goalWeight != 0)
                    {
                        userstats += string.Format("\nTarget body weight: {0:0.##} {1}", Math.Round(new MultiUnityWeight((decimal)goalWeight, "kg").Lb, 2), "lbs");
                    }
                }
                if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("BodyFat")?.Value))
                {
                    userstats += $"\nBody Fat : " + LocalDBManager.Instance.GetDBSetting("BodyFat")?.Value + " %";
                }
                if (_userBodyWeight < goalWeight)
                {
                    userstats += "\nGoal: gain muscle mass";
                }
                else
                {
                    userstats += "\nGoal: build lean muscle and burn fat";
                }

                userstats += $"\n Age: {LocalDBManager.Instance.GetDBSetting("Age")?.Value}";

                var homePagedata = await GetStatsMessage();
                if (!string.IsNullOrEmpty(homePagedata))
                    userstats += $"{homePagedata}";
                msgFromUser.Add(new GptMessage() { role = "system", content = userstats });
                Console.Write("Analysis Prompt" + msgFromUser.FirstOrDefault());
                Console.Write("Analysis Prompt1" + msgFromUser.LastOrDefault());

            }
            catch (Exception ex)
            {


            }
            msgFromUser.Reverse();
            try
            {
                using (var _httpClient = new HttpClient(clientHandler))
                {
                    _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", chatBotSecretKey);
                    _httpClient.Timeout = TimeSpan.FromSeconds(30);
                    string model = "gpt-4o-mini";//"text-davinci-002"; // model for GPT-3.5 Turbo
                    var requestUrl = "https://www.chatbase.co/api/v1/chat";
                    var requestBody = new
                    {
                        messages = msgFromUser,
                        model = model,
                        temperature = 0,
                        stream = true,
                        chatbotId = chatBotId,
                        conversationId = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                    };
                    var requestBodyJson = Newtonsoft.Json.JsonConvert.SerializeObject(requestBody);
                    var requestContent = new StringContent(requestBodyJson, System.Text.Encoding.UTF8, "application/json");

                    var response = await _httpClient.SendAsync(new HttpRequestMessage(HttpMethod.Post, requestUrl)
                    {
                        Content = requestContent
                    });

                    // var response = await _httpClient.PostAsync(requestUrl, requestContent);


                    if (response == null)
                    {
                        if (noOfAttempt > 0)
                        {
                            noOfAttempt -= 1;
                            SendMessagesForAIAnalysis(msg, isFromUser, noOfAttempt, botModel);
                            return;
                        }

                        await ConnectionError();
                        noOfAttempt = 4;
                        SendMessagesForAIAnalysis(msg, isFromUser, noOfAttempt, botModel);
                        //DrMuscleTyping.IsVisible = false;
                        return;
                    }
                    var responseBodyJson = await response.Content.ReadAsStringAsync();

                    var chatResponse = responseBodyJson;



                    if (string.IsNullOrEmpty(chatResponse))
                    {
                        analysisCard.IsVisible = true;
                        LblStrengthUpTextPart1.Text = chatResponse;
                        return;
                    }
                    else if (chatResponse.ToLower().Contains("\"code\":\"error\""))
                    {
                        analysisCard.IsVisible = true;
                        LblStrengthUpTextPart1.Text = "Oops! Could not load message. Please try again later. Contact us if the issue persists.";
                        App.IsWeightChangeFromOtherScreen = false;
                        App.IsMealPlanChange = false;
                        return;
                    }
                    else
                    {
                        if(chatResponse.ToLower().Contains("i'm sorry, I'm not sure. a human from the team will reply in 1 business day."))
                        {
                            analysisCard.IsVisible = false;
                        }
                        else
                        {
                            analysisCard.IsVisible = true;
                        }


                        //chatResponse = Regex.Replace(chatResponse, @"\b\d+\.\s(?!$)", "");
                        //// Remove colons and ensure a newline after a colon if there are words following it
                        string pattern = @"(\d)(g)";

                        // Regex.Replace to insert a space between the digit and 'g'
                        chatResponse = Regex.Replace(chatResponse, pattern, "$1 $2");
                        //chatResponse = Regex.Replace(chatResponse, @":\s+(\w)", "\n$1");
                        ////chatResponse = Regex.Replace(chatResponse, @":(\s*\w)", ":$1");

                        //chatResponse = Regex.Replace(chatResponse, @":\s*", ".\n\n", RegexOptions.None);


                        string[] lines = chatResponse.Split('\n');



                        string substringToRemove = "Next, would you like";

                        using (StringReader reader = new StringReader(chatResponse))
                        {
                            using (StringWriter writer = new StringWriter())
                            {
                                string line;
                                while ((line = reader.ReadLine()) != null)
                                {
                                    if (!line.Contains(substringToRemove))
                                    {
                                        writer.WriteLine(line);
                                    }
                                }
                                chatResponse = writer.ToString();
                            }
                        }

                        LblStrengthUpTextPart1.Text = chatResponse.TrimEnd();
                        App.IsWeightChangeFromOtherScreen = false;
                        App.IsMealPlanChange = false;
                        LocalDBManager.Instance.SetDBSetting("AnalysisReport", chatResponse.TrimEnd());
                        //if (chatResponse.Count() < 100)
                        //    BotList.Remove(botModel);
                        //Console.WriteLine("Response = " + chatResponse.TrimEnd());
                        DrMuscleRestClient.Instance.SendAdminMessageWithoutLoader(new ChatModel()
                        {
                            ReceiverId = AppThemeConstants.ChatReceiverId,
                            Message = chatResponse.TrimEnd(),
                            IsFromAI = true
                        });

                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                if (noOfAttempt > 0)
                {
                    noOfAttempt -= 1;
                    SendMessagesForAIAnalysis(msg, isFromUser, noOfAttempt, botModel);
                    return;
                }
                await ConnectionError();
                noOfAttempt = 4;
                SendMessagesForAIAnalysis(msg, isFromUser, noOfAttempt, botModel);

            }
        }
        catch (Exception exc)
        {

        }
    }
    async Task ConnectionError()
    {
        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        // {
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     Message = AppResources.PleaseCheckInternetConnection,
        //     Title = AppResources.ConnectionError,
        //     OkText = "Try again"
        // });

        await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Try again","");
    }
    async Task<string> GetStatsMessage()
    {
        var stasts = "";
        try
        {
            //Weight Charts changes 
            if (!string.IsNullOrEmpty(CurrentLog.Instance.WeightChangedPercentage))
                stasts += $"\nBody weight progress: {CurrentLog.Instance.WeightChangedPercentage}";//Weight Charts changes 

            if (!string.IsNullOrEmpty(CurrentLog.Instance.FatChangedPercentage))
                stasts += $"\nBody fat progress: {CurrentLog.Instance.FatChangedPercentage}";//Fat Charts changes 
            if (!string.IsNullOrEmpty(CurrentLog.Instance.FFMChangedPercentage))
                stasts += $"\nFFM progress: {CurrentLog.Instance.FFMChangedPercentage}";

            // Coach Tips Card 
            //if (!string.IsNullOrEmpty(CurrentLog.Instance.CoachTipsText))
            //    stasts += $" ({CurrentLog.Instance.CoachTipsText})";

        }
        catch (Exception e)
        {

        }

        //GET Calories, Fat, Protein, Carbs
        try
        {
            Macros macros1 = new Macros();
            decimal _userBodyWeight = 0;
            if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
            {
                _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
                Config.CurrentWeight = _userBodyWeight.ToString();
            }
            decimal _targetIntake = 0;
            if (LocalDBManager.Instance.GetDBSetting("TargetIntake")?.Value != null)
            {
                _targetIntake = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("TargetIntake").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
            }

            var data = LocalDBManager.Instance.GetDBSetting("Macros")?.Value;
            if (!string.IsNullOrEmpty(data))
            {
                macros1 = JsonConvert.DeserializeObject<Macros>(data);
                _targetIntake = macros1.Calories;
            }
            string macros = $"\nTarget caloric intake: {Math.Round(_targetIntake)}";

            var newLowerTargetIntake = _targetIntake - (Math.Round(_userBodyWeight * (decimal)2.15) * 4);
            var newHigherTargetIntake = _targetIntake - (Math.Round(_userBodyWeight * (decimal)2.15) * 4);
            var carbs = $"{Math.Round((double)newLowerTargetIntake * 0.2 / 4)} - {Math.Round((double)newHigherTargetIntake * 0.8 / 4)} g";


            if (!string.IsNullOrEmpty(data))
                macros += $"\nCarbs range: " + Math.Round(macros1.Carbs).ToString() + " g";
            else
            {
                //macros += $"\nCarbs range: " + Math.Round((double)_targetIntake * 0.45 / 3.87) + "-" + Math.Round((double)_targetIntake * 0.65 / 3.87) + " g";
                macros += $"\nCarbs range: " + carbs;
            }


            macros += $"\nProtein range: ";

            //if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
            //{
            //    if (!string.IsNullOrEmpty(data))
            //        macros += Math.Round(macros1.Protein).ToString() + " g";
            //    else
            //        macros += Math.Round(new MultiUnityWeight((decimal)_userBodyWeight, "kg").Kg * (decimal)1.6) + "-" + Math.Round(new MultiUnityWeight((decimal)_userBodyWeight, "kg").Kg * (decimal)2.2) + " g";
            //}
            //else
            //{
            //    if (!string.IsNullOrEmpty(data))
            //        macros += Math.Round(macros1.Protein).ToString() + " g";
            //    else
            //    {
            //        //macros += Math.Round(new MultiUnityWeight((decimal)_userBodyWeight, "kg").Lb * (decimal)0.7) + "-" + Math.Round(new MultiUnityWeight((decimal)_userBodyWeight, "kg").Lb * (decimal)1.0) + " g";
            //        var protein = $"{Math.Round(_userBodyWeight * (decimal)1.8)} - {Math.Round(_userBodyWeight * (decimal)2.5)} g";
            //        macros += protein;
            //    }
            //}

            if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
            {
                if (!string.IsNullOrEmpty(data))
                    macros += Math.Round(macros1.Protein).ToString() + " g";
                else
                    macros += Math.Round(new MultiUnityWeight((decimal)_userBodyWeight, "kg").Kg * (decimal)1.6) + "-" + Math.Round(new MultiUnityWeight((decimal)_userBodyWeight, "kg").Kg * (decimal)2.2) + " g";
            }
            else
            {
                if (!string.IsNullOrEmpty(data))
                    macros += Math.Round(macros1.Protein).ToString() + " g";
                else
                    macros += Math.Round(new MultiUnityWeight((decimal)_userBodyWeight, "kg").Lb * (decimal)0.7) + "-" + Math.Round(new MultiUnityWeight((decimal)_userBodyWeight, "kg").Lb * (decimal)1.0) + " g";
            }

            macros += $"\nFats range: ";
            var fats = $"{Math.Round((double)newLowerTargetIntake * 0.2 / 9)} - {Math.Round((double)newHigherTargetIntake * 0.8 / 9)} g";
            if (!string.IsNullOrEmpty(data))
                macros += Math.Round(macros1.Fat).ToString() + " g";
            else
            {
                //macros += Math.Round((double)_targetIntake * 0.2 / 9) + "-" + Math.Round((double)_targetIntake * 0.35 / 9) + " g";
                macros += fats;
            }

            stasts += $"\n{macros}";
        }
        catch (Exception e)
        {
            Console.WriteLine(e);

        }

        try
        {
            var statsModel = new BotModel()
            {
                Type = BotType.Stats
            };
            var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
            var levelUpBotModel = new BotModel();
            bool IsEstimated = false;
            levelUpBotModel.Type = BotType.LevelUp;

            DateTime? creationDate = null;
            try
            {
                creationDate = new DateTime(Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("creation_date").Value));
            }
            catch (Exception)
            {

            }
            //TODO: changed for New UI
            //lstChats.IsVisible = true;
            //lstChats.IsVisible = false;

            if (workouts != null)
            {
                if (workouts.Sets != null)
                {
                    if (workouts.Averages.Count > 1)
                    {
                        if (workouts.Averages[1].Average.Kg == 0)
                            IsEstimated = true;
                        OneRMAverage last = workouts.Averages.ToList()[workouts.Averages.Count - 1];
                        OneRMAverage before = workouts.Averages.ToList()[workouts.Averages.Count - 2];
                        decimal progresskg = (last.Average.Kg - before.Average.Kg) * 100 / (before.Average.Kg < 1 ? 1 : before.Average.Kg);
                        bool inKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg";
                        // strProgress += String.Format("- {0}: {1}{2} ({3}%)\n", AppResources.MaxStrength, (last.Average.Kg - before.Average.Kg) > 0 ? "+" : "", inKg ? Math.Round(last.Average.Kg - before.Average.Kg) + " kg" : Math.Round(last.Average.Lb - before.Average.Lb) + " lbs", Math.Round(progresskg)).ReplaceWithDot();
                        if ((last.Average.Kg - before.Average.Kg) >= 0)
                        {
                            statsModel.StrengthPerText = String.Format("Last 3 weeks, your strength went {0}{1}% (on average).", (last.Average.Kg - before.Average.Kg) >= 0 ? "+" : "", Math.Round(progresskg)).ReplaceWithDot();


                            //StrengthArrowText.Text =  statsModel.StrengthPerText;
                            //statsModel.StrengthMessage = String.Format(" {0}{1} {2}", (last.Average.Kg - before.Average.Kg) >= 0 ? "+ " : "", inKg ? Math.Round(last.Average.Kg - before.Average.Kg) + " kg" : Math.Round(last.Average.Lb - before.Average.Lb) + " lbs", AppResources.MaxStrength).ReplaceWithDot();
                            statsModel.StrengthImage = "up_arrow.png";
                            //Green
                            statsModel.StrengthTextColor = Color.FromHex("#5CD196");
                            //StrengthArrowText.TextColor = Color.FromHex("#5CD196");
                            var perceStr = "";
                            if (before.Average.Kg == 0)
                            {
                                perceStr = String.Format("{0}!", Math.Round(inKg ? last.Average.Kg : last.Average.Lb)).ReplaceWithDot();
                                stasts += String.Format("\nStrength progress: {0} {1}{2} over the past few weeks", "Average 1RM up", (last.Average.Kg - before.Average.Kg) >= 0 ? "" : "", Math.Round(inKg ? last.Average.Kg : last.Average.Lb)).ReplaceWithDot();
                            }
                            else
                            {
                                perceStr = String.Format("{0}%!", Math.Round(progresskg)).ReplaceWithDot();
                                stasts += String.Format("\nStrength progress: {0} {1}{2}% over the past few weeks", "Average 1RM up", (last.Average.Kg - before.Average.Kg) >= 0 ? "" : "", Math.Round(progresskg)).ReplaceWithDot();
                            }

                        }
                        else
                        {
                            var perceStr = "";
                            perceStr = String.Format("{0}%", Math.Round(progresskg)).ReplaceWithDot().Replace("-", "");
                            //LblStrengthProgress.Text = String.Format("{0} {1}{2}!", LblStrengthProgress.Text, (last.Average.Kg - before.Average.Kg) >= 0 ? "+" : "", Math.Round(progresskg)).ReplaceWithDot();
                            //statsModel.StrengthMessage = String.Format(" {0}{1} {2}", (last.Average.Kg - before.Average.Kg) >= 0 ? "+ " : "", inKg ? Math.Round(last.Average.Kg - before.Average.Kg) + " kg" : Math.Round(last.Average.Lb - before.Average.Lb) + " lbs", AppResources.MaxStrength).ReplaceWithDot();
                            //Red
                            //StrengthArrowText.TextColor = Color.FromHex("#BA1C31");
                            stasts = String.Format("\nStrength progress: {0} {1}{2}% over the past few weeks", "Average 1RM down", (last.Average.Kg - before.Average.Kg) >= 0 ? "" : "", Math.Round(progresskg)).ReplaceWithDot().Replace("-", "");


                        }
                        //statsModel.StrengthMessage = AppResources.MaxStrength;
                        workouts.Sets.Reverse();
                        workouts.SetsDate.Reverse();

                        if (workouts.Sets.Count > 1)
                        {
                            bool isflg = false;
                            foreach (var set in workouts.Sets)
                            {
                                if (set != 0)
                                    isflg = true;
                            }

                            int firstSets = workouts.Sets[workouts.Sets.Count - 1];
                            int lastSets = workouts.Sets[workouts.Sets.Count - 2];
                            try
                            {
                                decimal progressSets = (firstSets - lastSets) * 100 / (lastSets == 0 ? 1 : lastSets);
                                if (firstSets == 0)
                                {
                                    progressSets = lastSets;
                                }
                                // strProgress += String.Format("- {0}: {1}{2} ({3}%)\n", AppResources.WorkSetsNoColon, (firstSets - lastSets) >= 0 ? "+" : "", firstSets - lastSets, Math.Round(progressSets)).ReplaceWithDot();
                                if ((firstSets - lastSets) >= 0)
                                {

                                    if (lastSets == 0)
                                        stasts += String.Format("\nVolume progress: {0} {1}{2} in the last 7 days", "Work sets up", (firstSets - lastSets) >= 0 ? "" : "", firstSets);
                                    else
                                        stasts += String.Format("\nVolume progress: {0} {1}{2}% in the last 7 days", "Work sets up", (firstSets - lastSets) >= 0 ? "" : "", Math.Round(progressSets));
                                }
                                else
                                {

                                    if (firstSets == 0)
                                        stasts += String.Format("\nVolume progress: {0} {1}{2} in the last 7 days", "Work sets down", (firstSets - lastSets) >= 0 ? "" : "", firstSets);
                                    else
                                        stasts += String.Format("\nVolume progress:{0} {1}{2}% in the last 7 days", "Work sets down", (firstSets - lastSets) >= 0 ? "" : "", Math.Round(progressSets)).Replace("-", "");
                                }

                            }
                            catch (Exception ex)
                            {
                            }
                        }

                        workouts.Sets.Reverse();
                        workouts.SetsDate.Reverse();

                        try
                        {
                            levelUpBotModel.Type = BotType.LevelUp;



                            var exerciseModel = workouts.HistoryExerciseModel;
                            if (exerciseModel != null)
                            {
                                var unit = inKg ? AppResources.Kg.ToLower() : AppResources.Lbs.ToLower();
                                var weightLifted = inKg ? exerciseModel.TotalWeight.Kg : exerciseModel.TotalWeight.Lb;
                                stasts += $"\nTotals: {exerciseModel.TotalWorkoutCompleted} workouts done";
                                stasts += inKg
                                    ? $" ({Math.Round(weightLifted)} kg lifted)"
                                    : $" ({Math.Round(weightLifted)} lbs lifted)";
                            }
                        }
                        catch (Exception ex)
                        {

                        }

                        try
                        {


                            if (!string.IsNullOrWhiteSpace(workouts?.GetUserProgramInfoResponseModel?.RecommendedProgram?.Label))
                            {
                                stasts += $"\nCurrent program {workouts?.GetUserProgramInfoResponseModel?.RecommendedProgram?.Label}";
                            }
                        }
                        catch (Exception ex)
                        {

                        }

                        try
                        {
                            if (workouts != null && workouts.GetUserProgramInfoResponseModel != null)
                            {
                                if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram != null)
                                {
                                    if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.RemainingToLevelUp != null)
                                        stasts += $" ({(int)workouts.GetUserProgramInfoResponseModel.RecommendedProgram.RemainingToLevelUp} workouts before next program)";
                                }
                            }
                        }
                        catch (Exception exception)
                        {

                        }

                        //Calculate week streak
                        if (workouts.ConsecutiveWeeks != null && workouts.ConsecutiveWeeks.Count > 0)
                        {
                            var lastTime = workouts.ConsecutiveWeeks.Last();
                            var year = Convert.ToString(lastTime.MaxWeek).Substring(0, 4);
                            var weekOfYear = Convert.ToString(lastTime.MaxWeek).Substring(4, 2);
                            CultureInfo myCI = new CultureInfo("en-US");
                            Calendar cal = myCI.Calendar;

                            if (int.Parse(year) == DateTime.Now.Year)
                            {
                                var currentWeekOfYear = cal.GetWeekOfYear(DateTime.Now, CalendarWeekRule.FirstDay, DayOfWeek.Monday);
                                if (int.Parse(weekOfYear) >= currentWeekOfYear)
                                {
                                    levelUpBotModel.ChainCount = Convert.ToString(lastTime.ConsecutiveWeeks);
                                }

                                else if (int.Parse(weekOfYear) == currentWeekOfYear - 1)
                                {
                                    levelUpBotModel.ChainCount = Convert.ToString(lastTime.ConsecutiveWeeks);
                                }
                            }

                            stasts += $"\nWorkout week streak: {levelUpBotModel.ChainCount}";
                        }


                        try
                        {

                            bool IsInserted = false;
                            //await AddAnswer(AppResources.GotIt);
                            //Today workout
                            TimeSpan timeSpan;
                            String dayStr = "days";
                            int days = 0;
                            int hours = 0;
                            int minutes = 0;
                            timeSpan = new TimeSpan(days, hours, minutes);
                            CurrentLog.Instance.IsRest = false;
                            if (workouts.LastWorkoutDate != null)
                            {

                                days = (int)(DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime())
                                    .TotalDays;
                                hours = (int)(DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime())
                                    .TotalHours;
                                minutes = (int)(DateTime.Now -
                                                ((DateTime)workouts.LastWorkoutDate).ToLocalTime())
                                    .TotalMinutes;
                                if (days > 0)
                                    dayStr = days == 1 ? "day" : "days";
                                else if (hours > 0 && hours < 72)
                                    dayStr = hours <= 1 ? "hour" : "hours";
                                else if (minutes < 60)
                                    dayStr = minutes <= 1 ? "minute" : "minutes";

                                var d = 0;
                                if (days > 0)
                                    d = days;
                                else
                                {
                                    d = timeSpan.Days;
                                    //hours = (int)timeSpan.TotalHours;
                                    //minutes = (int)timeSpan.TotalMinutes;
                                    if (days > 0)
                                        dayStr = d == 1 ? "day" : "days";
                                    else if (hours > 0 && hours < 72)
                                        dayStr = hours <= 1 ? "hour" : "hours";
                                    else if (minutes < 60)
                                        dayStr = minutes <= 1 ? "minute" : "minutes";


                                }
                            }
                            else if (workouts.Averages.Count > 1)
                            {
                                timeSpan = DateTime.Now.ToLocalTime()
                                    .Subtract(workouts.Averages[0].Date.ToLocalTime());
                                days = timeSpan.Days;
                                dayStr = timeSpan.Days == 1 ? "day" : "days";
                            }

                            var lifted = new BotModel();
                            lifted.Type = BotType.Lifted;


                            if (workouts.LastConsecutiveWorkoutDays > 1 && workouts.LastWorkoutDate != null &&
                                (DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays <
                                2 && workouts != null && workouts.GetUserProgramInfoResponseModel != null &&
                                workouts.GetUserProgramInfoResponseModel.RecommendedProgram != null &&
                                workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate != null &&
                                workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.IsSystemExercise)
                            {
                                var RequiredHours = 18;
                                if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                        .Contains("bodyweight") ||
                                    workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                        .Contains("mobility") ||
                                    workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                        .Contains("powerlifting") ||
                                    workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                        .Contains("full-body") || workouts.GetUserProgramInfoResponseModel
                                        .RecommendedProgram.Label.ToLower().Contains("bands"))
                                {
                                    RequiredHours = 42;
                                }
                                else if
                                    (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                         .Contains("[home] push") ||
                                     workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                         .Contains("[home] pull") ||
                                     workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                         .Contains("[home] legs") ||
                                     workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                         .Contains("[gym] push") ||
                                     workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                         .Contains("[gym] pull") || workouts.GetUserProgramInfoResponseModel
                                         .RecommendedProgram.Label.ToLower().Contains("[gym] legs"))

                                {
                                    RequiredHours = 18;
                                    if (workouts.LastConsecutiveWorkoutDays > 5 &&
                                        workouts.LastWorkoutDate != null &&
                                        (DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime())
                                        .TotalDays < 3)
                                    {
                                        RequiredHours = 42;
                                    }
                                }
                                else if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                         .ToLower().Contains("split"))
                                {
                                    RequiredHours = 42;
                                }

                                if (hours < RequiredHours)
                                {
                                    lifted.StrengthTextColor = AppThemeConstants.DarkRedColor;
                                    var h = RequiredHours - hours <= 1 ? "hour" : "hours";
                                    lifted.TrainRest = "Rest";
                                    CurrentLog.Instance.IsRest = true;
                                    lifted.SinceTime = $"{hours} {h} since last workout (suggested recovery time: {RequiredHours} hours, so continue resting)"; ;
                                    lifted.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                                    //lifted.TrainRestText = $"More to recover".ToLower().FirstCharToUpper();
                                    lifted.TrainRestText =
                                        "Coach says"; // "Fatigued";//$"At least {RequiredHours - hours} {h} more to recover".ToLower().FirstCharToUpper();

                                }
                                else
                                {
                                    lifted.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                                    lifted.StrengthTextColor = AppThemeConstants.GreenColor;
                                    var h = RequiredHours - hours <= 1 ? "hour" : "hours";
                                    lifted.TrainRest = "Train";
                                    lifted.SinceTime = $"{hours} {h} since last workout (suggested recovery time: {RequiredHours} hours, so train now)"; ; //hours.ToString();
                                                                                                                                                           //lifted.TrainRestText = $"More to recover".ToLower().FirstCharToUpper();
                                    lifted.TrainRestText = $"Coach says";

                                }

                                //BotList.Add(restModel);
                                IsInserted = true;
                                // await AddQuestion($"{AppResources.YouHaveBeenWorkingOut} {workouts.LastConsecutiveWorkoutDays} {AppResources.DaysInARowISuggestTalkingADayOffAreYouSureYouWantToWorkOutToday} Up next is {workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}.");
                            }
                            else if (workouts.LastWorkoutDate != null)
                            {
                                if (days > 0 && hours >= 42)
                                {
                                    lifted.SinceTime = $"{days} {dayStr}, so train now";
                                    lifted.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                                    lifted.TrainRest = "Train";
                                    lifted.StrengthTextColor = AppThemeConstants.GreenColor;
                                    lifted.TrainRestText =
                                        "Coach says"; // "Recovered";// (days > 9 ? "I may recommend lighter weights" : "You should have recovered").ToLower().FirstCharToUpper();
                                                      //BotList.Add(restModel);
                                    IsInserted = true;
                                    //await AddQuestion(days > 9 ? $"{AppResources.YourLastWorkoutWas} {days} {dayStr} ago. I may recommend a light session. Start planned workout?" : $"Your last workout was {days} {dayStr} ago. You should have recovered. Up next is {workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}.");
                                }
                                else if (hours > 0)
                                {
                                    var RequiredHours = 18;
                                    if (workouts != null && workouts.GetUserProgramInfoResponseModel != null &&
                                        workouts.GetUserProgramInfoResponseModel.RecommendedProgram != null &&
                                        workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate != null &&
                                        workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate
                                            .IsSystemExercise)
                                    {
                                        if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("bodyweight") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("mobility") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("powerlifting") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("full-body") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("bands"))
                                        {
                                            RequiredHours = 42;
                                            if (!string.IsNullOrEmpty(LocalDBManager.Instance
                                                    .GetDBSetting("Age")?.Value))
                                            {
                                                if (int.Parse(
                                                        LocalDBManager.Instance.GetDBSetting("Age")?.Value) <
                                                    30)
                                                    RequiredHours = 18;
                                            }

                                            if (workouts.LastConsecutiveWorkoutDays > 1 &&
                                                workouts.LastWorkoutDate != null &&
                                                (DateTime.Now -
                                                 ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays <
                                                2)
                                                RequiredHours = 42;
                                        }
                                        else if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram
                                                 .Label.ToLower().Contains("split"))
                                        {
                                            RequiredHours = 18;
                                            if (!string.IsNullOrEmpty(LocalDBManager.Instance
                                                    .GetDBSetting("Age")?.Value))
                                            {
                                                if (int.Parse(
                                                        LocalDBManager.Instance.GetDBSetting("Age")?.Value) >
                                                    50)
                                                    RequiredHours = 42;
                                            }
                                        }
                                    }

                                    if (hours < RequiredHours)
                                    {

                                        lifted.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                                        lifted.StrengthTextColor = AppThemeConstants.DarkRedColor;
                                        var h = RequiredHours - hours <= 1 ? "hour" : "hours";
                                        lifted.TrainRest = "Rest";
                                        CurrentLog.Instance.IsRest = true;
                                        lifted.SinceTime =
                                            $"{hours} {h} since last workout (suggested recovery time: {RequiredHours} hours, so  continue resting)"; //$"{hours}/{RequiredHours} {h}"; //hours.ToString();
                                                                                                                                                      //lifted.TrainRestText = $"More to recover".ToLower().FirstCharToUpper();
                                        lifted.TrainRestText =
                                            "Coach says"; // "Fatigued";//$"At least {RequiredHours - hours} {h} more to recover".ToLower().FirstCharToUpper();

                                        //BotList.Add(restModel);
                                        IsInserted = true;
                                        //await AddQuestion($"Your last workout was {hours} {dayStr} ago. I'm not sure it makes sense to work out again now... Up next is {workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}.");
                                    }
                                    else
                                    {
                                        var h = hours <= 1 ? "hour" : "hours";
                                        lifted.SinceTime = $"{hours} {h} since last workout (suggested recovery time: {RequiredHours} hours, so train now)"; ;
                                        lifted.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                                        lifted.TrainRest = "Train";
                                        lifted.StrengthTextColor = AppThemeConstants.GreenColor;
                                        lifted.TrainRestText =
                                            "Coach says"; // "Recovered";// "You should have recovered".ToLower().FirstCharToUpper();
                                                          //BotList.Add(restModel);
                                        IsInserted = true;
                                        // await AddQuestion($"Your last workout was {hours} {dayStr} ago. You should have recovered. Up next is {workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}.");
                                    }

                                }
                                else
                                {
                                    var RequiredHours = 18;
                                    if (workouts != null && workouts.GetUserProgramInfoResponseModel != null &&
                                        workouts.GetUserProgramInfoResponseModel.RecommendedProgram != null &&
                                        workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate != null &&
                                        workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate
                                            .IsSystemExercise)
                                    {
                                        if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("bodyweight") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("mobility") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("powerlifting") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("full-body") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("bands"))
                                        {
                                            RequiredHours = 42;
                                            if (!string.IsNullOrEmpty(LocalDBManager.Instance
                                                    .GetDBSetting("Age")?.Value))
                                            {
                                                if (int.Parse(
                                                        LocalDBManager.Instance.GetDBSetting("Age")?.Value) <
                                                    30)
                                                    RequiredHours = 18;
                                            }

                                            if (workouts.LastConsecutiveWorkoutDays > 1 &&
                                                workouts.LastWorkoutDate != null &&
                                                (DateTime.Now -
                                                 ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays <
                                                2)
                                                RequiredHours = 42;
                                        }
                                        else if
                                            (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                 .ToLower().Contains("[home] push") ||
                                             workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                 .ToLower().Contains("[home] pull") ||
                                             workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                 .ToLower().Contains("[home] legs") ||
                                             workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                 .ToLower().Contains("[gym] push") ||
                                             workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                 .ToLower().Contains("[gym] pull") ||
                                             workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                 .ToLower().Contains("[gym] legs"))
                                        {
                                            RequiredHours = 18;
                                            if (workouts.LastConsecutiveWorkoutDays > 5 &&
                                                workouts.LastWorkoutDate != null &&
                                                (DateTime.Now -
                                                 ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays <
                                                3)

                                            {
                                                RequiredHours = 42;
                                            }
                                        }
                                        else if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram
                                                 .Label.ToLower().Contains("split"))
                                        {
                                            RequiredHours = 18;
                                            if (!string.IsNullOrEmpty(LocalDBManager.Instance
                                                    .GetDBSetting("Age")?.Value))
                                            {
                                                if (int.Parse(
                                                        LocalDBManager.Instance.GetDBSetting("Age")?.Value) >
                                                    50)
                                                    RequiredHours = 42;
                                            }

                                        }
                                    }

                                    lifted.SinceTime = $"0/{RequiredHours} hours, so continue resting"; //minutes.ToString();
                                    lifted.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                                    lifted.TrainRest = "Rest";
                                    CurrentLog.Instance.IsRest = true;
                                    lifted.StrengthTextColor = AppThemeConstants.DarkRedColor;
                                    lifted.TrainRestText =
                                        "Coach says"; // "Fatigued";//$"{RequiredHours} hours more to recover".ToLower().FirstCharToUpper();

                                    //BotList.Add(restModel);
                                    IsInserted = true;
                                    //await AddQuestion($"Your last workout was {minutes} {dayStr} ago. I'm not sure it makes sense to work out again today... Up next is {workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}.");
                                }
                            }

                            if (!IsInserted)
                            {

                                if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
                                {
                                    var value = Convert.ToDecimal(
                                        LocalDBManager.Instance.GetDBSetting("BodyWeight").Value
                                            .ReplaceWithDot(),
                                        System.Globalization.CultureInfo.InvariantCulture);
                                    var weight1 = new MultiUnityWeight(value, "kg");
                                    lifted.LbsLifted = string.Format("{0:0.##}",
                                        LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg"
                                            ? weight1.Kg
                                            : weight1.Lb);
                                }
                                else
                                    lifted.LbsLifted = "N/A"; //_weightLifted;

                                lifted.LbsLiftedText = "Body weight";

                                lifted.SinceTime = "N/A";
                                lifted.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                                lifted.TrainRest = "Train";
                                lifted.StrengthTextColor = AppThemeConstants.GreenColor;
                                lifted.TrainRestText =
                                    "Coach says"; // "Recovered";// "No workout yet".ToLower().FirstCharToUpper();
                                                  //BotList.Add(restModel);
                            }

                            stasts += $"\nRecovery time: {lifted.SinceTime}";

                        }
                        catch (Exception ex)
                        {
                        }
                    }


                }
                if (workouts?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Exercises?.Count > 0)
                {
                    var count = workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Exercises.Count;
                    var exe = count > 1 ? "exercises" : "exercise";
                    stasts +=
                        $"\nNext workout:{workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}";
                    stasts +=
                        $" with {count} {exe} (estimated duration: {count * 8} min)";
                }


            }

        }
        catch (Exception ex)
        {

        }
        return stasts;
    }
    public decimal TruncateDecimal(decimal value, int precision)
    {
        decimal step = (decimal)Math.Pow(10, precision);
        decimal tmp = Math.Truncate(step * value);
        return tmp / step;
    }

    //private async void getUseeWeights()
    //{
    //    try
    //    {
    //        allWeights = await DrMuscleRestClient.Instance.GetUserWeights();
    //        var newWeightList = new List<UserWeight>();
    //        decimal latesFatValue = 0;
    //        //var count = 0;
    //        foreach (var item in allWeights)
    //        {
    //            if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
    //            {
    //                massUnitType.Text = "KG";
    //                item.Weight = Math.Round(new MultiUnityWeight(item.Weight, "kg").Kg, 1);
    //                item.Label = string.Format("{0:0.#}", item.Weight);

    //                item.TrendWeight = Math.Round(new MultiUnityWeight(item.TrendWeight ?? 0, "kg").Kg, 1);
    //                item.TrendWeightLabel = string.Format("{0:0.#}", item.TrendWeight ?? 0);
    //            }
    //            else
    //            {
    //                massUnitType.Text = "LBS";

    //                item.Weight = Math.Round(new MultiUnityWeight(item.Weight, "kg").Lb, 2);
    //                item.Label = string.Format("{0:0.#}", item.Weight);

    //                item.TrendWeight = Math.Round(new MultiUnityWeight(item.TrendWeight ?? 0, "kg").Lb, 1);
    //                item.TrendWeightLabel = string.Format("{0:0.#}", item.TrendWeight);
    //            }

    //            //item.Fat = count++;
    //            if (item?.Fat > 0)
    //            {
    //                if (latesFatValue == 0)
    //                    latesFatValue = item.Fat ?? 0;
    //                decimal fatMass = (decimal)(item.Weight * (item.Fat / 100)); // Calculate fat mass
    //                item.FFM = Math.Round(item.Weight - fatMass, 1);
    //                item.FFMLabel = string.Format("{0:0.#}", item.FFM);
    //            }
    //            else
    //            {
    //                item.FFMLabel = "";
    //            }


    //        }
    //        if (latesFatValue > 0)
    //            LocalDBManager.Instance.SetDBSetting("BodyFat", latesFatValue.ToString());
    //        ParseUserWeights();

    //    }
    //    catch (Exception ex)
    //    {

    //    }
    //}

    void ParseUserWeights()
    {

        var list = allWeights.GroupBy(x => x.CreatedDate.Year);
        source = new List<UserWeight>();
        UserWeightList = new ObservableCollection<UserWeight>();
        page = 1;
        foreach (var key in list)
        {
            source.Add(new UserWeight() { Label = key.Key.ToString(), Weight = 0 });

            foreach (var weight in key)
            {
                source.Add(weight);
            }

        }
        foreach (var item in source.Take(step))
        {
            UserWeightList.Add(item);
        }
        
        // Update UI with the parsed data
        Device.BeginInvokeOnMainThread( () =>
        {
            HistoryListView.ItemsSource = null;
            HistoryListView.ItemsSource = UserWeightList;
            HistoryListView.HeightRequest = (Device.RuntimePlatform == Device.Android)? 47 * UserWeightList.Count: 46 * UserWeightList.Count;
            
        });
    }


    private async void ScrollView_Scrolled(object sender, ScrolledEventArgs e)
    {
        try
        {
            var scrollView = (ScrollView)sender;
            if (!IsViewVisible)
                return;


            await Task.Factory.StartNew(async () =>
              {
                  MainThread.BeginInvokeOnMainThread(() =>
                  {
                      if(scrollView != null)
                      {
                          if (scrollView.ScrollY >= (scrollView.ContentSize.Height - scrollView.Height))
                          {
                              System.Diagnostics.Debug.WriteLine($"loading weight {DateTime.Now.Ticks}");
                              // Load more items when reaching the bottom
                              if (source?.Count > step * page)
                              {
                                  var newpage = source.Skip(step * page).Take(step); // This method means that the data of the step * page item is skipped and the data of another step is obtained.
                                  foreach (var item in newpage)
                                  {
                                      UserWeightList.Add(item);
                                  }
                                  HistoryListView.HeightRequest = (Device.RuntimePlatform == Device.Android) ? 47 * UserWeightList?.Count ?? 1 : 46 * UserWeightList?.Count ?? 1;
                                  page++;
                              }
                          }
                      }
                      
                  });
              });

        }
        catch (Exception ex)
        {

        }
        // Check if the user has scrolled to the bottom of the ScrollView
        
    }

    void OnCancelClicked(object sender, System.EventArgs e)
    {
        StackLayout s = ((StackLayout)((Button)sender).Parent);

        if (s.Children.Count == 4)
        {
            SetVisibility(s.Children[0], false);
            SetVisibility(s.Children[1], false);
            SetVisibility(s.Children[2], false);
            SetVisibility(s.Children[3], true);
        }
        else
        {
            SetVisibility(s.Children[0], false);
            SetVisibility(s.Children[1], false);
            SetVisibility(s.Children[2], true);
        }
    }

    void OnContextMenuClicked(object sender, System.EventArgs e)
    {
        hideButtons();
        StackLayout s = ((StackLayout)((Button)sender).Parent);
        contextMenu = s;
        Device.BeginInvokeOnMainThread(() => {
            SetVisibility(s.Children[0], true);
            SetVisibility(s.Children[1], true);
            SetVisibility(s.Children[2], false);
        });


    }
    void SetVisibility(IView view, bool isVisible)
    {
        if (view is View mauiView)
        {
            mauiView.IsVisible = isVisible;
        }
    }
    private void HistoryListViewOnItemSelected(object sender, SelectedItemChangedEventArgs e)
    {
        hideButtons();
    }

    private void hideButtons()
    {
        if (contextMenu != null)
        {
            try
            {
                SetVisibility(contextMenu.Children[0], false);
                SetVisibility(contextMenu.Children[1], false);
                SetVisibility(contextMenu.Children[2], true);

            }
            catch (Exception exception)
            {


            }
        }
    }
    public async void OnDeleteWeightClicked(object sender, EventArgs e)
    {
        var mi = ((Button)sender);
        UserWeight m = (UserWeight)mi.CommandParameter;
        OnCancelClicked(sender, e);

        if (allWeights.Count == 1)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     Message = "You can't remove all the weights",
            //     Title = "Error"
            // });

            await HelperClass.DisplayCustomPopupForResult("Error",
                       "You can't remove all the weights","Ok","");
            return;
        }

      var r = await HelperClass.DisplayCustomPopupForResult("Are you sure?","",AppResources.Delete,AppResources.Cancel);
                       

                

        // var r = await UserDialogs.Instance.ConfirmAsync(new ConfirmConfig()
        // {
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     Title = "Are you sure?",
        //     CancelText = AppResources.Cancel,
        //     OkText = AppResources.Delete
        // });
        if (r == Views.PopupAction.OK)
        {
            var result = await DrMuscleRestClient.Instance.DeleteUserWeightHistory(m);

            //allWeights.Remove(m);
            var data = LocalDBManager.Instance.GetDBSetting("OverallWeights")?.Value;
            if (data != null)
            {
                var weightList = JsonConvert.DeserializeObject<List<UserWeight>>(data);
                // Filter the list to keep only records with matching IDs
                var filteredList = weightList.Where(item => item.Id != m.Id).ToList();
                LocalDBManager.Instance.SetDBSetting("OverallWeights", JsonConvert.SerializeObject(filteredList));
                ((App)Application.Current).weightContext.Weights = filteredList;
                ((App)Application.Current).weightContext.SaveContexts();
                if (filteredList?.Count > 0)
                {
                    LocalDBManager.Instance.SetDBSetting("BodyWeight", filteredList[0].Weight.ToString().ReplaceWithDot());
                }
            }
            Device.BeginInvokeOnMainThread(async () =>
            {
                //HistoryListView.IsVisible = false;
                //HistoryListView.ItemsSource = null;
                await UpdateTargetIntake();
                await LoadSavedWeightFromServer();
                LoadAnalysis();
            });
        }
    }

    private async Task UpdateTargetIntake()
    {
        var userWeight = await DrMuscleRestClient.Instance.GetTargetIntakeListWithoutLoader();
        if (userWeight != null)
        {
            if (userWeight.LastTargetIntake != null)
                LocalDBManager.Instance.SetDBSetting("LastTargetIntake", userWeight.LastTargetIntake.ToString().ReplaceWithDot());
            if (userWeight.TargetIntake != null)
                LocalDBManager.Instance.SetDBSetting("TargetIntake", userWeight.TargetIntake.ToString().ReplaceWithDot());

        }
    }
    public async void OnEditClicked(object sender, EventArgs e)
    {
        try
        {
            var mi = ((Button)sender);
            UserWeight m = (UserWeight)mi.CommandParameter;
            bool IsLastestWeight = false;
            if (lastWeight == m)
            {
                IsLastestWeight = true;
            }
            var modalPage1 = new BodyProgressPopup(m, IsLastestWeight);
            modalPage1.ActionSelected += async (sender1, action) =>
            {
                if (action == BodyProgressPopup.PopupAction.OK)
                {
                    //Device.BeginInvokeOnMainThread(async () =>
                    //{
                    //HistoryListView.IsVisible = false;
                    //HistoryListView.ItemsSource = null;
                    var weightList = await DrMuscleRestClient.Instance.GetUserWeights();
                    if (weightList == null)
                        return;
                    LocalDBManager.Instance.SetDBSetting("OverallWeights", JsonConvert.SerializeObject(weightList));
                    ((App)Application.Current).weightContext.Weights = weightList;
                    ((App)Application.Current).weightContext.SaveContexts();
                    if (IsLastestWeight)
                    {
                        App.IsWeightChangeFromOtherScreen = false;
                        App.IsMealPlanChange = false;
                    }
                    LocalDBManager.Instance.SetDBSetting("Macros", "");
                    var email = LocalDBManager.Instance.GetDBSetting("email")?.Value;
                    Preferences.Set($"Macros{email}", "");

                    UpdateTargetIntake();
                    await LoadSavedWeightFromServer();
                    LoadAnalysis();
                    //});
                }
                else if (action == BodyProgressPopup.PopupAction.Cancel)
                {
                    OnCancelClicked(sender, e);
                }
            };
            await this.ShowPopupAsync(modalPage1);

        }
        catch (Exception ex)
        {

        }


    }

    private async void SetsChart_Tapped(object sender, EventArgs e)
    {
        // UserDialogs.Instance.Alert(new AlertConfig()
        // {
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     Message = "Your volume in the last 3 weeks.",
        //     Title = "Work sets"
        // });

        await HelperClass.DisplayCustomPopupForResult("Work sets",
                       "Your volume in the last 3 weeks.","Ok","");
    }
    private string _formatter(double d)
    {
        if (IndexToDateLabel.TryGetValue(d, out var label))
        {
            return label; // Return the label if found
        }

        return "";
    }
    private string _formatter2(double d)
    {
        if (IndexToDateLabel2.TryGetValue(d, out var label))
        {
            return label; // Return the label if found
        }

        return "";
    }
    private string _formatter3(double d)
    {
        if (IndexToDateLabel3.TryGetValue(d, out var label))
        {
            return label; // Return the label if found
        }

        return "";
    }

    private void TapGestureRecognizer_OnTapped(object sender, EventArgs e)
    {
        try
        {
            SubscriptionPage subscriptionPage = new SubscriptionPage();
            subscriptionPage.OnBeforeShow();
            Navigation.PushAsync(subscriptionPage);
            //        PagesFactory.PushAsync<SubscriptionPage>();
        }
        catch (Exception ex)
        {

        }
    }

    private async void MoreTips_Clicked(object sender, EventArgs e)
    {
        try
        {
            ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
            await Task.Delay(300);
            MessagingCenter.Send<HelpWithGoalChatMessage>(new HelpWithGoalChatMessage(), "FullReportChatMessage");
        }
        catch (Exception ex)
        {
        }
    }

    private async void BtnShare_Clicked(object sender, EventArgs e)
    {
        await HelperClass.ShareApp("AI_Analysis", "Share_AI_Analysis", LblStrengthUpTextPart1.Text);
    }

    private async void ShareWeightProgress_Clicked(object sender, EventArgs e)
    {
        try
        {
            analysisCard.IsVisible = false;
            historyTable.IsVisible = false;
            EmptyStack.IsVisible = false; 
            await Task.Delay(200);
            var imageStream = await stackOptions.CaptureAsync();

            HelperClass.ShareImage(await imageStream.OpenReadAsync(), firebaseEventName: "share_weight_progress");
            analysisCard.IsVisible = true;
            historyTable.IsVisible = true;
            EmptyStack.IsVisible = true;
        }
        catch (Exception ex)
        {
            analysisCard.IsVisible = true;
            historyTable.IsVisible = true;
            EmptyStack.IsVisible = true;
        }
    }
    private async void SetupWeightTracker(List<UserWeight> userWeights)
    {
        try
        {

            if (userWeights == null)
                return;
            if (userWeights.Count == 0)
            {
                return;
            }
            bool isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ? true : false;
            decimal _userBodyWeight = 0;
            if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
            {
                _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
                Config.CurrentWeight = _userBodyWeight.ToString();
            }

            decimal _targetIntake = 0;
            if (LocalDBManager.Instance.GetDBSetting("TargetIntake")?.Value != null)
            {
                _targetIntake = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("TargetIntake").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
            }
            var startWeight = Convert.ToDecimal(userWeights.Last().Weight.ToString().ReplaceWithDot(), CultureInfo.InvariantCulture);
            if (userWeights.Count > 1)
                startWeight = Convert.ToDecimal(userWeights[1].Weight.ToString().ReplaceWithDot(), CultureInfo.InvariantCulture);
            var fromChangePoint = startWeight;
            var weights = userWeights.Where(x => x.CreatedDate > DateTime.Now.AddDays(-10)).ToList();

            var averageWeight = _userBodyWeight;
            var CurrentWeight = _userBodyWeight;
            //Adding code to get average weight 
            if (weights.Count() > 0)
            {
                //averageWeight = weights.Average(x => x.Weight);

            }



            decimal goalWeight = 0;

            if (LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value != null)
            {
                goalWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value.ReplaceWithDot(), CultureInfo.InvariantCulture);

            }
            else
            {
            }
            var togoOfGoal = "";
            if (isKg)
            {

                LblCurrentText2.Text = string.Format("{0:0.##} {1}", Math.Round(CurrentWeight, 2), "kg");

                LblGoalText2.Text = goalWeight == 0 ? "?" : string.Format("{0:0.##} {1}", Math.Round(goalWeight, 2), "kg");
                togoOfGoal = goalWeight == 0 ? "?" : string.Format("{0:0.##}", Math.Round(goalWeight, 2));
                LblStartText2.Text = string.Format("{0:0.##} {1}", Math.Round(userWeights.Last().Weight, 2), "kg");


            }
            else
            {

                var truncateWeight = TruncateDecimal(CurrentWeight, 3);
                var lbWeight = new MultiUnityWeight(truncateWeight, "kg").Lb;

                goalWeight = TruncateDecimal(goalWeight, 3);
                //var lbGoalWeight = new MultiUnityWeight(truncateGoalWeight, "kg").Lb;

                LblCurrentText2.Text = string.Format("{0:0.##} {1}", Math.Round(lbWeight, 2), "lbs");

                LblGoalText2.Text = goalWeight == 0 ? "?" : string.Format("{0:0.##} {1}", Math.Round(new MultiUnityWeight((decimal)goalWeight, "kg").Lb, 2), "lbs");

                togoOfGoal = goalWeight == 0 ? "?" : string.Format("{0:0.##}", Math.Round(new MultiUnityWeight((decimal)goalWeight, "kg").Lb, 2));
                LblStartText2.Text = string.Format("{0:0.##} {1}", Math.Round(userWeights.Last().Weight, 2), "lbs");
                //LblStartText2.Text = string.Format("{0:0.##} {1}", Math.Round(new MultiUnityWeight((decimal)userWeights.Last().Weight, "kg").Lb, 2), "lbs");
            }

            bool isGain = false;
            if (CurrentWeight < goalWeight)
            {
                isGain = true;

            }
            if (goalWeight != 0)
            {
                string Gender = LocalDBManager.Instance.GetDBSetting("gender").Value.Trim();
                var creationDate = new DateTime(Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("creation_date").Value));
                decimal weeks = 0;
                if (creationDate != null)
                {
                    weeks = (int)(DateTime.Now - creationDate).TotalDays / 7;
                }
                int lowReps = 0;
                int highreps = 0;
                try
                {
                    lowReps = int.Parse(LocalDBManager.Instance.GetDBSetting("repsminimum").Value);
                    highreps = int.Parse(LocalDBManager.Instance.GetDBSetting("repsmaximum").Value);
                }
                catch (Exception)
                {

                }
                var result = "";
                if (lowReps >= 5 && highreps <= 12)
                    result = "This helps you build muscle and strength.";
                else if (lowReps >= 8 && highreps <= 15)
                    result = "This helps you build muscle and burn fat.";
                else if (lowReps >= 5 && highreps <= 15)
                    result = "This helps you build muscle.";
                else if (lowReps >= 12 && highreps <= 20)
                    result = "This helps you burn fat.";
                else if (highreps >= 16)
                    result = "This helps you build muscle and burn fat.";
                else
                {
                    if (LocalDBManager.Instance.GetDBSetting("Demoreprange") != null)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "BuildMuscle")
                        {
                            result = "This helps you build muscle.";
                        }
                        else if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "BuildMuscleBurnFat")
                        {
                            result = "This helps you build muscle and burn fat.";
                        }
                        else if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "FatBurning")
                        {
                            result = "This helps you burn fat.";
                        }
                    }
                }
                decimal rate = (decimal)2.3;
                if (result.Contains("build muscle and burn fat"))
                {
                    rate = (decimal)2.4;
                }
                else if (result.Contains("build muscle"))
                {
                    rate = (decimal)2.3;
                }
                else if (result.Contains("burn fat"))
                {
                    rate = (decimal)2.4;
                }
                decimal gainDouble = 0;
                if (Gender == "Man")
                {
                    if (weeks <= 18)
                        gainDouble = ((decimal)0.015 - (decimal)0.000096899 * weeks) * averageWeight;
                    else if (weeks > 18 && weeks <= 42)
                        gainDouble = ((decimal)0.011101 - (decimal)0.000053368 * weeks) * averageWeight;
                    else if (weeks > 42)
                        gainDouble = (decimal)0.00188 * averageWeight;
                }
                else
                {
                    if (weeks <= 18)
                        gainDouble = (((decimal)0.015 - (decimal)0.000096899 * weeks) * averageWeight) / 2;
                    else if (weeks > 18 && weeks <= 42)
                        gainDouble = (((decimal)0.011101 - (decimal)0.000053368 * weeks) * averageWeight) / 2;
                    else if (weeks > 42)
                        gainDouble = ((decimal)0.00188 * averageWeight) / 2;
                }
                //Convert to day
                gainDouble = gainDouble / 30;


                decimal loseDouble = ((decimal)0.01429 * averageWeight) / 30;


                string gain = string.Format("{0:0.###}", isKg ? Math.Round(gainDouble, 3) : Math.Round(new MultiUnityWeight(gainDouble, WeightUnities.kg).Lb, 3));

                string lose = string.Format("{0:0.###}", isKg ? Math.Round(loseDouble, 3) : Math.Round(new MultiUnityWeight(loseDouble, WeightUnities.kg).Lb, 3));
                var weekText = weeks <= 1 ? "week" : "weeks";
                int days = 0;

                if (userWeights.Count > 1)
                {
                    //To calculate from start weight
                    //days = Math.Abs((int)(userWeights.Last().CreatedDate.Date - userWeights.First().CreatedDate.Date).TotalDays);
                    //To calculate from last change
                    days = Math.Abs((int)(userWeights[1].CreatedDate.Date - userWeights.First().CreatedDate.Date).TotalDays);
                }

                double totalChanged = 0;
                if (userWeights.Count > 1)
                    totalChanged = (double)(((userWeights.First().Weight - userWeights[1].Weight) * 100) / userWeights[1].Weight);
                double dailyChanged = (double)totalChanged;

                if (days != 0)
                    dailyChanged = totalChanged / days;
                bool isLess = false;
                if (days == 0)
                    days = 1;
                if (CurrentWeight > goalWeight)
                {
                    //Lose weight
                    if (Math.Round(CurrentWeight, 1) >= Math.Round(startWeight, 1))
                    {
                        isLess = true;
                    }
                    else
                    {
                        if (loseDouble > (Math.Abs((startWeight - CurrentWeight) / days)))
                            isLess = true;
                        else
                            isLess = false;
                    }
                }
                else
                {
                    //Gain
                    if (Math.Round(CurrentWeight, 1) <= Math.Round(startWeight, 1))
                    {
                        isLess = false;
                    }
                    else
                    {
                        if (gainDouble < (Math.Abs((startWeight - CurrentWeight) / days)))
                            isLess = true;
                        else
                            isLess = false;
                    }

                }

                var lessMoreText = "";

                if (CurrentWeight <= goalWeight)
                {
                    //Gain weight
                    if (isLess)
                    {
                        lessMoreText = "so you're probably gaining fat.";//$"You're probably gaining fat. Eat less (but aim for {Math.Round(CurrentWeight * rate)} g protein / day).";
                    }
                    else
                    {

                        lessMoreText = $"Eat more to reach your goal faster."; //$"You're probably leaving muscle on the table. Eat more (and aim for {Math.Round(CurrentWeight * rate)} g protein / day).";
                    }
                }
                else
                {
                    //lose weight
                    if (isLess)
                    {
                        //lessMoreText = $"you could speed that up by eating less. And aim to eat {Math.Round(CurrentWeight * rate)} g of protein a day.";
                        lessMoreText = "Eat less to reach your goal faster.";//$"To speed that up, eat less (but aim for {Math.Round(CurrentWeight * rate)} g protein / day).";

                    }
                    else
                    {
                        //lessMoreText = $"you're probably losing muscle mass too. Eat more to prevent that. And aim to eat {Math.Round(CurrentWeight * rate)} g of protein a day.";
                        lessMoreText = "so you're probably losing muscle mass.";//$"You're probably losing muscle mass. Eat more (and aim for {Math.Round(CurrentWeight * rate)} g protein / day).";
                    }
                }

                var goalGainWeight = string.Format("{0:0.##}", Math.Round(CurrentWeight * rate) / 1000, 2);


                var gainWeight = string.Format("{0:0.##}", Math.Abs(Math.Round(CurrentWeight - startWeight, 2)));
                var gainInaMonth = Math.Round(CurrentWeight - startWeight, 2) / days;
                var gainInaMonthText = string.Format("{0:0.##}", Math.Round(Math.Abs((CurrentWeight - startWeight)) / days, 2));
                var gainDiffernece = string.Format("{0:0.##}", Math.Abs(Math.Round(goalWeight - startWeight, 2)));
                var remainDiffernece = string.Format("{0:0.##}", Math.Abs(Math.Round(goalWeight - CurrentWeight, 2)));
                var massunit = "kg";
                if (!isKg)
                {
                    massunit = "lbs";
                    gainWeight = string.Format("{0:0.##}", Math.Abs(Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb - new MultiUnityWeight((decimal)startWeight, "kg").Lb, 2)));

                    gainDiffernece = string.Format("{0:0.##}", Math.Abs(Math.Round(new MultiUnityWeight((decimal)goalWeight, "kg").Lb - new MultiUnityWeight((decimal)startWeight, "kg").Lb, 2)));

                    remainDiffernece = string.Format("{0:0.##}", Math.Abs(Math.Round(new MultiUnityWeight((decimal)goalWeight, "kg").Lb - new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb, 2)));
                    goalGainWeight = string.Format("{0:0.##}", Math.Round(new MultiUnityWeight(CurrentWeight * rate, "kg").Lb / (decimal)453.59237, 2));

                    gainInaMonthText = string.Format("{0:0.##}", Math.Abs(Math.Round((new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb - new MultiUnityWeight((decimal)startWeight, "kg").Lb) / days, 2)));
                }

                //loseDouble and gainInaMonth // Check if https://app.startinfinity.com/b/7N8FXx54wWE/sDxhY6Rxbjh/974d2c5c-d3d3-4f73-910e-b10fb10f092c?t=comments&open=true&view=c956a7e0-0553-42e6-af24-7b6c915d3c44
                // +-10 should lose or gain about then
                decimal percentageDifference = 0;//
                bool isOnTrack = false;
                try
                {
                    if (CurrentWeight > goalWeight)
                    {
                        percentageDifference = ((Math.Round(Math.Abs(loseDouble), 2) - Math.Round(Math.Abs(gainInaMonth), 2)) / Math.Round(Math.Abs(gainInaMonth), 2)) * 100;
                        if (Math.Round(Math.Abs(percentageDifference)) <= 10)
                            isOnTrack = true;
                    }
                    else
                    {

                        percentageDifference = ((Math.Round(Math.Abs(gainDouble), 2) - Math.Round(Math.Abs(gainInaMonth), 2)) / Math.Round(Math.Abs(gainInaMonth), 2)) * 100;
                        if (Math.Round(Math.Abs(percentageDifference)) <= 10)
                            isOnTrack = true;
                    }

                }
                catch (Exception ex)
                {

                }
                var proteinNo = "";
                if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
                {
                    proteinNo = Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Kg * (decimal)1.6) + "-" + Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Kg * (decimal)2.2);
                }
                else
                {
                    proteinNo = Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb * (decimal)0.7) + "-" + Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb * (decimal)1.0);
                }

                double userWithdays = 0;
                try
                {
                    userWithdays = Math.Abs((int)(userWeights.Last().CreatedDate.Date - userWeights.First().CreatedDate.Date).TotalDays);
                }
                catch (Exception e)
                {
                }

                //  startWeight = Convert.ToDecimal(userWeights[1].Weight, CultureInfo.InvariantCulture);

                var usercreationDate = new DateTime(Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("creation_date").Value));
                if (usercreationDate != null)
                {
                    userWithdays = (int)(DateTime.Now - usercreationDate).TotalDays;
                }


                if (Math.Round(CurrentWeight, 1) == Math.Round(startWeight, 1) && Math.Round(CurrentWeight, 1) == Math.Round(goalWeight, 1))
                {
                    LblWeightToGo2.Text = string.Format("Success! {0} ??", LblGoalText2.Text);
                    //TODO: Remove below comments when enable AI chart card
                    //LblXXWeightLeft.Text = "Congratulations on reaching your goal!";
                }
                else if (Math.Round(CurrentWeight, 1) == Math.Round(goalWeight, 1))
                {
                    LblWeightToGo2.Text = string.Format("Success! {0} ??", LblGoalText2.Text);

                }
                else if (CurrentWeight < goalWeight)
                {
                    //TODO: Remove below comments when enable AI chart card
                    //Gain weight
                    //LblXXWeightLeft.Text = $"Your weight is {LblCurrentText1.Text}, with {remainDiffernece} {massunit} left to goal. How can I help? Ask me anything.";
                    if (Math.Round(CurrentWeight, 1) == Math.Round(startWeight, 1))
                    {
                        LblWeightToGo2.Text = $"{remainDiffernece} {massunit} to goal of {togoOfGoal}";
                    }
                    else if (CurrentWeight > startWeight)
                    {

                        LblWeightToGo2.Text = $"{remainDiffernece} {massunit} to goal of {togoOfGoal}";
                        //   LblWeightTipText2.Text = $"{remainDiffernece} {massunit} to go. Since you have been with us for {weeks} {weekText}, you should gain about {gain} {massunit} a month. Currently, you're gaining {gainInaMonthText} {massunit} a month. So, {lessMoreText}";
                    }
                    else if (CurrentWeight < startWeight)
                    {

                        LblWeightToGo2.Text = $"{remainDiffernece} {massunit} to goal of {togoOfGoal}";


                    }
                }
                else
                {
                    //Loose weight
                    //TODO: Remove below comments when enable AI chart card
                    //LblXXWeightLeft.Text = $"Your weight is {LblCurrentText1.Text}, with {remainDiffernece} {massunit} left to goal. How can I help? Ask me anything.";

                    if (Math.Round(CurrentWeight, 1) == Math.Round(startWeight, 1))
                    {
                        LblWeightToGo2.Text = $"{remainDiffernece} {massunit} to goal of {togoOfGoal}";

                    }
                    else if (CurrentWeight > startWeight)
                    {

                        LblWeightToGo2.Text = $"{remainDiffernece} {massunit} to goal of {togoOfGoal}";

                    }
                    else if (CurrentWeight < startWeight)
                    {
                        LblWeightToGo2.Text = $"{remainDiffernece} {massunit} to goal of {togoOfGoal}";
                    }

                }
            }
            else
            {
                int days = 0;
                if (userWeights.Count > 1)
                {
                    days = Math.Abs((int)(userWeights[1].CreatedDate.Date - userWeights.First().CreatedDate.Date).TotalDays);
                    startWeight = Convert.ToDecimal(userWeights[1].Weight.ToString().ReplaceWithDot(), CultureInfo.InvariantCulture);
                }


                if (days == 0)
                    days = 1;
                var gainWeight = string.Format("{0:0.##}", Math.Abs(Math.Round(CurrentWeight - startWeight, 2)));
                var gainInaMonth = Math.Round(CurrentWeight - startWeight, 2) / days * 30;

                var massunit = "kg";
                if (!isKg)
                {
                    massunit = "lbs";
                    gainWeight = string.Format("{0:0.##}", Math.Abs(Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb - new MultiUnityWeight((decimal)startWeight, "kg").Lb, 2)));

                }
                if (Math.Round(CurrentWeight, 1) == Math.Round(startWeight, 1))
                {
                    LblWeightToGo2.Text = "Your are at your starting weight";

                }
                else if (CurrentWeight > startWeight)
                {
                    LblWeightToGo2.Text = string.Format("{0} {1} gained in {2} {3}", gainWeight, massunit, days, days > 1 ? "days" : "day");
                }
                else if (CurrentWeight < startWeight)
                {
                    LblWeightToGo2.Text = string.Format("{0} {1} lost in {2} {3}", gainWeight, massunit, days, days > 1 ? "days" : "day");
                }
            }

            if (!string.IsNullOrEmpty(CurrentLog.Instance.CoachTipsText))
            {
                CurrentLog.Instance.CoachTipsText.Replace("You're losing ", "about ");
                CurrentLog.Instance.CoachTipsText.Replace("You're gaining ", "about ");
            }
            if (Math.Round(CurrentWeight, 1) == Math.Round(startWeight, 1))
            {
                LbltrackerText2.Text = $"Your are at your starting weight";
                LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Colors.Green;
                LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Colors.Green;
                LbltrackerText2.Text = LbltrackerText2.Text;
            }

            else if (Math.Round(CurrentWeight, 1) == Math.Round(goalWeight, 1))
            {
                if (isKg)
                    LbltrackerText2.Text =
                        string.Format("Success! {0:0.##} kg ??", Math.Round(CurrentWeight, 2));
                else
                    LbltrackerText2.Text = string.Format("Success! {0:0.##} lbs ??", Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb, 2));
                LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Colors.Green;
                LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Colors.Green;
                LbltrackerText2.Text = LbltrackerText2.Text;
            }
            else if (CurrentWeight > goalWeight && goalWeight > startWeight)
            {
                //Progress smoothly
                LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Colors.Green;
                LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Colors.Green;

                if (isKg)
                {
                    LbltrackerText2.Text = string.Format("Success! {0:0.##} kg above goal", Math.Round(CurrentWeight - goalWeight, 2));

                    LbltrackerText2.Text = LbltrackerText2.Text;
                }
                else
                {
                    LbltrackerText2.Text = string.Format("Success! {0:0.##} lbs above goal", Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb - new MultiUnityWeight((decimal)goalWeight, "kg").Lb, 2));
                    LbltrackerText2.Text = LbltrackerText2.Text;
                }
            }
            else if (CurrentWeight < goalWeight && goalWeight < startWeight)
            {
                //Progress smoothly
                LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Colors.Green;
                LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Colors.Green;

                if (isKg)
                {
                    LbltrackerText2.Text = string.Format("Success! {0:0.##} kg under goal", Math.Round(goalWeight - CurrentWeight, 2));
                    LbltrackerText2.Text = LbltrackerText2.Text;
                }
                else
                {
                    LbltrackerText2.Text = string.Format("Success! {0:0.##} lbs under goal", Math.Round(new MultiUnityWeight((decimal)goalWeight, "kg").Lb - new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb, 2));
                    LbltrackerText2.Text = LbltrackerText2.Text;
                }
            }
            else if (CurrentWeight > startWeight)
            {
                //Overweight
                if (goalWeight < CurrentWeight)
                {
                    LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Colors.Red;
                }
                else
                {
                    LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Colors.Green;
                }
                if (isKg)
                {
                    LbltrackerText2.Text = string.Format("You have gained {0:0.##} kg", Math.Round(CurrentWeight - startWeight, 2));
                }
                else
                {
                    LbltrackerText2.Text = string.Format("You have gained {0:0.##} lbs", Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb - new MultiUnityWeight((decimal)startWeight, "kg").Lb, 2));
                }
                LbltrackerText2.Text = LbltrackerText2.Text;
            }

            else if (CurrentWeight < startWeight)
            {
                //Low weight
                if (goalWeight < CurrentWeight)
                {
                    LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Colors.Green;
                }
                else
                {
                    LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Colors.Red;
                }
                if (isKg)
                {
                    LbltrackerText2.Text = string.Format("You have lost {0:0.##} kg", Math.Round(startWeight - CurrentWeight, 2));
                    LbltrackerText2.Text = LbltrackerText2.Text;
                }
                else
                {
                    LbltrackerText2.Text =
             string.Format("You have lost {0:0.##} lbs", Math.Round(new MultiUnityWeight((decimal)startWeight, "kg").Lb - new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb, 2));

                    LbltrackerText2.Text = LbltrackerText2.Text;
                }
                //if (!isGain)
                //{
                //    LbltrackerText1.TextColor = FrmTracker1.BackgroundColor = Color.Green;
                //    LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Color.Green;

                //}
            }


            else if (CurrentWeight == startWeight)
            {
                LbltrackerText2.Text = $"Your weight is stable";
                LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Colors.Green;
            }

        }
        catch (Exception ex)
        {

        }
    }


    async void btnUpdateGoal_Clicked(System.Object sender, System.EventArgs e)
    {

        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = "Try again"
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Try again","");
            return;
        }
        try
        {
            string massUnit = (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg") ? " kg" : " lbs";
            // if (Device.RuntimePlatform == Device.Android)
            {
                CustomPromptConfig customPromptConfig = new CustomPromptConfig("Update goal weight", "Tap to enter your goal weight", AppResources.Ok, "Cancel", "", Keyboard.Numeric,"",7);
                await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);
                if (customPromptConfig != null && customPromptConfig.Handler?.PlatformView != null)
                {
                    customPromptConfig.ActionSelected += async (sender1, action) =>
                    {
                        if (action == Views.PopupAction.OK)
                        {
                            PromptResult result = new PromptResult(true, customPromptConfig.text);
                            if (string.IsNullOrWhiteSpace(result.Text) || Convert.ToDecimal(result.Text.ReplaceWithDot(), CultureInfo.InvariantCulture) < 1)
                            {
                                return;
                            }
                            var weightText = result.Text.ReplaceWithDot();
                            decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);

                            LocalDBManager.Instance.SetDBSetting("WeightGoal", new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg.ToString().ReplaceWithDot());
                            var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal").Value.ReplaceWithDot(), System.Globalization.CultureInfo.InvariantCulture);
                            var weights = new MultiUnityWeight(value, "kg");
                            //LblBodyweight.Text = string.Format("{0:0.##}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? weights.Kg : weights.Lb);
                            await DrMuscleRestClient.Instance.SetUserWeightGoal(new UserInfosModel()
                            {
                                WeightGoal = new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value)
                            });

                            var userInfo = await DrMuscleRestClient.Instance.GetTargetIntake();
                            if (userInfo != null && userInfo.TargetIntake != null)
                                LocalDBManager.Instance.SetDBSetting("TargetIntake", userInfo.TargetIntake.ToString());
                            //getUseeWeights();
                            await UpdateTargetIntake();
                            decimal _userBodyWeight = 0;
                            if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
                            {
                                _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
                                if (_userBodyWeight < Convert.ToDecimal(weightText, CultureInfo.InvariantCulture))
                                {
                                    LocalDBManager.Instance.SetDBSetting("Macros", "");
                                    var email = LocalDBManager.Instance.GetDBSetting("email")?.Value;
                                    Preferences.Set($"Macros{email}", "");
                                }
                            }
                            //LblGoalText2.Text = weightText + massUnit;

                            await LoadSavedWeightFromServer();
                        }
                    };
                }
            }
            // else
            // {
            //     PromptConfig firsttimeExercisePopup = new PromptConfig()
            //     {
            //         InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
            //         IsCancellable = true,
            //         Title = "Update goal weight",
            //         MaxLength = 7,
            //         Placeholder = "Tap to enter your goal weight",
            //         OkText = AppResources.Ok,
            //         AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //         OnAction = async (weightResponse) =>
            //         {
            //             if (weightResponse.Ok)
            //             {
            //                 try
            //                 {


            //                     if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value.ReplaceWithDot(), CultureInfo.InvariantCulture) < 1)
            //                     {
            //                         return;
            //                     }
            //                     var weightText = weightResponse.Value.ReplaceWithDot();
            //                     decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);

            //                     LocalDBManager.Instance.SetDBSetting("WeightGoal", new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg.ToString().Replace(",", "."));
            //                     var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal").Value.ReplaceWithDot(), System.Globalization.CultureInfo.InvariantCulture);
            //                     var weights = new MultiUnityWeight(value, "kg");
            //                     //LblBodyweight.Text = string.Format("{0:0.##}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? weights.Kg : weights.Lb);
            //                     await DrMuscleRestClient.Instance.SetUserWeightGoal(new UserInfosModel()
            //                     {
            //                         WeightGoal = new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value)
            //                     });

            //                     var userInfo = await DrMuscleRestClient.Instance.GetTargetIntake();
            //                     if (userInfo.TargetIntake != null)
            //                         LocalDBManager.Instance.SetDBSetting("TargetIntake", userInfo.TargetIntake.ToString());
            //                     //getUseeWeights();
            //                     await UpdateTargetIntake();
            //                     //LblGoalText2.Text = weightText + massUnit;
            //                     decimal _userBodyWeight = 0;
            //                     if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
            //                     {
            //                         _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
            //                         if (_userBodyWeight < Convert.ToDecimal(weightText, CultureInfo.InvariantCulture))
            //                         {
            //                             LocalDBManager.Instance.SetDBSetting("Macros", "");
            //                             var email = LocalDBManager.Instance.GetDBSetting("email")?.Value;
            //                             Preferences.Set($"Macros{email}", "");
            //                         }
            //                     }
                                
            //                     LoadSavedWeightFromServer();
            //                 }
            //                 catch (Exception ex)
            //                 {

            //                 }
            //                 return;
            //             }
            //         }
            //     };

            //     //firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
            //     Acr.UserDialogs.UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            // }

            
        }
        catch (Exception ex)
        {

        }
    }
    private void InfoIcon_Tapped(object sender, EventArgs e)
    {
        MakePopup("Weight trend", "Daily deigh-ins can't differentiate between muscle, fat, water, or even your last meal. Unlike daily weigh-ins, a weight trend focuses on long-term changes in your weight.", "Got it", "Learn more", true, "https://dr-muscle.com/weight-trend-vs-scale-weight/", true);
    }
    private async Task MakePopup(string title, string message, string cancelTitle, string OkTitle, bool isLink = false, string linkUrl = "", bool isFromTable = false)
    {
       var supersetConfig = await HelperClass.DisplayCustomPopup(title,message,AppResources.GotIt,OkTitle);
                supersetConfig.ActionSelected += async (sender,action) => {

                        if (action == Views.PopupAction.OK)
                        {
                            if (isFromTable)
                                MakePopup("Fat-free mass (FFM)", "FFM is your body weight minus the fat. Changes usually reflect muscle gain (or loss).", "Got it", "Learn more", true, "https://dr-muscle.com/bulking-how-fast-muscle/");
                        }else{
                            if (isLink)
                            {
                                Browser.OpenAsync(linkUrl, BrowserLaunchMode.SystemPreferred);
                                //Device.OpenUri(new Uri(linkUrl));
                            }
                        }
                };

              

        // ConfirmConfig supersetConfig = new ConfirmConfig()
        // {
        //     Title = title,
        //     Message = message,
        //     OkText = AppResources.GotIt,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     CancelText = OkTitle,
        // };

        // var x = await Acr.UserDialogs.UserDialogs.Instance.ConfirmAsync(supersetConfig);
        // if (!x)
        // {
        //     if (isLink)
        //     {
        //         Browser.OpenAsync(linkUrl, BrowserLaunchMode.SystemPreferred);
        //         //Device.OpenUri(new Uri(linkUrl));
        //     }
        // }
        // else
        // {
        //     if (isFromTable)
        //         MakePopup("Fat-free mass (FFM)", "FFM is your body weight minus the fat. Changes usually reflect muscle gain (or loss).", "Got it", "Learn more", true, "https://dr-muscle.com/bulking-how-fast-muscle/");
        // }
    }
    private async Task MakePopupTrend(string title, string message, string cancelTitle, string OkTitle, bool isLink = false, string linkUrl = "")
    {
       var supersetConfig = await HelperClass.DisplayCustomPopup(title,message,AppResources.GotIt,OkTitle);
                supersetConfig.ActionSelected += async (sender,action) => {

                        if (action == Views.PopupAction.OK)
                        {
                            
                        }else{
                            if (isLink)
                            {
                                Browser.OpenAsync(linkUrl, BrowserLaunchMode.SystemPreferred);
                                //Device.OpenUri(new Uri(linkUrl));
                            }
                        }
                };

               
        // ConfirmConfig supersetConfig = new ConfirmConfig()
        // {
        //     Title = title,
        //     Message = message,
        //     OkText = AppResources.GotIt,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     CancelText = OkTitle,
        // };

        // var x = await Acr.UserDialogs.UserDialogs.Instance.ConfirmAsync(supersetConfig);
        // if (!x)
        // {
        //     if (isLink)
        //     {
        //         Browser.OpenAsync(linkUrl, BrowserLaunchMode.SystemPreferred);
        //         //Device.OpenUri(new Uri(linkUrl));
        //     }
        // }
    }

    private void InfoIcon_Tapped1(object sender, EventArgs e)
    {
        MakePopup("Fat-free mass (FFM)", "FFM is your body weight minus the fat. Changes usually reflect muscle gain (or loss).", "Got it", "Learn more", true, "https://dr-muscle.com/bulking-how-fast-muscle/", false);
    }

    private void InfoIcon_Tapped2(object sender, EventArgs e)
    {
        MakePopupTrend("Weight trend", "Daily deigh-ins can't differentiate between muscle, fat, water, or even your last meal. Unlike daily weigh-ins, a weight trend focuses on long-term changes in your weight.", "Got it", "Learn more", true, "https://dr-muscle.com/weight-trend-vs-scale-weight/");
    }
    public class YearlyUserWeight : List<UserWeight>
    {
        public string YearDate { get; set; }
    }

    public class HistoryWeightDataTemplateSelector : DataTemplateSelector
    {
        public DataTemplate HistoryDateTemplate { get; set; }
        public DataTemplate HistorySetTemplate { get; set; }


        protected override DataTemplate OnSelectTemplate(object item, BindableObject container)
        {
            var we = ((UserWeight)item);
            if (we.Weight == 0)
            {
                return HistoryDateTemplate;
            }

            return HistorySetTemplate;
        }
    }
}