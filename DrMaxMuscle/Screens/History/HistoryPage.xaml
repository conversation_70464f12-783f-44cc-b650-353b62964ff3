﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
xmlns:control="clr-namespace:DrMaxMuscle.Controls"
xmlns:helpers="clr-namespace:DrMaxMuscle.Helpers"
xmlns:effects="clr-namespace:DrMaxMuscle.Effects"
xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             xmlns:history="clr-namespace:DrMaxMuscle.Screens.History"
             x:Class="DrMaxMuscle.Screens.History.HistoryPage"
             Title="HistoryPage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <DataTemplate x:Key="historyDateTemplate"
                          x:Name="DateTemplate">
                <ViewCell Height="45">
                    <StackLayout Orientation="Horizontal"
             BackgroundColor="Transparent"
             HeightRequest="45"
             VerticalOptions="Center">
                        <!--                        <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">-->
                        <Label Margin="0,0,5,0"
           Opacity="1"
           Text="{Binding Label}"
           Style="{StaticResource LabelStyle}"
           VerticalTextAlignment="Center"
           FontSize="16"
           FontAttributes="Bold"
           HorizontalOptions="StartAndExpand" />
                        <!--                        </StackLayout>-->
                        <StackLayout Orientation="Horizontal"
                 HorizontalOptions="EndAndExpand">
                            <t:DrMuscleButton Clicked="OnCancelClicked"
                                              Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}"
                                              CommandParameter="{Binding .}"
                                              IsVisible="false"
                                              HorizontalOptions="EndAndExpand"
                                              Style="{StaticResource ItemContextCancelButton}" />
                            <t:DrMuscleButton Clicked="OnDeleteWorkoutLogClicked"
                                              Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}"
                                              CommandParameter="{Binding .}"
                                              IsVisible="false"
                                              HorizontalOptions="EndAndExpand"
                                              BackgroundColor="Red" BorderColor="Red" TextColor="White"
                                              Style="{StaticResource ItemContextDeleteButton}" />
                            <t:DrMuscleButton Clicked="OnContextMenuClicked"
                                              CommandParameter="{Binding .}"
                                              HorizontalOptions="EndAndExpand"
                                              
                                              Style="{StaticResource ItemContextMoreButton}" />
                        </StackLayout>
                    </StackLayout>
                </ViewCell>
            </DataTemplate>
            <DataTemplate x:Key="historyExerciseTemplate"
                          x:Name="ExerciseTemplate">
                <ViewCell Height="45">
                    <StackLayout Orientation="Horizontal"
                                 BackgroundColor="Transparent"
                                 HeightRequest="45"
                                 VerticalOptions="Center">
                        <!--                        <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">-->
                        <Label Margin="20,0,0,0"
                               Opacity="1"
                               Text="{Binding Label}"
                               Style="{StaticResource LabelStyle}"
                               HorizontalOptions="FillAndExpand"
                               VerticalTextAlignment="Center"
                               FontSize="16"/>
                        <!--                        </StackLayout>-->
                        <StackLayout Orientation="Horizontal"
                                     HorizontalOptions="End">
                            <t:DrMuscleButton Clicked="OnCancelClicked"
                                              Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}"
                                              CommandParameter="{Binding .}"
                                              IsVisible="false"
                                              HorizontalOptions="EndAndExpand"
                                              Style="{StaticResource ItemContextCancelButton}" />
                            <t:DrMuscleButton Clicked="OnDeleteWorkoutLogExerciseClicked"
                                              Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}"
                                              CommandParameter="{Binding .}"
                                              IsVisible="false"
                                              HorizontalOptions="EndAndExpand"
                                              BackgroundColor="Red" BorderColor="Red" TextColor="White"
                                              Style="{StaticResource ItemContextDeleteButton}" />
                            <t:DrMuscleButton Clicked="OnContextMenuClicked"
                                              CommandParameter="{Binding .}"
                                              HorizontalOptions="EndAndExpand"
                                              
                                              Style="{StaticResource ItemContextMoreButton}" />
                        </StackLayout>
                    </StackLayout>
                </ViewCell>
            </DataTemplate>
            <DataTemplate x:Key="historySetTemplate"
                          x:Name="SetTemplate">
                <ViewCell Height="45">
                    <StackLayout Orientation="Horizontal"
                                 BackgroundColor="Transparent"
                                 HeightRequest="45"
                                 VerticalOptions="Center">
                        <!--                        <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">-->
                        <Label Margin="40,0,0,0"
                               Text="{Binding Label}"
                               HorizontalOptions="StartAndExpand"
                               VerticalOptions="FillAndExpand"
                               Style="{StaticResource LabelStyle}"
                               VerticalTextAlignment="Center"
                               FontSize="16"/>
                        <!--                        </StackLayout>-->
                        <StackLayout Orientation="Horizontal"
                                     HorizontalOptions="End">
                            <t:DrMuscleButton Clicked="OnCancelClicked"
                                              Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}"
                                              CommandParameter="{Binding .}"
                                              IsVisible="false"
                                              HorizontalOptions="End"
                                              Style="{StaticResource ItemContextCancelButton}" />
                            <t:DrMuscleButton Clicked="OnEditClicked"
                                              Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}"
                                              CommandParameter="{Binding .}"
                                              IsVisible="false"
                                              HorizontalOptions="End"
                                              Style="{StaticResource ItemContextEditButton}" />
                            <t:DrMuscleButton Clicked="OnDeleteWorkoutLogSerieClicked"
                                              Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}"
                                              CommandParameter="{Binding .}"
                                              IsVisible="false"
                                              HorizontalOptions="End"
                                              BackgroundColor="Red" BorderColor="Red" TextColor="White"
                                              Style="{StaticResource ItemContextDeleteButton}" />
                            <t:DrMuscleButton Clicked="OnContextMenuClicked"
                                              CommandParameter="{Binding .}"
                                              HorizontalOptions="End"
                                              
                                              Style="{StaticResource ItemContextMoreButton}" />
                        </StackLayout>
                    </StackLayout>
                </ViewCell>
            </DataTemplate>
            <DataTemplate x:Key="historyStatisticTemplate"
                          x:Name="StatisticTemplate">
                <ViewCell Height="45">
                    <!--<StackLayout Orientation="Vertical" HorizontalOptions="FillAndExpand" BackgroundColor="Transparent" HeightRequest="40" VerticalOptions="Center">-->
                    <Label Margin="40,5,0,5"
                           BackgroundColor="Transparent"
                           Opacity="1"
                           Text="{Binding Label}"
                           VerticalOptions="FillAndExpand"
                           Style="{StaticResource LabelStyle}"
                           VerticalTextAlignment="Center"
                           FontSize="12" />
                    <!--</StackLayout>-->
                </ViewCell>
            </DataTemplate>
            <history:HistoryDataTemplateSelector x:Key="historyDataTemplateSelector"
                                                 HistoryDateTemplate="{StaticResource historyDateTemplate}"
                                                 HistoryExerciseTemplate="{StaticResource historyExerciseTemplate}"
                                                 HistorySetTemplate="{StaticResource historySetTemplate}">
            </history:HistoryDataTemplateSelector>
        </ResourceDictionary>
    </ContentPage.Resources>
    <Grid>
        <StackLayout HorizontalOptions="FillAndExpand"
                 Padding="20,0,20,10"
                                      >

            <StackLayout x:Name="LogsStack"
                     
                     VerticalOptions="FillAndExpand">
                <t:DrMuscleListView x:Name="HistoryListView"
                    VerticalOptions="FillAndExpand"
                    BackgroundColor="Transparent"
                    SeparatorVisibility="None"
                    RowHeight="45" />

            </StackLayout>
            <Label x:Name="LblTimeFrame"
               Margin="10,8"
               Style="{StaticResource BoldLabelStyle}" />
            <StackLayout x:Name="PickerStack"
                     VerticalOptions="End">
                <Border
                    BackgroundColor="{OnPlatform Android='Transparent'}" Style="{StaticResource GradientStackStyleBlue}"
                    HeightRequest="40"
                    Margin="10,0,10,10"
                    Padding="0">
                    <Border.StrokeShape>
                        <Rectangle/>
                    </Border.StrokeShape>
                    <control:DropDownPicker x:Name="ExericsesPicker"
                                            SelectedIndexChanged="ExericsesPicker_SelectedIndexChanged"
                                            Focused="ExericsesPicker_Focused"
                        BackgroundColor="Transparent"
                        Margin="5" TextColor="White"
                        Image="{OnPlatform Android='white_down_arrow.png', iOS='black_down_arrow.png'}"
                                          HeightRequest="40"
              
                    Style="{StaticResource PickerStyle}" >
                    </control:DropDownPicker>
                </Border>
                <Border
                    BackgroundColor="{OnPlatform Android='Transparent'}" Style="{StaticResource GradientStackStyleBlue}"
                    Margin="10,0,10,0" HeightRequest="40">
                    <Border.StrokeShape>
                        <Rectangle/>
                    </Border.StrokeShape>
                    <control:DropDownPicker x:Name="DatePicker" BackgroundColor="Transparent"
                                            SelectedIndexChanged="DatePicker_SelectedIndexChanged"
                            Margin="5"
                            HeightRequest="40"    TextColor="White"                                            
                            Image="{OnPlatform Android='white_down_arrow.png', iOS='black_down_arrow.png'}"
                            Style="{StaticResource PickerStyle}" >
                    </control:DropDownPicker>
                    
                </Border>
                
            </StackLayout>
            
        </StackLayout>
        <!--<Label x:Name="NoDataLabel"
                   HorizontalOptions="Center"
                   Margin="20,0"
                   VerticalOptions="Center"
                   VerticalTextAlignment="Start"
                   AbsoluteLayout.LayoutFlags="All"
                   AbsoluteLayout.LayoutBounds="0, 0.5, 1, 1"
                   IsVisible="false"
                   Style="{StaticResource LabelStyle}"
                   Text="This is the beginning of your workout history. Finish and save an exercise to get started. You got this!" />-->
        <StackLayout HorizontalOptions="FillAndExpand" x:Name="NoDataLabel" VerticalOptions="FillAndExpand" Grid.Row="0" BackgroundColor="#D4D4D4" Spacing="10" IsVisible="false">
            <StackLayout  VerticalOptions="CenterAndExpand" HorizontalOptions="Center"   Margin="0,30,0,0"  Spacing="10" Padding="0,45,0,2">

                <Image WidthRequest="150" HeightRequest="150" HorizontalOptions="Center" VerticalOptions="Start" Source="startrophy.png" />


                <Label Text="No history yet!" Margin="0,30,0,0" HorizontalOptions="Center"  FontSize="20" FontAttributes="Bold" TextColor="Black" />
                <Label Text="Save an exercise to see your history here." HorizontalOptions="Center"  HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>
            </StackLayout>
            <Frame
                x:Name="ToolTipButton"
            Padding="0"
            Margin="25,0,25,25"
            IsClippedToBounds="true"
            CornerRadius="0"
                 VerticalOptions="EndAndExpand"
                        HorizontalOptions="FillAndExpand" 
                        HeightRequest="66" 
                >
            <!--
                // uncomment code please
                effects:TooltipEffect.Text="Tap me"
                               effects:TooltipEffect.BackgroundColor="{x:Static app:AppThemeConstants.ReysBlueColor}"
                                effects:TooltipEffect.TextColor="White"
                                effects:TooltipEffect.Position="Top"
                                effects:TooltipEffect.HasTooltip="True"
                                effects:TooltipEffect.HasShowTooltip="False">-->


                <t:DrMuscleButton x:Name="ButtonStartWorkout" Text="Start workout"  BackgroundColor="#195377" HeightRequest="66" 
                        BorderColor="Transparent"
                                
                        TextColor="White" Style="{StaticResource buttonStyle}" HorizontalOptions="FillAndExpand"
                                ></t:DrMuscleButton>
            </Frame>

        </StackLayout>
    </Grid>
</ContentPage>