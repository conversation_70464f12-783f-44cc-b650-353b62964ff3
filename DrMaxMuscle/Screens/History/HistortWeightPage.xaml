﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
            xmlns:helpers="clr-namespace:DrMaxMuscle.Helpers"
             x:Class="DrMaxMuscle.Screens.History.HistortWeightPage"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             BackgroundColor="#f4f4f4"
              xmlns:oxy="clr-namespace:OxyPlot.Maui.Skia;assembly=OxyPlot.Maui.Skia"
             Title="HistortWeightPage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <DataTemplate x:Key="historySetTemplate"
                      x:Name="SetTemplate">
                <ContentView Height="45">
                    <StackLayout
                    Padding="0"
                    Margin="0"
                    Spacing="0">
                        <BoxView
                     HeightRequest="1"
                     HorizontalOptions="FillAndExpand"
                     BackgroundColor="LightGray" />
                        <StackLayout Orientation="Horizontal"
                             Padding="0"
                             BackgroundColor="Transparent"
                             HeightRequest="45"
                             HorizontalOptions="FillAndExpand"
                             VerticalOptions="Center">
                            <Grid
                        HorizontalOptions="FillAndExpand"
                        ColumnSpacing="0"
                                
                        >
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width=".17*" />
                                    <ColumnDefinition Width=".30*" />
                                    <ColumnDefinition Width=".24*" />
                                    <ColumnDefinition Width=".29*" />
                                </Grid.ColumnDefinitions>
                                <!--<StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">-->
                                <Label 
                            Grid.Column="0"
                            Margin="0"
                           Opacity="1"
                            Padding="0"
                            WidthRequest="50"
                           Text="{Binding CreatedDate, StringFormat='{0:MMM d}'}"
                           FontSize="14.5"
                            TextColor="#26262B"
                           VerticalTextAlignment="Center"
                           HorizontalTextAlignment="Center"
                           HorizontalOptions="FillAndExpand" />
                                <StackLayout Orientation="Horizontal"
                             Padding="0"
                             Grid.Column="1"
                             Margin="0"
                             Opacity="1"
                             Spacing="0"
                             HorizontalOptions="CenterAndExpand">
                                    <Label 
                                    Padding="0"
                                    Margin="0"
                                   Opacity="1"
                                   Text="{Binding Label}"
                                   FontSize="14.5"
                                   TextColor="#26262B"
                                   VerticalTextAlignment="Center"/>
                                    <Label 
                                    Padding="0"
                                    Margin="2,0,0,0"
                                    Opacity="1"
                                    Text="{Binding TrendWeightLabel , StringFormat='({0})'}"
                                    FontSize="14.5"
                                    TextColor="#26262B"
                                    VerticalTextAlignment="Center"/>
                                </StackLayout>
                                <StackLayout 
                                 Orientation="Horizontal"
                                 Padding="0"
                                 Grid.Column="2"
                                 Margin="0"
                                 Opacity="1"
                                 Spacing="0"
                                 HorizontalOptions="CenterAndExpand">
                                    <Label 
                                    Padding="0"
                                    Margin="0"
                                    Opacity="1"
                                    Text="{Binding Fat}"
                                    FontSize="14.5"
                                    TextColor="#26262B"
                                    VerticalTextAlignment="Center" />
                                    <Label 
                                    Padding="0"
                                    Margin="2,0,0,0"
                                    Opacity="1"
                                    FontSize="14.5"
                                    Text="{Binding TrendFatLabel}"
                                    TextColor="#26262B"
                                    VerticalTextAlignment="Center">

                                    </Label>
                                </StackLayout>
                                <StackLayout 
                                 Orientation="Horizontal"
                                 Padding="0"
                                 Grid.Column="3"
                                 Margin="0"
                                 Opacity="1"
                                 Spacing="0"
                                 HorizontalOptions="CenterAndExpand">
                                    <Label 
                                    Padding="0"
                                    Margin="0"
                                    Opacity="1"
                                    Text="{Binding FFMLabel}"
                                    FontSize="14.5"
                                    TextColor="#26262B"
                                    VerticalTextAlignment="Center" />
                                    <Label 
                                    Padding="0"
                                    Margin="2,0,0,0"
                                    Opacity="1"
                                    Text="{Binding TrendFFMLabel}"
                                    FontSize="14.5"
                                    TextColor="#26262B"
                                    VerticalTextAlignment="Center" >

                                    </Label>
                                </StackLayout>


                                <!--</StackLayout>-->
                            </Grid>
                            <StackLayout 
                        Orientation="Horizontal"
                        Padding="0"
                        Margin="0"
                                 HorizontalOptions="End">
                                <!-- <t:DrMuscleButton Clicked="OnCancelClicked" -->
                                <!--                   Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" -->
                                <!--                   CommandParameter="{Binding .}" -->
                                <!--                   IsVisible="false" -->
                                <!--                   HorizontalOptions="End" -->
                                <!--                   Style="{StaticResource ItemContextCancelButton}" /> -->

                                <t:DrMuscleButton Clicked="OnEditClicked"
                                          Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}"
                                          CommandParameter="{Binding .}"
                                          IsVisible="false"
                                          HorizontalOptions="End"
                                          Style="{StaticResource ItemContextEditButton}" />
                                <t:DrMuscleButton Clicked="OnDeleteWeightClicked"
                                          Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}"
                                          CommandParameter="{Binding .}"
                                          IsVisible="false"
                                          HorizontalOptions="End"
                                          BackgroundColor="Red" BorderColor="Red" TextColor="White"
                                          Style="{StaticResource ItemContextDeleteButton}" />
                                <t:DrMuscleButton Clicked="OnContextMenuClicked"
                                          CommandParameter="{Binding .}"
                                          HorizontalOptions="End"
                                          Padding="0"
                                          WidthRequest="30"
                                          Style="{StaticResource ItemContextMoreButton}" />
                            </StackLayout>

                        </StackLayout>
                        <!-- Horizontal border line  -->

                    </StackLayout>
                </ContentView>
            </DataTemplate>
            <DataTemplate x:Key="historyHeaderTemplate"
                      x:Name="SetHeaderTemplate">
                <ContentView Height="40">
                    <StackLayout
                     Padding="0"
                     Margin="0"
                     Spacing="0">

                        <BoxView
                     HeightRequest="1"
                     HorizontalOptions="FillAndExpand"
                     BackgroundColor="LightGray" />
                        <StackLayout Orientation="Horizontal"
                             BackgroundColor="#E6EFF5"
                             Padding="13,0"
                             HeightRequest="40"
                             VerticalOptions="Center">
                            <!--                        <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">-->
                            <Label Margin="0,0,5,0"
                           Opacity="1"
                           Text="{Binding Label}"
                           Style="{StaticResource LabelStyle}"
                           VerticalTextAlignment="Center"
                                   FontSize="15"
                                   FontAttributes="Bold"
                           HorizontalOptions="Center" />
                        </StackLayout>
                    </StackLayout>
                </ContentView>
            </DataTemplate>
        </ResourceDictionary>
    </ContentPage.Resources>
    <Grid>
        <ScrollView x:Name="scrollView">
            <StackLayout
    x:Name="stackOptions"
        VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
             Padding="0,0,0,10"
             Spacing="0">
                <StackLayout>
                    <controls:CustomFrame
                x:Name="WeightBox2"
                Margin="10,10,10,0"
                Padding="0,10,0,22"
                CornerRadius="12"
                HasShadow="True"
                IsClippedToBounds="True">

                        <StackLayout>
                            <!--<microcharts:ChartView
                        x:Name="chartViewWeight"
                        IsVisible="false"
                        HorizontalOptions="FillAndExpand"
                        HeightRequest="200" />-->
                            <StackLayout
                            Orientation="Horizontal"
                            Spacing="2"
                            VerticalOptions="Start"
                            HorizontalOptions="FillAndExpand"
                            Margin="0.5,0,10,5">
                                <Label
                                    Padding="0"
                                x:Name="LblWeightGoal2"
                                Margin="29,0,0,0"
                                HorizontalOptions="StartAndExpand"
                                VerticalOptions="Center"
                                LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                FontSize="20"
                                TextColor="Black"
                                FontAttributes="Bold"
                                Text="Body weight trend" />
                                <StackLayout
                                Padding="0"
                                HeightRequest="40"
                                WidthRequest="40"
                                VerticalOptions="Center"
                                HorizontalOptions="End"
                                Margin="0,-1,0,0">
                                    <Image
                                    Source="infoicon.png"
                                    Aspect="AspectFit"
                                    Margin="0,5,0,0"
                                    HeightRequest="25"
                                    VerticalOptions="CenterAndExpand"
                                    HorizontalOptions="CenterAndExpand"
                                    WidthRequest="25"
                                    
                                    >

                                    </Image>
                                    <StackLayout.GestureRecognizers>
                                        <TapGestureRecognizer Tapped="InfoIcon_Tapped2"/>
                                    </StackLayout.GestureRecognizers>
                                </StackLayout>
                            </StackLayout>
                           
                            <Grid HeightRequest="280">
                            <oxy:PlotView x:Name="plotView" VerticalOptions="StartAndExpand" HeightRequest="280" />
                                <Border Stroke="Transparent" BackgroundColor="Transparent" HeightRequest="280">
                                <Border.GestureRecognizers>
                                    <TapGestureRecognizer />
                                </Border.GestureRecognizers>
                            </Border>
                                </Grid>


                            <Label
                        Margin="29,9,20,0"
                        x:Name="WeightArrowText"
                        FontSize="17"
                        HorizontalOptions="Start"
                        HorizontalTextAlignment="Start"
                            Text="Body weight trend"
                        TextColor="#AA000000" />

                        </StackLayout>
                    </controls:CustomFrame>
                    <controls:CustomFrame
                x:Name="FatBox"
                Margin="10,10,10,0"
                Padding="0,10,0,22"
                CornerRadius="12"
                HasShadow="True"
                IsClippedToBounds="True">

                        <StackLayout>

                            <StackLayout
                                Orientation="Horizontal"
                                Spacing="2"
                                VerticalOptions="Start"
                                HorizontalOptions="FillAndExpand"
                                Margin="0.5,7.5,10,10">
                                <Label
                                    Padding="0"
                                    x:Name="LblFatGoal2"
                                    Margin="24,0,0,0"
                                    HorizontalOptions="StartAndExpand"
                                    VerticalOptions="Start"
                                    FontSize="20"
                                    TextColor="Black"
                                    FontAttributes="Bold"
                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                    Text="Body fat trend" />
                                <!--<Image
                              Source="infoicon.png"
                              Aspect="AspectFit"
                             Margin="0,0,0,0"
                              HeightRequest="25"
                              WidthRequest="25"
                             VerticalOptions="Center"
                              HorizontalOptions="End"
                               >
                                <Image.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="InfoIcon_Tapped1"/>
                                </Image.GestureRecognizers>
                            </Image>-->
                            </StackLayout>

                            <Grid x:Name="plotViewFatGrid" HeightRequest="280">
                                <oxy:PlotView x:Name="plotViewFat" VerticalOptions="StartAndExpand" HeightRequest="280" />
                                <Border Stroke="Transparent" BackgroundColor="Transparent" HeightRequest="280">
                                    <Border.GestureRecognizers>
                                        <TapGestureRecognizer />
                                    </Border.GestureRecognizers>
                                </Border>
                            </Grid>
                            <Label
                                Margin="24,9,20,0"
                                x:Name="FatArrowText"
                                FontSize="17"
                                HorizontalOptions="Start"
                                HorizontalTextAlignment="Start"
                                Text=""
                                TextColor="#AA000000" />

                            <StackLayout
                                x:Name="FatChartPremiumStack"
                                Margin="25,2,20,0"
                                HorizontalOptions="FillAndExpand"
                                IsVisible="False">
                                <Label
                                    Margin="1,0,0,0"
                                    Text="Premium feature."
                                    FontSize="17"
                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                                    TextColor="#AA000000">
                                </Label>
                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="0,14,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <Border
                                        Stroke="Transparent"
                                        StrokeShape="RoundRectangle 6,6,6,6"
                                        Padding="0"
                                        Grid.Column="1"
                                        Style="{StaticResource GradientBorderStyleBlue}"
                                        Margin="0"
                                        HeightRequest="45"
                                        VerticalOptions="Center"
                                        HorizontalOptions="FillAndExpand">
                                        <t:DrMuscleButton
                                            CornerRadius="6"
                                            VerticalOptions="Fill"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text="LEARN MORE"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            Clicked="TapGestureRecognizer_OnTapped"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                    </Border>

                                </Grid>

                            </StackLayout>

                        </StackLayout>
                    </controls:CustomFrame>
                    <controls:CustomFrame
                    x:Name="FFMBox"
                    Margin="10,10,10,0"
                    Padding="0,10,0,22"
                    CornerRadius="12"
                    HasShadow="True"
                    IsClippedToBounds="True">

                        <StackLayout>
                            <StackLayout
                            Orientation="Horizontal"
                            Spacing="2"
                            VerticalOptions="Start"
                            HorizontalOptions="FillAndExpand"
                            Margin="0.5,3,10,5">
                                <Label
                                    Padding="0"
                                x:Name="LblFFMGoal2"
                                Margin="20,0,0,0"
                                HorizontalOptions="StartAndExpand"
                                VerticalOptions="Center"
                                LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                FontSize="20"
                                TextColor="Black"
                                FontAttributes="Bold"
                                Text="Fat-free mass trend" />
                                <StackLayout
                                Padding="0"
                                HeightRequest="40"
                                WidthRequest="40"
                                VerticalOptions="Center"
                                HorizontalOptions="End"
                                Margin="0,-5,0,0">
                                    <Image
                                    Source="infoicon.png"
                                    Aspect="AspectFit"
                                    Margin="0,5,0,0"
                                    HeightRequest="25"
                                    VerticalOptions="CenterAndExpand"
                                    HorizontalOptions="CenterAndExpand"
                                    WidthRequest="25"
        
                                    >

                                    </Image>
                                    <StackLayout.GestureRecognizers>
                                        <TapGestureRecognizer Tapped="InfoIcon_Tapped1"/>
                                    </StackLayout.GestureRecognizers>
                                </StackLayout>
                            </StackLayout>
                            <Grid x:Name="plotViewFFMGrid" HeightRequest="280">
                                <oxy:PlotView x:Name="plotViewFFM" VerticalOptions="StartAndExpand" HeightRequest="280" />
                                <Border Stroke="Transparent" BackgroundColor="Transparent" HeightRequest="280">
                                    <Border.GestureRecognizers>
                                        <TapGestureRecognizer />
                                    </Border.GestureRecognizers>
                                </Border>
                            </Grid>
                            <Label
                                Margin="25,9,20,0"
                                x:Name="FFMArrowText"
                                FontSize="17"
                                HorizontalOptions="Start"
                                Text=""
                                HorizontalTextAlignment="Start"
                                TextColor="#AA000000" />

                            <StackLayout
                                x:Name="FFMFatChartPremiumStack"
                                Margin="25,2,20,0"
                                HorizontalOptions="FillAndExpand"
                                IsVisible="False">
                                <Label
                                    Margin="5,0,0,0"
                                    Text="Premium feature."
                                    FontSize="17"
                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                                    TextColor="#AA000000">
                                </Label>
                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="0,14,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <Border
                                        Stroke="Transparent"
                                        StrokeShape="RoundRectangle 6,6,6,6"
                                        Padding="0"
                                        Grid.Column="1"
                                        Style="{StaticResource GradientBorderStyleBlue}"
                                        Margin="0"
                                        HeightRequest="45"
                                        VerticalOptions="Center"
                                        HorizontalOptions="FillAndExpand">
                                        <t:DrMuscleButton
                                            CornerRadius="6"
                                            VerticalOptions="Fill"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text="LEARN MORE"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            Clicked="TapGestureRecognizer_OnTapped"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                    </Border>

                                </Grid>

                            </StackLayout>
                        </StackLayout>
                        
                        
                    </controls:CustomFrame>

                        <controls:CustomFrame
                    x:Name="WeightProgress2"
                    Margin="10,10,10,0"
                    Padding="10,10,10,10"
                    CornerRadius="12"
                    HasShadow="True">
                        <StackLayout>
                            <Grid
        RowSpacing="0"
        Margin="0,17,0,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition
                Height="Auto" />
                                    <RowDefinition
                Height="Auto" />
                                    <RowDefinition
                Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition
                Width="40" />
                                    <ColumnDefinition
                Width="*" />
                                </Grid.ColumnDefinitions>
                                <Image
            Grid.Column="0"
            VerticalOptions="Start"
            HorizontalOptions="Center"
            Margin="0,-8,0,0"
            Source="bodyweight.png"
            WidthRequest="27"
            HeightRequest="27" />
                                <StackLayout
            Grid.Column="1"
            Grid.Row="0">
                                    <Label
                x:Name="LblWeightToGo2"
                Text="Weight progress"
                TextColor="Black"
                FontAttributes="Bold"
                FontSize="19"
                Margin="0,-8,0,9" />
                                </StackLayout>

                                <Grid
            Grid.Row="1"
            Grid.Column="0"
            Grid.ColumnSpan="2"
            Margin="0,0"
            RowSpacing="0"
            BackgroundColor="White"
            ColumnSpacing="5">
                                    <Grid.RowDefinitions>
                                        <!--<RowDefinition Height="60" />-->
                                        <RowDefinition
                    Height="*" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition
                    Width="*" />
                                        <ColumnDefinition
                    Width="*" />
                                        <ColumnDefinition
                    Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <!--Start weight-->
                                    <Frame
                CornerRadius="6"
                Grid.Row="0"
                Grid.Column="0"
                HeightRequest="75"
                                        BorderColor="Transparent"
                BackgroundColor="White"
                HasShadow="False"
                Padding="0">
                                        <StackLayout
                    VerticalOptions="Center"
                    HorizontalOptions="Center">
                                            <Label
                        x:Name="LblStartText2"
                                                FontSize="17"
FontAttributes="Bold"
                        Style="{StaticResource LabelStyle}"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        TextColor="Black" />
                                            <Label
                        x:Name="LblStartWeight2"
                        Text="Start weight"
                        FontSize="15"
                        LineHeight="{OnPlatform Android='1.1',iOS='1'}" 
                        TextColor="#AA000000"
                        HorizontalOptions="CenterAndExpand"
                        HorizontalTextAlignment="Center" />
                                        </StackLayout>
                                    </Frame>

                                    <!--Current weight-->
                                    <Frame
                CornerRadius="6"
                Grid.Row="0"
                Grid.Column="1"
                HeightRequest="75"
                BackgroundColor="White"
                                        BorderColor="Transparent"
                HasShadow="False"
                Padding="0">
                                        <StackLayout
                    VerticalOptions="Center"
                    HorizontalOptions="Center">
                                            <Label
                        x:Name="LblCurrentText2"
                        FontSize="17"
FontAttributes="Bold"
                        Style="{StaticResource LabelStyle}"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        TextColor="Black" />
                                            <Label
                        x:Name="LblCurrentWeight2"
                        Text="Current weight"
                        FontSize="15"
                        LineHeight="{OnPlatform Android='1.1',iOS='1.1'}" 
                        TextColor="#AA000000"
                        HorizontalOptions="CenterAndExpand"
                        HorizontalTextAlignment="Center" />
                                        </StackLayout>
                                    </Frame>


                                    <!--Goal weight-->
                                    <Frame
                CornerRadius="6"
                Grid.Row="0"
                Grid.Column="2"
                HeightRequest="75"
                HasShadow="False"
                BackgroundColor="White"
                                        BorderColor="Transparent"
                Padding="0">
                                        <StackLayout
                    VerticalOptions="Center"
                    HorizontalOptions="Center">
                                            <Label
                        x:Name="LblGoalText2"
                        FontSize="17"
FontAttributes="Bold"
                        Style="{StaticResource LabelStyle}"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        TextColor="Black" />
                                            <Label
                        x:Name="LblGoalWeight2"
                        Text="Goal weight"
                        FontSize="15"
                        LineHeight="{OnPlatform Android='1.1',iOS='1'}" 
                        TextColor="#AA000000"
                        HorizontalOptions="CenterAndExpand"
                        HorizontalTextAlignment="Center" />
                                        </StackLayout>
                                    </Frame>
                                    <!--Tracker-->
                                    <StackLayout
                Grid.Row="0"
                IsVisible="false"
                Grid.Column="0"
                Grid.ColumnSpan="3">
                                        <Label
                    HorizontalOptions="CenterAndExpand"
                    x:Name="LbltrackerText2"
                    FontSize="17" />
                                        <Frame
                    x:Name="FrmTracker2"
                    HasShadow="False"
                    Margin="20,0"
                    Padding="0"
                    HeightRequest="10"
                    CornerRadius="5" />
                                    </StackLayout>
                                </Grid>

                            </Grid>
                            <Grid
            HorizontalOptions="FillAndExpand"
            Margin="1,10,1,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width=".5*"/>
                                    <ColumnDefinition Width=".5*"/>
                                </Grid.ColumnDefinitions>
                                <t:DrMuscleButton
                Text="UPDATE GOAL"
                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                FontAttributes="Bold"
                Grid.Column="0"
                HorizontalOptions="Center"
                Clicked="btnUpdateGoal_Clicked"
                VerticalOptions="Center"
                Style="{StaticResource buttonLinkStyle}"
                TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                <Border
    Stroke="Transparent"
    StrokeShape="RoundRectangle 6,6,6,6"
    Padding="0"
                                    Grid.Column="1"
    Style="{StaticResource GradientBorderStyleBlue}"
    Margin="0"
    HeightRequest="45"
    VerticalOptions="Center"
    HorizontalOptions="FillAndExpand">
                                    <t:DrMuscleButton
                    CornerRadius="6"
                    VerticalOptions="Fill"
                    HeightRequest="45"
                    FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                    HorizontalOptions="FillAndExpand"
                    Text="SHARE"
                    Clicked="ShareWeightProgress_Clicked"
                    IsVisible="true"
                    Style="{StaticResource highEmphasisButtonStyle}"
                    BackgroundColor="Transparent"
                    BorderColor="Transparent"
                    TextColor="White" />
                                </Border>

                            </Grid>
                        </StackLayout>
                        </controls:CustomFrame>
                        <controls:CustomFrame
                    x:Name="analysisCard"
                    Margin="10,10,10,0"
                    Padding="0,10,10,10"
                    CornerRadius="12"
                    HasShadow="True">
                        <StackLayout
                        Padding="10,15,10,15">

                            <Grid
                            Margin="0,0,0,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition
                                    Height="*" />
                                    <RowDefinition
                                    Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition
                                    Width="40" />
                                    <ColumnDefinition
                                    Width="*" />
                                </Grid.ColumnDefinitions>

                                <Image
                                x:Name="iconImage"
                                Source="lamp.png"
                                Margin="{OnPlatform Android='0,-6,0,0', iOS='0,-8,0,0'}"
                                Grid.Row="0"
                                WidthRequest="27"
                                VerticalOptions="Start"
                                HeightRequest="27" />
                                <StackLayout
                                Grid.Column="1"
                                Grid.Row="0"
                                Grid.RowSpan="2">
                                    <Label
                                    x:Name="LblStrengthUpQuestion"
                                    Text=""
                                    Margin="0,-8,0,9"
                                    TextColor="Black"
                                    FontAttributes="Bold"
                                    FontSize="19" />
                                    <Label
                                    Margin="0,8,0,0"
                                    Text=""
                                    FontSize="17"
                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                                    TextColor="#AA000000">
                                        <Label.FormattedText>
                                            <FormattedString>
                                                <Span x:Name="LblStrengthUpTextPart1" Text="" LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"/>
                                                <Span
                                                x:Name="LblStrengthUpTextPart2"
                                                Text=""
                                                TextColor="{x:Static app:AppThemeConstants.BlueLightColor}" LineHeight="{OnPlatform Android='1.3',iOS='1.2'}">
                                                    <Span.GestureRecognizers>
                                                        <TapGestureRecognizer
                                                        Tapped="TapGestureRecognizer_OnTapped"></TapGestureRecognizer>
                                                    </Span.GestureRecognizers>
                                                </Span>
                                            </FormattedString>
                                        </Label.FormattedText>
                                    </Label>

                                    <Grid
                                        x:Name="AICardLearnMoreGrid"
                                        IsVisible="False"
                                        HorizontalOptions="FillAndExpand"
                                        Margin="0,14,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width=".5*"/>
                                            <ColumnDefinition Width=".5*"/>
                                        </Grid.ColumnDefinitions>
                                        <Border
                                            Stroke="Transparent"
                                            StrokeShape="RoundRectangle 6,6,6,6"
                                            Padding="0"
                                            Grid.Column="1"
                                            Style="{StaticResource GradientBorderStyleBlue}"
                                            Margin="0"
                                            HeightRequest="45"
                                            VerticalOptions="Center"
                                            HorizontalOptions="FillAndExpand">
                                            <t:DrMuscleButton
                                                CornerRadius="6"
                                                VerticalOptions="Fill"
                                                HeightRequest="45"
                                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                                HorizontalOptions="FillAndExpand"
                                                Text="LEARN MORE"
                                                IsVisible="true"
                                                Style="{StaticResource highEmphasisButtonStyle}"
                                                Clicked="TapGestureRecognizer_OnTapped"
                                                BackgroundColor="Transparent"
                                                BorderColor="Transparent"
                                                TextColor="White" />
                                        </Border>

                                    </Grid>

                                </StackLayout>
                            </Grid>
                            <Grid
                            x:Name="gridChatButtons"
                            HorizontalOptions="FillAndExpand"
                            Margin="1,20,-9,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width=".5*"/>
                                    <ColumnDefinition Width=".5*"/>
                                </Grid.ColumnDefinitions>
                                <t:DrMuscleButton
                                x:Name="BtnMoreTips"
                                FontSize="13"
                                FontAttributes="Bold"
                                Grid.Column="0"
                                HorizontalOptions="Center"
                                Text="MORE TIPS"
                                Clicked="MoreTips_Clicked"
                                Style="{StaticResource buttonLinkStyle}"
                                VerticalOptions="Center"
                                TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                <Border
Stroke="Transparent"
StrokeShape="RoundRectangle 6,6,6,6"
Padding="0"
                                    x:Name="BtnShare"
                                Grid.Column="1"
Style="{StaticResource GradientBorderStyleBlue}"
Margin="0"
HeightRequest="45"
VerticalOptions="Center"
HorizontalOptions="FillAndExpand">

                                    <t:DrMuscleButton
                                    VerticalOptions="Center"
                                    HeightRequest="45"
                                    FontSize="13"
                                    CornerRadius="6"
                                    HorizontalOptions="FillAndExpand"
                                    Text="SHARE"
                                    IsVisible="true"
                                    Style="{StaticResource highEmphasisButtonStyle}"
                                    BackgroundColor="Transparent"
                                    BorderColor="Transparent"
                                    TextColor="White"
                                    Clicked="BtnShare_Clicked"/>
                                </Border>

                            </Grid>
                        </StackLayout>
                        </controls:CustomFrame>
                        <controls:CustomFrame
                        x:Name="historyTable"
                        Margin="10,10,10,0"
                        Padding="0"
                        CornerRadius="12"
                        HasShadow="True">
                                        <StackLayout>

                                            <Grid
                        HeightRequest="45"
                        Margin="0,0,0,0"
                        ColumnSpacing="0"
                       Padding="0,13,0,9"
                        >
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width=".16*" />
                                                    <ColumnDefinition Width=".25*" />
                                                    <ColumnDefinition Width=".21*" />
                                                    <ColumnDefinition Width=".24*" />
                                                    <ColumnDefinition Width=".12*" />
                                                </Grid.ColumnDefinitions>


                                                <!--<StackLayout Orientation="Horizontal"
                          BackgroundColor="Transparent"
                          HeightRequest="45"
                              Margin="0,10,0,5"
                          Padding="20,0"
                          VerticalOptions="StartAndExpand">-->
                                                <!--                        <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">-->
                                                <Label 
                             Grid.Column="0"
                             Margin="0"
                            Padding="0"
                            Opacity="1"
                            Text="DATE"
                            Style="{StaticResource LabelStyle}"
                            VerticalTextAlignment="Center"
                            FontSize="16"
FontAttributes="Bold"
                            HorizontalTextAlignment="Center"
                            HorizontalOptions="FillAndExpand" />

                                                <Label 
                            Padding="0"
                             Grid.Column="1"
                             Margin="0"
                            Opacity="1"
                            x:Name="massUnitType"
                            Text=""
                            Style="{StaticResource LabelStyle}"
                            VerticalTextAlignment="Center"
                            FontSize="16"
FontAttributes="Bold"
                            HorizontalTextAlignment="Center"
                            HorizontalOptions="FillAndExpand" />
                                                <Label 
                            Padding="0"
                             Grid.Column="2"
                             Margin="0"
                            Opacity="1"
                            x:Name="fatLbl"
                            Text=""
                            Style="{StaticResource LabelStyle}"
                            VerticalTextAlignment="Center"
                            FontSize="16"
FontAttributes="Bold"
                            HorizontalTextAlignment="Center"
                            HorizontalOptions="FillAndExpand" />
                                                <Label 
                            Padding="0"
                             Grid.Column="3"
                             Margin="0"
                            Opacity="1"
                            x:Name="ffmLbl"
                            Text=""
                            Style="{StaticResource LabelStyle}"
                            VerticalTextAlignment="Center"
                            FontSize="16"
FontAttributes="Bold"
                            HorizontalTextAlignment="Center"
                            HorizontalOptions="FillAndExpand" />
                                                <Image
                             Grid.Column="4"
                            Margin="0,0,10,0"
                             Source="infoicon.png"
                             Aspect="AspectFit"
                             HeightRequest="25"
                             WidthRequest="25"
                             HorizontalOptions="Center"
                              >
                                                    <Image.GestureRecognizers>
                                                        <TapGestureRecognizer Tapped="InfoIcon_Tapped"/>
                                                    </Image.GestureRecognizers>
                                                </Image>
                                            </Grid>
                                            <!--</StackLayout>-->
                                            <CollectionView 
                                VerticalScrollBarVisibility="Never"
                        x:Name="HistoryListView"
                         VerticalOptions="Start"
                         BackgroundColor="Transparent"
                         Margin="0,0,0,0" >

                                            </CollectionView>
                                        </StackLayout>
                                    </controls:CustomFrame>
                                    <StackLayout
                    x:Name="EmptyStack"
                    HeightRequest="100">

                    </StackLayout>
                </StackLayout>



            </StackLayout>
        </ScrollView>

        <Frame
        Padding="0"
        Margin="20"
        IsClippedToBounds="true"
        CornerRadius="6"
        VerticalOptions="End"
        HorizontalOptions="FillAndExpand"
        HeightRequest="66"
        Style="{StaticResource GradientFrameStyleBlue}">
            <t:DrMuscleButton
            CornerRadius="6"
            VerticalOptions="Fill"
            HeightRequest="66"
            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
            HorizontalOptions="FillAndExpand"
            Text="CHECK IN"
            Style="{StaticResource highEmphasisButtonStyle}"
            BackgroundColor="Transparent"
            BorderColor="Transparent"
            TextColor="White"
            Clicked="CheckInClicked"/>
        </Frame>
    </Grid>
</ContentPage>