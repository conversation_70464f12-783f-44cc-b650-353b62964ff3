﻿using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;

namespace DrMaxMuscle.Screens.Exercises;

public partial class ExerciseVideoPage : ContentPage
{
	public ExerciseVideoPage()
	{
        try
        {

            InitializeComponent();
            this.ToolbarItems.Clear();
            if (Device.RuntimePlatform.Equals(Device.Android))
                NavigationPage.SetHasNavigationBar(this, false);
            try
            {
                CurrentLog.Instance.EndExerciseActivityPage = GetType();
                DependencyService.Get<IFirebase>().SetScreenName("exercise_video_page");
                string url = string.IsNullOrEmpty(CurrentLog.Instance.VideoExercise.VideoUrl) ? Constants.AppThemeConstants.ExerciseVideoLink : CurrentLog.Instance.VideoExercise.VideoUrl;

                webView.Source = url;
            }
            catch (Exception ex)
            {

            }
        }
        catch (Exception ex)
        {

        }
    }

   

    protected override void OnAppearing()
    {
        base.OnAppearing();
        try
        {
            MessagingCenter.Send(this, "AllowLandscape");
        }
        catch (Exception ex)
        {

        }
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        try
        {
            if (Device.RuntimePlatform.Equals(Device.iOS))
            {
                //if (DeviceDisplay.MainDisplayInfo.Orientation == DisplayOrientation.Landscape)
                //    DependencyService.Get<IOrientationService>().Portrait();
            }
            webView.Source = "https://dr-muscle.com";
            MessagingCenter.Send(this, "PreventLandscape"); //during page close setting back to portrait
        }
        catch (Exception ex)
        {

        }
    }

    void webView_Navigated(System.Object sender, WebNavigatedEventArgs e)
    {
    }
}
