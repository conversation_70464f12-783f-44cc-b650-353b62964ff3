﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="DrMaxMuscle.Screens.Exercises.AllExercisePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:CustomCell="clr-namespace:DrMaxMuscle.Cells"
    xmlns:constnats="clr-namespace:DrMaxMuscle.Constants"
    xmlns:converter="clr-namespace:DrMaxMuscle.Convertors"
    xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:helpers="clr-namespace:DrMaxMuscle.Helpers"
    xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
    xmlns:local="clr-namespace:DrMaxMuscle"
    xmlns:t="clr-namespace:DrMaxMuscle.Layout"
    Title="AllExercisePage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <helpers:NegateBooleanConverter x:Key="BooleanInverter" />
            <converter:IdToBodyPartConverter x:Key="IdToBodyConverter" />

        </ResourceDictionary>
    </ContentPage.Resources>
    <Grid Margin="20,0">
        <StackLayout VerticalOptions="FillAndExpand">

            <Border
                x:Name="SearchContainer"
                Margin="0,10,0,0"
                Padding="0"
                BackgroundColor="#F1f1f1">
                <StackLayout
                    BackgroundColor="#F1F1F1"
                    Orientation="Horizontal"
                    Spacing="0">
                    <Image
                        x:Name="ImgSearch"
                        Margin="10,0,2,0"
                        Aspect="AspectFit"
                        BackgroundColor="#f1f1f1"
                        HeightRequest="18"
                        Source="icon_search_gray.png"
                        VerticalOptions="Center"
                        WidthRequest="15" />
                    <t:DrMuscleEntry
                        x:Name="SearchEntry"
                        Margin="0"
                        BackgroundColor="#F1F1F1"
                        HeightRequest="40"
                        Focused="SearchEntry_Focused"
                        Unfocused="SearchEntry_Unfocused"
                        HorizontalOptions="FillAndExpand"
                        TextChanged="Handle_SearchTextChanged"
                        TextColor="Black" />
                    <Label
                        x:Name="BtnCancel"
                        Margin="0,0,7,0"
                        BackgroundColor="#F1F1F1"
                        IsVisible="false"
                        VerticalOptions="FillAndExpand"
                        VerticalTextAlignment="Center">
                        <Label.GestureRecognizers>
                            <TapGestureRecognizer Tapped="Handle_CancelTapped" />
                        </Label.GestureRecognizers>
                    </Label>
                </StackLayout>
            </Border>

            <!--<t:DrMuscleListView
                BackgroundColor="Transparent"
                x:Name="ExpandableList"
                SeparatorColor="White"
                SeparatorVisibility="None"
        ItemsSource="{Binding ExeList}"
                HasUnevenRows="True"
        IsGroupingEnabled="True">
            <t:DrMuscleListView.ItemTemplate>
                <DataTemplate>
                    <ViewCell Height="60" BindingContextChanged="OnBindingContextChanged">
                  <StackLayout Orientation="Horizontal" BackgroundColor="Transparent" Padding="25,0,0,0">
                        <Label Text="{Binding Label}" HorizontalOptions="StartAndExpand" FlowDirection="LeftToRight" HorizontalTextAlignment="Start" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" ></Label>
                        <StackLayout Orientation="Horizontal" HorizontalOptions="End">

                            <t:DrMuscleButton Clicked="OnVideo"  Text="{Binding [Video].Value, Mode=OneWay,  Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" ImageSource="play_dark_blue.png" ContentLayout="Top,0" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextVideoButton}" TextColor="#195377" BackgroundColor="Transparent" />
                            <t:DrMuscleButton Clicked="OnReset" CommandParameter="{Binding .}" ImageSource="more_dark_blue.png" TextColor="#195377" ContentLayout="Top,0"  Text="More" HorizontalOptions="End" VerticalOptions="Center" Margin="0,0,-3,0" Style="{StaticResource ItemContextVideoButton}"  BackgroundColor="Transparent"/>
                        </StackLayout>
                  </StackLayout>
                </ViewCell>
                </DataTemplate>
            </t:DrMuscleListView.ItemTemplate>
            <t:DrMuscleListView.GroupHeaderTemplate >
                <DataTemplate>
                    <ViewCell Height="70" ios:Cell.DefaultBackgroundColor="Transparent">
                        <StackLayout Orientation="Horizontal" Padding="0,5,0,5" HorizontalOptions="FillAndExpand" BackgroundColor="Transparent">
                            <ffimageloading:CachedImage Source="{Binding Id, Converter={StaticResource IdToBodyConverter}}" HeightRequest="60" WidthRequest="45" Aspect="AspectFit">
                                        </ffimageloading:CachedImage>
                            <Label Text="{Binding Name}"
                                   BackgroundColor="Transparent"
                                   TextColor="{x:Static constnats:AppThemeConstants.OffBlackColor}"
                                   HorizontalOptions="StartAndExpand"
                                   VerticalOptions="Center"
                                   HorizontalTextAlignment="Start"
                                   VerticalTextAlignment="Center" Style="{StaticResource BoldLabelStyle}"
                                 />
                            <Image x:Name="StateImage" PropertyChanged="StateImage_PropertyChanged"
                                   HorizontalOptions="End"
                                   VerticalOptions="CenterAndExpand"
                                   Margin="0,0,9,0"
                               Source="{Binding StateIcon}" WidthRequest="25" HeightRequest="32" Aspect="AspectFit" BackgroundColor="Transparent"
                               />
                            <StackLayout.GestureRecognizers>
                                <TapGestureRecognizer  NumberOfTapsRequired="1" CommandParameter="{Binding .}" Tapped="Section_Tapped"/>
                            </StackLayout.GestureRecognizers>
                        </StackLayout>
                    </ViewCell>
                </DataTemplate>
            </t:DrMuscleListView.GroupHeaderTemplate>
                <t:DrMuscleListView.Footer>
                            <BoxView HeightRequest="100" BackgroundColor="Transparent" />
                        </t:DrMuscleListView.Footer>
            </t:DrMuscleListView>-->
            <t:DrMuscleListView
                x:Name="ExpandableList"
                ios:ListView.SeparatorStyle="FullWidth"
                ios:ListView.GroupHeaderStyle="Grouped"
                BackgroundColor="Transparent"
                HasUnevenRows="True"
                IsGroupingEnabled="True"
                ItemsSource="{Binding ExeList}"
                Scrolled="ExerciseListView_Scrolled"
                SeparatorColor="#264457"
                SeparatorVisibility="Default">
                <t:DrMuscleListView.ItemTemplate>
                    <DataTemplate>
                        <ViewCell Height="60" BindingContextChanged="OnBindingContextChanged">
                            <StackLayout
                                Padding="0,0,0,0"
                                BackgroundColor="Transparent"
                                Orientation="Horizontal">
                                <Label
                                    HorizontalOptions="StartAndExpand"
                                    Style="{StaticResource LabelStyle}"
                                    Text="{Binding Label}"
                                    VerticalTextAlignment="Center" />
                                <StackLayout HorizontalOptions="End" Orientation="Horizontal">
                                    <t:DrMuscleButton
                                        BackgroundColor="Transparent"
                                        Clicked="OnVideo"
                                        CommandParameter="{Binding .}"
                                        ContentLayout="Top,0"
                                        HorizontalOptions="End"
                                        ImageSource="play_dark_blue.png"
                                        IsVisible="false"
                                        Style="{StaticResource ItemContextVideoButton}"
                                        Text="{Binding [Video].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}"
                                        TextColor="#195377"
                                        VerticalOptions="Center" />
                                    <t:DrMuscleButton
                                        Margin="0,0,-3,0"
                                        BackgroundColor="Transparent"
                                        Clicked="OnReset"
                                        CommandParameter="{Binding .}"
                                        ContentLayout="Top,0"
                                        HorizontalOptions="End"
                                        ImageSource="more_dark_blue.png"
                                        Style="{StaticResource ItemContextVideoButton}"
                                        Text="More"
                                        TextColor="#195377"
                                        VerticalOptions="Center" />

                                </StackLayout>
                            </StackLayout>
                        </ViewCell>
                    </DataTemplate>
                </t:DrMuscleListView.ItemTemplate>
                <t:DrMuscleListView.GroupHeaderTemplate>
                    <DataTemplate>
                        <ViewCell Height="70" ios:Cell.DefaultBackgroundColor="Transparent">
                            <StackLayout Orientation="Vertical" 
                                         BackgroundColor="Transparent"
                                         Padding="0">
                                <StackLayout
                                Padding="0,5,0,5"
                                BackgroundColor="Transparent"
                                HorizontalOptions="FillAndExpand"
                                Orientation="Horizontal">
                                    <ffimageloading:CachedImage
                                    Aspect="AspectFit"
                                    ErrorPlaceholder="backgroundblack.png"
                                    HeightRequest="60"
                                    Source="{Binding Id, Converter={StaticResource IdToBodyConverter}}"
                                    WidthRequest="45" />
                                    <Label
                                    BackgroundColor="Transparent"
                                    HorizontalOptions="StartAndExpand"
                                    Style="{StaticResource BoldLabelStyle}"
                                    Text="{Binding Name}"
                                    TextColor="{x:Static constnats:AppThemeConstants.OffBlackColor}"
                                    VerticalOptions="Center"
                                    VerticalTextAlignment="Center" />
                                    <Image
                                    x:Name="StateImage"
                                    Aspect="AspectFit"
                                    BackgroundColor="Transparent"
                                    HeightRequest="32"
                                    HorizontalOptions="End"
                                    PropertyChanged="StateImage_PropertyChanged"
                                    Source="{Binding StateIcon}"
                                    VerticalOptions="CenterAndExpand"
                                    WidthRequest="25" />
                                    <StackLayout.GestureRecognizers>
                                        <TapGestureRecognizer
                                        CommandParameter="{Binding .}"
                                        NumberOfTapsRequired="1"
                                        Tapped="Section_Tapped" />
                                    </StackLayout.GestureRecognizers>
                                </StackLayout>
                                <BoxView HeightRequest="1"
                                         HorizontalOptions="FillAndExpand"
                                         VerticalOptions="End"
                                         BackgroundColor="#264457">       
                                </BoxView>
                            </StackLayout>
                        </ViewCell>
                    </DataTemplate>
                </t:DrMuscleListView.GroupHeaderTemplate>
                <t:DrMuscleListView.Footer>
                    <StackLayout HeightRequest="100" BackgroundColor="Transparent" />
                </t:DrMuscleListView.Footer>
            </t:DrMuscleListView>
        </StackLayout>


        <Frame Padding="0"
               Margin="0,0,0,20"
               IsClippedToBounds="true"
               CornerRadius="6"
               VerticalOptions="End"
               HorizontalOptions="FillAndExpand"
               HeightRequest="68"
               BorderColor="Transparent"
               Style="{StaticResource GradientStackStyleBlue}">
            <t:DrMuscleButton CornerRadius="6"
                              VerticalOptions="End"
                              HeightRequest="68"
                              FontSize="{x:Static constnats:AppThemeConstants.CapitalTitleFontSize}"
                              HorizontalOptions="FillAndExpand"
                              Text="CREATE CUSTOM EXERCISE"
                              Style="{StaticResource highEmphasisButtonStyle}"
                              BackgroundColor="Transparent"
                              BorderColor="Transparent"
                              TextColor="White"
                              Clicked="NewExerciseTapped" />
        </Frame>

    </Grid>
</ContentPage>