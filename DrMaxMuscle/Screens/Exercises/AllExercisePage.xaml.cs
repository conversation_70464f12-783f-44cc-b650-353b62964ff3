﻿namespace DrMaxMuscle.Screens.Exercises;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Layout;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.Subscription;
using DrMaxMuscle.Screens.Workouts;
using DrMaxMuscle.Utility;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using Newtonsoft.Json;
using RGPopup.Maui.Exceptions;
using RGPopup.Maui.Services;
using Rollbar;

public partial class AllExercisePage : ContentPage
{
    public ObservableRangeCollection<BodyPartSection> ExeList { get; set; }
           = new ObservableRangeCollection<BodyPartSection>();
    private List<ExerciceModel> exercises;
    public ObservableCollection<ExerciceModel> exerciseItems = new ObservableCollection<ExerciceModel>();
    public ObservableCollection<ExerciceModel> exerciseItemsResult = new ObservableCollection<ExerciceModel>();


    public ObservableCollection<ExerciceModel> favouriteItems = new ObservableCollection<ExerciceModel>();


    public ObservableCollection<ExerciceModel> customItems = new ObservableCollection<ExerciceModel>();
    //public ObservableCollection<ExerciceModel> customItemsResult = new ObservableCollection<ExerciceModel>();

    AddUserExerciseModel newAddUserExercise;
    public AllExercisePage()
    {
        
        InitializeComponent();
        RefreshLocalized();
        ExpandableList.ItemTapped += ExerciseListView_ItemTapped;

        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
        if (LocalDBManager.Instance.GetDBSetting("ExerciseTypeList") == null)
            LocalDBManager.Instance.SetDBSetting("ExerciseTypeList", "0");

        //OnBeforeShow();
    }
    void RefreshLocalized()
    {
        Title = AppResources.Exercises;
        BtnCancel.Text = AppResources.Cancel;
        SearchEntry.Placeholder = AppResources.SearchExercises;
    }



    protected override async void OnAppearing()
    {
        base.OnAppearing();
        DependencyService.Get<IFirebase>().SetScreenName("all_exercise_page");
        if (CurrentLog.Instance.IsFavouriteUpdated || CurrentLog.Instance.IsExerciseDeleted)
            OnBeforeShow();
        if (Config.ShowExercisePopup == false)
        {
            if (App.IsExercisePopup)
                return;
            App.IsExercisePopup = true;

           var ShowPopUp = await HelperClass.DisplayCustomPopup("Exercises","Browse hundreds of exercises by body part (with video) or tap the plus button to add your own (see bottom).",
            AppResources.GotIt,AppResources.RemindMe);
                ShowPopUp.ActionSelected += async (sender,action) => {

                        if (action == Views.PopupAction.OK)
                        {
                          Config.ShowExercisePopup = true;
                        }
                        else
                        {
                            Config.ShowExercisePopup = false;
                        }
                };

                //await Task.Delay(100);
               
                
            // ConfirmConfig ShowPopUp = new ConfirmConfig()
            // {
            //     Title = "Exercises",
            //     Message = "Browse hundreds of exercises by body part (with video) or tap the plus button to add your own (see bottom).",
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = AppResources.GotIt,
            //     CancelText = AppResources.RemindMe,
            //     OnAction = async (bool ok) =>
            //     {
            //         if (ok)
            //         {
            //             Config.ShowExercisePopup = true;
            //         }
            //         else
            //         {
            //             Config.ShowExercisePopup = false;
            //         }
            //     }
            // };
            // await Task.Delay(100);
            // UserDialogs.Instance.Confirm(ShowPopUp);
        }
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
    }
    public async void OnBeforeShow()
    {
        
        if (LocalDBManager.Instance.GetDBSetting("ExerciseTypeList") == null)
            LocalDBManager.Instance.SetDBSetting("ExerciseTypeList", "0");

        try
        {
            CurrentLog.Instance.IsBodyPartUpdated = false;
            CurrentLog.Instance.IsFavouriteUpdated = false;
            // GetUserExerciseResponseModel itemsSource = await DrMuscleRestClient.Instance.GetUserExercise(LocalDBManager.Instance.GetDBSetting("email").Value);
            //
            //Loading exercises from json
            try
            {

                string jsonFileName = "Exercises.json";
                ExerciceModel exerciseList = new ExerciceModel();
                var assembly = typeof(KenkoChooseYourWorkoutExercisePage).GetType().Assembly;

                var stream = await FileSystem.OpenAppPackageFileAsync(jsonFileName);
                using (var reader = new System.IO.StreamReader(stream))
                {
                    var jsonString = reader.ReadToEnd();

                    //Converting JSON Array Objects into generic list    
                    var list = JsonConvert.DeserializeObject<List<DBExerciseModel>>(jsonString);
                    exercises = new List<ExerciceModel>();
                    foreach (var item in list)
                    {
                        exercises.Add(new ExerciceModel()
                        {
                            Id = item.Id,
                            Label = item.Label,
                            BodyPartId = item.BodyPartId,
                            EquipmentId = item.EquipmentId,
                            IsBodyweight = item.IsBodyweight,
                            IsEasy = item.IsEasy,
                            IsMedium = item.IsMedium,
                            IsPlate = item.EquipmentId == 3,
                            IsSystemExercise = true,
                            VideoUrl = item.VideoUrl,
                            IsTimeBased = item.IsTimeBased,
                            IsUnilateral = item.IsUnilateral,
                            IsFlexibility = item.IsFlexibility,
                            IsWeighted = item.IsWeighted,
                            IsOneHanded = item.IsOneHanded,
                            IsAssisted = item.IsAssisted
                        });
                    }
                    exercises = exercises.ToList();
                }

            }
            catch (Exception ex)
            {

            }
            //exercises = itemsSource.Exercises;

            if (exercises == null || exercises.Count == 0)
            {
                exercises = new List<ExerciceModel>();
                GetUserExerciseResponseModel itemsSource = await DrMuscleRestClient.Instance.GetUserExercise(LocalDBManager.Instance.GetDBSetting("email").Value);
                if (itemsSource != null && itemsSource.Exercises != null)
                {
                    exercises.AddRange(itemsSource.Exercises);
                }
            }
            List<ExerciceModel> exo;
            var undefined = exercises.Where(ex => ex.BodyPartId == 1).ToList();

            exo = exercises.Where(x => x.BodyPartId != 1).ToList();

            if (undefined.Count > 0)
                exo.AddRange(undefined);
            exercises = exo;

            await UpdateExerciseList();

            try
            {
                GetUserExerciseResponseModel itemsSource = await DrMuscleRestClient.Instance.GetCustomExerciseForUser(LocalDBManager.Instance.GetDBSetting("email").Value);
                

                if (itemsSource != null && itemsSource.Exercises != null)
                {
                    customItems.Clear();
                    var bPartSection = ExeList.Where(x => x.Id == 26).ToList();
                    if (bPartSection.Count > 0)
                    {
                        ExeList.Remove(bPartSection[0]);
                    }
                    foreach (var item in itemsSource.Exercises)
                    {
                        customItems.Add(item);
                        var bodyPartSection = ExeList.Where(x => x.Id == 26).ToList();
                        if (bodyPartSection.Count > 0)
                        {
                            BodyPartSection body = bodyPartSection[0];
                            if (body.Expanded)
                                body.Add(item);
                            body._bodyPart.Exercices.Add(item);
                            body.Exercises.Add(item);
                        }
                        else
                        {
                            var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(26), Id = 26 };
                            bodyPartShoulders.Exercices.Add(item);
                            BodyPartSection section = new BodyPartSection(bodyPartShoulders, false);
                            ExeList.Insert(0, section);
                            ExpandableList.ItemsSource = ExeList;
                        }
                    }
                }

            }
            catch (Exception ex)
            {

            }
            GetUserExerciseResponseModel itemsSources = await DrMuscleRestClient.Instance.GetFavoriteExercises();
            if (itemsSources != null && itemsSources.Exercises != null)
            {
                favouriteItems.Clear();
                var bPartSection = ExeList.Where(x => x.Id == 25).ToList();
                if (bPartSection.Count > 0)
                {
                    ExeList.Remove(bPartSection[0]);
                }
                foreach (var item in itemsSources.Exercises)
                {
                    favouriteItems.Add(item);
                    var bodyPartSection = ExeList.Where(x => x.Id == 25).ToList();
                    if (bodyPartSection.Count > 0)
                    {
                        BodyPartSection body = bodyPartSection[0];
                        if (body.Expanded)
                            body.Add(item);
                        body._bodyPart.Exercices.Add(item);
                        body.Exercises.Add(item);
                    }
                    else
                    {
                        var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(25), Id = 25 };
                        bodyPartShoulders.Exercices.Add(item);
                        BodyPartSection section = new BodyPartSection(bodyPartShoulders, false);
                        ExeList.Insert(0, section);
                        ExpandableList.ItemsSource = ExeList;
                    }
                }
            }

        }
        catch (Exception e)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                            AppResources.PleaseCheckInternetConnection,"Try again","");
        }

    }

    //public async Task RunNewExercise(ExerciceModel model)
    //{
    //  await RunExercise(model);
    //}

    async void OnAppearingViewUpdate()
    {
        if (CurrentLog.Instance.IsBodyPartUpdated)
        {
            CurrentLog.Instance.IsBodyPartUpdated = false;
            await UpdateExerciseList();
        }
        if (CurrentLog.Instance.IsFavouriteUpdated)
        {
            CurrentLog.Instance.IsExerciseDeleted = false;
            CurrentLog.Instance.IsBodyPartUpdated = false;
            OnAppearing();
        }
        if (CurrentLog.Instance.IsExerciseDeleted)
        {
            CurrentLog.Instance.IsExerciseDeleted = false;
            OnAppearing();
        }
    }

    void OnCancelClicked(object sender, System.EventArgs e)
    {
        //StackLayout s = ((StackLayout)((Button)sender).Parent);
        //s.Children[0].IsVisible = false;
        //s.Children[1].IsVisible = false;
        //s.Children[2].IsVisible = false;
        //s.Children[3].IsVisible = false;
        //s.Children[4].IsVisible = false;
        //s.Children[5].IsVisible = true;
    }

    void OnContextMenuClicked(object sender, System.EventArgs e)
    {
        StackLayout s = ((StackLayout)((Button)sender).Parent);
        var mi = ((Button)sender);
        ExerciceModel m = (ExerciceModel)mi.CommandParameter;
        if (!m.IsSystemExercise)
        {
            //s.Children[0].IsVisible = false;
            //s.Children[1].IsVisible = true;
            //s.Children[2].IsVisible = false;
            //s.Children[3].IsVisible = true;
            //s.Children[4].IsVisible = false;
            //s.Children[5].IsVisible = false;
        }
        else
        {
            //s.Children[0].IsVisible = true;
            //s.Children[1].IsVisible = false;
            //if (!string.IsNullOrEmpty(m.VideoUrl))
            //s.Children[2].IsVisible = true;
            //s.Children[3].IsVisible = true;
            //s.Children[4].IsVisible = false;
            //s.Children[5].IsVisible = false;
        }
    }

    public async void OnVideo(object sender, EventArgs e)
    {
        try
        {
            CurrentLog.Instance.VideoExercise = ((ExerciceModel)((Button)sender).CommandParameter);
            if (Device.RuntimePlatform.Equals(Device.iOS))
            {
                DependencyService.Get<IOrientationService>().Portrait();
                DependencyService.Get<IOrientationService>().Landscape();
            }
            await Navigation.PushAsync(new ExerciseVideoPage());
            OnCancelClicked(sender, e);
        }
        catch (Exception ex)
        {

        }
    }

    protected void Name_OnTextChanged(PromptTextChangedArgs obj)
    {

        try
        {
            if (!string.IsNullOrEmpty(obj?.Value))
            {
                if (obj.Value.Length == 1)
                    obj.Value = char.ToUpper(obj.Value[0]) + "";
                else if (obj.Value.Length > 1)
                    obj.Value = char.ToUpper(obj.Value[0]) + obj.Value.Substring(1);
            }
        }
        catch (Exception ex)
        {

        }
    }

    private async void ExerciseListView_ItemTapped(object sender, ItemTappedEventArgs e)
    {
        try
        {
            if (App.IsV1UserTrial || App.IsFreePlan)
            {
                if (((ExerciceModel)e.Item).Id != -1)
                {
                    //if (CurrentLog.Instance.SwapContext != null)
                    //{
                    //    var swapContext = CurrentLog.Instance.SwapContext;
                    //    swapContext.TargetExerciseId = ((ExerciceModel)e.Item).Id;
                    //    ExerciceModel model = ((ExerciceModel)e.Item);
                    //    swapContext.Label = model.Label;
                    //    swapContext.IsBodyweight = model.IsBodyweight;
                    //    swapContext.IsSystemExercise = model.IsSystemExercise;
                    //    swapContext.IsEasy = model.IsEasy;
                    //    swapContext.VideoUrl = model.VideoUrl;
                    //    ((App)Application.Current).SwapExericesContexts.Swaps.Add(swapContext);
                    //    ((App)Application.Current).SwapExericesContexts.SaveContexts();
                    //    Device.BeginInvokeOnMainThread(async () =>
                    //    {

                    //        await PagesFactory.PushAsync<ChooseYourWorkoutExercisePage>();
                    //        CurrentLog.Instance.SwapContext = null;
                    //    });
                    //    return;
                    //}
                    CurrentLog.Instance.IsFromExercise = true;

                    CurrentLog.Instance.CurrentExercise = (ExerciceModel)e.Item;
                    var kenko = new KenkoSingleExercisePage();
                    await Navigation.PushAsync(kenko);
                }
                else
                {
                    AddMyOwnExercise();
                }
            }
            else
            {
                try
                {
                    await Navigation.PushAsync(new SubscriptionPage());
                }
                catch (Exception ex)
                {

                }
            }
        }
        catch (Exception ex)
        {

        }
    }




    private async void AddMyOwnExercise()
    {
        try
        {
        CustomPromptConfig customPromptConfig = new CustomPromptConfig(AppResources.NewExercise, AppResources.TapHereToEnterName, AppResources.Create,
         AppResources.Cancel,AppResources.LetsNameYourNewExercise,Keyboard.Text,"");

            customPromptConfig.ActionSelected += async (sender1, action) =>
            {
                if (action == PopupAction.OK)
                {
                    PromptResult result = new PromptResult(true, customPromptConfig.text);
                    if (string.IsNullOrWhiteSpace(customPromptConfig.text))
                    {
                        return;
                    }
                    await Task.Delay(500);
                    AddExercisesAction(action,customPromptConfig.text);
                }
            };
            await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);
        }
            catch (Exception ex)
        {

        }
        // PromptConfig p = new PromptConfig()
        // {
        //     InputType = InputType.Name,
        //     IsCancellable = true,
        //     Title = AppResources.NewExercise,
        //     Message = AppResources.LetsNameYourNewExercise,
        //     Placeholder = AppResources.TapHereToEnterName,
        //     OkText = AppResources.Create,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = new Action<PromptResult>(AddExercisesAction)
        // };
        // p.OnTextChanged += Name_OnTextChanged;
        // UserDialogs.Instance.Prompt(p);
    }

    public async void OnRename(object sender, EventArgs e)
    {
        var mi = ((Button)sender);
        ExerciceModel m = (ExerciceModel)mi.CommandParameter;

        CustomPromptConfig customPromptConfig = new CustomPromptConfig(string.Format("{0} \"{1}\"", AppResources.RenameExercise, m.Label), AppResources.EnterNewName, AppResources.Rename,
         AppResources.Cancel,"",Keyboard.Default,"");

            customPromptConfig.ActionSelected += (sender1, action) =>
            {
                if (action == PopupAction.OK)
                {
                    if (string.IsNullOrWhiteSpace(customPromptConfig.text))
                    {
                        return;
                    }
                    RenameExercisesAction(m,customPromptConfig.text);
                }
            };
            await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);

        // PromptConfig p = new PromptConfig()
        // {
        //     InputType = InputType.Default,
        //     IsCancellable = true,
        //     Title = string.Format("{0} \"{1}\"", AppResources.RenameExercise, m.Label),
        //     Placeholder = AppResources.EnterNewName,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OkText = AppResources.Rename,
        //     OnAction = new Action<PromptResult>((PromptResult response) =>
        //     {
        //         if (response.Ok)
        //         {
        //             RenameExercisesAction(m, response.Text);
        //         }
        //     })
        // };
        // p.OnTextChanged += Name_OnTextChanged;
        // UserDialogs.Instance.Prompt(p);

    }

    public async void RenameExercisesAction( ExerciceModel model, string newLabel)
    {
        int itemIndex = exercises.IndexOf(model);
        model.Label = newLabel;
        BooleanModel result = await DrMuscleRestClient.Instance.RenameExercise(model);
        if (result.Result)
        {
            if (itemIndex >= 0)
            {
                exercises.RemoveAt(itemIndex);
                exercises.Insert(itemIndex, model);
                await UpdateExerciseList();
            }
        }
    }

    public async void DeleteExercisesAction(ExerciceModel model)
    {
        int itemIndex = exerciseItemsResult.IndexOf(model);

        try
        {
            ExerciceModel m = model;
            SwapExerciseContext sec = ((App)Application.Current).SwapExericesContexts.Swaps.First(s => s.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && s.TargetExerciseId == m.Id);
            if (((App)Application.Current).SwapExericesContexts.Swaps.Contains(sec))
            {
                ((App)Application.Current).SwapExericesContexts.Swaps.Remove(sec);
                ((App)Application.Current).SwapExericesContexts.SaveContexts();
            }
        }
        catch (Exception ex)
        {

        }




        BooleanModel result = await DrMuscleRestClient.Instance.DeleteExercise(model);
        if (result.Result)
        {
            exerciseItemsResult.RemoveAt(itemIndex);
            exerciseItems.Remove(model);
        }
    }

    public async void OnDelete(object sender, EventArgs e)
    {
        var mi = ((Button)sender);
        ExerciceModel m = (ExerciceModel)mi.CommandParameter;

        var ShowPopUp = await HelperClass.DisplayCustomPopup(AppResources.DeleteExercise,string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
            AppResources.Delete,AppResources.Cancel);
                ShowPopUp.ActionSelected += async (sender,action) => {

                        if (action == Views.PopupAction.OK)
                        {
                           DeleteExercisesAction(m);
                        }
                        
                };

               // await PopupNavigation.Instance.PushAsync(ShowPopUp);

        // ConfirmConfig p = new ConfirmConfig()
        // {
        //     Title = AppResources.DeleteExercise,
        //     Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
        //     OkText = AppResources.Delete,
        //     CancelText = AppResources.Cancel,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
        // };
        // p.OnAction = (obj) =>
        // {
        //     if (obj)
        //     {
        //         DeleteExercisesAction(m);
        //     }
        // };
        // UserDialogs.Instance.Confirm(p);
    }

    public async void ResetExercisesAction(ExerciceModel model)
    {
        BooleanModel result = await DrMuscleRestClient.Instance.ResetExercise(model);

        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + model.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + model.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());

    }

    public async void OnReset(object sender, EventArgs e)
    {
        try
        {
            var mi = ((Button)sender);
            ExerciceModel m = (ExerciceModel)mi.CommandParameter;
            CurrentLog.Instance.WorkoutTemplateCurrentExercise = m;
            if (m.IsSystemExercise)
                await Navigation.PushAsync(new ExerciseSettingsPage());
            else
            {
                CurrentLog.Instance.ExerciseSettingsPage = GetType();
                await Navigation.PushAsync(new ExerciseCustomSettingsPage());
            }
            OnCancelClicked(sender, e);
        }
        catch (Exception ex)
        {

        }
    }

    private void OnBindingContextChanged(object sender, EventArgs e)
    {
        base.OnBindingContextChanged();

        if (((BindableObject)sender).BindingContext == null)
            return;
        ExerciceModel m = (ExerciceModel)((BindableObject)sender).BindingContext;
        var btnVideo = (DrMuscleButton)((StackLayout)((StackLayout)((ViewCell)sender).View).Children[1]).Children[0];
        if (string.IsNullOrEmpty(m.VideoUrl))
            btnVideo.IsVisible = false;
        else
            btnVideo.IsVisible = true;
        if (m.IsSystemExercise)
        {

        }
    }

    void Section_Tapped(object sender, System.EventArgs e)
    {
        try
        {
            if (sender == null)
                return;

            
                var obj = (BodyPartSection)((StackLayout)sender).BindingContext;
                obj.Expanded = !obj.Expanded;
           
        }
        catch (Exception exception)
        {
            Console.WriteLine(exception);

        }

    }
    //
    void NewExerciseTapped(object sender, System.EventArgs e)
    {
        AddMyOwnExercise();
    }
    private async Task UpdateExerciseList()
    {
        try
        {
            if (exercises == null)
                return;
            exerciseItems.Clear();
            exerciseItemsResult.Clear();


            ExeList.Clear();
            List<ExerciceModel> exo;
            var undefined = exercises.Where(ex => ex.BodyPartId == 1).ToList();
            var neck = exercises.Where(ex => ex.BodyPartId == 10).ToList();
            var mobility = exercises.Where(ex => ex.IsFlexibility).ToList();
            exo = exercises.ToList();


            if (ExeList?.Count == 0)
            {
                //foreach (ExerciceModel em in exo)
                //exerciseItems.Add(em);
                var customExerAdded = false;
                foreach (var groupItem in exo.Except(mobility).GroupBy(x => x.BodyPartId).ToList())
                {
                    var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(groupItem.Key), Id = (int)groupItem.Key };
                    foreach (var item in groupItem.OrderBy(x => x.Label))
                    {
                        bodyPartShoulders.Exercices.Add(item);
                    }
                    if (groupItem.Key == 14)
                    {
                        var idArray = bodyPartShoulders.Exercices.Select(x => x.Id).ToArray();
                        System.Diagnostics.Debug.WriteLine(idArray);
                    }
                    BodyPartSection section = new BodyPartSection(bodyPartShoulders, false);
                    ExeList.Add(section);
                }


                ExpandableList.ItemsSource = ExeList.OrderBy(x => x.Name);
                foreach (var item in customItems.OrderBy(x => x.Label))
                {
                    var bodyPartSection = ExeList.Where(x => x.Id == 26).ToList();
                    if (bodyPartSection.Count > 0)
                    {
                        BodyPartSection body = bodyPartSection[0];
                        if (body.Expanded)
                            body.Add(item);
                        body._bodyPart.Exercices.Add(item);
                        body.Exercises.Add(item);
                    }
                    else
                    {
                        var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(26), Id = 26 };
                        bodyPartFavourite.Exercices.Add(item);
                        BodyPartSection sections = new BodyPartSection(bodyPartFavourite);
                        ExeList.Insert(0, sections);

                        ExpandableList.ItemsSource = ExeList;

                    }
                }

                foreach (var item in favouriteItems.OrderBy(x => x.Label))
                {
                    var bodyPartSection = ExeList.Where(x => x.Id == 25).ToList();
                    if (bodyPartSection.Count > 0)
                    {
                        BodyPartSection body = bodyPartSection[0];
                        if (body.Expanded)
                            body.Add(item);
                        body._bodyPart.Exercices.Add(item);
                        body.Exercises.Add(item);
                    }
                    else
                    {
                        var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(25), Id = 25 };
                        bodyPartFavourite.Exercices.Add(item);
                        BodyPartSection sections = new BodyPartSection(bodyPartFavourite);
                        ExeList.Insert(0, sections);

                        ExpandableList.ItemsSource = ExeList;

                    }
                }

                foreach (var item in mobility.OrderBy(x => x.Label))
                {
                    var bodyPartSection = ExeList.Where(x => x.Id == 28).ToList();
                    if (bodyPartSection.Count > 0)
                    {
                        BodyPartSection body = bodyPartSection[0];
                        if (body.Expanded)
                            body.Add(item);
                        body._bodyPart.Exercices.Add(item);
                        body.Exercises.Add(item);
                    }
                    else
                    {
                        var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(28), Id = 28 };
                        bodyPartFavourite.Exercices.Add(item);
                        BodyPartSection sections = new BodyPartSection(bodyPartFavourite, false);
                        ExeList.Add(sections);

                        ExpandableList.ItemsSource = ExeList;

                    }
                }
            }
            foreach (var item in exo)
            {
                exerciseItems.Add(item);
            }
            exerciseItemsResult = exerciseItems;

        }
        catch (Exception ex)
        {

        }

    }

    private async void AddExercisesAction(PopupAction response,string Text)
    {
        if (response == PopupAction.OK)
        {
            try
            {
                ActionSheetConfig config = new ActionSheetConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };

                config.Add("Normal", () =>
                {
                    var userExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = false
                    };
                    AskForIsEasy(userExercise);
                });
                config.Add("Cardio", () =>
                {
                    var userExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = true,
                        IsFlexibility = false,
                        IsTimeBased = true,
                        BodyPartId = 12
                    };
                    AskForIsEasy(userExercise);
                });
                config.Add("Bodyweight", () =>
                {
                    var userExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = true
                    };
                    AskForIsEasy(userExercise);
                });
                config.Add("Weighted Bodyweight", () =>
                {
                    var userExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = false,
                        IsWeighted = true
                    };
                    AskForIsEasy(userExercise);
                });
                config.Add("Flexibility or mobility", () =>
                {
                    newAddUserExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = true,
                        IsFlexibility = true,
                        IsEasy = false,
                        IsMedium = false,
                        IsTimeBased = false
                    };
                    CreateNewExercise();
                });
                config.SetTitle("Exercise type?");
                UserDialogs.Instance.ActionSheet(config);
                //ConfirmConfig IsEasyPopUp = new ConfirmConfig()
                //{
                //    Title = AppResources.IsThisABodyweightExercise,
                //    //Title = $"Let's set up your {response.Text}",
                //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //    OkText = AppResources.YesBodyweight,
                //    CancelText = AppResources.No,
                //    OnAction = async (bool ok) =>
                //    {
                //        var userExercise = new AddUserExerciseModel()
                //        {
                //            Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                //            ExerciseName = response.Text,
                //            IsBodyweight = ok
                //        };
                //        AskForIsEasy(userExercise);
                //    }
                //};
                //await Task.Delay(100);
                //UserDialogs.Instance.Confirm(IsEasyPopUp);

            }
            catch (Exception e)
            {
                // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = AppResources.PleaseCheckInternetConnection,
                //     Title = AppResources.ConnectionError
                // });

                await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                            AppResources.PleaseCheckInternetConnection,"Try again","");
                //await UserDialogs.Instance.AlertAsync(AppResources.PleaseCheckInternetConnection, AppResources.Error);
            }
        }
    }


    /// <summary>
    /// Actionsheet for exercise type
    /// </summary>
    /// <param name="newUserExercise"></param>
    ///
    async void CreatePopupForExerciseType()
    {
        try
        {
            if (newAddUserExercise.BodyPartId == 12)
            {
                CreateExercise();
                return;
            }
            ActionSheetConfig actionSheetConfig = new ActionSheetConfig();
            actionSheetConfig.AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray);

            actionSheetConfig.Add("Hard", () =>
            {
                CreateNewExercise();
            });

            actionSheetConfig.Add("Medium", () =>
            {
                newAddUserExercise.IsMedium = true;
                CreateNewExercise();
            });
            actionSheetConfig.Add("Easy", () =>
            {
                newAddUserExercise.IsEasy = true;
                CreateNewExercise();
            });
            actionSheetConfig.SetTitle("How hard should the exercise be?");
            UserDialogs.Instance.ActionSheet(actionSheetConfig);

        }
        catch (Exception ex)
        {

        }
    }

    async void AskForIsEasy(AddUserExerciseModel newUserExercise)
    {
        newAddUserExercise = newUserExercise;
        CreatePopupForExerciseType();
    }


    async void CreateNewExercise()
    {
        try
        {
            if (newAddUserExercise.BodyPartId == 12)
            {
                CreateExercise();
                return;
            }
            ActionSheetConfig actionSheetConfig = new ActionSheetConfig();
            actionSheetConfig.AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray);

            actionSheetConfig.Add("Abs", () =>
            {
                newAddUserExercise.BodyPartId = 7;
                CreateExercise();
            });

            actionSheetConfig.Add("Back", () =>
            {
                newAddUserExercise.BodyPartId = 4;
                CreateExercise();
            });
            actionSheetConfig.Add("Lower back, glutes & hamstrings", () =>
            {
                newAddUserExercise.BodyPartId = 14;
                CreateExercise();
            });
            actionSheetConfig.Add("Biceps", () =>
            {
                newAddUserExercise.BodyPartId = 5;
                CreateExercise();
            });
            actionSheetConfig.Add("Calves", () =>
            {
                newAddUserExercise.BodyPartId = 9;
                CreateExercise();
            });
            //actionSheetConfig.Add("Cardio", () =>
            //{
            //    newAddUserExercise.BodyPartId = 12;
            //    CreateExercise();
            //});
            actionSheetConfig.Add("Chest", () =>
            {
                newAddUserExercise.BodyPartId = 3;
                CreateExercise();
            });
            //actionSheetConfig.Add("Flexibility & Mobility", () =>
            //{
            //    newAddUserExercise.BodyPartId = 13;
            //    CreateExercise();
            //});
            actionSheetConfig.Add("Forearm", () =>
            {
                newAddUserExercise.BodyPartId = 11;
                CreateExercise();
            });
            actionSheetConfig.Add("Legs", () =>
            {
                newAddUserExercise.BodyPartId = 8;
                CreateExercise();
            });
            actionSheetConfig.Add("Neck", () =>
            {
                newAddUserExercise.BodyPartId = 10;
                CreateExercise();
            });
            actionSheetConfig.Add("Shoulders", () =>
            {
                newAddUserExercise.BodyPartId = 2;
                CreateExercise();
            });

            actionSheetConfig.Add("Triceps", () =>
            {
                newAddUserExercise.BodyPartId = 6;
                CreateExercise();
            });

            actionSheetConfig.SetTitle("Body part");
            UserDialogs.Instance.ActionSheet(actionSheetConfig);
        }
        catch (Exception ex)
        {

        }

    }
    private void StateImage_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName.Equals("Source"))
        {
            var image = sender as Image;
            image.Opacity = 0;
            image.FadeTo(1, 1000);
        }
    }

    async void FinalCreateExercise()
    {
        try
        {

            ExerciceModel newExercise = await DrMuscleRestClient.Instance.CreateNewExercise(newAddUserExercise);
            CurrentLog.Instance.IsAddedNewExercise = true;
            newExercise.BodyPartId = newAddUserExercise.BodyPartId;
            //exercises.Add(newExercise);
            customItems.Add(newExercise);
            await UpdateExerciseList();
            if (CurrentLog.Instance.SwapContext == null)
            {

                CurrentLog.Instance.IsFromExercise = true;
                CurrentLog.Instance.CurrentExercise = newExercise;

                if (!Config.IsFirstExerciseCreatedPopup && customItems.Count == 1)
                {
                    Config.IsFirstExerciseCreatedPopup = true;
                    var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
                    var modalPage = new Views.GeneralPopup("FirstWorkout.png", "Success!", "First exercise created", "Open exercise");
                    //TODO: MAUI
                    //modalPage.Disappearing += (sender2, e2) =>
                    //{
                    //    waitHandle.Set();
                    //};
                    //await PopupNavigation.Instance.PushAsync(modalPage);

                    //await Task.Run(() => waitHandle.WaitOne());
                }
                var kenko = new KenkoSingleExercisePage();
                await Navigation.PushAsync(kenko);

            }
        }
        catch (Exception ex)
        {

        }
    }
    async void CreateExercise()
    {
        try
        {
            var ShowPopUp = await HelperClass.DisplayCustomPopup("Left/right side separately?", "Train your left and right sides separately, or both sides together?",
                 "Left/right separately", "Both sides together");
            ShowPopUp.ActionSelected += async (sender, action) =>
            {

                if (action == Views.PopupAction.OK)
                {
                    newAddUserExercise.IsUnilateral = true;
                }

                if (newAddUserExercise.IsFlexibility || newAddUserExercise.BodyPartId == 12)
                    FinalCreateExercise();
                else
                    AskForTimeBasedExercise();

            };
            //await Task.Delay(100);



            // ConfirmConfig IsEasyPopUp = new ConfirmConfig()
            // {
            //     Title = "Left/right side separately?",
            //     Message = "Train your left and right sides separately, or both sides together?",
            //     //Title = $"Let's set up your {response.Text}",
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = "Left/right separately",
            //     CancelText = "Both sides together",
            //     OnAction = async (bool ok) =>
            //     {
            //         newAddUserExercise.IsUnilateral = ok;
            //         if (newAddUserExercise.IsFlexibility || newAddUserExercise.BodyPartId == 12)
            //             FinalCreateExercise();
            //         else
            //             AskForTimeBasedExercise();
            //     }
            // };
            // await Task.Delay(100);
            // UserDialogs.Instance.Confirm(IsEasyPopUp);
        }
        catch (Exception ex)
        {

        }
    }

    async void AskForTimeBasedExercise()
    {
        try
        {
            var isConfirm = await HelperClass.DisplayCustomPopupForResult("", "Should this exercise progress in reps or seconds?",
                 "Seconds", AppResources.Reps);



            // ConfirmConfig TimebasePopUp = new ConfirmConfig()
            // {
            //     Message = "Should this exercise progress in reps or seconds?",
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = "Seconds",
            //     CancelText = AppResources.Reps,
            // };

            // var isConfirm = await UserDialogs.Instance.ConfirmAsync(TimebasePopUp);
            if (isConfirm == PopupAction.OK)
            {
                newAddUserExercise.IsTimeBased = true;
                if (!newAddUserExercise.IsWeighted)
                    newAddUserExercise.IsBodyweight = true;
            }
            else
            {
                newAddUserExercise.IsTimeBased = false;
            }
            FinalCreateExercise();
        }
        catch (Exception ex)
        { 

        }
    }

    void Handle_SearchTextChanged(object sender, TextChangedEventArgs e)
    {
        var mobilityexe = exerciseItems.Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant()) && x.IsFlexibility).ToList();

        var list = exerciseItems.Except(mobilityexe).Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant())).ToList();
        var favList = favouriteItems.Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant())).ToList();
        var customList = customItems.Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant())).ToList();

        ExeList.Clear();

        //if (CurrentLog.Instance.SwapContext != null)
        //{
        //    var swapContext = CurrentLog.Instance.SwapContext;
        //    if (swapContext.SourceBodyPartId != null)
        //    {
        //        var bodyPart = list.Where(x => x.BodyPartId == swapContext.SourceBodyPartId).ToList();
        //        var notBodyPart = list.Where(x => x.BodyPartId != swapContext.SourceBodyPartId).ToList();

        //        var bodyPartShoulders = new BodyPartModel() { Label = $"{AppThemeConstants.GetBodyPartName(swapContext.SourceBodyPartId)} exercises" };
        //        foreach (var item in bodyPart)
        //        {
        //            bodyPartShoulders.Exercices.Add(item);
        //        }
        //        BodyPartSection section = new BodyPartSection(bodyPartShoulders);
        //        ExeList.Add(section);

        //        var otherBodyPartShoulders = new BodyPartModel() { Label = $"Other exercises" };
        //        foreach (var item in notBodyPart.OrderBy(x => x.BodyPartId))
        //        {
        //            otherBodyPartShoulders.Exercices.Add(item);
        //        }
        //        BodyPartSection otherSection = new BodyPartSection(otherBodyPartShoulders);
        //        ExeList.Add(otherSection);
        //        Device.BeginInvokeOnMainThread(() =>
        //        {
        //            ExpandableList.ItemsSource = ExeList;
        //        });


        //    }
        //    else
        //    {

        //    }
        //    System.Diagnostics.Debug.WriteLine($"SourceBodyPartId{swapContext.SourceBodyPartId}");
        //}

        if (ExeList.Count == 0)
        {
            //foreach (ExerciceModel em in exo)
            //exerciseItems.Add(em);

            foreach (var groupItem in list.GroupBy(x => x.BodyPartId).ToList())
            {
                var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(groupItem.Key), Id = (int)groupItem.Key };
                foreach (var item in groupItem.OrderBy(x => x.Label))
                {
                    bodyPartShoulders.Exercices.Add(item);
                }
                BodyPartSection section = new BodyPartSection(bodyPartShoulders);
                ExeList.Add(section);
            }
            
                ExpandableList.ItemsSource = ExeList.OrderBy(x => x.Name);



            foreach (var item in customList.OrderBy(x => x.Label))
            {
                var bodyPartSection = ExeList.Where(x => x.Id == 26).ToList();
                if (bodyPartSection.Count > 0)
                {
                    BodyPartSection body = bodyPartSection[0];
                    if (body.Expanded)
                        body.Add(item);
                    body._bodyPart.Exercices.Add(item);
                    body.Exercises.Add(item);
                }
                else
                {
                    var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(26), Id = 26 };
                    bodyPartFavourite.Exercices.Add(item);
                    BodyPartSection sections = new BodyPartSection(bodyPartFavourite);
                    ExeList.Insert(0, sections);
                   
                        ExpandableList.ItemsSource = ExeList;
                    
                }
            }

            foreach (var item in favList.OrderBy(x => x.Label))
            {
                var bodyPartSection = ExeList.Where(x => x.Id == 25).ToList();
                if (bodyPartSection.Count > 0)
                {
                    BodyPartSection body = bodyPartSection[0];
                    if (body.Expanded)
                        body.Add(item);
                    body._bodyPart.Exercices.Add(item);
                    body.Exercises.Add(item);
                }
                else
                {
                    var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(25), Id = 25 };
                    bodyPartFavourite.Exercices.Add(item);
                    BodyPartSection sections = new BodyPartSection(bodyPartFavourite);
                    ExeList.Insert(0, sections);
                    
                        ExpandableList.ItemsSource = ExeList;
                    
                }
            }

            foreach (var item in mobilityexe.OrderBy(x => x.Label))
            {
                var bodyPartSection = ExeList.Where(x => x.Id == 28).ToList();
                if (bodyPartSection.Count > 0)
                {
                    BodyPartSection body = bodyPartSection[0];
                    if (body.Expanded)
                        body.Add(item);
                    body._bodyPart.Exercices.Add(item);
                    body.Exercises.Add(item);
                }
                else
                {
                    var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(28), Id = 28 };
                    bodyPartFavourite.Exercices.Add(item);
                    BodyPartSection sections = new BodyPartSection(bodyPartFavourite);
                    ExeList.Add(sections);
                    
                        ExpandableList.ItemsSource = ExeList;
                    
                }
            }

        }
        if (e.NewTextValue.Length > 0)
            BtnCancel.IsVisible = true;
        else
            BtnCancel.IsVisible = false;
    }

    void Handle_CancelTapped(object sender, System.EventArgs e)
    {
        SearchEntry.Text = "";
    }

    void ExerciseListView_Scrolled(System.Object sender, Microsoft.Maui.Controls.ScrolledEventArgs e)
    {
        if (!SearchEntry.IsFocused && (PopupNavigation.Instance.PopupStack?.Count()) == 0)
        {
            SearchEntry.HideSoftInputAsync(CancellationToken.None);
        }
    }
    void SearchEntry_Focused(object sender, FocusEventArgs e)
    {
        SearchEntry.Focus();
    }
    void SearchEntry_Unfocused(object sender, FocusEventArgs e)
    {
        SearchEntry.HideSoftInputAsync(CancellationToken.None);
    }
}

