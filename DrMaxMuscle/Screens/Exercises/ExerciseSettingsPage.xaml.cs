﻿using System.Text.RegularExpressions;
using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Utility;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using Microsoft.Maui.Networking;
using RGPopup.Maui.Services;

namespace DrMaxMuscle.Screens.Exercises;

public partial class ExerciseSettingsPage : ContentPage
{
    bool IsLoading = false;
    int? style = null;
    int BackOff = 0;
    int SelectedIndexForSet = 0;
    bool isSettingsupUI = false;
    bool? isUnilateralSetup = null;
    public ExerciseSettingsPage()
    {
        InitializeComponent();

        RefreshLocalized();

        RepsMinimumLess.Clicked += async (sender, e) =>
        {
            int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

            if (currentRepsMinimum > 5)
                currentRepsMinimum = currentRepsMinimum - 1;
            else
            {
                if (currentRepsMinimum == 1)
                {
                    // UserDialogs.Instance.AlertAsync(new AlertConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = "Minimum reps must be 1.",
                    //     Title = AppResources.LessThan5Reps
                    // });

                    await HelperClass.DisplayCustomPopupForResult(AppResources.LessThan5Reps,
                            "Minimum reps must be 1.",AppResources.Ok,"");
                    return;
                }
                //UserDialogs.Instance.AlertAsync(new AlertConfig()
                //{
                //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //    Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                //    Title = AppResources.LessThan5Reps
                //});
                if (CurrentLog.Instance.IsMinRepsWarning)
                {
                    currentRepsMinimum = currentRepsMinimum - 1;
                    RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                }
                else
                {
                   var ShowPopUp = await HelperClass.DisplayCustomPopup("Are you sure?",AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                    "Confirm","Cancel");
                        ShowPopUp.ActionSelected += async (sender,action) => {

                                if (action == Views.PopupAction.OK)
                                {
                                    CurrentLog.Instance.IsMinRepsWarning = true;
                                    currentRepsMinimum = currentRepsMinimum - 1;
                                    RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                                }
                                
                        };
                                              
                    // UserDialogs.Instance.Confirm(new ConfirmConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                    //     Title = "Are you sure?",
                    //     OkText = "Confirm",
                    //     CancelText = "Cancel",
                    //     OnAction = (obj) =>
                    //     {
                    //         if (obj)
                    //         {
                    //             CurrentLog.Instance.IsMinRepsWarning = true;
                    //             currentRepsMinimum = currentRepsMinimum - 1;
                    //             RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                    //         }
                    //     }
                    // });
                }
            }
            RepsMinimumLabel.Text = currentRepsMinimum.ToString();
        };

        RepsMinimumMore.Clicked += async (sender, e) =>
        {

            int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

            if (currentRepsMinimum >= currentRepsMaximum)
            await HelperClass.DisplayCustomPopupForResult("",
                            AppResources.PleaseIncreaseMaxRepsToIncreaseMinimumRepsFurther,AppResources.Ok,"");
                // UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = AppResources.PleaseIncreaseMaxRepsToIncreaseMinimumRepsFurther,
                // });
            else
            {
                if (currentRepsMinimum < 30)
                    currentRepsMinimum = currentRepsMinimum + 1;
                else
                {
                    //UserDialogs.Instance.AlertAsync(new AlertConfig()
                    //{
                    //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //    Message = AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,
                    //    Title = AppResources.MoreThan30Reps
                    //});
                    if (CurrentLog.Instance.IsMaxRepsWarning)
                    {
                        currentRepsMinimum = currentRepsMinimum + 1;
                        RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                    }
                    else
                    {
                      var ShowPopUp = await HelperClass.DisplayCustomPopup("Are you sure?",AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,
                    "Confirm","Cancel");
                        ShowPopUp.ActionSelected += async (sender,action) => {

                                if (action == Views.PopupAction.OK)
                                {
                                    CurrentLog.Instance.IsMinRepsWarning = true;
                                    currentRepsMinimum = currentRepsMinimum + 1;
                                    RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                                }
                                
                        };
                       
                        // UserDialogs.Instance.Confirm(new ConfirmConfig()
                        // {
                        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        //     Message = AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,
                        //     Title = "Are you sure?",
                        //     OkText = "Confirm",
                        //     CancelText = "Cancel",
                        //     OnAction = (obj) =>
                        //     {
                        //         if (obj)
                        //         {
                        //             CurrentLog.Instance.IsMinRepsWarning = true;
                        //             currentRepsMinimum = currentRepsMinimum + 1;
                        //             RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                        //         }
                        //     }
                        // });
                    }
                }
                RepsMinimumLabel.Text = currentRepsMinimum.ToString();
            }
        };

        //TimebaseSwitch.Toggled += async (sender, e) =>
        //{
        //    if (TimebaseSwitch.IsToggled)
        //    {
        //        LocalDBManager.Instance.SetDBSetting("TimeBase" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id, "true");
        //    }
        //    else
        //    {
        //        LocalDBManager.Instance.SetDBSetting("TimeBase" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id, "false");
        //    }
        //};

        CustomRepsSwitch.Toggled += async (sender, e) =>
        {
            if (CustomRepsSwitch.IsToggled)
            {
                LoadCustomReps();
                RepsStack.IsVisible = true;
            }
            else
            {
                RepsStack.IsVisible = false;
            }
            SaveCustomSettings();
        };
        CustomSetsSwitch.Toggled += async (sender, e) =>
        {
            if (CustomSetsSwitch.IsToggled)
            {
                SetsStack.IsVisible = true;
            }
            else
            {
                SetsStack.IsVisible = false;
            }
            SaveCustomSettings();

        };
        IncrementSwitch.Toggled += async (sender, e) =>
        {
            if (IncrementSwitch.IsToggled)
            {
                StackIncrements.IsVisible = true;
            }
            else
            {
                StackIncrements.IsVisible = false;
            }
            SaveCustomSettings();
        };

        WarmupSwitch.Toggled += async (sender, e) =>
        {
            if (WarmupSwitch.IsToggled)
            {
                StackWarmup.IsVisible = true;
            }
            else
            {
                StackWarmup.IsVisible = false;
            }
            SaveCustomSettings();
        };

        FavoriteSwitch.Toggled += async (sender, e) => {
            SaveCustomSettings();
            if (((Switch)sender).IsToggled)
            {
                //
                if (!Config.IsFirstFavoritePopup && !IsLoading)
                {
                    Config.IsFirstFavoritePopup = true;
                    var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
                    var modalPage = new Views.RGGeneralPopup("medal.png", "Success!", "First exercise added to favorites", "View exercise");
                    if (modalPage != null)
                    {
                        modalPage.Closed += (sender2, e2) =>
                        {
                            waitHandle.Set();
                        };
                        await Application.Current.MainPage.ShowPopupAsync(modalPage);

                        await Task.Run(() => waitHandle.WaitOne());
                    }

                }
            }
            CurrentLog.Instance.IsFavouriteUpdated = true;
        };

        UnilateralSwitch.Toggled += async (sender, e) =>
        {
            if (!isSettingsupUI)
            {
                isUnilateralSetup = UnilateralSwitch.IsToggled;
                SaveCustomSettings();
            }

        };
        // Contrôle du label reps maximum 
        RepsMaximumLess.Clicked += async (sender, e) =>
        {

            int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

            if (currentRepsMaximum <= currentRepsMinimum)
            await HelperClass.DisplayCustomPopupForResult("",
                            AppResources.PleaseDecreaseMinimumRepsToDecreaseMaxRepsFurther,AppResources.Ok,"");
                // UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = AppResources.PleaseDecreaseMinimumRepsToDecreaseMaxRepsFurther,
                // });
            else
            {
                if (currentRepsMaximum > 5)
                    currentRepsMaximum = currentRepsMaximum - 1;
                else
                {
                    //UserDialogs.Instance.AlertAsync(new AlertConfig()
                    //{
                    //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //    Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                    //    Title = AppResources.LessThan5Reps
                    //});
                    if (CurrentLog.Instance.IsMinRepsWarning)
                    {
                        currentRepsMaximum = currentRepsMaximum - 1;
                        RepsMaximumLabel.Text = currentRepsMaximum.ToString();
                    }
                    else
                    {
                      var ShowPopUp = await HelperClass.DisplayCustomPopup("Are you sure?",AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                    "Confirm","Cancel");
                        ShowPopUp.ActionSelected += async (sender,action) => {

                                if (action == Views.PopupAction.OK)
                                {
                                    CurrentLog.Instance.IsMinRepsWarning = true;
                                    currentRepsMinimum = currentRepsMinimum - 1;
                                    RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                                }
                                
                        };
                     

                        // UserDialogs.Instance.Confirm(new ConfirmConfig()
                        // {
                        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        //     Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                        //     Title = "Are you sure?",
                        //     OkText = "Confirm",
                        //     CancelText = "Cancel",
                        //     OnAction = (obj) =>
                        //     {
                        //         if (obj)
                        //         {
                        //             CurrentLog.Instance.IsMaxRepsWarning = true;
                        //             currentRepsMaximum = currentRepsMaximum - 1;
                        //             RepsMaximumLabel.Text = currentRepsMaximum.ToString();
                        //         }
                        //     }
                        // });
                    }
                }
                RepsMaximumLabel.Text = currentRepsMaximum.ToString();
            }
        };

        RepsMaximumMore.Clicked += async (sender, e) =>
        {
            int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

            if (currentRepsMaximum < 30)
                currentRepsMaximum = currentRepsMaximum + 1;
            else
            {
                //UserDialogs.Instance.AlertAsync(new AlertConfig()
                //{
                //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //    Message = AppResources.SetsOfMoreThan30RepsAreRatherPainfulTakeALongTimeToDoAndDoNotBuildMuscleFasterForBestResultsMaxRepsIs30,
                //    Title = AppResources.MoreThan30Reps
                //});
                if (CurrentLog.Instance.IsMaxRepsWarning)
                {
                    currentRepsMaximum = currentRepsMaximum + 1;
                    RepsMaximumLabel.Text = currentRepsMaximum.ToString();
                }
                else
                {
                   var ShowPopUp = await HelperClass.DisplayCustomPopup("Are you sure?",AppResources.SetsOfMoreThan30RepsAreRatherPainfulTakeALongTimeToDoAndDoNotBuildMuscleFasterForBestResultsMaxRepsIs30,
                    "Confirm","Cancel");
                        ShowPopUp.ActionSelected += async (sender,action) => {

                                if (action == Views.PopupAction.OK)
                                {
                                    CurrentLog.Instance.IsMinRepsWarning = true;
                                    currentRepsMinimum = currentRepsMinimum + 1;
                                    RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                                }
                                
                        };
                      
                    // UserDialogs.Instance.Confirm(new ConfirmConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = AppResources.SetsOfMoreThan30RepsAreRatherPainfulTakeALongTimeToDoAndDoNotBuildMuscleFasterForBestResultsMaxRepsIs30,
                    //     Title = "Are you sure?",
                    //     OkText = "Confirm",
                    //     CancelText = "Cancel",
                    //     OnAction = (obj) =>
                    //     {
                    //         if (obj)
                    //         {
                    //             CurrentLog.Instance.IsMaxRepsWarning = true;
                    //             currentRepsMaximum = currentRepsMaximum + 1;
                    //             RepsMaximumLabel.Text = currentRepsMaximum.ToString();
                    //         }
                    //     }
                    // });
                }
            }

            RepsMaximumLabel.Text = currentRepsMaximum.ToString();
        };

        // Bouton save custom reps

        SaveCustomRepsButton.Clicked += async (sender, e) =>
        {
            try
            {
                if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                {
                    ConnectionErrorPopup();
                    return;
                }
                SaveCustomSettings();
            }
            catch (Exception)
            {
                ConnectionErrorPopup();
            }
        };

        SaveWarmupButton.Clicked += async (sender, e) =>
        {
            try
            {
                SaveCustomSettings();
            }
            catch (Exception)
            {

            }
        };

        SaveSetCountButton.Clicked += async (sender, e) =>
        {
            try
            {
                if (!string.IsNullOrEmpty(SetEntry.Text))
                {
                    var count = int.Parse(SetEntry.Text.ReplaceWithDot());
                    if (count < 2 || count > 99)
                    {
                        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                        // {
                        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        //     Message = "At least 2 work sets",
                        // }); 
                        await HelperClass.DisplayCustomPopupForResult("",
                            "At least 2 work sets",AppResources.Ok,"");
                        return;
                    }
                    SetEntry.Unfocus();

                    SaveCustomSettings();
                }
                else
                {
                    SetEntry.Unfocus();

                    SaveCustomSettings();
                }

            }
            catch (Exception)
            {

            }
        };

        SaveIncrementsButton.Clicked += async (sender, e) =>
        {
            try
            {
                SaveCustomSettings();
                
            }
            catch (Exception ex)
            {

            }
        };
        //Set style

        

        UnitEntry.Keyboard = Device.RuntimePlatform.Equals(Device.Android) ? Keyboard.Telephone : Keyboard.Numeric;
        MinEntry.Keyboard = Device.RuntimePlatform.Equals(Device.Android) ? Keyboard.Telephone : Keyboard.Numeric;
        MaxEntry.Keyboard = Device.RuntimePlatform.Equals(Device.Android) ? Keyboard.Telephone : Keyboard.Numeric;

        VideoInstructionButton.Clicked += VideoInstructionButton_Clicked;
        ResetButton.Clicked += ResetButton_ClickedAsync;
        OnBeforeShow();
    }


    private void RefreshLocalized()
    {
        LblNotes.Text = AppResources.Notes;

        LblSettings.Text = AppResources.SettingsUppercase;

        LblCustomReps.Text = AppResources.UseCustomReps;
        LblCustomSet.Text = AppResources.UseCustomSetStyle;

        LblCustomIncrements.Text = AppResources.UseCustomIncrements;
        VideoInstructionButton.Text = AppResources.VideoAndInstruction;
        LblMore.Text = AppResources.MoreUppercase;
        ResetButton.Text = "Delete all history";

        LblMin.Text = AppResources.Min;
        LblMax.Text = AppResources.Max;
        SaveCustomRepsButton.Text = AppResources.SaveCustomReps;

        Min.Text = AppResources.MinWeight;
        Max.Text = AppResources.MaxWeight;
        LblRestPauseSets.Text = AppResources.RestPauseSetsAreHarderButTheyHalveWorkoutTime;
        //LblNormalSets.Text = AppResources.NormalSets;
        //LblRestPasue.Text = AppResources.RestPauseSets;

        Increments.Text = AppResources.Increments;
        SaveIncrementsButton.Text = AppResources.SaveIncrements;
        SaveWarmupButton.Text = "Save warm-up sets";
        LblHowManyWarmups.Text = AppResources.WarmUpSets;
        WarmupEntry.Placeholder = AppResources.TapToSet;
        LblCustomWarmUp.Text = AppResources.UseCustomWarmUps;
    }

    public async void OnBeforeShow()
    {

        isSettingsupUI = true;
        try
        {

            if (CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsBodyweight)
            {
                List<string> setstyles = new List<string>();
                setstyles.Add("Rest-pause");
                setstyles.Add("Normal");
                SetStylePicker.ItemsSource = setstyles;

                //PyramidGradient.IsVisible = false;
                //BxSaperator2.IsVisible = false;
                MainIncrementStack.IsVisible = false;

                //RPyramidGradient.IsVisible = false;
                //BxSaperator3.IsVisible = false;
            }
            else
            {
                List<string> setstyles = new List<string>();
                setstyles.Add("Rest-pause");
                setstyles.Add("Drop");
                setstyles.Add("Normal");
                setstyles.Add("Pyramid");
                setstyles.Add("Reverse Pyramid");

                SetStylePicker.ItemsSource = setstyles;

                //PyramidGradient.IsVisible = true;
                //BxSaperator2.IsVisible = true;
                MainIncrementStack.IsVisible = true;

                //RPyramidGradient.IsVisible = true;
                //BxSaperator3.IsVisible = true;
            }
            SetStylePicker.SelectedIndex = 0;
            Title = CurrentLog.Instance.WorkoutTemplateCurrentExercise.Label;
            IsLoading = true;
            VideoInstructionButton.IsVisible = !string.IsNullOrEmpty(CurrentLog.Instance.WorkoutTemplateCurrentExercise.VideoUrl);

            ExerciseSettingsModel exerciseSettingsModel = await DrMuscleRestClient.Instance.GetExerciseSettings(new ExerciseSettingsModel()
            {
                Id = CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id,
            });
            //if (CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsBodyweight || !CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsSystemExercise)
            //    TimerBaseStack.IsVisible = true;
            //else
            //    TimerBaseStack.IsVisible = false;

            if (LocalDBManager.Instance.GetDBSetting("TimeBase" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id) == null)
                LocalDBManager.Instance.SetDBSetting("TimeBase" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id, "false");
            //TimebaseSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("TimeBase" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id).Value == "true";

            if (exerciseSettingsModel != null)
            {
                NotesEnrty.Text = exerciseSettingsModel.Notes;
                IncrementSwitch.IsToggled = exerciseSettingsModel.IsCustomIncrements;
                if (exerciseSettingsModel.Increments != null)
                {
                    UnitEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{exerciseSettingsModel.Increments.Kg}" : $"{exerciseSettingsModel.Increments.Lb}";
                }
                else
                    UnitEntry.Text = "";
                if (exerciseSettingsModel.Min != null)
                {
                    MinEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{exerciseSettingsModel.Min.Kg}" : $"{exerciseSettingsModel.Min.Lb}";
                }
                else
                    MinEntry.Text = "";
                if (exerciseSettingsModel.Max != null)
                {
                    MaxEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{exerciseSettingsModel.Max.Kg}" : $"{exerciseSettingsModel.Max.Lb}";
                }
                else
                    MaxEntry.Text = "";
                SetEntry.Text = Convert.ToString(exerciseSettingsModel.SetCount);
                CustomRepsSwitch.IsToggled = exerciseSettingsModel.IsCustomReps.Value;
                CustomSetsSwitch.IsToggled = exerciseSettingsModel.IsCustomSets.Value;
                if (exerciseSettingsModel.IsCustomReps.Value)
                {
                    RepsMinimumLabel.Text = exerciseSettingsModel.RepsMinValue.ToString();
                    RepsMaximumLabel.Text = exerciseSettingsModel.RepsMaxValue.ToString();
                }
                WarmupSwitch.IsToggled = exerciseSettingsModel.IsCustomWarmups;
                if (exerciseSettingsModel.IsCustomWarmups)
                {
                    WarmupEntry.Text = Convert.ToString(exerciseSettingsModel.WarmupsValue);
                }
                else
                    WarmupEntry.Text = "";
                CustomSetsSwitch.IsToggled = exerciseSettingsModel.IsCustomSets.Value;
                if (exerciseSettingsModel.IsCustomSets.Value)
                {
                    if (exerciseSettingsModel.IsDropSet == true && CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsBodyweight == false)
                    {
                        DropStyle();
                        SetStylePicker.SelectedIndex = 1;
                    }
                    else if (exerciseSettingsModel.IsPyramid && CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsBodyweight == false)
                    {
                        RPyramid();
                        SetStylePicker.SelectedIndex = 3;
                    }
                    else if (exerciseSettingsModel.IsNormalSets == null && CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsBodyweight == false)
                    {
                        Pyramid();
                        SetStylePicker.SelectedIndex = 4;
                    }
                    else if (exerciseSettingsModel?.IsNormalSets != null && (bool)exerciseSettingsModel?.IsNormalSets == true)
                    {
                        NormalStyle();
                        SetStylePicker.SelectedIndex = CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsBodyweight == false ? 2 : 1;
                    }

                    else
                    {
                        RestPasue();
                        SetStylePicker.SelectedIndex = 0;
                    }
                }
                else
                {
                    RestPasue();
                    SetStylePicker.SelectedIndex = 0;
                }
                SelectedIndexForSet = SetStylePicker.SelectedIndex;
                if (exerciseSettingsModel.IsBackOffSet == null)
                    DefaultClick();
                else if ((bool)exerciseSettingsModel.IsBackOffSet == true)
                    YesClicked();
                else
                    NoClicked();
                FavoriteSwitch.IsToggled = exerciseSettingsModel.IsFavorite;
                isUnilateralSetup = exerciseSettingsModel.IsDefaultUnilateral;
                UnilateralSwitch.IsToggled = exerciseSettingsModel.IsDefaultUnilateral == null ? false : (bool)exerciseSettingsModel.IsDefaultUnilateral;
            }
            else
            {
                UnitEntry.Text = "";
                NotesEnrty.Text = "";
                MinEntry.Text = "";
                MaxEntry.Text = "";
                WarmupEntry.Text = "";
                SetEntry.Text = "";
                WarmupSwitch.IsToggled = false;
                CustomRepsSwitch.IsToggled = false;
                CustomSetsSwitch.IsToggled = false;
                IncrementSwitch.IsToggled = false;
                FavoriteSwitch.IsToggled = false;
                UnilateralSwitch.IsToggled = false;
                isUnilateralSetup = null;
                RestPasue();
                DefaultClick();
                LblRestPauseSets.Text = "Harder, but makes your workouts ~59% faster";

                LoadCustomReps();
            }
            if (CurrentLog.Instance.AutoEnableIncrements)
            {
                CurrentLog.Instance.AutoEnableIncrements = false;
                await scrollView.ScrollToAsync(MainIncrementStack, ScrollToPosition.Start, false);
                IncrementSwitch.IsToggled = true;
            }

        }
        catch (Exception ex)
        {

        }
        IsLoading = false;
        isSettingsupUI = false;
    }

    protected override void OnAppearing()
    {
        try
        {
            DismissKeyboard();
            DependencyService.Get<IFirebase>().SetScreenName("exercise_settings_page");
        }
        catch (Exception ex)
        {

        }

    }

    protected override void OnDisappearing()
    {
        DismissKeyboard();
    }

   
    async void BackOffPicker_Unfocused(object sender, FocusEventArgs e)
    {
        SaveCustomSettings();
    }
    
    public async void BtnRestPauseClicked(object sender, EventArgs args)
    {
        RestPasue();
        SaveCustomSettings();
    }
    void RestPasue()
    {
        style = 0;
        LblRestPauseSets.Text = "Harder, but makes your workouts ~59% faster";
        
    }

    public async void BtnNormalClicked(object sender, EventArgs args)
    {
        NormalStyle();

        SaveCustomSettings();
    }

    void NormalStyle()
    {
        style = 1;
        LblRestPauseSets.Text = "Takes more time, but builds more strength";
        
    }

    void DropStyle()
    {
        style = 4;
        LblRestPauseSets.Text = "Weights decrease from set to set—great for dumbbell, machine, and pulley exercises";
        
    }
    void BtnPyramid_Clicked(System.Object sender, System.EventArgs e)
    {
        Pyramid();
        SaveCustomSettings();
    }
    void Pyramid()
    {
        LblRestPauseSets.Text = "Reps increase from set to set";
        style = 2;
        
    }

    void BtnRPyramid_Clicked(System.Object sender, System.EventArgs e)
    {
        RPyramid();
        SaveCustomSettings();
    }
    void RPyramid()
    {
        LblRestPauseSets.Text = "Reps decrease from set to set";
        style = 3;

        
    }

    void SetStylePicker_Unfocused(System.Object sender, FocusEventArgs e)
    {
        if (SelectedIndexForSet != SetStylePicker.SelectedIndex)
        {
            switch (SetStylePicker.SelectedIndex)
            {
                case 0:
                    LblRestPauseSets.Text = "Harder, but makes your workouts ~59% faster";
                    style = 0;
                    break;
                case 1:
                    //Drop
                    LblRestPauseSets.Text = "Weights decrease from set to set—great for dumbbell, machine, and pulley exercises";
                    style = 4;

                    break;
                case 2:
                    LblRestPauseSets.Text = "Takes more time, but builds more strength";
                    style = 1;
                    break;
                case 3:
                    //Pyramid
                    LblRestPauseSets.Text = "Reps decrease from set to set";
                    style = 3;

                    break;
                case 4:
                    //Reverse pyramid
                    LblRestPauseSets.Text = "Reps increase from set to set";
                    style = 2;
                    break;

                default:
                    break;
            }
            SelectedIndexForSet = style ?? 0;
            SaveCustomSettings();
        }

    }


    void ResetReco()
    {
        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());
    }
    void DismissKeyboard()
    {
        try
        {


            UnitEntry.Unfocus();
            MinEntry.Unfocus();
            MaxEntry.Unfocus();
            WarmupEntry.Unfocus();
            SetEntry.Unfocus();
            if (NotesEnrty.IsFocused)
                NotesEnrty.Unfocus();
        }
        catch (Exception ex)
        {

        }
    }
    void NotesEntry_Unfocused(object sender, FocusEventArgs e)
    {
        SaveCustomSettings();
    }


/* Unmerged change from project 'DrMaxMuscle (net8.0-ios)'
Before:
    void ResetButton_Clicked(object sender, EventArgs e)
After:
    void ResetButton_ClickedAsync(object sender, EventArgs e)
*/
    async void ResetButton_ClickedAsync(object sender, EventArgs e)
    {
        var mi = ((Button)sender);

        ExerciceModel m = (ExerciceModel)mi.CommandParameter;

        var exerciseLabel = CurrentLog.Instance?.WorkoutTemplateCurrentExercise?.Label ?? "";

        var ShowPopUp = await HelperClass.DisplayCustomPopup("Delete history", $"Delete ALL {exerciseLabel} history for ALL workouts? This cannot be undone.",
        "Delete all", AppResources.Cancel);
        ShowPopUp.ActionSelected += async (sender,action) => {

                    if (action == Views.PopupAction.OK)
                    {
                        ResetExercisesAction(m);
                    }
                    
            };
           

        // ConfirmConfig p = new ConfirmConfig()
        // {
        //     Title = "Delete history",//AppResources.ResetExercise,
        //     Message = $"Delete ALL {CurrentLog.Instance.WorkoutTemplateCurrentExercise.Label} history for ALL workouts? This cannot be undone.",// string.Format("Are you sure you want to reset this exercise and delete all its history? This cannot be undone.", m.Label),
        //     OkText = "Delete all",
        //     CancelText = AppResources.Cancel,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
        // };
        // p.OnAction = (obj) =>
        // {
        //     if (obj)
        //     {
        //         ResetExercisesAction(m);
        //     }
        // };
        // UserDialogs.Instance.Confirm(p);
    }

    public void LoadCustomReps()
    {
        try
        {
            RepsMinimumLabel.Text = LocalDBManager.Instance.GetDBSetting("repsminimum").Value;
            RepsMaximumLabel.Text = LocalDBManager.Instance.GetDBSetting("repsmaximum").Value;
        }
        catch (Exception)
        {
            ConnectionErrorPopup();

        }

    }

    public async void ResetExercisesAction(ExerciceModel model)
    {
        BooleanModel result = await DrMuscleRestClient.Instance.ResetExercise(new ExerciceModel()
        {
            Id = CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id
        });
        ResetReco();
    }

    private async void VideoInstructionButton_Clicked(object sender, EventArgs e)
    {
        try
        {
            CurrentLog.Instance.VideoExercise = CurrentLog.Instance.WorkoutTemplateCurrentExercise;
            if (Device.RuntimePlatform.Equals(Device.iOS))
                DependencyService.Get<IOrientationService>().Landscape();

            await Navigation.PushAsync(new ExerciseVideoPage());
        }
        catch (Exception ex)
        {

        }
    }


    void UnitEntry_TextChanged(object sender, TextChangedEventArgs e)
    {
        const string textRegex = @"^\d+(?:[\.,]\d{0,2})?$";
        var text = e.NewTextValue.Replace(",", ".");
        bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        if (IsValid == false && !string.IsNullOrEmpty(e.NewTextValue))
        {
            ((Entry)sender).Text = e.NewTextValue.Substring(0, e.NewTextValue.Length - 1);
        }
    }

    private async void SaveCustomSettings()
    {
        if (IsLoading)
            return;

        var exerciseSettings = new ExerciseSettingsModel();
        exerciseSettings.Id = CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id;
        exerciseSettings.Notes = NotesEnrty.Text;
        exerciseSettings.IsCustomIncrements = IncrementSwitch.IsToggled;
        ResetReco();
        if (WarmupSwitch.IsToggled)
        {
            if (!string.IsNullOrEmpty(WarmupEntry.Text))
            {
                var text = WarmupEntry.Text.Replace(",", ".");
                var result = Convert.ToInt32(text, System.Globalization.CultureInfo.InvariantCulture);
                if (string.IsNullOrEmpty(result.ToString()))
                {

                }
                else
                {
                    exerciseSettings.IsCustomWarmups = true;
                    exerciseSettings.WarmupsValue = result;
                }
            }
        }

        if (IncrementSwitch.IsToggled)
        {
            if (!string.IsNullOrEmpty(UnitEntry.Text))
            {
                var text = UnitEntry.Text.Replace(",", ".");
                var result = Convert.ToDecimal(text, System.Globalization.CultureInfo.InvariantCulture);
                if (string.IsNullOrEmpty(result.ToString()) || result.ToString().Equals("0"))
                {

                }
                else
                {
                    var IncrementsUnit = new MultiUnityWeight((decimal)result, LocalDBManager.Instance.GetDBSetting("massunit")?.Value);
                    exerciseSettings.Increments = IncrementsUnit;
                }
            }
            if (!string.IsNullOrEmpty(MinEntry.Text))
            {
                var minValue = MinEntry.Text.Replace(",", ".");
                var minResult = Convert.ToDecimal(minValue, System.Globalization.CultureInfo.InvariantCulture);
                if (string.IsNullOrEmpty(minResult.ToString()))
                {

                }
                else
                {
                    var MinVal = new MultiUnityWeight((decimal)minResult, LocalDBManager.Instance.GetDBSetting("massunit")?.Value);
                    exerciseSettings.Min = MinVal;
                }
            }
            if (!string.IsNullOrEmpty(MaxEntry.Text))
            {
                var maxValue = MaxEntry.Text.Replace(",", ".");
                var maxResult = Convert.ToDecimal(maxValue, System.Globalization.CultureInfo.InvariantCulture);
                if (string.IsNullOrEmpty(maxResult.ToString()) || maxResult.ToString().Equals("0"))
                {

                }
                else
                {
                    var MaxVal = new MultiUnityWeight((decimal)maxResult, LocalDBManager.Instance.GetDBSetting("massunit")?.Value);
                    exerciseSettings.Max = MaxVal;
                }
            }
            if (exerciseSettings.Max != null && exerciseSettings.Min != null)
            {
                if (exerciseSettings.Min.Kg > exerciseSettings.Max.Kg)
                {
                    // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = AppResources.MinValueShouldNotGreaterThenMax,
                    //     Title = AppResources.Error
                    // });

                    await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
                            AppResources.MinValueShouldNotGreaterThenMax,AppResources.Ok,"");

                    return;
                }
            }
        }
        exerciseSettings.IsCustomReps = CustomRepsSwitch.IsToggled;
        exerciseSettings.IsCustomSets = CustomSetsSwitch.IsToggled;
        if (CustomRepsSwitch.IsToggled)
        {
            int RepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int RepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);
            exerciseSettings.RepsMinValue = RepsMinimum;
            exerciseSettings.RepsMaxValue = RepsMaximum;
            RepsStack.IsVisible = true;
        }
        if (CustomSetsSwitch.IsToggled)
        {
            if (style == 4)
            {
                exerciseSettings.IsDropSet = true;
                exerciseSettings.IsNormalSets = null;
            }
            else if (style == 0)
                exerciseSettings.IsNormalSets = false;
            else if (style == 1)
                exerciseSettings.IsNormalSets = true;
            else if (style == 3)
            {
                exerciseSettings.IsNormalSets = false;
                exerciseSettings.IsPyramid = true;
            }
            else
                exerciseSettings.IsNormalSets = null;
        }
        if (!string.IsNullOrEmpty(SetEntry.Text))
        {
            var count = int.Parse(SetEntry.Text.ReplaceWithDot());
            if (count >= 2 || count <= 99)
            {
                exerciseSettings.SetCount = count;
            }
        }
        else
        {
            exerciseSettings.SetCount = null;
        }
        if (BackOff == 0)
            exerciseSettings.IsBackOffSet = null;
        else if (BackOff == 1)
            exerciseSettings.IsBackOffSet = true;
        else
            exerciseSettings.IsBackOffSet = false;
        exerciseSettings.IsFavorite = FavoriteSwitch.IsToggled;
        //exerciseSettings.IsDefaultUnilateral = isUnilateralSetup;
        if (UnilateralSwitch.IsToggled)
            exerciseSettings.IsDefaultUnilateral = true;
        else
            exerciseSettings.IsDefaultUnilateral = null;
        await DrMuscleRestClient.Instance.AddUpdateUserDefaultExerciseSettings(exerciseSettings);
    }

    void BtnDefault_Clicked(System.Object sender, System.EventArgs e)
    {
        DefaultClick();
        SaveCustomSettings();

    }

    void BtnYes_Clicked(System.Object sender, System.EventArgs e)
    {
        YesClicked();
        SaveCustomSettings();
    }

    void BtnNo_Clicked(System.Object sender, System.EventArgs e)
    {
        NoClicked();
        SaveCustomSettings();
    }

    public Color GetTransparentGradient()
    {
        return Colors.Transparent;
    }

    public Color GetBlueGradient()
    {
        return Constants.AppThemeConstants.BlueColor;
    }
    void DefaultClick()
    {
        BackOff = 0;
        BtnYes.BackgroundColor = Colors.Transparent;
        YesGradient.BackgroundColor = GetTransparentGradient();
        DefaultGradient.BackgroundColor = GetBlueGradient();
        BtnYes.TextColor = Color.FromHex("#0C2432");
        BtnDefault.TextColor = Colors.White;

        BtnNo.TextColor = Color.FromHex("#0C2432");
        NoGradient.BackgroundColor = GetTransparentGradient();
        BtnNo.BackgroundColor = Colors.Transparent;
    }


    void YesClicked()
    {
        BackOff = 1;
        BtnYes.BackgroundColor = Colors.Transparent;
        //BtnKg.BackgroundColor = Color.FromHex("#5CD196");
        BtnYes.TextColor = Colors.White;
        BtnDefault.TextColor = Color.FromHex("#0C2432");
        YesGradient.BackgroundColor = GetBlueGradient();
        DefaultGradient.BackgroundColor = GetTransparentGradient();

        BtnNo.TextColor = Color.FromHex("#0C2432");
        NoGradient.BackgroundColor = GetTransparentGradient();

    }


    void NoClicked()
    {
        BackOff = 2;
        BtnDefault.BackgroundColor = Colors.Transparent;
        BtnDefault.TextColor = Color.FromHex("#0C2432");
        BtnYes.TextColor = Color.FromHex("#0C2432");
        YesGradient.BackgroundColor = GetTransparentGradient();
        DefaultGradient.BackgroundColor = GetTransparentGradient();

        BtnNo.TextColor = Colors.White; ;
        NoGradient.BackgroundColor = GetBlueGradient();
    }

    void SetEntry_TextChanged(System.Object sender, TextChangedEventArgs e)
    {
        const string textRegex = @"^\d+(?:\d{0,2})?$";
        var text = e.NewTextValue.Replace(",", ".");
        bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        if (IsValid == false && !string.IsNullOrEmpty(e.NewTextValue))
        {
            ((Entry)sender).Text = e.NewTextValue.Substring(0, e.NewTextValue.Length - 1);
        }
    }

    async Task ConnectionErrorPopup()
    {
        
        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        // {
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     Message = AppResources.PleaseCheckInternetConnection,
        //     Title = AppResources.ConnectionError,
        //     OkText = "Try again"
        // });

        await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                            AppResources.PleaseCheckInternetConnection,"Try again","");

        
    }
}

