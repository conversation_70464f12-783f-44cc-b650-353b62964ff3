using Controls.UserDialogs.Maui;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.Workouts;
using Microsoft.Maui.Networking;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Screens.Subscription;
using DrMaxMuscle.Views;
using RGPopup.Maui.Services;
using DrMaxMuscle.Utility;

namespace DrMaxMuscle.Screens.Exercises;

public partial class NewExercisePage : ContentPage
{
	public NewExercisePage()
	{
		InitializeComponent();
        DrMuscleWorkoutsButton.Clicked += DrMuscleWorkoutsButton_Clicked;
        ExercisesButton.Clicked += ExercisesButton_Clicked;
        MyWorkoutButton.Clicked += MyWorkoutButton_Clicked;
        RefreshLocalized();
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
    }

    private async void DrMuscleWorkoutsButton_Clicked(object sender, EventArgs e)
    {
        try
        {
            // uncomment code please
            //if (CrossConnectivity.Current.IsConnected)
            //{
            //    if (!App.IsV1UserTrial && !App.IsFreePlan)
            //    {
            //        await PagesFactory.PushAsync<SubscriptionPage>();
            //        return;
            //    }
            //}
            ChooseGymOrHome chooseGymOrHome = new ChooseGymOrHome();
            chooseGymOrHome.OnBeforeShow();
            await Navigation.PushAsync(chooseGymOrHome);
        }
        catch (Exception ex)
        {

        }
    }
    private async void ExercisesButton_Clicked(object sender, EventArgs e)
    {
        try
        {
            // uncomment code please
            var allExerPage = new AllExercisePage();
            allExerPage.OnBeforeShow();
            await Navigation.PushAsync(allExerPage);
        }
        catch (Exception ex)
        {

        }
    }

    private async void MyWorkoutButton_Clicked(object sender, EventArgs e)
    {
        try
        {
            // uncomment code please
            //if (CrossConnectivity.Current.IsConnected)
            //{
            //    if (!App.IsV1UserTrial && !App.IsFreePlan)
            //    {
            //        await PagesFactory.PushAsync<SubscriptionPage>();
            //        return;
            //    }
            //}
            if (App.IsFreePlan)
            {

                var ShowPopUp = await HelperClass.DisplayCustomPopup("You discovered a premium feature!", "Upgrading will unlock custom workouts and programs.",
             "Upgrade", "Maybe later");
                ShowPopUp.ActionSelected += async (sender, action) =>
                {
                    try
                    {
                        if (action == Views.PopupAction.OK)
                        {
                            Navigation.PushAsync(new SubscriptionPage());
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                };
                //await Task.Delay(100);



                // ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
                // {
                //     Message = "Upgrading will unlock custom workouts and programs.",
                //     Title = "You discovered a premium feature!",
                //     //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = "Upgrade",
                //     CancelText = "Maybe later",
                //     Action = async (bool ok) =>
                //     {
                //         if (ok)
                //         {
                //             Navigation.PushAsync(new SubscriptionPage());

                //         }
                //         else
                //         {

                //         }
                //     }
                // };
                // await Task.Delay(100);
                // UserDialogs.Instance.Confirm(ShowWelcomePopUp2);
            }
            // uncomment code please
            else
            {
                //await PagesFactory.PushAsync<ChooseYourCustomWorkoutPage>();
                var customWorkoutPage = new ChooseYourCustomWorkoutPage();
                await Navigation.PushAsync(customWorkoutPage);
            }
        }
        catch (Exception ex)
        {

        }
    }
    private void RefreshLocalized()
    {
        Title = "Exercise";
        //DrMuscleWorkoutsButton.Text = AppResources.DrMuscleWorkouts;
        //MyWorkoutButton.Text = AppResources.CustomWorkouts;
        DrMuscleWorkoutsButton.Text = "Workouts";
        MyWorkoutButton.Text = "Custom workouts";
        ExercisesButton.Text = AppResources.Exercises;
    }
    // uncomment code please
    //protected override bool OnBackButtonPressed()
    //{
        
    //    //if (PopupNavigation.Instance.PopupStack.Count > 0)
    //    //{
    //    //    PopupNavigation.Instance.PopAllAsync();
    //    //    return true;
    //    //}
    //    //Device.BeginInvokeOnMainThread(async () =>
    //    //{
    //    //    ConfirmConfig exitPopUp = new ConfirmConfig()
    //    //    {
    //    //        Title = AppResources.Exit,
    //    //        Message = AppResources.AreYouSureYouWantToExit,
    //    //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
    //    //        OkText = AppResources.Yes,
    //    //        CancelText = AppResources.No,
    //    //    };

            
    //        //var result = await UserDialogs.Instance.ConfirmAsync(exitPopUp);
    //        //if (result)
    //        //{
    //        //    var kill = DependencyService.Get<IKillAppService>();
    //        //    kill.ExitApp();
    //        //}

    //    });

    //    return true;
    //}
}
