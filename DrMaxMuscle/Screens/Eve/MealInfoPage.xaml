<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
            xmlns:local="clr-namespace:DrMaxMuscle.Cells"
            xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             BackgroundColor="#f4f4f4"
             x:Class="DrMaxMuscle.Screens.Eve.MealInfoPage"
             Title="MealInfoPage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <local:BotDataTemplateSelector
            x:Key="BotTemplateSelector">
            </local:BotDataTemplateSelector>
        </ResourceDictionary>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid>
        <StackLayout>
            <StackLayout
IsVisible="false"
x:Name="daysStack"
Orientation="Horizontal"
Spacing="0"
Padding="15,5"
Margin="0,10,0,-10"
HorizontalOptions="FillAndExpand"
                HeightRequest="70"
VerticalOptions="Start">
                <Frame
                    BorderColor="Transparent"
    BackgroundColor="Transparent"
    Padding="0"
    Margin="0"
    HasShadow="False"
    HeightRequest="35"
    WidthRequest="40"
    HorizontalOptions="Start">
                    <Image
        x:Name="PreviousArrow"
        Aspect="AspectFit"
        Rotation="90"
        VerticalOptions="CenterAndExpand"
        HorizontalOptions="CenterAndExpand"
        HeightRequest="14"
        Source="gradient_down_arrow"
    />
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Tapped="PreviousDay"/>
                    </Frame.GestureRecognizers>
                </Frame>
                <Label
    TextColor="#26262B"
    FontSize="20"
                    Padding="0"
                    Margin="0"
    FontAttributes="Bold"
    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
    VerticalOptions="Center"
                    HorizontalTextAlignment="Center"
    HorizontalOptions="FillAndExpand"
                    BackgroundColor="Transparent"
    x:Name="daysLabel"
    Text="Day 1"/>
                <Frame
                    BorderColor="Transparent"
    BackgroundColor="Transparent"
    Padding="0"
    Margin="0"
    HasShadow="False"
    HeightRequest="35"
    WidthRequest="40"
    HorizontalOptions="End">
                    <Image
    x:Name="NextArrow"
        Aspect="AspectFit"
        Rotation="270"
        VerticalOptions="CenterAndExpand"
        HorizontalOptions="CenterAndExpand"
        HeightRequest="14"
        Source="gradient_down_arrow"
    />
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Tapped="NextDay"/>
                    </Frame.GestureRecognizers>
                </Frame>
            </StackLayout>
            <StackLayout
IsVisible="false"
x:Name="GenerateBtnsStack"
HorizontalOptions="FillAndExpand"
                VerticalOptions="EndAndExpand"
Margin="20"
Padding="0">
                <t:DrMuscleButton
    Text="Generate day"
    FontSize="{OnPlatform Android='15', iOS= '17'}"
    HeightRequest ="55"
    CornerRadius="0"
    Style="{StaticResource buttonLinkStyle}"
    TextColor="{x:Static app:AppThemeConstants.BlueColor}" 
    BorderColor="{x:Static app:AppThemeConstants.BlueColor}" 
    BorderWidth="2"
    FontAttributes="Bold"
    Clicked="GenerateNewDayMeal"
    />

                <Frame
        Padding="0"
    x:Name="copyBtn"
        Margin="0,8,0,0"
        IsClippedToBounds="true"
        CornerRadius="0"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="Center"
    Style="{StaticResource GradientFrameStyleBlue}"
        HeightRequest="60">
                    <t:DrMuscleButton
       Text="Copy previous day"
        FontSize="{OnPlatform Android='15', iOS= '17'}"
        HeightRequest ="55"
        CornerRadius="0"
        
        Clicked="CopyFromPrevious"
        Style="{StaticResource buttonLinkStyle}"
        FontAttributes="Bold"
        BackgroundColor="Transparent"
        BorderColor="Transparent"
        TextColor="White" />
                </Frame>

            </StackLayout>
            <Grid
            BackgroundColor="#f4f4f4"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand"
            x:Name="MainGrid"
            RowSpacing="1"
            Padding="0">
                <Grid.RowDefinitions>
                    <RowDefinition
                    Height="*" />
                    <RowDefinition
                    Height="1" />
                    <RowDefinition
                    x:Name="BottomViewHeight"
                    Height="5" />
                </Grid.RowDefinitions>
                <CollectionView
                Margin="2,10,2,0"
                Grid.Row="0"
                BackgroundColor="#f4f4f4"
                ItemTemplate="{StaticResource BotTemplateSelector}"
                x:Name="lstChats"
                
                SelectionMode="None"
                VerticalOptions="FillAndExpand"
                FlowDirection="LeftToRight">

                </CollectionView>

                <StackLayout
                Grid.Row="2"
                Margin="0,0,0,10"
                BackgroundColor="Transparent"
                VerticalOptions="EndAndExpand"
                x:Name="stackOptions" >
                    <!--
                -->
                    <!--<Image Source="PlusBlack.png" x:Name="FabImage" Margin="0,0,20,20" Grid.Row="0" Grid.RowSpan="3" HeightRequest="70" WidthRequest="70" VerticalOptions="End" HorizontalOptions="End" Aspect="AspectFit" >
            <Image.GestureRecognizers>
                <TapGestureRecognizer Tapped="NewTapped" />
            </Image.GestureRecognizers>

        </Image>-->
                    <!--
                <StackLayout x:Name="ActionStack" IsVisible="false" Grid.Row="0" Grid.RowSpan="3" BackgroundColor="#55000000" VerticalOptions="FillAndExpand" Padding="20,5,20,0" AbsoluteLayout.LayoutFlags="All"  AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
                    <StackLayout VerticalOptions="EndAndExpand" Spacing="10">
                        <Frame
                            Margin="0,15,0,0"
                            Style="{StaticResource GradientFrameStyleBlue}"
                            HorizontalOptions="FillAndExpand" CornerRadius="0" >
                            

                            <Button x:Name="newMealPlanButton" Text="Get a new meal plan"
Clicked="BtnAddMealPref_Clicked"
Style="{StaticResource highEmphasisButtonStyle}" BorderColor="Transparent" BackgroundColor="Transparent" VerticalOptions="End" />
                        </Frame>

                        <Image Source="PlusBlack.png"  Margin="0,0,0,20" HeightRequest="70" WidthRequest="70" VerticalOptions="End" HorizontalOptions="End" Aspect="AspectFit"  >
                            <Image.GestureRecognizers>
                                <TapGestureRecognizer Tapped="NewTapped" />
                            </Image.GestureRecognizers>
                        </Image>

                    </StackLayout>
                    <StackLayout.GestureRecognizers>
                        <TapGestureRecognizer Tapped="NewTapped" />
                    </StackLayout.GestureRecognizers>
                -->
                </StackLayout>

            </Grid>
        </StackLayout>
        <t:RightSideMasterPage Padding="0" Margin="0" IsVisible="False" x:Name="SlideMenu" HorizontalOptions="EndAndExpand" VerticalOptions="FillAndExpand"/>
            
        </Grid>
    </ContentPage.Content>
</ContentPage>