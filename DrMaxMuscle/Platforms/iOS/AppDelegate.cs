﻿using Foundation;
using UIKit;
using System;
using System.Collections.Generic;
using System.Linq;


using System.Diagnostics;
using System.IO;
using AVFoundation;
using Plugin.Vibrate;
using Microsoft.AppCenter;

using System.Threading.Tasks;
using RGPopup.Maui;
using MediaPlayer;
using UserNotifications;
using Microsoft.AppCenter.Crashes;
//using Firebase.Crashlytics;
using ObjCRuntime;
//using BranchXamarinSDK;
//using DrMuscleWatch;
using FFImageLoading.Transformations;
using Microsoft.Maui.Controls.Compatibility.Platform.iOS;
using DrMaxMuscle.Message;
using DrMaxMuscle.Helpers;
using BranchSDK;
using CarPlay;
// using Plugin.GoogleClient.MAUI;
using static Sentry.MeasurementUnit;
using Rollbar;
using RGPopup.Maui.IOS;
using BranchSDK.iOS;
using Plugin.Firebase.Auth;
using Plugin.Firebase.Auth.Google;
using DrMaxMuscle.Plateforms.iOS.Firebase;
using Plugin.Firebase.Bundled.Platforms.iOS;
using Plugin.Firebase.Bundled.Shared;

//using Sentry;
namespace DrMaxMuscle
{
    [Register("AppDelegate")]
    public class AppDelegate : MauiUIApplicationDelegate, IBranchSessionInterface
    {
        bool _isIOS13, _isMute = false;
        AVAudioPlayer _player, player;
        AVAudioPlayer _backgroundplayer;
        NSTimer timer;
        public bool allowRotation;

        protected override MauiApp CreateMauiApp() => MauiProgram.CreateMauiApp();

        public override bool FinishedLaunching(UIApplication app, NSDictionary options)
        {

            //App.ScreenWidth = UIScreen.MainScreen.Bounds.Size.Width;
            //App.ScreenHeight = UIScreen.MainScreen.Bounds.Size.Height;
            ObjCRuntime.Class.ThrowOnInitFailure = false;

            Branch.EnableLogging = true;
            BranchIOS.Init("key_test_hEm21E3XksHK81qboYkjjleoEvop2CXH", options, this);
            //             BranchSDK.BranchIOS.Init("key_test_dummy123", launchOptions: options, callback: (paramsObj) => {
            //     // your callback code here
            // });


            Task.Run(async () =>
            {
                try
                {
                    // Set a timeout for each initialization task to avoid blocking too long.
                    await Task.WhenAll(
                        //  Task.Run(() => RollbarHelper.ConfigureRollbar()).TimeoutAfter(TimeSpan.FromSeconds(5)),
                        // Task.Run(() => CrossFirebase.Initialize()).TimeoutAfter(TimeSpan.FromSeconds(5)),
                        // Task.Run(() => RollbarHelper.RegisterForGlobalExceptionHandling()).TimeoutAfter(TimeSpan.FromSeconds(15)),
                        // Task.Run(() => CrossFirebase.Initialize(new CrossFirebaseSettings(isAuthEnabled: true, isCloudMessagingEnabled: true, isCrashlyticsEnabled: true, isAnalyticsEnabled: true))).TimeoutAfter(TimeSpan.FromSeconds(5)),
                        Task.Run(() => AppCenter.Start("2cf9718a-f817-43b4-b7c4-9bbd54d00221", typeof(Crashes))).TimeoutAfter(TimeSpan.FromSeconds(5))
                    );
                }
                catch (TimeoutException ex)
                {
                    Console.WriteLine($"Initialization timed out: {ex.Message}");
                    // Handle the timeout gracefully, possibly retry or notify the user
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Initialization error: {ex.Message}");
                    // Handle other initialization errors
                }
            });
            //// configure Rollbar:
            //RollbarHelper.ConfigureRollbar();
            //CrossFirebase.Initialize();
            ////subscribe to all known unhandled exception events application-wide:
            //RollbarHelper.RegisterForGlobalExceptionHandling();
            //AppCenter.Start("2cf9718a-f817-43b4-b7c4-9bbd54d00221", typeof(Crashes));
            //SentryXamarin.Init(option =>
            //{
            //    option.Dsn = "https://<EMAIL>/4505860883283968";
            //    // When configuring for the first time, to see what the SDK is doing:
            //    option.Debug = true;
            //    // Set TracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring.
            //    // We recommend adjusting this value in production.
            //    option.TracesSampleRate = 1.0;
            //    option.AddXamarinFormsIntegration();
            //    option.AttachScreenshots = true;
            //});

            //Task.Factory.StartNew(async () =>
            //{
            //    await Task.Delay(4000);
            //    PurchaseManager manager = PurchaseManager.Instance;
            //    SegmentedControlRenderer.Init();
            //});
            //SlideOverKit.iOS.SlideOverKit.Init();
            //Firebase.Core.App.Configure();

            //FirebasePushNotificationManager.Initialize(options, true);
            App.StatusBarHeight = UIApplication.SharedApplication.StatusBarFrame.Height;
            //ShapeRenderer.Init();
            //Facebook.CoreKit.ApplicationDelegate.SharedInstance.FinishedLaunching(app, options);
            //FFImageLoading.Forms.Platform.CachedImageRenderer.Init();
            // set Debug mode
            var ignore = new CornersTransformation();
            Popup.Init();


            App.ScreenWidth = UIScreen.MainScreen.Bounds.Size.Width;
            App.ScreenHeight = UIScreen.MainScreen.Bounds.Size.Height;
            // LoadApplication(new App());
            UIApplication.SharedApplication.IdleTimerDisabled = true;
            _isIOS13 = UIDevice.CurrentDevice.CheckSystemVersion(13, 0);




            MessagingCenter.Subscribe<StartTimerMessage>(this, "StartTimerMessage", async message =>
            {
                nint taskId = UIApplication.SharedApplication.BeginBackgroundTask("TimerStartTask", OnExpiration);
                try
                {
                    Timer.Instance.PCLStartTimer();
                    while (Timer.Instance?.State == "RUNNING")
                    {
                        await System.Threading.Tasks.Task.Delay(TimeSpan.FromSeconds(1));
                        Debug.WriteLine("StartTimerMessage Subscribe calling Seconds...");
                    }
                    //if (!UIDevice.CurrentDevice.CheckSystemVersion(13, 0))
                }
                catch (Exception ex)
                {
                }
                finally
                {
                    UIApplication.SharedApplication.EndBackgroundTask(taskId);
                }
            });
            MessagingCenter.Subscribe<StopTimerMessage>(this, "StopTimerMessageOff", async message =>
            {
                nint taskId = UIApplication.SharedApplication.BeginBackgroundTask("TimerStopTask", OnExpiration);
                try
                {
                    await Timer.Instance.PCLStopTimer();
                    while (Timer.Instance?.State == "RUNNING")
                    {
                        await System.Threading.Tasks.Task.Delay(TimeSpan.FromSeconds(1));
                    }
                    //if (!UIDevice.CurrentDevice.CheckSystemVersion(13, 0))
                }
                catch (Exception ex)
                {

                }
                finally
                {
                    UIApplication.SharedApplication.EndBackgroundTask(taskId);
                }
            });

            MessagingCenter.Subscribe<StopTimerMessage>(this, "StopTimerMessage", async message =>
            {
                nint taskId = UIApplication.SharedApplication.BeginBackgroundTask("TimerStopTask", OnExpiration);
                try
                {
                    await Timer.Instance.PCLStopTimer();
                    while (Timer.Instance.State == "RUNNING")
                    {
                        await System.Threading.Tasks.Task.Delay(TimeSpan.FromSeconds(1));
                    }
                }
                catch (Exception ex)
                {

                }
                finally
                {
                    UIApplication.SharedApplication.EndBackgroundTask(taskId);
                }

            });

            MessagingCenter.Subscribe<PlayAudioFileMessage>(this, "PlayAudioFileMessage", async message =>
            {
                //nint taskId = UIApplication.SharedApplication.BeginBackgroundTask("PlaySoundTask", OnExpiration);
                try
                {

                    NSError error;
                    AVAudioSession instance = AVAudioSession.SharedInstance();
                    instance.SetCategory(new NSString("AVAudioSessionCategoryPlayback"), AVAudioSessionCategoryOptions.MixWithOthers, out error);

                    instance.SetMode(new NSString("AVAudioSessionModeDefault"), out error);
                    instance.SetActive(true, AVAudioSessionSetActiveOptions.NotifyOthersOnDeactivation, out error);
                    if (message.IsEmptyAudio)
                    {
                        var soundFile = "emptyAudio.mp3";

                        string sFilePath = NSBundle.MainBundle.PathForResource(Path.GetFileNameWithoutExtension(soundFile), Path.GetExtension(soundFile));
                        var url = NSUrl.FromString(sFilePath);
                        _player = AVAudioPlayer.FromUrl(url);
                        _player.FinishedPlaying += (object sender, AVStatusEventArgs e) =>
                        {
                            _player = null;
                        };
                        //if (!_isMute)
                        _player.SetVolume(message.Volume, message.Volume);
                        _player.Play();
                        return;
                    }

                    if (message.IsFromComplete)
                    {
                        var soundFile = "complete_workout.mp3";

                        string sFilePath = NSBundle.MainBundle.PathForResource(Path.GetFileNameWithoutExtension(soundFile), Path.GetExtension(soundFile));
                        var url = NSUrl.FromString(sFilePath);
                        _player = AVAudioPlayer.FromUrl(url);
                        _player.SetVolume(message.Volume, message.Volume);
                        _player.FinishedPlaying += (object sender, AVStatusEventArgs e) =>
                        {
                            _player = null;
                        };
                        //if (!_isMute)
                        _player.Play();
                        Vibration.Vibrate(TimeSpan.FromSeconds(2));
                        return;
                    }

                    if (message.Is321)
                    {
                        var soundFile = "timer123.mp3";

                        string sFilePath = NSBundle.MainBundle.PathForResource(Path.GetFileNameWithoutExtension(soundFile), Path.GetExtension(soundFile));
                        var url = NSUrl.FromString(sFilePath);
                        player = AVAudioPlayer.FromUrl(url);
                        _player.SetVolume(message.Volume, message.Volume);
                        player.FinishedPlaying += (object sender, AVStatusEventArgs e) =>
                        {
                            player = null;
                        };
                        //if (!_isMute)
                        player.Play();
                        return;
                    }

                    if (LocalDBManager.Instance.GetDBSetting("timer_sound")?.Value == "true" || LocalDBManager.Instance.GetDBSetting("timer_reps_sound")?.Value == "true")
                    {
                        var soundFile = "alarma.mp3";
                        if (Timer.Instance.NextRepsCount <= 0 || Timer.Instance.NextRepsCount > 60)
                        {

                        }
                        else if (LocalDBManager.Instance.GetDBSetting("timer_reps_sound")?.Value == "true")
                        {
                            soundFile = $"reps{Timer.Instance.NextRepsCount}.mp3";
                        }

                        string sFilePath = NSBundle.MainBundle.PathForResource(Path.GetFileNameWithoutExtension(soundFile), Path.GetExtension(soundFile));
                        var url = NSUrl.FromString(sFilePath);
                        _player = AVAudioPlayer.FromUrl(url);
                        _player.SetVolume(message.Volume, message.Volume);
                        _player.FinishedPlaying += (object sender, AVStatusEventArgs e) =>
                        {
                            _player = null;
                        };
                        //if (!_isMute)
                        _player.Play();
                    }



                    if (LocalDBManager.Instance.GetDBSetting("timer_vibrate")?.Value == "true")
                        Vibration.Vibrate(TimeSpan.FromSeconds(5));

                    while (Timer.Instance.State == "RUNNING")
                    {
                        await System.Threading.Tasks.Task.Delay(TimeSpan.FromSeconds(5));
                    }


                }
                catch (Exception ex)
                {

                }
            });
            if (options != null && options.ContainsKey(UIApplication.LaunchOptionsRemoteNotificationKey))
            {
                HandleNoticationTapped();
            }
            if (options != null && options.ContainsKey(UIApplication.LaunchOptionsLocalNotificationKey))
            {
                var localNotification = options[UIApplication.LaunchOptionsLocalNotificationKey] as UILocalNotification;
                if (localNotification != null)
                {
                    if (localNotification.UserInfo != null && localNotification.UserInfo.ContainsKey(new NSString($"DrMuscleNotification1151")))
                    {
                        CurrentLog.Instance.IsRecoveredWorkout = true;
                        HandleLocalNoticationTapped();
                    }

                    if (localNotification.UserInfo != null && localNotification.UserInfo.ContainsKey(new NSString($"DrMuscleNotification1352")))
                    {
                        var workoutId = localNotification.UserInfo["Extra"];
                        CurrentLog.Instance.IsRecoveredWorkout = true;
                        HandleWorkoutLocalNoticationTapped(Convert.ToString(workoutId));
                    }
                }


            }

            try
            {
                UINavigationBar.Appearance.SetBackgroundImage(UIImage.FromFile("topnav.png"), UIBarMetrics.Default);
                UINavigationBar.Appearance.SetBackgroundImage(UIImage.FromFile("topnav.png"), UIBarMetrics.Compact);
                UINavigationBar.Appearance.Translucent = false;
            }
            catch (Exception ex)
            {

            }


            //WCSessionManager.SharedManager.StartSession();
            try
            {
                if (Window != null)
                {
                    Window.RootViewController.View.SemanticContentAttribute = UISemanticContentAttribute.ForceLeftToRight;
                    foreach (var subview in Window.RootViewController.View.Subviews)
                    {
                        subview.SemanticContentAttribute = UISemanticContentAttribute.ForceLeftToRight;
                    }
                }
            }
            catch (Exception ex)
            {

            }
            RegisterForRemoteNotifications(app);
            // Set a custom User-Agent globally
            NSUserDefaults.StandardUserDefaults.RegisterDefaults(new NSDictionary(
                "UserAgent", "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1"
            ));
            Task.Delay(1000).Wait();
            CrossFirebase.Initialize(new CrossFirebaseSettings(isAuthEnabled: true, isCloudMessagingEnabled: true, isCrashlyticsEnabled: false, isAnalyticsEnabled: true));
            //CrossFirebase.Initialize(new CrossFirebaseSettings(isAuthEnabled: true, isCloudMessagingEnabled: true, isCrashlyticsEnabled: true, isAnalyticsEnabled: true));
            MyFirebaseAuthGoogleImplementation.Instance = new MyFirebaseAuthGoogleImplementation();
            MyFirebaseAuthGoogleImplementation.Initialize();
            return base.FinishedLaunching(app, options);
        }
        private void HandleNoticationTapped()
        {
            try
            {
                System.Threading.Tasks.Task.Run(async () =>
                    {
                        //Add your code here.
                        await Task.Delay(12000);
                        MessagingCenter.Send<NavigationOnNotificationTappedMessage>(new NavigationOnNotificationTappedMessage(""), "NavigationOnNotificationTappedMessage");
                    }).ConfigureAwait(false);
            }
            catch (Exception ex)
            {

            }

        }

        public void InitSessionComplete(Dictionary<string, object> data)
    {
        LogMessage("InitSessionComplete: ");
        foreach (var key in data.Keys)
        {
            LogMessage(key + " : " + data[key].ToString());
        }
    }

    public void SessionRequestError(BranchError error)
    {
        LogMessage("SessionRequestError: ");
        LogMessage("Error Message: " + error.ErrorMessage);
        LogMessage("Error Code: " + error.ErrorCode);
    }

    void LogMessage(string message)
    {
        Console.WriteLine(message);
    }



        public override void OnActivated(UIApplication application)
        {
            try
            {
                base.OnActivated(application);
            }
            catch (Exception ex)
            {

            }
        }

        [Export("application:didFailToContinueUserActivityWithType:error:")]
        public void DidFailToContinueUserActivity(UIApplication application, string userActivityType, NSError error)
        {
            try
            {
                // collect data about the NSError:
                IDictionary<string, object> custom = new Dictionary<string, object>();
                custom["NSError.Description"] = error.Description;
                custom["NSError.DebugDescription"] = error.DebugDescription;
                custom["NSError.Code"] = error.Code;
                custom["NSError.Domain"] = error.Domain;
                custom["NSError.LocalizedDescription"] = error.LocalizedDescription;
                custom["NSError.LocalizedFailureReason"] = error.LocalizedFailureReason;
                custom["NSError.LocalizedRecoveryOptions"] = error.LocalizedRecoveryOptions;
                custom["NSError.LocalizedRecoverySuggestion"] = error.LocalizedRecoverySuggestion;

                // capture the user activity type:
                string message = "NSError during user activity type: " + userActivityType;

                // report the error to Rollbar:
                RollbarLocator
                  .RollbarInstance
                  .AsBlockingLogger(RollbarHelper.RollbarTimeout)
                  .Error(message, custom);

            }
            catch (Exception ex)
            {

            }
            
        }
        //public override void OnActivated(UIApplication application)
        //{
        //    (application.ConnectedScenes.AnyObject as UIWindowScene)?.Windows.FirstOrDefault()?.MakeKeyWindow();
        //    base.OnActivated(application);
        //}

        public override void OnResignActivation(UIApplication application)
        {
            try
            {
                // Safely access the window scene
                var windowScene = application.ConnectedScenes.AnyObject as UIWindowScene;
                if (windowScene != null)
                {
                    var window = windowScene.Windows.FirstOrDefault();
                    if (window != null)
                    {
                        window.MakeKeyWindow();
                    }
                }
                base.OnResignActivation(application);

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in OnResignActivation: {ex.Message}");
                // Continue with base implementation even if our code fails
                base.OnResignActivation(application);
            }
        }

        private void HandleLocalNoticationTapped()
        {
            System.Threading.Tasks.Task.Run(async () =>
            {
                //Add your code here.
                await Task.Delay(4000);
                MessagingCenter.Send<NavigationOnNotificationTappedMessage>(new NavigationOnNotificationTappedMessage("Local"), "NavigationOnNotificationTappedMessage");
            }).ConfigureAwait(false);

        }
        private void HandleWorkoutLocalNoticationTapped(string workoutId)
        {
            System.Threading.Tasks.Task.Run(async () =>
            {
                //Add your code here.
                await Task.Delay(4000);
                MessagingCenter.Send<NavigationOnNotificationTappedMessage>(new NavigationOnNotificationTappedMessage("Workout", workoutId), "NavigationOnNotificationTappedMessage");
            }).ConfigureAwait(false);

        }
        void OnExpiration()
        {
            bool test = true;
        }

        [Export("applicationDidEnterBackground:")]
        public async void DidEnterBackground(UIApplication application)
        {
            nint taskId = UIApplication.SharedApplication.BeginBackgroundTask("BackgroundWorkTimerInBackground", OnExpiration);
            try
            {
                if (_isIOS13 && Timer.Instance.Remaining > 0 && Timer.Instance.State == "RUNNING")
                {
                    LocalDBManager.Instance?.SetDBSetting("LastBackgroundTimee", DateTime.Now.Ticks.ToString());
                    LocalDBManager.Instance?.SetDBSetting("LastBackgroundTimerTime", Timer.Instance.Remaining.ToString());
                    RegisterNotification(Timer.Instance.Remaining);
                    Timer.Instance.StopAllTimer();
                    Timer.Instance.TimerDone();
                }
            }
            catch (Exception ex)
            {

            }
            finally
            {
                UIApplication.SharedApplication.EndBackgroundTask(taskId);
            }
        }


        [Export("applicationWillEnterForeground:")]
        public void WillEnterForeground(UIApplication application)
        {
            try
            {

                UIApplication.SharedApplication.ApplicationIconBadgeNumber = 0;

                CheckNotification();
            }
            catch (Exception ex)
            {

            }
        }
        private async void CheckNotification()
        {
            try
            {


                if (_isIOS13)
                {

                    var notifications = await UNUserNotificationCenter.Current.GetPendingNotificationRequestsAsync();

                    //if (notifications.Count() > 0)
                    //{
                    if (LocalDBManager.Instance.GetDBSetting("email") == null)
                        return;
                    var ticks = LocalDBManager.Instance.GetDBSetting("LastBackgroundTimee")?.Value;
                    //
                    var timerTime = LocalDBManager.Instance.GetDBSetting("LastBackgroundTimerTime")?.Value;
                    timerTime = timerTime == null ? "0" : timerTime;

                    var date = new DateTime(long.Parse(ticks == null ? "0" : ticks));

                    var seconds = (DateTime.Now - date).TotalSeconds;
                    var remaininTime = Convert.ToDouble(LocalDBManager.Instance.GetDBSetting("timer_remaining").Value);
                    MessagingCenter.Send<EnterForegroundMessage>(new EnterForegroundMessage(), "EnterForegroundMessage");
                    if (seconds > double.Parse(timerTime))
                        Timer.Instance.Remaining = 0;
                    else
                        Timer.Instance.Remaining = (int)(double.Parse(timerTime) - seconds);
                    var remaining = Timer.Instance.Remaining;
                    _isMute = true;
                    if (remaining > 0)
                    {

                        _isMute = false;
                        Timer.Instance.stopRequest = true;
                        var val = LocalDBManager.Instance.GetDBSetting("timer_remaining").Value;
                        LocalDBManager.Instance.SetDBSetting("timer_remaining", remaining.ToString());
                        await Timer.Instance.StartTimer();
                        Timer.Instance.Remaining = remaining;
                        LocalDBManager.Instance.SetDBSetting("timer_remaining", val);
                    }
                    else
                    {
                        await Task.Delay(2000);
                        _isMute = false;
                    }
                    //}
                    //UNUserNotificationCenter.Current.RemoveAllDeliveredNotifications();
                    //UIApplication.SharedApplication.CancelAllLocalNotifications();

                }
            }
            catch (Exception ex)
            {

            }
        }
        [Export("application:openURL:options:")]
        public bool OpenUrl(UIApplication app, NSUrl url, NSDictionary options)
        {
            System.Diagnostics.Debug.WriteLine($"options={options}");

            //var google = GoogleClientManager.OnOpenUrl(app, url, options);
            //if (google)
            //    return google;
            var openOptions = new UIApplicationOpenUrlOptions(options);

            BranchIOS.getInstance().OpenUrl(url);


            return true;
        }

        [Export("application:openURL:sourceApplication:annotation:")]
        public bool OpenUrl(UIApplication application, NSUrl url, string sourceApplication, NSObject annotation)
        {
            //ApplicationDelegate.SharedInstance.OpenUrl(application, url, sourceApplication, annotation);
            BranchIOS.getInstance().OpenUrl(url);

            return true;
        }

        
        [Export("application:didRegisterForRemoteNotificationsWithDeviceToken:")]
        public void RegisteredForRemoteNotifications(UIApplication application, NSData deviceToken)
        {

        }

        [Export("application:didFailToRegisterForRemoteNotificationsWithError:")]
        public void FailedToRegisterForRemoteNotifications(UIApplication application, NSError error)
        {

        }
        public void RegisterNotification(long time)
        {
            try
            {
                UNUserNotificationCenter center = UNUserNotificationCenter.Current;

                //creat a UNMutableNotificationContent which contains your notification content
                UNMutableNotificationContent notificationContent = new UNMutableNotificationContent();

                notificationContent.Title = "Rest over";
                notificationContent.Body = "Get back to work!";
                if (Timer.Instance.NextRepsCount != 0)
                {
                    notificationContent.Body = $"Get back to work, next {Timer.Instance.NextRepsCount} reps";
                }

                notificationContent.Sound = UNNotificationSound.Default;

                UNTimeIntervalNotificationTrigger trigger = UNTimeIntervalNotificationTrigger.CreateTrigger(time, false);

                UNNotificationRequest request = UNNotificationRequest.FromIdentifier("RestTimer", notificationContent, trigger);


                center.AddNotificationRequest(request, (NSError obj) =>
                {



                });
            }
            catch (Exception ex)
            {

            }

        }
        private void RegisterForRemoteNotifications(UIApplication application)
        {
            try
            {
                UNUserNotificationCenter.Current.RequestAuthorization(
                        UNAuthorizationOptions.Alert | UNAuthorizationOptions.Badge | UNAuthorizationOptions.Sound,
                        (isSuccess, error) =>
                        {
                            if (error != null)
                            {
                                // no op, not going to do anything about this for now
                            }
                        });

                application.RegisterForRemoteNotifications();
            }
            catch (Exception ex)
            {

            }
        }
    }
    public static class TaskExtensions
    {
        public static async Task TimeoutAfter(this Task task, TimeSpan timeout)
        {
            if (task == await Task.WhenAny(task, Task.Delay(timeout)))
                await task; // Task completed within timeout
            else
                return;
        }

        public static async Task<T> TimeoutAfter<T>(this Task<T> task, TimeSpan timeout)
        {
            if (task == await Task.WhenAny(task, Task.Delay(timeout)))
                return await task; // Task completed within timeout
            else
                return await task;
        }
    }
}