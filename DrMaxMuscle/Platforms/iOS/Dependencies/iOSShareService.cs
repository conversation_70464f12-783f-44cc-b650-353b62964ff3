﻿using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Platforms.iOS.Dependencies;
using Foundation;
using Microsoft.Maui.Controls.Compatibility.Platform.iOS;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UIKit;

//[assembly: Dependency(typeof(iOSShareService))]
namespace DrMaxMuscle.Platforms.iOS.Dependencies
{
    public class iOSShareService : IShareService
    {
        public async void Share(string subject, string message, Stream image)
        {
            try
            {
                ImageSource imageSource = ImageSource.FromStream(() => image);

                //make handler to get UIImage (Native)
                var imageHandler = new StreamImagesourceHandler();
                var uiImage = await imageHandler.LoadImageAsync(imageSource);

                //make NSObject of native image and message
                var img = NSObject.FromObject(uiImage);
                var mess = NSObject.FromObject(message);
                var activityItems = new[] { mess, img };

                //create view of sharing
                var activityController = new UIActivityViewController(activityItems, null);
                //get top controller (parent)
                var topController = GetVisibleViewController();
                //set the SourceView for iPad.
                if (UIDevice.CurrentDevice.UserInterfaceIdiom == UIUserInterfaceIdiom.Pad)
                {
                    activityController.PopoverPresentationController.SourceView = topController.View;
                }
                //show the sharing view
                topController?.PresentViewController(new UINavigationController(activityController), true, () => { });
            }
            catch (Exception ex)
            {

            }
        }

        private UIViewController GetVisibleViewController()
        {
            var root = UIApplication.SharedApplication.KeyWindow.RootViewController;
            try
            {
                while (true)
                {
                    switch (root)
                    {
                        case UINavigationController navigationController:
                            root = navigationController.VisibleViewController;
                            continue;
                        case UITabBarController uiTabBarController:
                            root = uiTabBarController.SelectedViewController;
                            continue;
                    }

                    if (root.PresentedViewController == null)
                        return root;

                    root = root.PresentedViewController;
                }
            }
            catch (Exception ex)
            {
                return root;
            }
        }
    }
}