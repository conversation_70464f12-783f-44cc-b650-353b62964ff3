﻿using System;
using DrMaxMuscle.Dependencies;
using Foundation;
using UIKit;
//[assembly: Xamarin.Forms.Dependency(typeof(OrientationService))]

namespace DrMaxMuscle.Platforms.iOS.Dependencies
{
   
    public class OrientationService : IOrientationService
    {
        public void Landscape()
        {
            AppDelegate appDelegate = (AppDelegate)UIApplication.SharedApplication.Delegate;
            //appDelegate.allowRotation = true;

            UIDevice.CurrentDevice.SetValueForKey(new NSNumber((int)UIInterfaceOrientation.LandscapeLeft), new NSString("orientation"));
        }

        public void Portrait()
        {
            AppDelegate appDelegate = (AppDelegate)UIApplication.SharedApplication.Delegate;
            //appDelegate.allowRotation = false;

            UIDevice.CurrentDevice.SetValueForKey(new NSNumber((int)UIInterfaceOrientation.Portrait), new NSString("orientation"));
        }
    }
}
