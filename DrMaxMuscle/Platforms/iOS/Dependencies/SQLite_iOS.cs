﻿using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Platforms.iOS.Dependencies;
using SQLite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
//[assembly: Dependency(typeof(SQLite_iOS))]
namespace DrMaxMuscle.Platforms.iOS.Dependencies
{
    public class SQLite_iOS : ISQLite
    {
        public SQLite_iOS() { }
        public SQLiteConnection GetConnection()
        {
            var sqliteFilename = "dmmdb.db3";
            string documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.Personal); // Documents folder
            string libraryPath = Path.Combine(documentsPath, "..", "Library"); // Library folder
            var path = Path.Combine(libraryPath, sqliteFilename);
            // Create the connection
            var conn = new SQLite.SQLiteConnection(path, SQLiteOpenFlags.ReadWrite | SQLiteOpenFlags.Create | SQLiteOpenFlags.FullMutex);
            // Return the database connection
            conn.BusyTimeout = TimeSpan.FromSeconds(3);
            return conn;
        }
    }
}
