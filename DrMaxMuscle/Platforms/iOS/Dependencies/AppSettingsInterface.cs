﻿using System;
using DrMaxMuscle.Dependencies;
using Foundation;
using StoreKit;
using UIKit;


namespace DrMaxMuscle.Platforms.iOS.Dependencies
{
    public class AppSettingsInterface : IAppSettingsHelper
    {
        public void OpenAppSettings()
        {
            var url = new NSUrl($"app-settings:");
            UIApplication.SharedApplication.OpenUrl(url);
        }

        public  void RateApp()
        {
            if (UIDevice.CurrentDevice.CheckSystemVersion(10, 3))
            {
                SKStoreReviewController.RequestReview();
            }
        }
    }
}

