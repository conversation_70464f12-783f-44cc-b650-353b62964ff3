﻿using System;
using DrMaxMuscle.Dependencies;
using Foundation;
using UIKit;


namespace DrMaxMuscle.Platforms.iOS.Dependencies
{
    public class VersionInfoService : IVersionInfoService
    {
        public string GetDeviceUniqueId()
        {
            return UIDevice.CurrentDevice.IdentifierForVendor.ToString();
        }

        public int GetVersionInfo()
        {
            try
            {

            var ary = string.Format("{0}", NSBundle.MainBundle.InfoDictionary[new NSString("CFBundleShortVersionString")]).Split('.');
            return int.Parse(ary[ary.Length - 1]);

            }
            catch (Exception ex)
            {

            }
            return 0;
        }
    }
}
