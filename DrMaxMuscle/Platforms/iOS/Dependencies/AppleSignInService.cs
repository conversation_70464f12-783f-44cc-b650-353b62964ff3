﻿using AuthenticationServices;
using CoreFoundation;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Utility;
using Foundation;
using Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UIKit;

namespace DrMaxMuscle.Platforms.iOS.Dependencies
{
    public class AppleSignInService : NSObject, IAppleSignInService, IASAuthorizationControllerDelegate, IASAuthorizationControllerPresentationContextProviding
    {
        public bool IsAvailable => UIDevice.CurrentDevice.CheckSystemVersion(13, 0);

        TaskCompletionSource<ASAuthorizationAppleIdCredential> tcsCredential;

        public async Task<AppleSignInCredentialState> GetCredentialStateAsync(string userId)
        {
            var appleIdProvider = new ASAuthorizationAppleIdProvider();
            var credentialState = await appleIdProvider.GetCredentialStateAsync(userId);
            switch (credentialState)
            {
                case ASAuthorizationAppleIdProviderCredentialState.Authorized:
                    // The Apple ID credential is valid.
                    return AppleSignInCredentialState.Authorized;
                case ASAuthorizationAppleIdProviderCredentialState.Revoked:
                    // The Apple ID credential is revoked.
                    return AppleSignInCredentialState.Revoked;
                case ASAuthorizationAppleIdProviderCredentialState.NotFound:
                    // No credential was found, so show the sign-in UI.
                    return AppleSignInCredentialState.NotFound;
                default:
                    return AppleSignInCredentialState.Unknown;
            }

        }
        public async Task<AppleAccount> SignInAsync()
        {
            var appleIdProvider = new ASAuthorizationAppleIdProvider();

            // Create a fresh request to force re-authentication
            var request = appleIdProvider.CreateRequest();
            request.RequestedScopes = new[] { ASAuthorizationScope.Email, ASAuthorizationScope.FullName };

            // Force re-authentication to bypass cached token issues
            request.User = null; // Null forces re-evaluation of user's identity

            // Initialize TaskCompletionSource
            //tcsCredential = new TaskCompletionSource<ASAuthorizationAppleIdCredential>();
            tcsCredential = new TaskCompletionSource<ASAuthorizationAppleIdCredential>(TaskCreationOptions.RunContinuationsAsynchronously);
            // Create and perform the authorization request
            var authorizationController = new ASAuthorizationController(new[] { request });
            authorizationController.Delegate = this;
            authorizationController.PresentationContextProvider = this;
            authorizationController.PerformRequests();

            // Wait for the credential response
            var creds = await tcsCredential.Task;

            if (creds == null)
            {
                Console.WriteLine("Retry failed. Sign-In process cannot proceed.");
                return null; // or handle it as an error/failure
            }
            //// Handle null credentials
            //if (creds == null)
            //{
            //    Console.WriteLine("Apple Sign-In failed or was canceled.");
            //    return null;
            //}

            // Build the AppleAccount object
            var appleAccount = new AppleAccount
            {
                Token = creds.IdentityToken != null ? new NSString(creds.IdentityToken, NSStringEncoding.UTF8).ToString() : null,
                Email = creds.Email , // Null on subsequent logins
                UserId = creds.User, // Always available
                Name = creds.FullName != null
                    ? NSPersonNameComponentsFormatter.GetLocalizedString(creds.FullName, NSPersonNameComponentsFormatterStyle.Default, 0)
                    : null,
                GivenName = creds.FullName?.GivenName,
                FamilyName = creds.FullName?.FamilyName,
                RealUserStatus = creds.RealUserStatus.ToString()
            };
            // Log real user status for debugging
            if (creds.RealUserStatus == ASUserDetectionStatus.LikelyReal)
            {
                Console.WriteLine("The user is likely real.");
            }
            else
            {
                Console.WriteLine("The user could not be verified as real.");
            }

            return appleAccount;
            //Old code working fine but issue is on iOS 18

            //var appleIdProvider = new ASAuthorizationAppleIdProvider();
            //var request = appleIdProvider.CreateRequest();
            //request.RequestedScopes = new[] { ASAuthorizationScope.Email, ASAuthorizationScope.FullName };

            //var authorizationController = new ASAuthorizationController(new[] { request });
            //authorizationController.Delegate = this;
            //authorizationController.PresentationContextProvider = this;
            //authorizationController.PerformRequests();

            //tcsCredential = new TaskCompletionSource<ASAuthorizationAppleIdCredential>();

            //var creds = await tcsCredential.Task;

            //if (creds == null)
            //    return null;


            ////var appleSignInRequest = new ASAuthorizationAppleIdProvider().CreateRequest();
            //////appleSignInRequest.RequestedScopes = new[] { ASAuthorizationScope.Email, ASAuthorizationScope.FullName };

            ////var authorizationController = new ASAuthorizationController(new[] { appleSignInRequest });
            ////authorizationController.Delegate = this;
            ////authorizationController.PresentationContextProvider = this;

            ////authorizationController.PerformRequests();


            //var appleAccount = new AppleAccount();
            //appleAccount.Token = new NSString(creds.IdentityToken, NSStringEncoding.UTF8).ToString();
            //appleAccount.Email = creds.Email;
            //appleAccount.UserId = creds.User;
            ////appleAccount.Name = NSPersonNameComponentsFormatter.GetLocalizedString(creds.FullName, NSPersonNameComponentsFormatterStyle.Default, NSPersonNameComponentsFormatterOptions.Phonetic);
            //appleAccount.Name = NSPersonNameComponentsFormatter.GetLocalizedString(creds.FullName, NSPersonNameComponentsFormatterStyle.Default, 0);

            //appleAccount.GivenName = creds.FullName.GivenName;
            //appleAccount.FamilyName = creds.FullName.FamilyName;

            //appleAccount.RealUserStatus = creds.RealUserStatus.ToString();

            //return appleAccount;
        }

        #region IASAuthorizationController Delegate

        [Export("authorizationController:didCompleteWithAuthorization:")]
        public void DidComplete(ASAuthorizationController controller, ASAuthorization authorization)
        {
            var creds = authorization.GetCredential<ASAuthorizationAppleIdCredential>();
            DispatchQueue.MainQueue.DispatchAsync(() =>
            {
                tcsCredential?.TrySetResult(creds);
            });
        }

        [Export("authorizationController:didCompleteWithError:")]
        public void DidComplete(ASAuthorizationController controller, NSError error)
        {
            // Handle error

            //Acr.UserDialogs.UserDialogs.Instance.Alert($"{error}", "Error", "OK");
            // if controller.authorizationRequests.contains(where: { $0 is ASAuthorizationPasswordRequest }) {
            //if (error != null && error.Code == 1000 && controller.AuthorizationRequests.Length>0 && controller.AuthorizationRequests[0] is ASAuthorizationAppleIdRequest)
            //{
            //    var requestAppleID = new ASAuthorizationAppleIdProvider().CreateRequest();
            //    requestAppleID.RequestedScopes = new[] { ASAuthorizationScope.Email, ASAuthorizationScope.FullName };
            //    requestAppleID.RequestedOperation = ASAuthorizationOperation.Implicit;
            //    performRequest(requestAppleID);
            //}
            //else
            if (tcsCredential != null && !tcsCredential.Task.IsCompleted)
            {
                tcsCredential?.TrySetResult(null);
            }
            Console.WriteLine(error);
        }

        private void performRequest(ASAuthorizationRequest request)
        {
            var controller = new ASAuthorizationController(authorizationRequests: new[] { request });
            controller.Delegate = this;
            controller.PresentationContextProvider = this;
            controller.PerformRequests();
        }
        #endregion

        #region IASAuthorizationControllerPresentation Context Providing

        public UIWindow GetPresentationAnchor(ASAuthorizationController controller)
        {
            return UIApplication.SharedApplication.KeyWindow;
        }

        #endregion

    }
}
