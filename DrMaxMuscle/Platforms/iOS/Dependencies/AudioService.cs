﻿using AVFoundation;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Platforms.iOS.Dependencies;
using Foundation;
using Plugin.Vibrate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
[assembly: Dependency(typeof(AudioService))]
namespace DrMaxMuscle.Platforms.iOS.Dependencies
{
    public class AudioService : IAudio
    {
        public AudioService()
        {
        }

        public void PlayAudioFile(string fileName, bool sound, bool vibrate,bool IsFromComplete, float volume = 1f)
        {
            try
            {
                if (sound)
                {
                    string sFilePath = NSBundle.MainBundle.PathForResource
                    (Path.GetFileNameWithoutExtension(fileName), Path.GetExtension(fileName));
                    var url = NSUrl.FromString(sFilePath);
                    var _player = AVAudioPlayer.FromUrl(url);
                    _player.FinishedPlaying += (object sender, AVStatusEventArgs e) =>
                    {
                        _player = null;
                    };
                    _player.SetVolume(volume, volume);
                    _player.Play();
                }
                if (vibrate)
                    CrossVibrate.Current.Vibration(TimeSpan.FromSeconds(3));
            }
            catch (Exception ex)
            {

            }
        }
    }
}
