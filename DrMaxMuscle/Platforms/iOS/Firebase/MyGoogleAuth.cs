﻿#region Assembly Plugin.Firebase.Auth.Google, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// /Users/<USER>/.nuget/packages/plugin.firebase.auth.google/3.1.1/lib/net8.0-ios18.0/Plugin.Firebase.Auth.Google.dll
// Decompiled with ICSharpCode.Decompiler 
#endregion

using Firebase.Auth;
using Foundation;
using UIKit;
using Google.SignIn;

namespace DrMaxMuscle.Plateforms.iOS.Firebase;

public sealed class MyGoogleAuth : NSObject
{
    private UIViewController _viewController;

    private TaskCompletionSource<AuthCredential> _tcs;

    public Task<AuthCredential> GetCredentialAsync(UIViewController viewController)
    {
        _viewController = viewController;
        _tcs = new TaskCompletionSource<AuthCredential>();
        SignIn.SharedInstance.SignInWithPresentingViewController(viewController, DidSignIn);
        return _tcs.Task;
    }

    public void DidSignIn(SignInResult signIn, NSError error)
    {
        if (signIn == null)
        {
            _tcs?.SetException(new TaskCanceledException("Unknown error."));
        }
        else
        {
            GoogleUser user = signIn.User;

            if (user != null && error == null)
            {
                _tcs?.SetResult(GoogleAuthProvider.GetCredential(user.IdToken.TokenString, user.AccessToken.TokenString));
            }
            else if (user == null && error == null)
            {
                _tcs?.SetException(new TaskCanceledException("Unknown error."));
            }
            else
            {
                _tcs?.SetException(new NSErrorException(error));
            }
        }
    }

    [Export("signInWillDispatch:error:")]
    public void WillDispatch(SignIn signIn, NSError error)
    {
    }

    [Export("signIn:presentViewController:")]
    public void PresentViewController(SignIn signIn, UIViewController viewController)
    {
        _viewController?.PresentViewController(viewController, animated: true, null);
    }

    [Export("signIn:dismissViewController:")]
    public void DismissViewController(SignIn signIn, UIViewController viewController)
    {
        _viewController?.DismissViewController(animated: true, null);
    }

    public void SignOut()
    {
        SignIn.SharedInstance.SignOutUser();
    }
}
#if false // Decompilation log
'267' items in cache
------------------
Resolve: 'System.Runtime, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
Found single assembly: 'System.Runtime, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
Load from: '/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.14/ref/net8.0/System.Runtime.dll'
------------------
Resolve: 'Microsoft.iOS, Version=18.0.0.0, Culture=neutral, PublicKeyToken=84e04ff9cfb79065'
Found single assembly: 'Microsoft.iOS, Version=18.0.0.0, Culture=neutral, PublicKeyToken=84e04ff9cfb79065'
Load from: '/Users/<USER>/.nuget/packages/microsoft.ios.ref.net8.0_18.0/18.0.8324/ref/net8.0/Microsoft.iOS.dll'
------------------
Resolve: 'Firebase.Auth, Version=11.0.0.0, Culture=neutral, PublicKeyToken=null'
Found single assembly: 'Firebase.Auth, Version=11.0.0.0, Culture=neutral, PublicKeyToken=null'
Load from: '/Users/<USER>/.nuget/packages/adame.firebase.ios.auth/11.0.0/lib/net6.0-ios16.1/Firebase.Auth.dll'
------------------
Resolve: 'System.Runtime.InteropServices, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
Found single assembly: 'System.Runtime.InteropServices, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
Load from: '/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.14/ref/net8.0/System.Runtime.InteropServices.dll'
------------------
Resolve: 'Google.SignIn, Version=8.0.0.0, Culture=neutral, PublicKeyToken=null'
Found single assembly: 'Google.SignIn, Version=8.0.0.0, Culture=neutral, PublicKeyToken=null'
Load from: '/Users/<USER>/.nuget/packages/adame.google.ios.signin/8.0.0/lib/net6.0-ios16.1/Google.SignIn.dll'
------------------
Resolve: 'Plugin.Firebase.Auth, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'
Found single assembly: 'Plugin.Firebase.Auth, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'
Load from: '/Users/<USER>/.nuget/packages/plugin.firebase.auth/3.1.1/lib/net8.0-ios18.0/Plugin.Firebase.Auth.dll'
------------------
Resolve: 'Plugin.Firebase.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'
Found single assembly: 'Plugin.Firebase.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'
Load from: '/Users/<USER>/.nuget/packages/plugin.firebase.core/3.1.1/lib/net8.0-ios18.0/Plugin.Firebase.Core.dll'
------------------
Resolve: 'System.Runtime.CompilerServices.Unsafe, Version=8.0.0.0, Culture=neutral, PublicKeyToken=null'
Found single assembly: 'System.Runtime.CompilerServices.Unsafe, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
Load from: '/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.14/ref/net8.0/System.Runtime.CompilerServices.Unsafe.dll'
#endif
