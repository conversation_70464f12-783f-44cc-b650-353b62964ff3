﻿using DrMaxMuscle.Layout;
using DrMaxMuscle.Platforms.iOS.Renderer;
using Microsoft.Maui.Controls.Compatibility;
using Microsoft.Maui.Controls.Compatibility.Platform.iOS;
using Microsoft.Maui.Controls.Platform;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UIKit;

[assembly: ExportRenderer(typeof(DrMuscleButton), typeof(DrMuscleButtonRender))]
namespace DrMaxMuscle.Platforms.iOS.Renderer
{
    public class DrMuscleButtonRender : ButtonRenderer
    {
        protected override void OnElementChanged(ElementChangedEventArgs<Button> e)
        {
            base.OnElementChanged(e);
            if (Control != null)
            {
                //Control.LineBreakMode = UIKit.UILineBreakMode.TailTruncation;

                Control.TitleLabel.LineBreakMode = UILineBreakMode.WordWrap;
                Control.TitleLabel.Lines = 0;
                Control.TitleLabel.TextAlignment = UITextAlignment.Center;

            }
        }
    }
}
