﻿using CoreFoundation;
using CoreGraphics;
using DrMaxMuscle.Controls;
using Foundation;
using Microsoft.Maui.Controls.Compatibility.Platform.iOS;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UIKit;

namespace DrMaxMuscle.Platforms.iOS.Renderer
{
    public class ExtendedLabelRenderer : LabelHandler
    {
        public ExtendedLabelRenderer() : base(PropertyMapper)
        {
        }

        public ExtendedLabelRenderer(PropertyMapper propertyMapper) : base(propertyMapper)
        {
        }

        public static new PropertyMapper<Label, ExtendedLabelRenderer> PropertyMapper = new(LabelHandler.Mapper)
        {
            // Add any additional properties you need to handle
        };


        protected override void ConnectHandler(MauiLabel platformView)
        {
            base.ConnectHandler(platformView);

            try
            {
                if (VirtualView is Label label && platformView != null)
                {

                    // Handle long press for copying text to clipboard
                    var longPressRecognizer = new UILongPressGestureRecognizer(async (recognizer) =>
                    {
                        if (recognizer.State == UIGestureRecognizerState.Began)
                        {
                            UIPasteboard.General.String = platformView.Text;
                            ShowAlert("", "Text copied to clipboard");
                        }
                    });

                    platformView.AddGestureRecognizer(longPressRecognizer);

                    // Make sure the text is clickable
                    platformView.UserInteractionEnabled = true;
                }
            }
            catch (Exception ex)
            {

            }
        }

        private void ShowAlert(string title, string message)
        {
            var alert = UIAlertController.Create(title, message, UIAlertControllerStyle.Alert);
            alert.AddAction(UIAlertAction.Create("OK", UIAlertActionStyle.Default, null));
            UIApplication.SharedApplication.KeyWindow.RootViewController.PresentViewController(alert, true, null);
        }

        protected override void DisconnectHandler(MauiLabel platformView)
        {
            // Clean up events
            if(platformView != null)
            {
                foreach (var recognizer in platformView.GestureRecognizers)
                {
                    platformView.RemoveGestureRecognizer(recognizer);
                }
            }
            
            base.DisconnectHandler(platformView);
        }
    }
}