﻿using DrMaxMuscle.Controls;
using Foundation;
using Microsoft.Maui.Controls.Compatibility.Platform.iOS;
using Microsoft.Maui.Handlers;
using CoreText;
using System.Collections.Generic;
using UIKit;
using Microsoft.Maui.Controls.Platform;
using System.ComponentModel;
using CoreGraphics;

namespace DrMaxMuscle.Platforms.iOS.Renderer
{
    public class HyperlinkLabelHandler : ViewRenderer<HyperlinkLabel, HyperlinkUIView>
    {
        //protected override void OnElementChanged(ElementChangedEventArgs<HyperlinkLabel> e)
        //{
        //    base.OnElementChanged(e);

        //    if (e.NewElement != e.OldElement)
        //    {
        //        if (e.OldElement != null)
        //            e.OldElement.PropertyChanged -= Element_PropertyChanged;

        //        if (e.NewElement != null)
        //            e.NewElement.PropertyChanged += Element_PropertyChanged;
        //    }
        //    try
        //    {
        //        if (e.NewElement != null)
        //        {
        //            //if (Control != null)
        //            //{
        //            foreach (var item in this.Subviews)
        //            {
        //                item.RemoveFromSuperview();
        //            }
        //            var textView = new HyperlinkUIView();
        //            SetNativeControl(textView);
        //            //textView.DataDetectorTypes = UIDataDetectorType.All;
        //            SetText();
        //            //}
        //        }
        //    }
        //    catch (System.Exception ex)
        //    {

        //    }

        //}

        //private void Element_PropertyChanged(object sender, PropertyChangedEventArgs e)
        //{
        //    if (Control != null)
        //    {
        //        if (e.PropertyName == HyperlinkLabel.RawTextProperty.PropertyName)
        //            SetText();
        //    }
        //}

        //private void SetText()
        //{
        //    try
        //    {

        //        CTStringAttributes attrs = new CTStringAttributes();
        //        string text = Element.GetText(out List<HyperlinkLabelLink> links);
        //        if (text != null)
        //        {
        //            var str = new NSMutableAttributedString(text);
        //            //str.AddAttribute(UIStringAttributeKey.Font, Element.Font.ToUIFont(), new NSRange(0, str.Length));
        //            var textColor = (Color)Element.GetValue(Label.TextColorProperty);
        //            str.AddAttribute(UIStringAttributeKey.ForegroundColor, textColor.ToUIColor(Colors.Black),
        //                new NSRange(0, str.Length));


        //            foreach (var item in links)
        //            {
        //                str.AddAttribute(UIStringAttributeKey.Link, new NSUrl(item.Link), new NSRange(item.Start, item.Text.Length));
        //                str.AddAttribute(UIStringAttributeKey.UnderlineStyle, NSNumber.FromInt32((int)NSUnderlineStyle.Single), new NSRange(item.Start, item.Text.Length));
        //            }
        //            Control.AttributedText = str;


        //        }

        //    }
        //    catch (System.Exception ex)
        //    {

        //    }
        //}
    }
    public class HyperlinkUIView : UITextView
    {
        public HyperlinkUIView()
        {
            Selectable = true;
            Editable = false;
            BackgroundColor = UIColor.Clear;
            var dictionary = new NSDictionary(
                UIStringAttributeKey.ForegroundColor, Constants.AppThemeConstants.BlueLightColor.ToUIColor(),
                UIStringAttributeKey.UnderlineColor, Constants.AppThemeConstants.BlueLightColor.ToUIColor()
            );
            ContentInset = new UIEdgeInsets(0, 0, 0, 0);
            TextContainerInset = new UIEdgeInsets(0, 0, 0, 0);

            TextContainer.LineFragmentPadding = 0;
            WeakLinkTextAttributes = dictionary;
            ScrollEnabled = false;
        }

        public override bool CanBecomeFirstResponder => false;

        public override bool GestureRecognizerShouldBegin(UIGestureRecognizer gestureRecognizer)
        {
            //Preventing standard actions on UITextView that are triggered after long press
            if (gestureRecognizer is UILongPressGestureRecognizer longpress
                && longpress.MinimumPressDuration == .5)
                return false;

            return true;
        }

        //public override bool CanPerform(Selector action, NSObject withSender) => false;

        public override void LayoutSubviews()
        {
            //Make the TextView as large as its content
            base.LayoutSubviews();
            try
            {
                var x = new CGSize(this.Frame.Size.Width, double.MaxValue);

                var fits = SizeThatFits(x);

                var frame = Frame;

                frame.Size = fits;
            }
            catch (Exception ex)
            {
                
            }
        }
    }
}