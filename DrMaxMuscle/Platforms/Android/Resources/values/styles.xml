﻿<?xml version="1.0" encoding="utf-8" ?>
<resources>

  <!--Maybe delete if useless now-->
  <style name="AlertDialogCustomGray" parent="Theme.AppCompat.Dialog.Alert">
    <item name="buttonBarNegativeButtonStyle">@style/dialog_button_negative</item>
    <item name="buttonBarPositiveButtonStyle">@style/dialog_button_negative</item>
    <item name="android:background">@drawable/gradientpopupbackground</item>
    <item name="android:editTextStyle">@style/App_EditTextStyle</item>
    <item name="android:layout_marginBottom">4dp</item>
    <item name="android:layout_marginTop">2dp</item>
  </style>
  <style name="AlertDialogCustomGreen" parent="Theme.AppCompat.Dialog.Alert">
    <item name="buttonBarNegativeButtonStyle">@style/dialog_button_negative</item>
    <item name="buttonBarPositiveButtonStyle">@style/dialog_button_negative</item>
    <item name="android:background">@drawable/gradientpopupbackground</item>
    <item name="android:editTextStyle">@style/App_EditTextStyleGreen</item>
	<item name="android:gravity">center</item>
	<item name="android:layout_gravity">center</item>
	<item name="android:textAlignment">center</item>
  </style>
  <style name="AlertDialogFirstTimeExercise" parent="Theme.AppCompat.Dialog.Alert">
    <!--<item name="android:textSize">15sp</item>-->
    <item name="android:buttonBarNegativeButtonStyle">@style/dialog_button_negative</item>
    <item name="android:buttonBarPositiveButtonStyle">@style/dialog_button_positiveGreen</item>
    <item name="android:editTextStyle">@style/App_EditTextStyle</item>
  </style>
  <style name="AlertDialogCustomRed" parent="Theme.AppCompat.Dialog.Alert">
    <item name="android:buttonBarNegativeButtonStyle">@style/dialog_button_negative</item>
    <item name="android:buttonBarPositiveButtonStyle">@style/dialog_button_positiveRed</item>
    <item name="android:editTextStyle">@style/App_EditTextStyle</item>
  </style>

  <style name="dialog_button_negative">
    <item name="android:background">@android:color/transparent</item>
    <item name="android:layout_marginLeft">8dp</item>

    <item name="android:textColor">#FFFFFF</item>
  </style>


  <style name="dialog_button_positiveRed">
    <!--<item name="android:background">@drawable/buttonRed</item>-->
    <item name="android:layout_marginLeft">8dp</item>
    <item name="android:textColor">#FFFFFF</item>
  </style>
  <style name="dialog_button_positiveGreen">
    <!--<item name="android:background">@drawable/buttonGreen</item>-->
    <item name="android:layout_marginLeft">8dp</item>
    <item name="android:textColor">#FFFFFF</item>
  </style>
  <style name="App_EditTextStyle" parent="@android:style/Widget.EditText">
    <item name="android:background">#CCFFFFFF</item>
    <item name="android:textColor">#000000</item>
    <item name="android:paddingLeft">8dp</item>
    <item name="android:layout_marginLeft">8dp</item>

    <item name="android:paddingRight">8dp</item>
    <item name="android:paddingTop">8dp</item>
    <item name="android:paddingBottom">8dp</item>
  </style>
  <style name="App_EditTextStyleGreen" parent="@android:style/Widget.EditText">
    <item name="android:background">#44FFFFFF</item>
    <item name="android:textColor">#FFFFFF</item>
    <item name="android:paddingLeft">8dp</item>
    <item name="android:layout_marginLeft">8dp</item>

    <item name="android:paddingRight">8dp</item>
    <item name="android:paddingTop">8dp</item>
    <item name="android:paddingBottom">8dp</item>
  </style>
  </resources>