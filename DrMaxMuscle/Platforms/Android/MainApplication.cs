﻿using Android.App;
using Android.Runtime;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Platforms.Android.Dependencies;
using DrMaxMuscle.Platforms.Android.Renderers;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using Microsoft.Maui.Handlers;

namespace DrMaxMuscle
{
    [Application]
    public class MainApplication : MauiApplication
    {
        public MainApplication(IntPtr handle, JniHandleOwnership ownership)
            : base(handle, ownership)
        {
        }

        protected override MauiApp CreateMauiApp() {
            //Microsoft.Maui.Handlers.EntryHandler.Mapper.AppendToMapping(nameof(Entry), (Microsoft.Maui.Handlers.IEntryHandler handler, IEntry view) =>
            //{
            //    // Check if PlatformView is not null
            //    if (handler.PlatformView != null)
            //    {
            //        // Check if view.Background is not null
            //        if (view is Layout.WorkoutEntry)
            //            {
            //            handler.PlatformView.Background = null;
            //            handler.PlatformView.SetBackgroundColor(view.Background.ToColor().ToAndroid());
            //        }
            //        else
            //        {
            //            //// Optionally handle the case where view.Background is null
            //            //handler.PlatformView.SetBackgroundColor(Colors.Transparent.ToAndroid());
            //        }
            //    }
            //});

            var builder = MauiProgram.CreateMauiApp();
            return builder;
        }
        
    }
}