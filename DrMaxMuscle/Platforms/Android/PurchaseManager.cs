﻿using DrMaxMuscle.Platforms.Android.Dependencies;
using DrMuscleWebApiSharedModel;
using Plugin.InAppBilling;
using Rollbar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Platforms.Android
{
    public class PurchaseManager
    {
        private IList<InAppBillingProduct> _subscriptions;
        private IList<InAppBillingPurchase> _subscriptionPurchases;
        private static PurchaseManager _instance;
        public event MonthlyAccessPurchased OnMonthlyAccessPurchased;
        public event YearlyAccessPurchased OnYearlyAccessPurchased;
        public event MealPlanAccessPurchased OnMealPlanAccessPurchased;
        public delegate void MonthlyAccessPurchased();
        public delegate void YearlyAccessPurchased();
        public delegate void MealPlanAccessPurchased();
        IInAppBilling billing = CrossInAppBilling.Current;
        private string _payload = "b1b4d8c8d7f3464c8486a731a01e8c59";
        public static PurchaseManager Instance
        {
            get
            {
                if (_instance == null)
                    _instance = new PurchaseManager();
                return _instance;
            }
        }

        private PurchaseManager()
        {
            try
            {

                //Console.WriteLine("1");
                // var _sc = new Xamarin.InAppBilling.InAppBillingServiceConnection(CrossCurrentActivity.Current.Activity, "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAr5uQwFLKZ+2YYs+6tVUrT9u4mVF+X5Ht3GW4C7B8LJ9iBb6rC1+4ib7t1UFejbt4+Pio816/fDvdTrMuuSeIhT/3TjCiaLX1FEJ3/QFSKDPrufbqGaB01Hg/2cVTZ+RKCIaZrLmdunUnTspOtx5I0Ai19oTwINjfRcIgz7zLkHz+Ga9HMXzcDWGVULAJf7o4Zux1uiQygS+WRGhL7qG2d42ZrkgXJp9SyzFtEYSkdLOHxAHxEJtisUSWjVHvE+j74cIiQxONlMyWqAgM6qRGjPPEFnENOOvpGvX1W8vMC5ZADRaDTRbZ5fk2Q+DL4bGsd2pb7KgbKNzwcKQnRyWQxQIDAQAB");
                //   _sc.Connect();
                //Console.WriteLine("2");
                //_serviceConnection.OnConnected += SrviceConnection_OnConnected;
                //Console.WriteLine("3");
                //_serviceConnection.OnInAppBillingError += (error, message) =>
                //{
                //    Console.WriteLine("Error in app billing : " + message);
                //};
                //Console.WriteLine("4");
                Task.Factory.StartNew(async () => {
                    await Task.Delay(7000);
                    await GetInventory();
                    LoadPurchasedItems();
                });
                // 

                //_serviceConnection.Connect();
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                Console.Write(e.StackTrace);
            }
        }

        //private async void Connection()
        //{
        //    try
        //    {
        //        var connected = await billing.ConnectAsync(ItemType.Subscription);
        //        if (!connected)
        //            return;

        //        //make additional billing calls
        //    }
        //    finally
        //    {
        //        await billing.DisconnectAsync();
        //    }
        //}

        public async void BuyMealPlanAccess()
        {

            var billing = CrossInAppBilling.Current;
            try
            {
                var connected = await billing.ConnectAsync();//ItemType.Subscription
                if (!connected)
                {
                    //we are offline or can't connect, don't try to purchase
                    return;
                }

                //check purchases
                //var purchase = await billing.PurchaseAsync("subscription.monthly9then49", ItemType.Subscription, _payload);
                var purchase = await billing.PurchaseAsync("subscription.mealaddon.monthly1then19", ItemType.Subscription);

                //possibility that a null came through.
                if (purchase == null)
                {
                    //did not purchase
                }
                else
                {
                    //purchased!
                    //if (OnMonthlyAccessPurchased != null)
                    //    OnMonthlyAccessPurchased();
                    new Firebase_Droid().LogEvent("android_mealPlan_purchased", purchase.ProductId);
                    if (purchase.ProductId == "subscription.mealaddon.monthly1then19")
                    {
                        App.IsMealPlan = true;
                        if (OnMealPlanAccessPurchased != null)
                            OnMealPlanAccessPurchased();
                    }

                    LoadPurchasedItems();
                    await makeAcknowledge(purchase);

                    DateTime expirydate = purchase.TransactionDateUtc.ToLocalTime();
                    SubscriptionModel subscription = new SubscriptionModel()
                    {
                        PurchaseToken = purchase.PurchaseToken,
                        ExpiryDate = expirydate,
                        ProductId = purchase.ProductId,
                        Platform = 0,
                        OrderId = purchase.Id,
                    };
                    MessagingCenter.Send<SubscriptionModel>(subscription, "SubscriptionPurchaseMessage");
                    MessagingCenter.Send<SubscriptionModel>(subscription, "SubscriptionPurchaseIfNotExistMessage");
                }
            }
            catch (InAppBillingPurchaseException purchaseEx)
            {
                //Billing Exception handle this based on the type
                Console.WriteLine("Error: " + purchaseEx);
            }
            catch (Exception ex)
            {
                //Something else has gone wrong, log it
                Console.WriteLine("Issue connecting: " + ex);
            }
            finally
            {
                await billing.DisconnectAsync();
            }
        }

        public async void BuyMonthlyAccess()
        {

            var billing = CrossInAppBilling.Current;
            try
            {
                var connected = await billing.ConnectAsync();//ItemType.Subscription
                if (!connected)
                {
                    //we are offline or can't connect, don't try to purchase
                    return;
                }

                //check purchases
                //var purchase = await billing.PurchaseAsync("subscription.monthly9then49", ItemType.Subscription, _payload);
                var purchase = await billing.PurchaseAsync("subscription.monthly9then49.2021", ItemType.Subscription);

                //possibility that a null came through.
                if (purchase == null)
                {
                    //did not purchase
                }
                else
                {
                    //purchased!
                    //if (OnMonthlyAccessPurchased != null)
                    //    OnMonthlyAccessPurchased();
                    new Firebase_Droid().LogEvent("android_purchased", purchase.ProductId);
                    new Firebase_Droid().LogEvent("android_purchased_9", purchase.ProductId);
                    if (purchase.ProductId == "subscription.monthly9then49.2021")
                        if (OnMonthlyAccessPurchased != null)
                            OnMonthlyAccessPurchased();
                    LoadPurchasedItems();

                    await makeAcknowledge(purchase);
                    DateTime expirydate = purchase.TransactionDateUtc.ToLocalTime();
                    SubscriptionModel subscription = new SubscriptionModel()
                    {
                        PurchaseToken = purchase.PurchaseToken,
                        ExpiryDate = expirydate,
                        ProductId = purchase.ProductId,
                        Platform = 0,
                        OrderId = purchase.Id,
                    };
                    MessagingCenter.Send<SubscriptionModel>(subscription, "SubscriptionPurchaseMessage");
                }
            }
            catch (InAppBillingPurchaseException purchaseEx)
            {
                //Billing Exception handle this based on the type
                Console.WriteLine("Error: " + purchaseEx);
            }
            catch (Exception ex)
            {
                //Something else has gone wrong, log it
                Console.WriteLine("Issue connecting: " + ex);
            }
            finally
            {
                await billing.DisconnectAsync();
            }
        }
        public async void BuyMonthlyAccessOld()
        {

            var billing = CrossInAppBilling.Current;
            try
            {
                var connected = await billing.ConnectAsync();//ItemType.Subscription
                if (!connected)
                {
                    //we are offline or can't connect, don't try to purchase
                    return;
                }

                //check purchases
                //var purchase = await billing.PurchaseAsync("subscription.monthly49", ItemType.Subscription, _payload);
                var purchase = await billing.PurchaseAsync("subscription.monthly49", ItemType.Subscription);

                //possibility that a null came through.
                if (purchase == null)
                {
                    //did not purchase
                }
                else
                {
                    //purchased!
                    //if (OnMonthlyAccessPurchased != null)
                    //    OnMonthlyAccessPurchased();
                    new Firebase_Droid().LogEvent("android_purchased", purchase.ProductId);
                    if (purchase.ProductId == "subscription.monthly49")
                        if (OnMonthlyAccessPurchased != null)
                            OnMonthlyAccessPurchased();
                    LoadPurchasedItems();

                    await makeAcknowledge(purchase);
                    DateTime expirydate = purchase.TransactionDateUtc.ToLocalTime();
                    SubscriptionModel subscription = new SubscriptionModel()
                    {
                        PurchaseToken = purchase.PurchaseToken,
                        ExpiryDate = expirydate,
                        ProductId = purchase.ProductId,
                        Platform = 0,
                        OrderId = purchase.Id,
                    };
                    MessagingCenter.Send<SubscriptionModel>(subscription, "SubscriptionPurchaseMessage");
                }
            }
            catch (InAppBillingPurchaseException purchaseEx)
            {
                //Billing Exception handle this based on the type
                Console.WriteLine("Error: " + purchaseEx);
            }
            catch (Exception ex)
            {
                //Something else has gone wrong, log it
                Console.WriteLine("Issue connecting: " + ex);
            }
            finally
            {
                await billing.DisconnectAsync();
            }
        }

        public async void BuyYearlyAccessNew()
        {
            var billing = CrossInAppBilling.Current;
            try
            {
                var connected = await billing.ConnectAsync();//ItemType.Subscription
                if (!connected)
                {
                    //we are offline or can't connect, don't try to purchase
                    return;
                }

                //check purchases
                //var purchase = await billing.PurchaseAsync("subscription.annual89then389", ItemType.Subscription, _payload);
                var purchase = await billing.PurchaseAsync("subscription.annual89then389", ItemType.Subscription);

                //possibility that a null came through.
                if (purchase == null)
                {
                    //did not purchase
                }
                else
                {
                    //purchased!
                    if (OnYearlyAccessPurchased != null)
                        OnYearlyAccessPurchased();

                    LoadPurchasedItems();

                    //billing.FinalizePurchaseAsync(new string() { purchase.ProductId }, true);
                    await makeAcknowledge(purchase);
                    DateTime expirydate = purchase.TransactionDateUtc.ToLocalTime();
                    SubscriptionModel subscription = new SubscriptionModel()
                    {
                        PurchaseToken = purchase.PurchaseToken,
                        ExpiryDate = expirydate,
                        ProductId = purchase.ProductId,
                        Platform = 0,
                        OrderId = purchase.Id,
                    };
                    MessagingCenter.Send<SubscriptionModel>(subscription, "SubscriptionPurchaseMessage");
                }
            }
            catch (InAppBillingPurchaseException purchaseEx)
            {
                //Billing Exception handle this based on the type
                Console.WriteLine("Error: " + purchaseEx);
            }
            catch (Exception ex)
            {
                //Something else has gone wrong, log it
                Console.WriteLine("Issue connecting: " + ex);
            }
            finally
            {
                await billing.DisconnectAsync();
            }
        }

        private async Task makeAcknowledge(InAppBillingPurchase purchase)
        {
            try
            {
                // It is required to acknowledge the purchase, else it will be refunded
                await CrossInAppBilling.Current.FinalizePurchaseAsync(new[] { purchase.TransactionIdentifier });
                //await CrossInAppBilling.Current.FinalizePurchaseAsync(purchase.TransactionIdentifier);
            }
            catch (Exception ex)
            {
                // Log the main exception first
              //  RollbarLocator.RollbarInstance.Error(ex);

                // Log the inner exception if it exists
        //        if (ex.InnerException != null)
        //        {
        //            RollbarLocator.RollbarInstance.Error(ex.InnerException, new Dictionary<string, object>
        //{
        //    { "ErrorType", "InnerException" },
        //    { "TransactionIdentifier", purchase.TransactionIdentifier }
        //});
        //        }
                //await billing.DisconnectAsync();
                //return;
            }
            try
            {
                await CrossInAppBilling.Current.ConsumePurchaseAsync(purchase.ProductId,
                    purchase.TransactionIdentifier);
            }
            catch (Exception e)
            {
                // Log the main exception first
        //        RollbarLocator.RollbarInstance.Error(e);

        //        // Log the inner exception if it exists
        //        if (e.InnerException != null)
        //        {
        //            RollbarLocator.RollbarInstance.Error(e.InnerException, new Dictionary<string, object>
        //{
        //    { "ErrorType", "InnerException" },
        //    { "TransactionIdentifier", purchase.TransactionIdentifier }
        //});
        //        }
                //await billing.DisconnectAsync();
                //return;
            }
        }
        public async void BuyYearlyAccess()
        {
            //foreach (Product p in _subscriptions)
            //{

            //    if (p.ProductId == "subscription.annual389.2")
            //    {
            //        _serviceConnection.BillingHandler.BuyProduct(p);

            //        //if (OnYearlyAccessPurchased != null)
            //            //OnYearlyAccessPurchased();

            //        return;
            //    }
            //}

            var billing = CrossInAppBilling.Current;
            try
            {
                var connected = await billing.ConnectAsync();//ItemType.Subscription
                if (!connected)
                {
                    //we are offline or can't connect, don't try to purchase
                    return;
                }

                //check purchases
                //var purchase = await billing.PurchaseAsync("subscription.annual389.2", ItemType.Subscription, _payload);
                var purchase = await billing.PurchaseAsync("subscription.annual389.2", ItemType.Subscription);

                //possibility that a null came through.
                if (purchase == null)
                {
                    //did not purchase
                }
                else
                {
                    //purchased!
                    if (OnYearlyAccessPurchased != null)
                        OnYearlyAccessPurchased();

                    LoadPurchasedItems();

                    await makeAcknowledge(purchase);
                    DateTime expirydate = purchase.TransactionDateUtc.ToLocalTime();
                    SubscriptionModel subscription = new SubscriptionModel()
                    {
                        PurchaseToken = purchase.PurchaseToken,
                        ExpiryDate = expirydate,
                        ProductId = purchase.ProductId,
                        Platform = 0,
                        OrderId = purchase.Id,
                    };
                    MessagingCenter.Send<SubscriptionModel>(subscription, "SubscriptionPurchaseMessage");
                }
            }
            catch (InAppBillingPurchaseException purchaseEx)
            {
                //Billing Exception handle this based on the type
                Console.WriteLine("Error: " + purchaseEx);
            }
            catch (Exception ex)
            {
                //Something else has gone wrong, log it
                Console.WriteLine("Issue connecting: " + ex);
            }
            finally
            {
                await billing.DisconnectAsync();
            }
        }

        public IList<InAppBillingProduct> Subscriptions
        {
            get
            {
                return _subscriptions;
            }
        }

        public IList<InAppBillingPurchase> SubscriptionPurchases
        {
            get
            {
                return _subscriptionPurchases;
            }
        }
        public async Task<bool> LoadPurchasedItems()
        {
            var billing = CrossInAppBilling.Current;
            try
            {
                // var connected = await billing.ConnectAsync();//ItemType.Subscription
                //
                // if (!connected)
                // {
                //     //Couldn't connect
                //     return false;
                // }

                //check purchases
                _subscriptionPurchases = (await billing.GetPurchasesAsync(ItemType.Subscription)).ToList();

                foreach (var VARIABLE in _subscriptionPurchases)
                {
                    makeAcknowledge(VARIABLE);
                }
            }
            catch (InAppBillingPurchaseException purchaseEx)
            {
                // Log the main exception first
                //RollbarLocator.RollbarInstance.Error(purchaseEx);

                // Log the inner exception if it exists
                if (purchaseEx.InnerException != null)
                {
          
                }
                
                Console.WriteLine("Error: " + purchaseEx);
            }
            catch (Exception ex)
            {
               // RollbarLocator.RollbarInstance.Error(ex);
            }
            finally
            {
                await billing.DisconnectAsync();
            }

            return false;
        }
        public async Task GetInventory()
        {
            // Ask the open connection's billing handler to return a list of available products for the 
            // given list of items.
            // NOTE: We are asking for the Reserved Test Product IDs that allow you to test In-App
            // Billing without actually making a purchase.

            //_subscriptions = await _serviceConnection.BillingHandler.QueryInventoryAsync(new List<string>   {
            //    "subscription.monthly49",
            //    "subscription.annual389.2"
            //}, ItemType.Subscription);

            var billing = CrossInAppBilling.Current;
            try
            {
                var connected = await billing.ConnectAsync();//ItemType.Subscription

                //if (!connected)
                //{
                //    //Couldn't connect
                //    return;
                //}
                var products = new string[] { "subscription.monthly49", "subscription.annual389.2", "subscription.monthly9then49", "subscription.monthly9then49.2021", "subscription.mealaddon.monthly1then19" };//, "subscription.annual89then389" 
                //check purchases
                var list = await billing.GetProductInfoAsync(ItemType.Subscription, products);
                _subscriptions = list.ToList();

                //check for null just incase
                //if (_subscriptions?.Any(p => p.ProductId == productId) ?? false)
                //{
                //    //Purchase restored
                //    return true;
                //}
                //else
                //{
                //    //no purchases found
                //    return false;
                //}
            }
            catch (InAppBillingPurchaseException purchaseEx)
            {
                //RollbarLocator.RollbarInstance.Error(purchaseEx);
                Console.WriteLine("Error: " + purchaseEx);
            }
            catch (Exception ex)
            {
                //RollbarLocator.RollbarInstance.Error(ex);
            }
            finally
            {
                await billing.DisconnectAsync();
            }

            return;
        }

        public void Disconnect()
        {
            //_serviceConnection.Disconnect();
        }
    }
}

