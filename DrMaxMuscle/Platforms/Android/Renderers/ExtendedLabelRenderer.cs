﻿using Android.Content;
using Android.Text.Method;
using Android.Text.Util;
using Android.Widget;
using AndroidX.AppCompat.Widget;
using DrMaxMuscle.Controls;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using Microsoft.Maui.Controls.Platform;
using Microsoft.Maui.Handlers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Platforms.Android.Renderers
{
    public class ExtendedLabelRenderer : LabelHandler
    {
        public ExtendedLabelRenderer() : base(PropertyMapper, CommandMapper)
        {
        }

        // Property mapper for custom properties if needed
        public static new PropertyMapper<Label, ExtendedLabelRenderer> PropertyMapper = new(LabelHandler.Mapper)
        {
            // You can map additional properties here if needed
        };

        protected override AppCompatTextView CreatePlatformView()
        {
            // Create the AppCompatTextView that will be used for the label
            return new AppCompatTextView(Context);
        }

        protected override void ConnectHandler(AppCompatTextView platformView)
        {
            try
            {
                if (platformView != null)
                {
                    base.ConnectHandler(platformView);

                    if (VirtualView is ExtendedLabel extendedLabel)
                    {

                        // Set auto link mask
                        platformView.AutoLinkMask = MatchOptions.All;

                        platformView.Clickable = true;

                        // Set custom link colors (replace with your custom logic)
                        platformView.SetLinkTextColor(Constants.AppThemeConstants.BlueLightColor.ToAndroid());

                        // Handle long click for copying text to clipboard
                        platformView.LongClick += async (sender, e) =>
                        {
                            await Clipboard.Default.SetTextAsync(platformView.Text);
                            //ClipboardManager clipboard = (ClipboardManager)Context.GetSystemService(Context.ClipboardService);
                            //ClipData clip = ClipData.NewPlainText("label", platformView.Text);
                            //clipboard.SetPrimaryClip(clip);
                            Toast.MakeText(Context, "Text copied to clipboard", ToastLength.Short).Show();
                        };
                        // Set movement method for handling links
                        try
                        {
                            platformView.MovementMethod = LinkMovementMethod.Instance;
                        }
                        catch (Exception ex)
                        {
                            // Handle any exceptions here
                        }

                    }
                    else if (VirtualView is ExtendedLabelLink extendedLabellink)
                    {
                        platformView.AutoLinkMask = MatchOptions.All;

                        platformView.Clickable = true;

                        platformView.SetLinkTextColor(Constants.AppThemeConstants.BlueLightColor.ToAndroid());
                        try
                        {
                            platformView.MovementMethod = LinkMovementMethod.Instance;
                        }
                        catch (Exception ex)
                        {
                            // Handle any exceptions here
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle any exceptions here
            }
        }

        protected override void DisconnectHandler(AppCompatTextView platformView)
        {
            if (platformView != null)
            {
                // Clean up events
                platformView.LongClick -= null;
                base.DisconnectHandler(platformView);
            }
        }
    }
}