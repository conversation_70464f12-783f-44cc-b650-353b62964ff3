﻿using Android.Text.Method;
using Android.Text.Util;
using Android.Widget;
using AndroidX.AppCompat.Widget;
using DrMaxMuscle.Controls;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using Microsoft.Maui.Handlers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Platforms.Android.Renderers
{
    public class ExtendedLightBlueLabelRender : LabelHandler
    {
        public ExtendedLightBlueLabelRender() : base(PropertyMapper, CommandMapper)
        {
        }

        // Property mapper for custom properties if needed
        public static new PropertyMapper<Microsoft.Maui.Controls.Label, ExtendedLightBlueLabelRender> PropertyMapper = new(LabelHandler.Mapper)
        {
            // You can map additional properties here if needed
        };

        protected override AppCompatTextView CreatePlatformView()
        {
            // Create the AppCompatTextView that will be used for the label
            return new AppCompatTextView(Context);
        }

        protected override void ConnectHandler(AppCompatTextView platformView)
        {
            base.ConnectHandler(platformView);
            try
            {
                if (platformView != null)
                {
                    if (VirtualView is ExtendedLightBlueLabel extendedLabel)
                    {
                        // Set auto link mask
                        platformView.AutoLinkMask = MatchOptions.All;
                        platformView.Clickable = true;

                        // Set custom link colors (replace with your custom logic)
                        platformView.SetLinkTextColor(Constants.AppThemeConstants.ReysBlueColor.ToAndroid());

                        // Handle long click for copying text to clipboard
                        platformView.LongClick += async (sender, e) =>
                        {
                            await Clipboard.Default.SetTextAsync(platformView.Text);
                            //ClipboardManager clipboard = (ClipboardManager)Context.GetSystemService(Context.ClipboardService);
                            //ClipData clip = ClipData.NewPlainText("label", platformView.Text);
                            //clipboard.SetPrimaryClip(clip);
                            Toast.MakeText(Context, "Text copied to clipboard", ToastLength.Short).Show();
                        };

                        // Set movement method for handling links
                        try
                        {
                            platformView.MovementMethod = LinkMovementMethod.Instance;
                        }
                        catch (Exception ex)
                        {
                            // Handle any exceptions here
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle any exceptions here
            }
        }

        protected override void DisconnectHandler(AppCompatTextView platformView)
        {
            // Clean up events
            platformView.LongClick -= null;
            base.DisconnectHandler(platformView);
        }
    }
}