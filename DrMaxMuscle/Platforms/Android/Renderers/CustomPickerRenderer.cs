﻿using DrMaxMuscle.Platforms.Android.Renderers;
using Microsoft.Maui.Controls.Compatibility;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Platforms.Android.Renderers
{
    public class CustomPickerRenderer : PickerHandler
    {
        public CustomPickerRenderer()
        {
        }

        public CustomPickerRenderer(PropertyMapper mapper, CommandMapper commandMapper)
            : base(mapper, commandMapper)
        {
        }
        protected override void ConnectHandler(MauiPicker platformView)
        {
            base.ConnectHandler(platformView);
            try
            {
                if (platformView != null)
                {
                    platformView.SetPadding(20, 4, 0, 70);
                    platformView.TextSize = 15;
                    platformView.SetBackgroundColor(Constants.AppThemeConstants.BlueColor.ToPlatform());
                }
            }
            catch (Exception ex)
            {

            }
        }

        protected override void DisconnectHandler(MauiPicker platformView)
        {
            base.DisconnectHandler(platformView);
        }
    }
}
