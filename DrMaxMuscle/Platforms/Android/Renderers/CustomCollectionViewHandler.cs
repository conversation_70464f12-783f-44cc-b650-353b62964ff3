﻿using AndroidX.RecyclerView.Widget;
using Microsoft.Maui.Controls.Handlers.Items;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Platforms.Android.Renderers
{
    public class CustomCollectionViewHandler : CollectionViewHandler
    {
        protected override RecyclerView CreatePlatformView()
        {
            var recyclerView = base.CreatePlatformView();
            return recyclerView;
        }

        public void ScrollByPixels(int pixels)
        {
            PlatformView?.ScrollBy(0, pixels); // Scroll down by a fixed number of pixels
        }
    }
}
