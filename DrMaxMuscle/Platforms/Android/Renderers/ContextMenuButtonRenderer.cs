﻿using DrMaxMuscle.Controls;

using Android.Content;
using Microsoft.Maui.Controls.Compatibility.Platform.Android.AppCompat;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using Microsoft.Maui.Controls.Platform;
using AndroidGraphics = Android.Graphics;
using Microsoft.Maui.Controls.Compatibility;
using DrMaxMuscle.Platforms.Android.Renderers;
using Microsoft.Maui.Handlers;

namespace DrMaxMuscle.Platforms.Android.Renderers
{
    public class ContextMenuButtonRenderer : ImageButtonHandler
    {
        public ContextMenuButtonRenderer()
        {
            Mapper.AppendToMapping(nameof(ContextMenuButton), (handler, view) =>
            {
                try
                {
                    if (handler != null && handler.PlatformView != null)
                    {
                        if (view is ContextMenuButton)
                        {
                            ((ContextMenuButton)view).GetCoordinates = () => GetCoordinatesNative(handler);
                        }
                    }
                }
                catch (Exception ex)
                {

                }
            });
        }

        private (int x, int y) GetCoordinatesNative(IImageButtonHandler handler)
        {
            try
            {
                var displayMetrics = MainActivity._currentActivity.Resources.DisplayMetrics;
                var density = displayMetrics.Density;

                var screenHeight = displayMetrics.HeightPixels;
                var appHeight = Application.Current.MainPage.Height;
                var heightOffset = screenHeight / density - appHeight;
                var windowBounds = new AndroidGraphics.Rect();
                var coords = new int[2];
                if (handler != null && handler.PlatformView != null)
                {                   
                    handler.PlatformView?.GetWindowVisibleDisplayFrame(windowBounds);                    
                    handler.PlatformView?.GetLocationOnScreen(coords);                    
                }
                return ((int)(coords[0] / density) + 18, (int)((coords[1] - windowBounds.Top) / density));
            }
            catch (Exception ex)
            {
                return (512, 200);
            }
        }
    }
}