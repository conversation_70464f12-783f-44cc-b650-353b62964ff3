﻿using Android.OS;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Platforms.Android.Dependencies;
using Firebase.Analytics;
using AndroidApp = Android.App.Application;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
//[assembly: Dependency(typeof(Firebase_Droid))]
namespace DrMaxMuscle.Platforms.Android.Dependencies
{
    public class Firebase_Droid : IFirebase
    {
        public void LogEvent(string key, string val)
        {
            FirebaseAnalytics firebaseAnalytics = FirebaseAnalytics.GetInstance(global::Android.App.Application.Context);
            var bundle = new Bundle();
            bundle.PutString(key, val);
            try
            {

                if (LocalDBManager.Instance.GetDBSetting("email") != null && LocalDBManager.Instance.GetDBSetting("email").Value.Contains("yopmail") || (LocalDBManager.Instance.GetDBSetting("Environment") != null && LocalDBManager.Instance.GetDBSetting("Environment").Value != "Production"))
                { }
                else if (LocalDBManager.Instance.GetDBSetting("email") != null && LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>"))
                {

                }
                else
                    firebaseAnalytics.LogEvent(key, bundle);
            }
            catch (Exception ex)
            {

            }

        }

        public void SetScreenName(string val)
        {
            FirebaseAnalytics firebaseAnalytics = FirebaseAnalytics.GetInstance(global::Android.App.Application.Context);
            try
            {

                if (LocalDBManager.Instance.GetDBSetting("email") != null && LocalDBManager.Instance.GetDBSetting("email").Value.Contains("yopmail") || (LocalDBManager.Instance.GetDBSetting("Environment") != null && LocalDBManager.Instance.GetDBSetting("Environment").Value != "Production"))
                { }
                else
                    firebaseAnalytics.SetCurrentScreen(MainActivity._currentActivity, val, null);

            }
            catch (Exception ex)
            {

            }
        }

        public void SetUserId(string name)
        {
            FirebaseAnalytics firebaseAnalytics = FirebaseAnalytics.GetInstance(global::Android.App.Application.Context);
            firebaseAnalytics.SetUserId(name);
        }
    }
}