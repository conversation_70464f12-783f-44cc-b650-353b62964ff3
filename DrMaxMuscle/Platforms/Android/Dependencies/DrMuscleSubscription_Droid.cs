﻿using Android.Content;
using Android.Content.PM;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMuscleWebApiSharedModel;
using Plugin.InAppBilling;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Platforms.Android.Dependencies
{
    public class DrMuscleSubscription_Droid : IDrMuscleSubcription
    {
        public DrMuscleSubscription_Droid()
        {
            PurchaseManager.Instance.OnMonthlyAccessPurchased += Instance_OnMonthlyAccessPurchased;
            PurchaseManager.Instance.OnYearlyAccessPurchased += Instance_OnYearlyAccessPurchased;
            PurchaseManager.Instance.OnMealPlanAccessPurchased += Instance_OnMealPlanAccessPurchased;
        }


        private bool _hasIntroductoryPrice = false;
        public event MonthlyAccessPurchased OnMonthlyAccessPurchased;
        public event YearlyAccessPurchased OnYearlyAccessPurchased;
        public event MealPlanAccessPurchased OnMealPlanAccessPurchased;

        void Instance_OnMonthlyAccessPurchased()
        {
            if (OnMonthlyAccessPurchased != null)
                OnMonthlyAccessPurchased();
        }

        void Instance_OnYearlyAccessPurchased()
        {
            if (OnYearlyAccessPurchased != null)
                OnYearlyAccessPurchased();
        }

        void Instance_OnMealPlanAccessPurchased()
        {
            if (OnMealPlanAccessPurchased != null)
                OnMealPlanAccessPurchased();
        }

        public async Task BuyMonthlyAccess()
        {
            if (_hasIntroductoryPrice)
                PurchaseManager.Instance.BuyMonthlyAccess();
            else
                PurchaseManager.Instance.BuyMonthlyAccessOld();
        }

        public async Task BuyYearlyAccess()
        {
            PurchaseManager.Instance.BuyYearlyAccess();
        }

        public async Task BuyMealPlanAccess()
        {
            PurchaseManager.Instance.BuyMealPlanAccess();
        }


        public async Task<string> GetMonthlyPrice()
        {
            try
            {

                if (PurchaseManager.Instance.Subscriptions == null)
                {
                    await PurchaseManager.Instance.GetInventory();
                }
                if (PurchaseManager.Instance.Subscriptions != null)
                {
                    if (PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly9then49.2021") != null)
                    {
                        if (PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly9then49.2021").AndroidExtras.SubscriptionOfferDetails.FirstOrDefault().PricingPhases?.Count > 1)
                        {
                            _hasIntroductoryPrice = true;
                            return string.Format("{0}", PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly9then49.2021").LocalizedPrice);
                        }
                        return string.Format("{0}", PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly9then49.2021").LocalizedPrice);
                    }
                    if (PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly49").AndroidExtras.SubscriptionOfferDetails.FirstOrDefault().PricingPhases?.Count > 1)
                    {
                        return string.Format("{0}", PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly49").LocalizedPrice);
                    }
                    return string.Format("{0}", PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly49").LocalizedPrice);
                }

            }
            catch (Exception ex)
            {

            }
            return "";
        }

        public async Task<string> GetMonthlyButtonLabel()
        {
            try
            {

                if (PurchaseManager.Instance.Subscriptions == null)
                {
                    await PurchaseManager.Instance.GetInventory();
                }
                if (PurchaseManager.Instance.Subscriptions != null)
                {
                    if (PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly9then49.2021") != null)
                    {
                        try
                        {

                            if (PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly9then49.2021").AndroidExtras.SubscriptionOfferDetails.FirstOrDefault().PricingPhases?.Count > 1)
                            {
                                _hasIntroductoryPrice = true;
                                return string.Format("1st month {0}, then {1}/month", PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly9then49.2021").AndroidExtras.SubscriptionOfferDetails.FirstOrDefault()?.PricingPhases[0].FormattedPrice, PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly9then49.2021").AndroidExtras.SubscriptionOfferDetails.FirstOrDefault()?.PricingPhases[1].FormattedPrice);
                            }

                        }
                        catch (Exception ex)
                        {

                        }
                        _hasIntroductoryPrice = true;
                        return string.Format("Sign up monthly ({0} / month)", PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly9then49.2021").LocalizedPrice);
                    }
                    else if (PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly49").AndroidExtras.SubscriptionOfferDetails.FirstOrDefault().PricingPhases?.Count > 1)
                    {
                        return string.Format("1st month {0}, then {1}/month", PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly49").AndroidExtras.SubscriptionOfferDetails.FirstOrDefault()?.PricingPhases[0].FormattedPrice, PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly49").AndroidExtras.SubscriptionOfferDetails.FirstOrDefault()?.PricingPhases[1].FormattedPrice);
                    }
                    return string.Format("Sign up monthly ({0} / month)", PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.monthly49").LocalizedPrice);
                }

            }
            catch (Exception ex)
            {

            }

            return "";
        }

        public async Task<string> GetMealPlanLabel()
        {
            try
            {


                if (PurchaseManager.Instance.Subscriptions == null)
                {
                    await PurchaseManager.Instance.GetInventory();
                }
                if (PurchaseManager.Instance.Subscriptions != null)
                {
                    if (PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.mealaddon.monthly1then19") != null)
                    {
                        if (PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.mealaddon.monthly1then19").AndroidExtras.SubscriptionOfferDetails.FirstOrDefault().PricingPhases?.Count > 1)
                        {
                            _hasIntroductoryPrice = true;
                            return string.Format("1st month {0}, then {1}/month", PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.mealaddon.monthly1then19").AndroidExtras.SubscriptionOfferDetails.FirstOrDefault()?.PricingPhases[0].FormattedPrice, PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.mealaddon.monthly1then19").AndroidExtras.SubscriptionOfferDetails.FirstOrDefault()?.PricingPhases[1].FormattedPrice);
                        }
                        return string.Format("Sign up monthly ({0}/month)", PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.mealaddon.monthly1then19").LocalizedPrice);
                    }

                    return string.Format("Sign up monthly ({0}/month)", PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.mealaddon.monthly1then19").LocalizedPrice);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            return "";
        }
        public async Task<string> GetYearlyPrice()
        {
            try
            {
                if (PurchaseManager.Instance.Subscriptions == null)
                    await PurchaseManager.Instance.GetInventory();
                return string.Format("{0}/year (4 months free)", PurchaseManager.Instance.Subscriptions.FirstOrDefault(l => l.ProductId == "subscription.annual389.2")?.LocalizedPrice);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);

            }
            return "Sign up Yearly";
        }
        public async Task<string> GetYearlyButtonLabel()
        {
            try
            {


                if (PurchaseManager.Instance.Subscriptions == null)
                    await PurchaseManager.Instance.GetInventory();

                if (PurchaseManager.Instance.Subscriptions != null)
                {
                    return string.Format("{0}/year (4 months free)", PurchaseManager.Instance.Subscriptions.First(l => l.ProductId == "subscription.annual389.2").LocalizedPrice);
                }

            }
            catch (Exception e)
            {
                Console.WriteLine(e);

            }
            return "";
        }

        public bool IsActiveSubscriptions()
        {
            if (PurchaseManager.Instance.SubscriptionPurchases != null)
            {
                if (PurchaseManager.Instance.SubscriptionPurchases.Any(l => l.ProductId == "subscription.monthly49" || l.ProductId == "subscription.monthly9then49.2021"))
                    return true;
                if (PurchaseManager.Instance.SubscriptionPurchases.Any(l => l.ProductId == "subscription.annual389.2"))
                    return true;
            }
            return false;
        }

        public bool IsActiveMealPlan()
        {
            if (PurchaseManager.Instance.SubscriptionPurchases != null)
            {
                if (PurchaseManager.Instance.SubscriptionPurchases.Any(l => l.ProductId == "subscription.mealaddon.monthly1then19"))
                    return true;
            }
            return false;
        }
        public bool IsMealPlanAccessPuchased()
        {
            if (PurchaseManager.Instance.SubscriptionPurchases != null)
            {
                //Add if not exist
                if (PurchaseManager.Instance.SubscriptionPurchases.Any(l => l.ProductId == "subscription.mealaddon.monthly1then19"))
                {
                    //Add if not exist
                    var purchase = PurchaseManager.Instance.SubscriptionPurchases.FirstOrDefault();
                    if (purchase == null)
                        return true;
                    SendDataToServer(purchase, "SubscriptionPurchaseMessage");
                    SendDataToServer(purchase, "SubscriptionPurchaseIfNotExistMessage");
                    if (purchase.ProductId == "mealaddon")
                    {
                        App.IsMealPlan = true;
                    }

                }
                return PurchaseManager.Instance.SubscriptionPurchases.Any(l => l.ProductId == "subscription.mealaddon.monthly1then19");
            }
            return false;
        }

        public bool IsMonthlyAccessPuchased()
        {
            if (PurchaseManager.Instance.SubscriptionPurchases != null)
            {
                //Add if not exist
                if (PurchaseManager.Instance.SubscriptionPurchases.Any(l => l.ProductId == "subscription.monthly9then49.2021"))
                {
                    //Add if not exist
                    var purchase = PurchaseManager.Instance.SubscriptionPurchases.FirstOrDefault();
                    if (purchase == null)
                        return true;
                    if (!CurrentLog.Instance.SendInformationToServer)
                    {
                        CurrentLog.Instance.SendInformationToServer = true;
                        SendDataToServer(purchase, "SubscriptionPurchaseMessage");
                        SendDataToServer(purchase, "SubscriptionPurchaseIfNotExistMessage");
                    }
                    // if (purchase.ProductId == "mealaddon")
                    // {
                    //     App.IsMealPlan = true;
                    // }
                    // else
                    // { 
                    App.IsV1User = true;
                    App.IsV1UserTrial = true;
                    // }
                }
                return PurchaseManager.Instance.SubscriptionPurchases.Any(l => l.ProductId == "subscription.monthly49");
            }
            return false;
        }

        public bool IsYearlyAccessPuchased()
        {
            if (PurchaseManager.Instance.SubscriptionPurchases != null)
            {
                if (PurchaseManager.Instance.SubscriptionPurchases.Any(l => l.ProductId == "subscription.annual389.2"))
                {
                    var purchase = PurchaseManager.Instance.SubscriptionPurchases.FirstOrDefault();
                    if (purchase == null)
                        return true;
                    if (!CurrentLog.Instance.SendInformationToServer)
                    {
                        CurrentLog.Instance.SendInformationToServer = true;
                        SendDataToServer(purchase, "SubscriptionPurchaseMessage");
                        SendDataToServer(purchase, "SubscriptionPurchaseIfNotExistMessage");
                    }
                    if (purchase.ProductId == "mealaddon")
                    {
                        App.IsMealPlan = true;
                    }
                    else
                    {
                        App.IsV1User = true;
                        App.IsV1UserTrial = true;
                    }
                }
                return PurchaseManager.Instance.SubscriptionPurchases.Any(l => l.ProductId == "subscription.annual389.2");
            }

            return false;
        }

        public void Init()
        {
            PurchaseManager i = PurchaseManager.Instance;
        }

        public void RestorePurchases()
        {
            //throw new NotImplementedException();
        }

        public string GetBuildVersion()
        {
            //Context context = Forms.Context;
            Context context = global::Android.App.Application.Context;
            PackageManager manager = context.PackageManager;
            PackageInfo i = manager.GetPackageInfo(context.PackageName, 0);
            return string.Format("Version {0} - Build {1}", i.VersionName, i.VersionCode);
        }

        private void SendDataToServer(InAppBillingPurchase purchase, string msg)
        {
            //TimeSpan time = TimeSpan.FromMilliseconds(purchase.PurchaseTime);
            DateTime expirydate = purchase.TransactionDateUtc.ToLocalTime(); //new DateTime(1970, 1, 1) + time;
            SubscriptionModel subscription = new SubscriptionModel()
            {
                PurchaseToken = purchase.PurchaseToken,
                ExpiryDate = expirydate,
                ProductId = purchase.ProductId,
                Platform = 0,
                OrderId = purchase.Id,
            };
            MessagingCenter.Send<SubscriptionModel>(subscription, msg);
        }
    }
}
