﻿using System;
using Android.App;
using Android.Content;
using AndroidApp = Android.App.Application;
using Android.Views.InputMethods;

namespace DrMaxMuscle.Platforms.Android.Dependencies
{
	public class DroidKeyboardHelper : DrMaxMuscle.Dependencies.IKeyboardHelper
	{

		public void HideKeyboard()
		{
			var context = AndroidApp.Context;
			var inputMethodManager = context.GetSystemService(Context.InputMethodService) as InputMethodManager;
			if (inputMethodManager != null )
			{
				try
				{

				var token = MainActivity._currentActivity.CurrentFocus?.WindowToken;
				inputMethodManager.HideSoftInputFromWindow(token, HideSoftInputFlags.None);

				//activity.Window.DecorView.ClearFocus();

                }
                catch (Exception ex)
                {

                }
            }
		}
	}
}
