﻿using Android.Media;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Platforms.Android.Dependencies;
using Plugin.Vibrate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
//[assembly: Dependency(typeof(AudioService))]
namespace DrMaxMuscle.Platforms.Android.Dependencies
{
    public class AudioService : IAudio
    {
        MediaPlayer player;
        public AudioService()
        {
        }

        public void PlayAudioFile(string fileName, bool sound, bool vibrate,bool IsFromComplete, float volume = 1)
        {
            try
            {

                if (sound)
                {

                    player = new MediaPlayer();
                    var fd = global::Android.App.Application.Context.Assets.OpenFd(fileName);
                    if (player != null && fd != null)
                    {
                        player.Prepared += (s, e) =>
                        {
                            player.SetVolume(volume, volume);
                            player.Start();
                        };
                        player.SetDataSource(fd.FileDescriptor, fd.StartOffset, fd.Length);
                        player.Prepare();
                        try
                        {
                            player.Completion += (sender, e) =>
                            {
                                try
                                {
                                    player.Release();
                                    player = null;
                                }
                                catch (Exception ex)
                                {

                                }
                            };
                        }
                        catch (Exception ex)
                        {

                        }
                    }
                }

                if (vibrate){
                    if(IsFromComplete){
                        CrossVibrate.Current.Vibration(TimeSpan.FromSeconds(2));
                    }else{
                        CrossVibrate.Current.Vibration(TimeSpan.FromSeconds(3));
                    }
                }
                    

            }
            catch (Exception ex)
            {

            }
        }
    }
}