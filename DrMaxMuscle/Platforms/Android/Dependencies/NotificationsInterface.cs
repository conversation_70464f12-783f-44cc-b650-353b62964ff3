﻿using AndroidX.Core.App;
using DrMaxMuscle.Dependencies;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

// Created by : inam
// purpose : This interface is implemented to enable notification in android.

[assembly: Dependency(typeof(DrMaxMuscle.Platforms.Android.Dependencies.NotificationsInterface))]
namespace DrMaxMuscle.Platforms.Android.Dependencies
{
    public class NotificationsInterface : INotificationsInterface
    {
        public NotificationsInterface()
        {
        }

        public bool registeredForNotifications()
        {
            try
            {
                var context = Microsoft.Maui.ApplicationModel.Platform.CurrentActivity?.ApplicationContext;

                var nm = NotificationManagerCompat.From(context);
                bool enabled = nm.AreNotificationsEnabled();
                return enabled;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }
}
