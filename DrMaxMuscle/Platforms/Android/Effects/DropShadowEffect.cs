﻿using System;
using System.Linq;
using DrMaxMuscle.Effects;
using Microsoft.Maui.Controls.Platform;
using Graphics = Android.Graphics;
using AndroidOS = Android.OS;
using AndroidView = Android.Views;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
//[assembly: ResolutionGroupName("CubiSoft")]
//[assembly: ExportEffect(typeof(DropShadowEffect), "DropShadowEffect")]
namespace DrMaxMuscle.Platforms.Android.Effects
{
	public class DropShadowEffect : PlatformEffect
	{
		protected override void OnAttached()
		{
			try
			{
				var control = Control??Container as AndroidView.View;

				var effect = (ViewShadowEffect)Element.Effects.FirstOrDefault(e => e is ViewShadowEffect);

				if (effect != null)
				{
					float radius = effect.Radius;
                    Graphics.Color color = effect.Color.ToAndroid();
                    //control.SetShadowLayer(radius, distanceX, distanceY, color);

                    //int currentapiVersion = Android.OS.Build.VERSION.SdkInt;
                    if (AndroidOS.Build.VERSION.SdkInt >= AndroidOS.BuildVersionCodes.Lollipop)
                    {
                        // Do something for lollipop and above versions
                        control.Elevation = radius;
                        control.TranslationZ = (effect.DistanceX + effect.DistanceY) / 2;
                    }
                    else
                    {
                        // do something for phones running an SDK before lollipop
                    }
                    
				}
			}
			catch (Exception ex)
			{
				Console.WriteLine("Cannot set property on attached control. Error: {0}", ex.Message);
			}
		}

		protected override void OnDetached()
		{
		}
	}
}
