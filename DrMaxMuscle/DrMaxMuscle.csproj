﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFrameworks>net8.0-android;net8.0-ios;</TargetFrameworks>
		<!--<TargetFrameworks>net8.0-android;</TargetFrameworks>-->
		<!--<TargetFrameworks>net8.0-ios;</TargetFrameworks>-->
		<!--<TargetFrameworks>net7.0-android;net7.0-ios;net7.0-maccatalyst</TargetFrameworks>-->
		<!--<TargetFrameworks
		Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net7.0-windows10.0.19041.0</TargetFrameworks>-->
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following
		this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net7.0-tizen</TargetFrameworks> -->
		<!--<RuntimeIdentifiers>ios-arm64</RuntimeIdentifiers>-->
		<!-- <ForceSimulatorX64ArchitectureInIDE>true</ForceSimulatorX64ArchitectureInIDE> -->
		<OutputType>Exe</OutputType>
		<RootNamespace>DrMaxMuscle</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<!-- Display name -->
		<ApplicationTitle>Dr. Muscle</ApplicationTitle>
		<!-- App Identifier -->
		<ApplicationId>com.drmaxmuscle.dr_max_muscle</ApplicationId>
		<ApplicationIdGuid>49d2b6b3-a0b7-4ccc-95be-c02b75bf7e21</ApplicationIdGuid>
		<!-- Versions will be set dynamically by CI/CD workflow -->
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">
			11.0</SupportedOSPlatformVersion>
		<!--<SupportedOSPlatformVersion
		Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) ==
		'maccatalyst'">13.1</SupportedOSPlatformVersion>-->
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">
			21.0</SupportedOSPlatformVersion>
		<!-- Assembly and File versions will be set dynamically by CI/CD workflow -->
		<!--<SupportedOSPlatformVersion
		Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) ==
		'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion
		Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) ==
		'windows'">10.0.17763.0</TargetPlatformMinVersion>
		<SupportedOSPlatformVersion
		Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) ==
		'tizen'">6.5</SupportedOSPlatformVersion>-->
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0-android|AnyCPU'">
		<AndroidPackageFormat>aab</AndroidPackageFormat>
		<ApplicationId>com.drmaxmuscle.dr_max_muscle</ApplicationId>
		<AndroidKeyStore>True</AndroidKeyStore>
		<AndroidSigningKeyStore>/Users/<USER>/Documents/Projects/DrMuscleapp/publishingdoc.keystore</AndroidSigningKeyStore>
		<AndroidSigningStorePass>db2MicorSystem</AndroidSigningStorePass>
		<AndroidSigningKeyAlias>publishingdoc</AndroidSigningKeyAlias>
		<AndroidSigningKeyPass>db2MicorSystem</AndroidSigningKeyPass>
		<AndroidLinkTool>r8</AndroidLinkTool>
		<JavaMaximumHeapSize>2G</JavaMaximumHeapSize>
		<AndroidEnableMultiDex>true</AndroidEnableMultiDex>
		<!-- Sends symbols to Sentry, enabling symbolication of stack traces. -->
		<SentryUploadSymbols>true</SentryUploadSymbols>
		<!-- Sends sources to Sentry, enabling display of source context. -->
		<SentryUploadSources>true</SentryUploadSources>
		<!-- If you are targeting Android, sends proguard mapping file to Sentry. -->
		<SentryUploadAndroidProguardMapping>true</SentryUploadAndroidProguardMapping>
	</PropertyGroup>
	<PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
		<WarningLevel>4</WarningLevel>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net8.0-android|AnyCPU'">
		<_MauiForceXamlCForDebug>true</_MauiForceXamlCForDebug>
		<ApplicationId>com.drmaxmuscle.dr_max_muscle</ApplicationId>
		<!--<AndroidSigningKeyStore>../publishingdoc.keystore</AndroidSigningKeyStore>
	  <AndroidSigningStorePass>nmrojvbnpG2B</AndroidSigningStorePass>
	  <AndroidSigningKeyAlias>publishingdoc</AndroidSigningKeyAlias>
	  <AndroidSigningKeyPass>nmrojvbnpG2B</AndroidSigningKeyPass>
	  <JavaMaximumHeapSize>2G</JavaMaximumHeapSize>
	  <AndroidKeyStore>True</AndroidKeyStore>-->
	</PropertyGroup>
	<PropertyGroup>
		<NoSymbolStrip>true</NoSymbolStrip>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0-android34.0|AnyCPU'">
		<AndroidPackageFormat>aab</AndroidPackageFormat>
		<Optimize>true</Optimize>
		<!-- Android versions will be set dynamically by CI/CD workflow -->
		<AndroidUseAapt2>True</AndroidUseAapt2>
		<AndroidCreatePackagePerAbi>False</AndroidCreatePackagePerAbi>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net8.0-ios|AnyCPU'">
		<SupportedOSPlatformVersion>13.0</SupportedOSPlatformVersion>
		<_MauiForceXamlCForDebug>True</_MauiForceXamlCForDebug>
		<UseInterpreter>False</UseInterpreter>
		<!-- iOS versions will be set dynamically by CI/CD workflow -->
		<CreatePackage>false</CreatePackage>
		<CodesignProvision>Automatic</CodesignProvision>
		<CodesignKey>iPhone Developer</CodesignKey>
		<MtouchEnableBitcode>false</MtouchEnableBitcode>
		<MtouchLink>SdkOnly</MtouchLink>
		<_ExportSymbolsExplicitly>false</_ExportSymbolsExplicitly>
		<ApplicationId>com.drmaxmuscle.dr_max_muscle</ApplicationId>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net8.0-android34.0|AnyCPU'">
		<_MauiForceXamlCForDebug>true</_MauiForceXamlCForDebug>
		<!-- Android Debug versions will be set dynamically by CI/CD workflow -->
		<AndroidPackageFormat>apk</AndroidPackageFormat>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0-ios|AnyCPU'">
		<SupportedOSPlatformVersion>13.0</SupportedOSPlatformVersion>
		<!--<DebugType>portable</DebugType>
		<DebugSymbols>true</DebugSymbols>
		<EnableCrashlytics>true</EnableCrashlytics>-->
		<!-- <MtouchDebug>true</MtouchDebug> -->
		<MtouchExtraArgs>--dsym=true</MtouchExtraArgs>
		<MtouchEnableBitcode>false</MtouchEnableBitcode>
		<MtouchLink>SdkOnly</MtouchLink>
		<_ExportSymbolsExplicitly>false</_ExportSymbolsExplicitly>
		<!-- iOS versions will be set dynamically by CI/CD workflow -->
		<CreatePackage>false</CreatePackage>

		<ApplicationId>com.drmaxmuscle.dr_max_muscle</ApplicationId>
		<UseInterpreter>true</UseInterpreter>
		<MtouchExtraArgs>--optimize:-static-block-to-delegate-lookup</MtouchExtraArgs>
		<!-- Sends symbols to Sentry, enabling symbolication of stack traces. -->
		<SentryUploadSymbols>true</SentryUploadSymbols>
		<!-- Sends sources to Sentry, enabling display of source context. -->
		<SentryUploadSources>true</SentryUploadSources>
		<!-- If you are targeting Android, sends proguard mapping file to Sentry. -->
		<SentryUploadAndroidProguardMapping>true</SentryUploadAndroidProguardMapping>
	</PropertyGroup>

	<PropertyGroup Condition="'$(TargetFramework)' == 'net8.0-ios' and '$(IsDeviceBuild)' == 'true'">
		<RuntimeIdentifier>ios-arm64</RuntimeIdentifier>
		<MtouchArgs>--gcc_flags -L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos</MtouchArgs>
	</PropertyGroup>

	<PropertyGroup Condition="'$(TargetFramework)' == 'net8.0-ios' and '$(IsDeviceBuild)' != 'true'">
		<RuntimeIdentifier>iossimulator-x64</RuntimeIdentifier>
		<MtouchArgs>--gcc_flags -L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator</MtouchArgs>
	</PropertyGroup>

	<PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
		<DebugSymbols>true</DebugSymbols>
		<WarningLevel>4</WarningLevel>
	</PropertyGroup>
	<ItemGroup>
		<!-- App Icon -->
		<!--<AndroidResource
		Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg"
		Color="#512BD4" />
		<AndroidResource Include="Resources\Images\appicon.svg" />-->
		<MauiIcon Include="Resources\AppIcon\icon.png" ForegroundScale="0.9" />
		<!-- Splash Screen -->
		<!--<MauiSplashScreen
		Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />-->
		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.svg" BaseSize="168,208" />
		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />
		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
		<MauiAsset Include="Exercises.json" />
		<MauiIcon Include="Resources\AppIcon\iosicon.svg" />
	</ItemGroup>
	<ItemGroup>
		<AndroidResource Remove="Platforms\MacCatalyst\**" />
		<AndroidResource Remove="Platforms\Tizen\**" />
		<AndroidResource Remove="Platforms\Windows\**" />
		<Compile Remove="Platforms\MacCatalyst\**" />
		<Compile Remove="Platforms\Tizen\**" />
		<Compile Remove="Platforms\Windows\**" />
		<EmbeddedResource Remove="Platforms\MacCatalyst\**" />
		<EmbeddedResource Remove="Platforms\Tizen\**" />
		<EmbeddedResource Remove="Platforms\Windows\**" />
		<MauiCss Remove="Platforms\MacCatalyst\**" />
		<MauiCss Remove="Platforms\Tizen\**" />
		<MauiCss Remove="Platforms\Windows\**" />
		<MauiXaml Remove="Platforms\MacCatalyst\**" />
		<MauiXaml Remove="Platforms\Tizen\**" />
		<MauiXaml Remove="Platforms\Windows\**" />
		<None Remove="Platforms\MacCatalyst\**" />
		<None Remove="Platforms\Tizen\**" />
		<None Remove="Platforms\Windows\**" />
		<None Remove="Platforms\iOS\Resources\" />
		<MauiImage Remove="Resources\Images\appicon.svg" />
		<MauiImage Remove="Resources\Images\dr_splash_news.svg" />
		<None Remove="Resources\AppIcon\Icon.png" />
		<None Remove="Resources\Images\alert_ic_blue.svg" />
		<None Remove="Resources\Images\appicon.svg" />
		<None Remove="Exercises.json" />
		<None Remove="Resources\Images\chekedgreen.svg" />
		<None Remove="Resources\Images\clock.svg" />
		<None Remove="Resources\Images\dr_splash_news.svg" />
		<None Remove="Resources\Images\edit_solid.svg" />
		<None Remove="Resources\Images\exercise.svg" />
		<None Remove="Resources\Images\fire.svg" />
		<None Remove="Resources\Images\firstworkout.svg" />
		<None Remove="Resources\Images\gradient_down_arrow.svg" />
		<None Remove="Resources\Images\ic_happy.svg" />
		<None Remove="Resources\Images\ic_neutral.svg" />
		<None Remove="Resources\Images\ic_sad.svg" />
		<None Remove="Resources\Images\ic_share.svg" />
		<None Remove="Resources\Images\medal.svg" />
		<None Remove="Resources\Images\nextworkout.svg" />
		<None Remove="Resources\Images\open.svg" />
		<None Remove="Resources\Images\progressicon.svg" />
		<None Remove="Resources\Images\records.svg" />
		<None Remove="Resources\Images\restart_icon.svg" />
		<None Remove="Resources\Images\resume_icon.svg" />
		<None Remove="Resources\Images\resume_icon_white.png" />
		<None Remove="Resources\Images\save_icon.png" />
		<None Remove="Resources\Images\startrophy.svg" />
		<None Remove="Resources\Images\survey_icon.svg" />
		<None Remove="Resources\Images\typing_loader.gif" />
		<None Remove="Resources\Images\workoutnow.svg" />
		<None Remove="Resources\Raw\alarma.mp3" />
		<None Remove="Resources\Raw\emptyAudio.wav" />
		<None Remove="Resources\Raw\reps1.mp3" />
		<None Remove="Resources\Raw\reps2.mp3" />
		<None Remove="Resources\Raw\reps3.mp3" />
		<None Remove="Resources\Raw\reps4.mp3" />
		<None Remove="Resources\Raw\reps5.mp3" />
		<None Remove="Resources\Raw\reps6.mp3" />
		<None Remove="Resources\Raw\reps7.mp3" />
		<None Remove="Resources\Raw\reps8.mp3" />
		<None Remove="Resources\Raw\reps9.mp3" />
		<None Remove="Resources\Raw\reps10.mp3" />
		<None Remove="Resources\Raw\reps11.mp3" />
		<None Remove="Resources\Raw\reps12.mp3" />
		<None Remove="Resources\Raw\reps13.mp3" />
		<None Remove="Resources\Raw\reps14.mp3" />
		<None Remove="Resources\Raw\reps15.mp3" />
		<None Remove="Resources\Raw\reps16.mp3" />
		<None Remove="Resources\Raw\reps17.mp3" />
		<None Remove="Resources\Raw\reps18.mp3" />
		<None Remove="Resources\Raw\reps19.mp3" />
		<None Remove="Resources\Raw\reps20.mp3" />
		<None Remove="Resources\Raw\reps21.mp3" />
		<None Remove="Resources\Raw\reps22.mp3" />
		<None Remove="Resources\Raw\reps23.mp3" />
		<None Remove="Resources\Raw\reps24.mp3" />
		<None Remove="Resources\Raw\reps25.mp3" />
		<None Remove="Resources\Raw\reps26.mp3" />
		<None Remove="Resources\Raw\reps27.mp3" />
		<None Remove="Resources\Raw\reps28.mp3" />
		<None Remove="Resources\Raw\reps29.mp3" />
		<None Remove="Resources\Raw\reps30.mp3" />
		<None Remove="Resources\Raw\reps31.mp3" />
		<None Remove="Resources\Raw\reps32.mp3" />
		<None Remove="Resources\Raw\reps33.mp3" />
		<None Remove="Resources\Raw\reps34.mp3" />
		<None Remove="Resources\Raw\reps35.mp3" />
		<None Remove="Resources\Raw\reps36.mp3" />
		<None Remove="Resources\Raw\reps37.mp3" />
		<None Remove="Resources\Raw\reps38.mp3" />
		<None Remove="Resources\Raw\reps39.mp3" />
		<None Remove="Resources\Raw\reps40.mp3" />
		<None Remove="Resources\Raw\reps41.mp3" />
		<None Remove="Resources\Raw\reps42.mp3" />
		<None Remove="Resources\Raw\reps43.mp3" />
		<None Remove="Resources\Raw\reps44.mp3" />
		<None Remove="Resources\Raw\reps45.mp3" />
		<None Remove="Resources\Raw\reps46.mp3" />
		<None Remove="Resources\Raw\reps47.mp3" />
		<None Remove="Resources\Raw\reps48.mp3" />
		<None Remove="Resources\Raw\reps49.mp3" />
		<None Remove="Resources\Raw\reps50.mp3" />
		<None Remove="Resources\Raw\reps51.mp3" />
		<None Remove="Resources\Raw\reps52.mp3" />
		<None Remove="Resources\Raw\reps53.mp3" />
		<None Remove="Resources\Raw\reps54.mp3" />
		<None Remove="Resources\Raw\reps55.mp3" />
		<None Remove="Resources\Raw\reps56.mp3" />
		<None Remove="Resources\Raw\reps57.mp3" />
		<None Remove="Resources\Raw\reps58.mp3" />
		<None Remove="Resources\Raw\reps59.mp3" />
		<None Remove="Resources\Raw\reps60.mp3" />
		<None Remove="Resources\Raw\timer123.mp3" />
		<None Remove="Resources\Images\deleteset.svg" />
		<None Remove="Resources\Images\deleteset_yellow.svg" />
		<None Remove="Resources\Images\history.svg" />
		<None Remove="Resources\Images\done2.svg" />
		<None Remove="Resources\Images\android_back.svg" />
		<None Remove="Resources\Images\calander.svg" />
		<None Remove="Resources\Images\orange.svg" />
		<None Remove="Resources\AppIcon\iosicon.svg" />
		<None Remove="Resources\Images\my_exercises.svg" />
		<None Remove="Resources\Images\favorites.svg" />
		<None Remove="Resources\Images\more_dark_blue.svg" />
		<None Remove="Resources\Images\icon_search_gray.svg" />
		<None Remove="Resources\Images\bar.svg" />
		<None Remove="Resources\Images\barblank.svg" />
		<None Remove="Resources\Images\barblankHalf.svg" />
		<None Remove="Resources\Images\barhalf.svg" />
		<None Remove="Resources\Images\barkg.svg" />
		<None Remove="Resources\Images\barKghalf.svg" />
		<None Remove="Resources\Images\plate2half.svg" />
		<None Remove="Resources\Images\plate2halfhalf.svg" />
		<None Remove="Resources\Images\plate5.svg" />
		<None Remove="Resources\Images\plate5half.svg" />
		<None Remove="Resources\Images\plate10.svg" />
		<None Remove="Resources\Images\plate10half.svg" />
		<None Remove="Resources\Images\plate25.svg" />
		<None Remove="Resources\Images\plate25half.svg" />
		<None Remove="Resources\Images\plate35.svg" />
		<None Remove="Resources\Images\plate35half.svg" />
		<None Remove="Resources\Images\plate45.svg" />
		<None Remove="Resources\Images\plate45half.svg" />
		<None Remove="Resources\Images\platekg1.svg" />
		<None Remove="Resources\Images\platekg1half.svg" />
		<None Remove="Resources\Images\platekg2.svg" />
		<None Remove="Resources\Images\platekg2half.svg" />
		<None Remove="Resources\Images\plateKg5.svg" />
		<None Remove="Resources\Images\platekg05.svg" />
		<None Remove="Resources\Images\platekg5half.svg" />
		<None Remove="Resources\Images\platekg05half.svg" />
		<None Remove="Resources\Images\platekg10.svg" />
		<None Remove="Resources\Images\platekg10half.svg" />
		<None Remove="Resources\Images\platekg15.svg" />
		<None Remove="Resources\Images\platekg15half.svg" />
		<None Remove="Resources\Images\platekg20.svg" />
		<None Remove="Resources\Images\platekg20half.svg" />
		<None Remove="Resources\Images\platekg25.svg" />
		<None Remove="Resources\Images\platekg25half.svg" />
		<None Remove="Resources\Images\rplate2half.svg" />
		<None Remove="Resources\Images\rplate2halfhalf.svg" />
		<None Remove="Resources\Images\rplate5.svg" />
		<None Remove="Resources\Images\rplate5half.svg" />
		<None Remove="Resources\Images\rplate10.svg" />
		<None Remove="Resources\Images\rplate10half.svg" />
		<None Remove="Resources\Images\rplate25.svg" />
		<None Remove="Resources\Images\rplate25half.svg" />
		<None Remove="Resources\Images\rplate35.svg" />
		<None Remove="Resources\Images\rplate35half.svg" />
		<None Remove="Resources\Images\rplate45.svg" />
		<None Remove="Resources\Images\rplate45half.svg" />
		<None Remove="Resources\Images\rplatekg1.svg" />
		<None Remove="Resources\Images\rplatekg1half.svg" />
		<None Remove="Resources\Images\rplatekg2.svg" />
		<None Remove="Resources\Images\rplatekg2half.svg" />
		<None Remove="Resources\Images\rplatekg5.svg" />
		<None Remove="Resources\Images\rplatekg05.svg" />
		<None Remove="Resources\Images\rplatekg5half.svg" />
		<None Remove="Resources\Images\rplatekg05half.svg" />
		<None Remove="Resources\Images\rplatekg10.svg" />
		<None Remove="Resources\Images\rplatekg10half.svg" />
		<None Remove="Resources\Images\rplatekg15.svg" />
		<None Remove="Resources\Images\rplatekg15half.svg" />
		<None Remove="Resources\Images\rplatekg20.svg" />
		<None Remove="Resources\Images\rplatekg20half.svg" />
		<None Remove="Resources\Images\rplatekg25.svg" />
		<None Remove="Resources\Images\rplatekg25half.svg" />
		<None Remove="Resources\Images\custom.svg" />
		<None Remove="Resources\Images\rcustom.svg" />
		<None Remove="Resources\Images\edit_plate.svg" />
		<None Remove="Resources\Images\selected.svg" />
		<None Remove="Resources\Images\dragindicator.svg" />
		<None Remove="Platforms\iOS\Effects\" />
		<None Remove="Platforms\Android\Effects\" />
		<None Remove="Resources\Images\swappedsuccess.svg" />
		<None Remove="Resources\Images\trophy.svg" />
		<None Remove="Resources\Images\topnav.svg" />
		<None Remove="Resources\Images\back_arrow.png" />
	</ItemGroup>
	<ItemGroup>
		<None Remove="Platforms\Android\Resources\drawable\gradientpopupbackground.xml" />
		<None Remove="Platforms\Android\Resources\values\styles.xml" />
		<None Remove="Platforms\Android\Resources\xml\network_security_config.xml" />
		<None Remove="Resources\Images\abs.svg" />
		<None Remove="Resources\Images\abs_transparent.svg" />
		<None Remove="Resources\Images\Airborne_Lunge.gif" />
		<None Remove="Resources\Images\Alternating_Single_Leg_Bridge.gif" />
		<None Remove="Resources\Images\apple.svg" />
		<None Remove="Resources\Images\applefruite.svg" />
		<None Remove="Resources\Images\artin.svg" />
		<None Remove="Resources\Images\back.svg" />
		<None Remove="Resources\Images\backgroundblack.svg" />
		<None Remove="Resources\Images\backspace_white.svg" />
		<None Remove="Resources\Images\back_transparent.svg" />
		<None Remove="Resources\Images\Barbell_Bent_Over_Row.gif" />
		<None Remove="Resources\Images\Barbell_Curl.gif" />
		<None Remove="Resources\Images\Barbell_Deadlift.gif" />
		<None Remove="Resources\Images\Barbell_Decline_Bench_Press.gif" />
		<None Remove="Resources\Images\Barbell_Front_Squat.gif" />
		<None Remove="Resources\Images\Barbell_Full_Squat.gif" />
		<None Remove="Resources\Images\Barbell_Hip_Thrust.gif" />
		<None Remove="Resources\Images\Barbell_Incline_Bench_Press.gif" />
		<None Remove="Resources\Images\Barbell_Reverse_Curl.gif" />
		<None Remove="Resources\Images\Barbell_Romanian_Deadlift.gif" />
		<None Remove="Resources\Images\Barbell_Standing_Leg_Calf_Raise.gif" />
		<None Remove="Resources\Images\Barbell_Sumo_Deadlift.gif" />
		<None Remove="Resources\Images\Barbell_sumo_squat.gif" />
		<None Remove="Resources\Images\Barbell_Underhand_Bent_over_Row.gif" />
		<None Remove="Resources\Images\Bench_press.gif" />
		<None Remove="Resources\Images\Bent_Over_Reverse_Fly_with_bands.gif" />
		<None Remove="Resources\Images\Bent_Over_Row_with_bands.gif" />
		<None Remove="Resources\Images\biceps.svg" />
		<None Remove="Resources\Images\biceps_transparent.svg" />
		<None Remove="Resources\Images\Bicycle_Crunch.gif" />
		<None Remove="Resources\Images\black_down_arrow.svg" />
		<None Remove="Resources\Images\bodyweight.svg" />
		<None Remove="Resources\Images\bodyweight_home.svg" />
		<None Remove="Resources\Images\Bodyweight_Lunge.gif" />
		<None Remove="Resources\Images\Bodyweight_Squat.gif" />
		<None Remove="Resources\Images\Bodyweight_Standing_Calf_Raise.gif" />
		<None Remove="Resources\Images\bottom.svg" />
		<None Remove="Resources\Images\bottom_two.svg" />
		<None Remove="Resources\Images\brandlogo.svg" />
		<None Remove="Resources\Images\Cable_Bar_Lateral_Pulldown.gif" />
		<None Remove="Resources\Images\Cable_Chop.gif" />
		<None Remove="Resources\Images\Cable_Close_Grip_Front_Lat_Pulldown.gif" />
		<None Remove="Resources\Images\Cable_Curl.gif" />
		<None Remove="Resources\Images\Cable_Kneeling_Crunch.gif" />
		<None Remove="Resources\Images\Cable_Overhead_Triceps_Extension.gif" />
		<None Remove="Resources\Images\Cable_Seated_Row.gif" />
		<None Remove="Resources\Images\Cable_Triceps_Pushdown.gif" />
		<None Remove="Resources\Images\Cable_Upright_Row.gif" />
		<None Remove="Resources\Images\calArrow.svg" />
		<None Remove="Resources\Images\calves.svg" />
		<None Remove="Resources\Images\calves_transparent.svg" />
		<None Remove="Resources\Images\cardio.svg" />
		<None Remove="Resources\Images\cardio_transparent.svg" />
		<None Remove="Resources\Images\carlphoto.svg" />
		<None Remove="Resources\Images\chain.svg" />
		<None Remove="Resources\Images\chat_tab.svg" />
		<None Remove="Resources\Images\chest.svg" />
		<None Remove="Resources\Images\chest_transparent.svg" />
		<None Remove="Resources\Images\Chin_Up.gif" />
		<None Remove="Resources\Images\Chin_up_UnderHand.gif" />
		<None Remove="Resources\Images\close.svg" />
		<None Remove="Resources\Images\close_eye.svg" />
		<None Remove="Resources\Images\Close_Gray.svg" />
		<None Remove="Resources\Images\Cossack_Squat.gif" />
		<None Remove="Resources\Images\Crab_Reach.gif" />
		<None Remove="Resources\Images\crunch.gif" />
		<None Remove="Resources\Images\Crunch_arms_overhead.gif" />
		<None Remove="Resources\Images\Deadlift_with_bands.gif" />
		<None Remove="Resources\Images\Decline_Push_Up.gif" />
		<None Remove="Resources\Images\Diamond_Push_up.gif" />
		<None Remove="Resources\Images\done.svg" />
		<None Remove="Resources\Images\done_two.svg" />
		<None Remove="Resources\Images\down.svg" />
		<None Remove="Resources\Images\down_arrow.svg" />
		<None Remove="Resources\Images\Down_Upward_Dog.gif" />
		<None Remove="Resources\Images\dr_icon_white.svg" />
		<None Remove="Resources\Images\dr_muscle_logo.svg" />
		<None Remove="Resources\Images\dr_sign.svg" />
		<None Remove="Resources\Images\Dumbbell_Bent_ove_Row.gif" />
		<None Remove="Resources\Images\Dumbbell_Biceps_Curl.gif" />
		<None Remove="Resources\Images\Dumbbell_Front_Raise.gif" />
		<None Remove="Resources\Images\Dumbbell_Lateral_Raise.gif" />
		<None Remove="Resources\Images\Dumbbell_Rear_Fly.gif" />
		<None Remove="Resources\Images\Dumbbell_Revers_grip_Biceps_Curl.gif" />
		<None Remove="Resources\Images\Dumbbell_Side_Bend.gif" />
		<None Remove="Resources\Images\Dumbbell_Split_Squat.gif" />
		<None Remove="Resources\Images\Dumbbell_Squat.gif" />
		<None Remove="Resources\Images\Dumbbell_Standing_Overhead_Press.gif" />
		<None Remove="Resources\Images\Dumbbell_Standing_Triceps_Extension.gif" />
		<None Remove="Resources\Images\Dumbbell_Straight_Arm_Twisting_Crunch.gif" />
		<None Remove="Resources\Images\Dumbbell_Sumo_Squat.gif" />
		<None Remove="Resources\Images\emptystar.svg" />
		<None Remove="Resources\Images\exercise_background.svg" />
		<None Remove="Resources\Images\facebook_icon.svg" />
		<None Remove="Resources\Images\Feet_Elevated_Diamond_Push_up.gif" />
		<None Remove="Resources\Images\finishSet_orange.svg" />
		<None Remove="Resources\Images\flexed_biceps.svg" />
		<None Remove="Resources\Images\flexibility.svg" />
		<None Remove="Resources\Images\forearm.svg" />
		<None Remove="Resources\Images\forearm_transparent.svg" />
		<None Remove="Resources\Images\Front_Squat_with_bands.gif" />
		<None Remove="Resources\Images\Glutes_Bridge.gif" />
		<None Remove="Resources\Images\google_icon.svg" />
		<None Remove="Resources\Images\gradient_background.svg" />
		<None Remove="Resources\Images\green.svg" />
		<None Remove="Resources\Images\Hammer_Curl_with_bands.gif" />
		<None Remove="Resources\Images\heartIcon.svg" />
		<None Remove="Resources\Images\hide.svg" />
		<None Remove="Resources\Images\High_Knee_to_Butt_Kick.gif" />
		<None Remove="Resources\Images\home_tab.svg" />
		<None Remove="Resources\Images\icon_1.svg" />
		<None Remove="Resources\Images\ic_edit.svg" />
		<None Remove="Resources\Images\ic_handshake.svg" />
		<None Remove="Resources\Images\ic_heart.svg" />
		<None Remove="Resources\Images\ic_meal_plan.svg" />
		<None Remove="Resources\Images\ic_minus.svg" />
		<None Remove="Resources\Images\ic_plus.svg" />
		<None Remove="Resources\Images\ic_share_exercise.svg" />
		<None Remove="Resources\Images\Inch_Worm.gif" />
		<None Remove="Resources\Images\infoicon.svg" />
		<None Remove="Resources\Images\jonus.svg" />
		<None Remove="Resources\Images\Kneeling_Push_up.gif" />
		<None Remove="Resources\Images\Knee_Touch_Crunch.gif" />
		<None Remove="Resources\Images\lamp.svg" />
		<None Remove="Resources\Images\Lateral_Raise_with_bands.gif" />
		<None Remove="Resources\Images\leaves.svg" />
		<None Remove="Resources\Images\leaves2.svg" />
		<None Remove="Resources\Images\legs.svg" />
		<None Remove="Resources\Images\legs_transparent.svg" />
		<None Remove="Resources\Images\Leg_Curl.gif" />
		<None Remove="Resources\Images\Leg_Press.gif" />
		<None Remove="Resources\Images\light_blue_arrow.svg" />
		<None Remove="Resources\Images\light_blue_arrow_down.svg" />
		<None Remove="Resources\Images\lists.svg" />
		<None Remove="Resources\Images\logo.svg" />
		<None Remove="Resources\Images\logo1.svg" />
		<None Remove="Resources\Images\logo2.svg" />
		<None Remove="Resources\Images\lower_back.svg" />
		<None Remove="Resources\Images\lower_back_transparent.svg" />
		<None Remove="Resources\Images\Lying_Barbell_Triceps_Extension_Skullcrusher.gif" />
		<None Remove="Resources\Images\mail_icon.svg" />
		<None Remove="Resources\Images\menu.svg" />
		<None Remove="Resources\Images\menu_blue.svg" />
		<None Remove="Resources\Images\me_tab.svg" />
		<None Remove="Resources\Images\middle.svg" />
		<None Remove="Resources\Images\middle2.svg" />
		<None Remove="Resources\Images\nav.svg" />
		<None Remove="Resources\Images\neck.svg" />
		<None Remove="Resources\Images\neck_transparent.svg" />
		<None Remove="Resources\Images\open_eye.svg" />
		<None Remove="Resources\Images\orange2.svg" />
		<None Remove="Resources\Images\Overhead_Shoulder_Stretch.gif" />
		<None Remove="Resources\Images\page2.svg" />
		<None Remove="Resources\Images\page3.svg" />
		<None Remove="Resources\Images\Pistol_Squat.gif" />
		<None Remove="Resources\Images\plate.svg" />
		<None Remove="Resources\Images\Play_dark_blue.svg" />
		<None Remove="Resources\Images\plusblack.svg" />
		<None Remove="Resources\Images\Pronated_Dumbbell_Curl.gif" />
		<None Remove="Resources\Images\pushup.gif" />
		<None Remove="Resources\Images\Push_up_with_bands.gif" />
		<None Remove="Resources\Images\restrecovery.svg" />
		<None Remove="Resources\Images\rotating_arrow.gif" />
		<None Remove="Resources\Images\Scorpion_Stretch.gif" />
		<None Remove="Resources\Images\Seated_Dumbbell_Triceps_Extension.gif" />
		<None Remove="Resources\Images\Seated_Leg_Extension.gif" />
		<None Remove="Resources\Images\Seated_Row_with_bands.gif" />
		<None Remove="Resources\Images\Sec_Pause_Squat.gif" />
		<None Remove="Resources\Images\sendmesageicon.svg" />
		<None Remove="Resources\Images\settingsbackground.svg" />
		<None Remove="Resources\Images\settings_tab.svg" />
		<None Remove="Resources\Images\shoulders.svg" />
		<None Remove="Resources\Images\shoulders_transparent.svg" />
		<None Remove="Resources\Images\Shoulder_Grip_Pull_up.gif" />
		<None Remove="Resources\Images\Shoulder_Press_with_bands.gif" />
		<None Remove="Resources\Images\Side_to_Side_Leg_Swing.gif" />
		<None Remove="Resources\Images\Single_Leg_Dip_on_floor.gif" />
		<None Remove="Resources\Images\Single_Leg_Glute_Bridge.gif" />
		<None Remove="Resources\Images\Splash.svg" />
		<None Remove="Resources\Images\Standing_Triceps_Extension_with_bands.gif" />
		<None Remove="Resources\Images\stars_5.svg" />
		<None Remove="Resources\Images\stopwatch.svg" />
		<None Remove="Resources\Images\Sumo_Squat_male.gif" />
		<None Remove="Resources\Images\Supine_Biceps_Curl_with_bands.gif" />
		<None Remove="Resources\Images\swap.svg" />
		<None Remove="Resources\Images\top.svg" />
		<None Remove="Resources\Images\top2.svg" />
		<None Remove="Resources\Images\triceps.svg" />
		<None Remove="Resources\Images\Triceps_Dip.gif" />
		<None Remove="Resources\Images\Triceps_Kickback_with_bands.gif" />
		<None Remove="Resources\Images\triceps_transparent.svg" />
		<None Remove="Resources\Images\truestate.svg" />
		<None Remove="Resources\Images\Twisting_Crunch.gif" />
		<None Remove="Resources\Images\undefined.svg" />
		<None Remove="Resources\Images\undefined_transparent.svg" />
		<None Remove="Resources\Images\Undone.svg" />
		<None Remove="Resources\Images\up.svg" />
		<None Remove="Resources\Images\up_arrow.svg" />
		<None Remove="Resources\Images\victoriaprofileround.svg" />
		<None Remove="Resources\Images\Wall_Arm_Push_up.gif" />
		<None Remove="Resources\Images\Weighted_Crunch.gif" />
		<None Remove="Resources\Images\white_down_arrow.svg" />
		<None Remove="Resources\Images\WideGrip_Pulldown.gif" />
		<None Remove="Resources\Images\workoutbackground.svg" />
		<None Remove="Resources\Images\workoutdone.svg" />
		<None Remove="Resources\Images\World_Greatest_Stretch.gif" />
		<None Remove="Resources\Images\Zercher_Squat.gif" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Acr.UserDialogs" Version="8.0.1" />
		<PackageReference Include="Branch-Xamarin-Linking-SDK" Version="9.0.1" />
		<PackageReference Include="CommunityToolkit.Maui" Version="9.1.1" />
		<PackageReference Include="Controls.UserDialogs.Maui" Version="1.4.0" />
		<PackageReference Include="FFImageLoading.Maui" Version="1.2.7" />
		<PackageReference Include="Microcharts.Maui" Version="1.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="7.0.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="OxyPlot.Core" Version="2.2.0" />
		<PackageReference Include="OxyPlot.Maui.Skia" Version="1.0.2" />
		<PackageReference Include="PancakeViewCompat" Version="7.0.0" />
		<PackageReference Include="Plugin.Firebase" Version="3.1.4" />
		<PackageReference Include="Plugin.Firebase.Auth.Google" Version="3.1.1" />
		<PackageReference Include="Plugin.InAppBilling" Version="8.0.5" />
		<PackageReference Include="Plugin.Maui.Calendar" Version="1.2.1" />
		<!--<PackageReference
		Include="SkiaSharp" Version="3.116.1" />-->
		<PackageReference Include="SkiaSharp.Extended.UI.Maui" Version="2.0.0" />
		<PackageReference Include="SkiaSharp.Views.Maui.Controls" Version="2.88.8" />
		<PackageReference Include="sqlite-net-pcl" Version="1.9.172" />
		<PackageReference Include="SQLitePCLRaw.bundle_green" Version="2.1.9" />
		<PackageReference Include="Xam.Plugin.Connectivity" Version="3.2.0" />
		<PackageReference Include="Xam.Plugin.LatestVersionStore" Version="2.1.2" />
		<PackageReference Include="Xam.Plugins.Settings" Version="3.1.1" />
		<PackageReference Include="Xam.Plugins.Vibrate" Version="4.0.0.5" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="8.0.100" />
		<PackageReference Include="Microsoft.AppCenter.Crashes" Version="5.0.5" />
		<PackageReference Include="RGPopup.Maui" Version="1.1.2" />
		<PackageReference Include="Sentry.Maui" Version="5.1.1" />
		<PackageReference Include="Plugin.StoreReview" Version="6.2.0" />
		<PackageReference Include="MPowerKit.ProgressRing" Version="1.2.0" />
		<PackageReference Include="SQLitePCLRaw.core" Version="2.1.9" />
		<PackageReference Include="Rollbar" Version="5.2.2" />
	</ItemGroup>
	<ItemGroup Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">
		<PackageReference Include="Xamarin.AndroidX.Collection" Version="1.5.0.1" />
		<PackageReference Include="Xamarin.AndroidX.Core" Version="1.16.0.1" />
		<PackageReference Include="Xamarin.AndroidX.Collection.Ktx" Version="1.5.0.1" />
		<PackageReference Include="Xamarin.AndroidX.Activity.Ktx" Version="********" />
		<PackageReference Include="Xamarin.AndroidX.Browser" Version="*******" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.LiveData.Core" Version="*******" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx" Version="*******" />
	</ItemGroup>
	<ItemGroup>
		<Compile Update="Cells\CongratulationsCell.xaml.cs">
			<DependentUpon>CongratulationsCell.xaml</DependentUpon>
		</Compile>
		<Compile Update="Screens\User\MainAIPage.xaml.cs">
			<DependentUpon>MainAIPage.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\MealBodyweightPopup.xaml.cs">
			<DependentUpon>MealBodyweightPopup.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\MealInfoPopup.xaml.cs">
			<DependentUpon>MealInfoPopup.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\ReminderPopup.xaml.cs">
			<DependentUpon>ReminderPopup.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\EndCheckinPopup.xaml.cs">
			<DependentUpon>EndCheckinPopup.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\WelcomeAIOverlay.xaml.cs">
			<DependentUpon>WelcomeAIOverlay.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\FullReview.xaml.cs">
			<DependentUpon>FullReview.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\FeedbackView.xaml.cs">
			<DependentUpon>FeedbackView.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\FullscreenMenu.xaml.cs">
			<DependentUpon>FullscreenMenu.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\BodyProgressPopup.xaml.cs">
			<DependentUpon>BodyProgressPopup.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\MealGeneralPopup.xaml.cs">
			<DependentUpon>MealGeneralPopup.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\EndExercisePopup.xaml.cs">
			<DependentUpon>EndExercisePopup.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\PreviewOverlay.xaml.cs">
			<DependentUpon>PreviewOverlay.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\BodyweightPopup.xaml.cs">
			<DependentUpon>BodyweightPopup.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\UserHeightView.xaml.cs">
			<DependentUpon>UserHeightView.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\TimerPopup.xaml.cs">
			<DependentUpon>TimerPopup.xaml</DependentUpon>
		</Compile>
		<Compile Update="Views\CongratulationsPopup.xaml.cs">
			<DependentUpon>CongratulationsPopup.xaml</DependentUpon>
		</Compile>
	</ItemGroup>
	<ItemGroup>
		<MauiXaml Update="Cells\AIAnalysisCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\AnchorLinkCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\AttributedLabel.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\ChartCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\CongratulationsCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\AnswerCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\EmptyCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\InboxCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\IncommingViewCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\LastWorkoutWasCardCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\LearnDayCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\LevelUpCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\LiftedCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\LinkCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\LinkGestureCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\LoadingPlaceholder.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\MealPlanCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\MealSurveyTemplate.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\ModeratorView.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\NewDemo1Cell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\NewDemo2Cell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\NewRecordCardCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\NewRecordCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\NextWorkoutLoadingCardCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\OutgoingViewCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\PhotoCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\QuestionCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\ReviewFullCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\SetBindingCloseItem.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\SetBindingItem.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\SetBindingNextItem.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\SetCloseCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\SetsCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\SetsNextCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\StatsCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\SummaryRest.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\SurveyTemplate.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\TipCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\UserOutgoingCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Cells\WelcomeCell.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Controls\CalendarHeaderView.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Controls\ChatInputBarView.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Controls\ContextMenuPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Controls\CustomImageButton.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Layout\RightSideMasterPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Layout\TestPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="MainTabbedPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="OnBoarding\Page1.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="OnBoarding\Page2.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="OnBoarding\Page3.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="OnBoarding\Page4.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="OnBoarding\WalkThroughPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\Demo\NewDemoPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\Eve\MealInfoPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\Exercises\NewExercisePage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\Exercises\SaveSetPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\History\HistortWeightPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\History\HistoryPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\Me\MeCombinePage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\Subscription\SubscriptionPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\User\ChatPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\User\ChatView.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\User\EquipmentSettingsPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\User\FAQPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\User\GroupChatPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\User\InboxPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\User\LearnPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\User\MainAIPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\User\MorePage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\User\OnBoarding\IntroPage1.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\User\OnBoarding\MainOnboardingPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\User\RegistrationPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\User\SettingsPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\User\WelcomePage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\Workouts\ChooseGymOrHome.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\Workouts\ChooseYourGymWorkoutPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\Workouts\ChooseYourHomeWorkoutPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\Workouts\FeaturedProgramPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\Workouts\KenkoChooseYourWorkoutExercisePage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\Workouts\KenkoDemoWorkoutExercisePage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Screens\Workouts\PinLockPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="SupportPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\MealBodyweightPopup.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\MealInfoPopup.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\MealRecipePopup.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\ReminderPopup.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\EndCheckinPopup.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\WelcomeAIOverlay.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\FullReview.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\FeedbackView.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\FullscreenMenu.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\BodyProgressPopup.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\MealGeneralPopup.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\CustomPromptConfig.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\EndExercisePopup.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\PreviewOverlay.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\BodyweightPopup.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\GeneralPopup.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\UserHeightView.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\TimerPopup.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\CongratulationsPopup.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Views\WeightGoalPopup.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
	</ItemGroup>
	<ItemGroup Condition="'$(TargetFramework)' == 'net7.0-android'">
		<PackageReference Include="AndHUD" Version="2.0.1">
		</PackageReference>
	</ItemGroup>
	<!--<ItemGroup>
	  <BundleResource Include="Resources\Images\appicon.svg" />
	</ItemGroup>-->
	<ItemGroup>
		<BundleResource Include="Resources\Raw\alarma.mp3" />
		<BundleResource Include="Resources\Raw\emptyAudio.wav" />
		<BundleResource Include="Resources\Raw\reps1.mp3" />
		<BundleResource Include="Resources\Raw\reps2.mp3" />
		<BundleResource Include="Resources\Raw\reps3.mp3" />
		<BundleResource Include="Resources\Raw\reps4.mp3" />
		<BundleResource Include="Resources\Raw\reps5.mp3" />
		<BundleResource Include="Resources\Raw\reps6.mp3" />
		<BundleResource Include="Resources\Raw\reps7.mp3" />
		<BundleResource Include="Resources\Raw\reps8.mp3" />
		<BundleResource Include="Resources\Raw\reps9.mp3" />
		<BundleResource Include="Resources\Raw\reps10.mp3" />
		<BundleResource Include="Resources\Raw\reps11.mp3" />
		<BundleResource Include="Resources\Raw\reps12.mp3" />
		<BundleResource Include="Resources\Raw\reps13.mp3" />
		<BundleResource Include="Resources\Raw\reps14.mp3" />
		<BundleResource Include="Resources\Raw\reps15.mp3" />
		<BundleResource Include="Resources\Raw\reps16.mp3" />
		<BundleResource Include="Resources\Raw\reps17.mp3" />
		<BundleResource Include="Resources\Raw\reps18.mp3" />
		<BundleResource Include="Resources\Raw\reps19.mp3" />
		<BundleResource Include="Resources\Raw\reps20.mp3" />
		<BundleResource Include="Resources\Raw\reps21.mp3" />
		<BundleResource Include="Resources\Raw\reps22.mp3" />
		<BundleResource Include="Resources\Raw\reps23.mp3" />
		<BundleResource Include="Resources\Raw\reps24.mp3" />
		<BundleResource Include="Resources\Raw\reps25.mp3" />
		<BundleResource Include="Resources\Raw\reps26.mp3" />
		<BundleResource Include="Resources\Raw\reps27.mp3" />
		<BundleResource Include="Resources\Raw\reps28.mp3" />
		<BundleResource Include="Resources\Raw\reps29.mp3" />
		<BundleResource Include="Resources\Raw\reps30.mp3" />
		<BundleResource Include="Resources\Raw\reps31.mp3" />
		<BundleResource Include="Resources\Raw\reps32.mp3" />
		<BundleResource Include="Resources\Raw\reps33.mp3" />
		<BundleResource Include="Resources\Raw\reps34.mp3" />
		<BundleResource Include="Resources\Raw\reps35.mp3" />
		<BundleResource Include="Resources\Raw\reps36.mp3" />
		<BundleResource Include="Resources\Raw\reps37.mp3" />
		<BundleResource Include="Resources\Raw\reps38.mp3" />
		<BundleResource Include="Resources\Raw\reps39.mp3" />
		<BundleResource Include="Resources\Raw\reps40.mp3" />
		<BundleResource Include="Resources\Raw\reps41.mp3" />
		<BundleResource Include="Resources\Raw\reps42.mp3" />
		<BundleResource Include="Resources\Raw\reps43.mp3" />
		<BundleResource Include="Resources\Raw\reps44.mp3" />
		<BundleResource Include="Resources\Raw\reps45.mp3" />
		<BundleResource Include="Resources\Raw\reps46.mp3" />
		<BundleResource Include="Resources\Raw\reps47.mp3" />
		<BundleResource Include="Resources\Raw\reps48.mp3" />
		<BundleResource Include="Resources\Raw\reps49.mp3" />
		<BundleResource Include="Resources\Raw\reps50.mp3" />
		<BundleResource Include="Resources\Raw\reps51.mp3" />
		<BundleResource Include="Resources\Raw\reps52.mp3" />
		<BundleResource Include="Resources\Raw\reps53.mp3" />
		<BundleResource Include="Resources\Raw\reps54.mp3" />
		<BundleResource Include="Resources\Raw\reps55.mp3" />
		<BundleResource Include="Resources\Raw\reps56.mp3" />
		<BundleResource Include="Resources\Raw\reps57.mp3" />
		<BundleResource Include="Resources\Raw\reps58.mp3" />
		<BundleResource Include="Resources\Raw\reps59.mp3" />
		<BundleResource Include="Resources\Raw\reps60.mp3" />
		<BundleResource Include="Resources\Raw\timer123.mp3" />
		<BundleResource Include="Resources\Images\my_exercises.svg" />
		<BundleResource Include="Resources\Images\favorites.svg" />
		<BundleResource Include="Resources\Images\more_dark_blue.svg" />
		<BundleResource Include="Resources\Images\selected.svg" />
		<BundleResource Include="Resources\Images\swappedsuccess.svg" />
		<BundleResource Include="Resources\Images\back_arrow.png" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\DrMuscleWebApiSharedModelMac1\DrMuscleWebApiSharedModelMac1.csproj" />
	</ItemGroup>
	<ItemGroup Condition="'$(TargetFramework)' == 'net8.0-ios'">
		<BundleResource Include="Platforms\iOS\GoogleService-Info.plist" Link="GoogleService-Info.plist" />
		<!--For
		app store warning-->
		<BundleResource Include="Platforms\iOS\PrivacyInfo.xcprivacy" />
	</ItemGroup>
	<ItemGroup Condition="'$(TargetFramework)' == 'net8.0-android'">
		<GoogleServicesJson Include="Platforms\Android\google-services.json" />
	</ItemGroup>
	<ItemGroup>
		<Folder Include="Platforms\iOS\Effects\" />
		<Folder Include="Platforms\Android\Effects\" />
		<Folder Include="Resources\Splash\" />
	</ItemGroup>
	<ItemGroup>
		<MauiSplashScreen Include="Resources\Images\dr_splash_news.svg" BaseSize="180,180" Color="#0c3c5a" />
	</ItemGroup>
	<ItemGroup>
	  <MauiSplashScreen Include="Resources\Images\dr_splash_news.svg" BaseSize="180,180" Color="#0c3c5a" />
	</ItemGroup>
	<ItemGroup>
		<None Update="Views\MealRecipePopup.xaml">
			<Generator>MSBuild:Compile</Generator>
		</None>
	</ItemGroup>
	<Target Name="LinkWithSwift" DependsOnTargets="_ParseBundlerArguments;_DetectSdkLocations" BeforeTargets="_LinkNativeExecutable">
		<PropertyGroup>
			<_SwiftPlatform Condition="$(RuntimeIdentifier.StartsWith('iossimulator-'))">iphonesimulator</_SwiftPlatform>
			<_SwiftPlatform Condition="$(RuntimeIdentifier.StartsWith('ios-'))">iphoneos</_SwiftPlatform>
		</PropertyGroup>
		<ItemGroup>
			<_CustomLinkFlags Include="-L" />
			<_CustomLinkFlags Include="/usr/lib/swift" />
			<_CustomLinkFlags Include="-L" />
			<_CustomLinkFlags Include="$(_SdkDevPath)/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/$(_SwiftPlatform)" />
			<_CustomLinkFlags Include="-Wl,-rpath" />
			<_CustomLinkFlags Include="-Wl,/usr/lib/swift" />
		</ItemGroup>
	</Target>
	<Target Name="DoNotLinkWithBrowserEngineKit" AfterTargets="_ComputeLinkNativeExecutableInputs">
		<ItemGroup>
			<_NativeExecutableFrameworks Remove="BrowserEngineKit" />
		</ItemGroup>
	</Target>
</Project>