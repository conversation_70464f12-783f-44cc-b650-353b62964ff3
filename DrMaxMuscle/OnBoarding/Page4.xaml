<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.OnBoarding.Page4"
             Shell.NavBarIsVisible="false"
             NavigationPage.HasNavigationBar="False"
             >
    <ScrollView HorizontalOptions="FillAndExpand">
        <Grid>
            <Image Source="page3" Aspect="AspectFill" />

            <StackLayout HorizontalOptions="FillAndExpand" Margin="0,100,0,0" x:Name="mainView">
                <!--<Image HeightRequest="130" WidthRequest="150" Source="logo1.png" x:Name="ImgLogo" />-->

                <StackLayout HorizontalOptions="FillAndExpand" Margin="0,10,0,0">

                    <Label TextColor="White" Margin="25,0" x:Name="LblTitle" Text="Before we begin,&#10;
is this app really free?" HorizontalOptions="CenterAndExpand" HorizontalTextAlignment="Center" Style="{StaticResource OnBoardingLabelStyle}" FontSize="21" FontAttributes="Bold" >

                    </Label>
                    <StackLayout Orientation="Vertical"  Margin="25,20,25,10" HorizontalOptions="CenterAndExpand">
                        <Label TextColor="White" Text="We believe in honesty" HorizontalOptions="CenterAndExpand" HorizontalTextAlignment="Center" Style="{StaticResource OnBoardingLabelStyle}" FontSize="21" FontAttributes="Bold" />
                        <Label TextColor="LightGray"  Text="Unlike other apps, we want&#10;
to be transparent here." HorizontalOptions="Center" HorizontalTextAlignment="Center"  Style="{StaticResource OnBoardingLabelStyle}" FontSize="17" />
                    </StackLayout>
                    <StackLayout Orientation="Vertical" Margin="25,10" HorizontalOptions="CenterAndExpand">
                        <Label TextColor="White" Text="We don't believe in ads" HorizontalOptions="CenterAndExpand" HorizontalTextAlignment="Center" Style="{StaticResource OnBoardingLabelStyle}" FontSize="21" FontAttributes="Bold" />
                        <Label TextColor="LightGray" HorizontalOptions="Center" HorizontalTextAlignment="Center"  Text="We don't sell your data&#10;
either. No gimmicks."  Style="{StaticResource OnBoardingLabelStyle}" FontSize="17" />
                    </StackLayout>
                    <StackLayout Orientation="Vertical" Margin="25,10" HorizontalOptions="CenterAndExpand">
                        <Label TextColor="White" Text="All features free for 14 days" HorizontalOptions="CenterAndExpand" HorizontalTextAlignment="Center" Style="{StaticResource OnBoardingLabelStyle}" FontSize="21" FontAttributes="Bold" />
                        <Label TextColor="LightGray" HorizontalOptions="Center" HorizontalTextAlignment="Center"  Text="The app automates everything."  Style="{StaticResource OnBoardingLabelStyle}" FontSize="17" />
                    </StackLayout>
                    <StackLayout Orientation="Vertical" Margin="25,10" HorizontalOptions="CenterAndExpand">
                        <Label TextColor="White"  Text="Free plan after 14 days" HorizontalOptions="CenterAndExpand" HorizontalTextAlignment="Center" Style="{StaticResource OnBoardingLabelStyle}" FontSize="21" FontAttributes="Bold" />
                        <Label TextColor="LightGray" HorizontalOptions="Center" HorizontalTextAlignment="Center"  Text="Some features limited.&#10;
Upgrade to unlock all features."  Style="{StaticResource OnBoardingLabelStyle}" FontSize="17" />
                    </StackLayout>
                    <StackLayout Orientation="Vertical" Margin="25,10" HorizontalOptions="CenterAndExpand">
                        <Label TextColor="White"  Text="Small, self-funded team" HorizontalOptions="CenterAndExpand" HorizontalTextAlignment="Center" Style="{StaticResource OnBoardingLabelStyle}" FontSize="21" FontAttributes="Bold" />
                        <Label TextColor="LightGray" HorizontalOptions="Center" HorizontalTextAlignment="Center"  Text="Our mission is to make the&#10;
world a fitter place. Without ads,&#10;
we depend on your support."  Style="{StaticResource OnBoardingLabelStyle}" FontSize="17" />
                    </StackLayout>

                    <StackLayout Orientation="Vertical" Margin="25,10" HorizontalOptions="CenterAndExpand">
                        <Label TextColor="White"  Text="Exclusive community" HorizontalOptions="CenterAndExpand" HorizontalTextAlignment="Center" Style="{StaticResource OnBoardingLabelStyle}" FontSize="21" FontAttributes="Bold" />
                        <Label TextColor="LightGray" HorizontalOptions="Center" HorizontalTextAlignment="Center"  Text="As a subscriber, you can suggest&#10;
and vote for new features, chat&#10;
with our founder, and more."  Style="{StaticResource OnBoardingLabelStyle}" FontSize="17" />
                    </StackLayout>

                    <StackLayout Orientation="Vertical" Margin="25,10" HorizontalOptions="CenterAndExpand">
                        <Label TextColor="LightGray" HorizontalOptions="Center" HorizontalTextAlignment="Center"  Text="Thanks for giving us a shot. We
&#10;hope to be long-term partners
&#10;on your fitness journey."  Style="{StaticResource OnBoardingLabelStyle}" FontSize="17" />
                    </StackLayout>

                    <StackLayout Margin="0,40,0,30" HorizontalOptions="Center" >
                        <Label Text="Sincerely," TextColor="LightGray"  Style="{StaticResource OnBoardingLabelStyle}" FontSize="17"/>
                        <Image x:Name="ImgSign" Source="dr_sign.png" WidthRequest="300"  />
                        <Label TextColor="LightGray" Text="Dr. Carl Juneau, PhD" HorizontalOptions="StartAndExpand" Style="{StaticResource OnBoardingLabelStyle}" FontSize="16"  x:Name="LblAuthor1" VerticalOptions="Center"  HorizontalTextAlignment="Center" />
                        <Label TextColor="LightGray" Text="Founder of Dr. Muscle" HorizontalOptions="StartAndExpand" Style="{StaticResource OnBoardingLabelStyle}" FontSize="16"  VerticalOptions="Center"  HorizontalTextAlignment="Center" />
                    </StackLayout>

                    <IndicatorView HorizontalOptions="Center" IsEnabled="False" VerticalOptions="EndAndExpand" IndicatorColor="Gray" SelectedIndicatorColor="White" x:Name="indicatorView" />
                    <Button x:Name="btnContinue"  HorizontalOptions="FillAndExpand" Text="Continue" TextColor="White" BackgroundColor="Transparent" CornerRadius="0" BorderWidth="2" BorderColor="White" HeightRequest="66" Margin="25" VerticalOptions="End" Clicked="btnContinue_Clicked" FontSize="17" />




                </StackLayout>

            </StackLayout>


        </Grid>
    </ScrollView>
</ContentView>