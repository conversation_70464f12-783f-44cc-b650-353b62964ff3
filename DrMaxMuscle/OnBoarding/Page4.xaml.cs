using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Screens.User;
using DrMaxMuscle.Screens.User.OnBoarding;

namespace DrMaxMuscle.OnBoarding;

public partial class Page4 : ContentView
{
	public Page4()
	{
		InitializeComponent();
        mainView.Margin = new Thickness(0, 70, 0, 0);
        indicatorView.ItemsSource = new string[] { "Page1", "Page2", "Page3", "Page4" };
        indicatorView.Position = 3;
        if (App.ScreenHeight > 668)
        {
            mainView.Margin = new Thickness(0, 110, 0, 0);
        }

        if (App.ScreenWidth > 375)
        {

            LblTitle.FontSize = 22;

            LblAuthor1.FontSize = 16;
        }
    }

    private void btnContinue_Clicked(object sender, EventArgs e)
    {
        try
        {
            //Application.Current.MainPage.Navigation.PopAsync();
            MainOnboardingPage page = new MainOnboardingPage();
            //WelcomePage page = new WelcomePage();
            page.OnBeforeShow();
            Application.Current.MainPage.Navigation.PushAsync(page);
            //Application.Current.MainPage = new NavigationPage(page);
        }
        catch (Exception ex)
        {
        }
    }
}