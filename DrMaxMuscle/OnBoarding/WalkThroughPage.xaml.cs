
using DrMaxMuscle.Screens.User;

namespace DrMaxMuscle.OnBoarding;

public partial class WalkThroughPage : ContentPage
{
    public WalkThroughPage()
    {
        InitializeComponent();
        carouserView.ItemsSource = new string[] { "Page1", "Page2", "Page3", "Page4" };
        indicatorView.ItemsSource = new string[] { "Page1", "Page2", "Page3", "Page4" };
    }
    private void carouserView_PositionChanged(object sender, PositionChangedEventArgs e)
    {
        try
        {
            if (e.PreviousPosition == 3 && e.CurrentPosition == 0)
            {
                carouserView.Position = 3;
                return;
            }
            btnContinue.IsVisible = e.CurrentPosition == 3 ? false : true;
            indicatorView.IsVisible = e.CurrentPosition == 3 ? false : true;
            indicatorView.Position = e.CurrentPosition;
            if (e.CurrentPosition == 0)
            {
                btnContinue.Text = "Get started";
            }
            else
            {
                btnContinue.Text = "Continue";
            }
        }
        catch (Exception ex)
        {

        }

    }

    void btnContinue_Clicked(System.Object sender, System.EventArgs e)
    {
        //try
        //{
        //    Device.BeginInvokeOnMainThread(async ()  =>
        //    {
        //        if (carouserView.Position == 0)
        //        {
        //            carouserView.Position = 1;
        //            indicatorView.Position = 1;
        //        }
        //        else if (carouserView.Position == 1)
        //        {
        //            carouserView.Position = 2;
        //            indicatorView.Position = 2;
        //        }
        //        else if (carouserView.Position == 2)
        //        {
        //            carouserView.Position = 3;
        //            indicatorView.Position = 3;
        //        }
        //        else
        //        {
        //            //WelcomePage page = new WelcomePage();
        //            //page.OnBeforeShow();
        //            //Navigation.PushAsync(page);
        //        }
        //        await Task.Delay(300);
        //    });

        //}
        //catch (Exception ex)
        //{

        //}
        try
        {
            // Simplify the navigation logic by directly moving to the next position
            var nextPosition = carouserView.Position + 1;

            // Check if we are at the end of the carousel
            if (nextPosition > 3) // Assuming you have 4 items, indexed 0 to 3
            {
                // Navigate to your welcome page or handle the end of carousel scenario
                // WelcomePage page = new WelcomePage();
                // page.OnBeforeShow();
                // await Navigation.PushAsync(page);
            }
            else
            {
                // Use ScrollTo for a more "natural" navigation method that might fix the update issue
                carouserView.ScrollTo(nextPosition, position: ScrollToPosition.Center, animate: true);
                indicatorView.Position = nextPosition; // Update the indicator view position

            }
        }
        catch (Exception ex)
        {
            // Consider logging the exception or handling it as needed
        }


    }
    protected override bool OnBackButtonPressed()
    {
        //if (PopupNavigation.Instance.PopupStack.Count > 0)
        //{
        //    PopupNavigation.Instance.PopAllAsync();
        //    return true;
        //}
        //Device.BeginInvokeOnMainThread(async () =>
        //{
        //    ConfirmConfig exitPopUp = new ConfirmConfig()
        //    {

        //        Title = AppResources.Exit,
        //        Message = AppResources.AreYouSureYouWantToExit,
        //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //        OkText = AppResources.Yes,
        //        CancelText = AppResources.No,
        //    };

        //    var result = await UserDialogs.Instance.ConfirmAsync(exitPopUp);
        //    if (result)
        //    {
        //        var kill = DependencyService.Get<IKillAppService>();
        //        kill.ExitApp();
        //    }
        //});
        return true;
    }
}