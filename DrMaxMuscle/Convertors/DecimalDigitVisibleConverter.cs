﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Convertors
{
    internal class DecimalDigitVisibleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var decDigitLength = (int)value;
            var digitIndex = System.Convert.ToInt32(parameter);

            return digitIndex < decDigitLength;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }
    }

}