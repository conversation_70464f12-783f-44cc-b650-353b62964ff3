﻿using DrMaxMuscle.Constants;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Convertors
{
    public class IdToBodyPartConverter : IValueConverter
    {

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if (value == null)
                    return "undefined.png";
                var content = (long)value;
                if (content == 14)
                    return "lower_back.png";
                if (content == 27)
                    return "selected.png";
                if (content == 13 || content == 28)
                    return "flexibility.png";

                return $"{AppThemeConstants.GetBodyPartName(content).ToLower()}.png".Replace(" ", "_");
            }
            catch (Exception ex)
            {

            }
            return "";


        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }
    }
}
