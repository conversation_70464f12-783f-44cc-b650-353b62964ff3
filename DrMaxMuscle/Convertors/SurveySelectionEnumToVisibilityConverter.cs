﻿using DrMaxMuscle.Helpers;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Convertors
{
    public class SurveySelectionEnumToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            SatisfactionSurveyEnum satisfactionSurveyEnum = (SatisfactionSurveyEnum)value;
            return satisfactionSurveyEnum != SatisfactionSurveyEnum.None;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }
    }
}
