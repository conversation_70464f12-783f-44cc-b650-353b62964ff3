﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Convertors
{
    internal class IntegerDigitVisibleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var intDigitLength = (int)value;
            var digitIndex = System.Convert.ToInt32(parameter);

            return digitIndex < intDigitLength;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }
    }
}
