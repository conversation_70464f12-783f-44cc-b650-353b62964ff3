﻿using DrMaxMuscle.Behaviors;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle
{
    class LazyContentPageBehavior : LoadContentOnActivateBehavior<ContentPage>
    {
        protected override void SetContent(ContentPage page, View contentView)
        {
            try
            {
                page.Content = contentView;
            }
            catch (Exception ex)
            {

            }
        }
    }
    class LazyNavigationPageBehavior : LoadContentOnActivateBehavior<NavigationPage>
    {
        protected override void SetContent(NavigationPage element, View contentView)
        {
            try
            {
                ((ContentPage)element.RootPage).Content = contentView;
            }
            catch (Exception ex)
            {

            }
        }
    }
}
