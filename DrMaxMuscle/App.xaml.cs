﻿using Controls.UserDialogs.Maui;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Controls;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Layout;
using DrMaxMuscle.OnBoarding;
using DrMaxMuscle.Screens.User;
using DrMaxMuscle.Screens.User.OnBoarding;
using DrMuscleWebApiSharedModel;
using Microsoft.Maui.Platform;
using System.Collections.ObjectModel;
using Microsoft.AppCenter.Crashes;
using Rollbar;
using DrMaxMuscle.Dependencies;
using System.Text.Json;
using Newtonsoft.Json;
using DrMaxMuscle.Screens.Eve;
using JsonSerializer = System.Text.Json.JsonSerializer;
#if ANDROID
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
#elif IOS
using Microsoft.Maui.Controls.Compatibility.Platform.iOS;
using UIKit;
#endif
using System.Diagnostics;

namespace DrMaxMuscle
{
    public partial class App : Application
    {
        public static bool isUnfinishExercises = false;
        public static bool isUnfinishSets = false;
        private bool isLoading = false;
        private bool isLoadingDisplay = false;
        public SwapExerciseContextList swapExerciseContexts;
        private UserWorkoutContext userWorkoutContext;
        private WorkoutListContext workoutListContext;
        private WorkoutHistoryContext workoutHistoryContext;
        private WorkoutLogContext workoutLogContext;
        private NewRecordModelContext newRecordModelContext;
        public WeightsContext weightContext;
        public static bool IsNUX = false;
        public static bool IsIntro = false;
        public static bool IsIntroBack = false;
        public static bool IsConnectedToWatch = false;
        public bool displayCreateNewAccount = true;
        public static bool IsEquipmentOpen = false;

        public static int workoutPerDay = 0;
        public static bool IsWelcomePopup1 = false;
        public static bool IsShowTooltip = false;
        public static bool IsWelcomePopup2 = false;
        public static bool IsExercisePopup = false;
        public static bool IsGymPopup = false;
        public static bool IsHomeGymPopup = false;
        public static bool IsBodyweightPopup = false;
        public static bool IsCustomPopup = false;
        public static bool IsLearnPopup = false;
        public static bool IsChatPopup = false;
        public static bool IsCongratulated = false;
        public static bool IsSettingsPopup = false;
        public static bool IsAskedLatestVersion = false;
        public static bool IsWelcomePopup3 = false;
        public static bool IsWelcomePopup4 = false;
        public static bool IsWelcomePopup5 = false;
        public static bool IsShowBackOffPopup = false;
        public static bool ShowAllSetPopup = false;
        public static bool MobilityWelcomePopup = false;
        public static bool IsPlatePopup = false;
        public static bool IsSupersetPopup = false;
        public static bool IsTrialExpiredPopup = false;
        public static bool IsSaveSetClicked = false;
        public static bool IsAddExercisesPopUp = false;
        public static bool IsWelcomeBack = false;
        public static bool IsTakeTimeOff = false;
        public static bool IsOnboarding = false;
        public static bool IsSidemenuOpen = false;
        public static bool IsHowHardAsked = false;
        public static bool IsShowBackOffTooltip = false;
        public static bool IsFeaturedPopup = false;
        public static bool IsSurprisePopup = false;
        public static bool IsFreePlan = false;
        public static bool IsDisplayPopup = false;

        public static bool IsResizeScreen = false;
        public static bool IsNewUser = false;
        public static bool IsNewFirstTime = false;
        public static bool IsSleeping = false;
        public static bool ShowEasyExercisePopUp = false;

        public static bool IsFromNotificaion = false;

        public static double NavigationBarHeight = 44;
        public static double StatusBarHeight = 20;
        public static bool IsV1User = false;
        public static bool IsMealPlan = false;
        public static bool IsTraining = false;
        public static bool IsV1UserTrial = false;
        public static long WorkoutId = 0;
        public static long BodypartId = 0;
        public static int Days = 0;
        public static int globalTime = 0;
        public static decimal PCWeight = 0;
        public static bool IsDemoProgress = false;
        public static bool IsDemo1Progress = false;
        public static bool IsHomeOpenAfterChat = false;
        public static bool IsResetPlan = false;
        public static bool IsProteinShakeExist = false;
        public static bool IsFatExist = false;
        public static bool IsBackToHome = false;

        public static string TotalWeeks = "0";
        public static string TotalCaloriesBurned = "0";
        public static string TotalNewRecords = "0";
        public static string TotalExercises = "0";

        public static int SurveyValue = 2;
        //public static bool isUserWeightPopupShown = false;
        //public static bool isTargetWeightPopupShown = false;
        //public static bool isHeightWeightPopupShown = false;

        public static int IsMainPage = 0;

        public static bool IsMealReponseLoaded = false;
        public static List<bool> WelcomeTooltop { get; set; }
        public static List<bool> BackoffTooltop { get; set; }
        public static List<string> MutedUserList = new List<string>();
        public static bool isChangePopupSubscribed = false;
        public static bool IsMealPlanOpenedSecerately = false;

        public static bool IsStopEvent { get; set; }
        public static bool IsGoogle { get; set; }
        public static bool IsApple { get; set; }
        public static bool IsWebsite { get; set; }
        public static bool IsMealPlanLoading = false;

        public static bool IsWeightChangeFromOtherScreen = false;
        public static bool IsMealPlanChange = false;
        public static double ScreenWidth { get; set; }
        public static double ScreenHeight { get; set; }

        public static int TotalWorkoutsDone { get; set; }
        public static MultiUnityWeight TotalWeightLifted { get; set; }
        public static MainTabbedPage tabpage;
        public static bool IsAppGoesBackground = false;
        public static bool ShowTopButtonWorkout = true;
        public static ObservableCollection<BotModel> BotList = new ObservableCollection<BotModel>();

        //Meal Plan related stuff
        public static  int minPro = 0;
        public static  int maxPro = 0;
        public static  int minCarb = 0;
        public static  int maxCarb = 0;
        public static  int minFat = 0;
        public static int  maxFat = 0;
        public static string MealTtypes = "";

        public static decimal BreakfastMinCalories = 0;
        public static decimal BreakfastMaxCalories = 0;
        public static decimal BreakfastMinProteinGrams = 0;
        public static decimal BreakfastMaxProteinGrams = 0;
        public static decimal BreakfastMinCarbsGrams = 0;
        public static decimal BreakfastMaxCarbsGrams = 0;
        public static decimal BreakfastMinFatGrams = 0;
        public static decimal BreakfastMaxFatGrams = 0;
         
        public static decimal LunchMinCalories = 0;
        public static decimal LunchMaxCalories = 0;
        public static decimal LunchMinProteinGrams = 0;
        public static decimal LunchMaxProteinGrams = 0;
        public static decimal LunchMinCarbsGrams = 0;
        public static decimal LunchMaxCarbsGrams = 0;
        public static decimal LunchMinFatGrams = 0;
        public static decimal LunchMaxFatGrams = 0;
        
        public static decimal DinnerMinCalories = 0;
        public static decimal DinnerMaxCalories = 0;
        public static decimal DinnerMinProteinGrams = 0;
        public static decimal DinnerMaxProteinGrams = 0;
        public static decimal DinnerMinCarbsGrams = 0;
        public static decimal DinnerMaxCarbsGrams = 0;
        public static decimal DinnerMinFatGrams = 0;
        public static decimal DinnerMaxFatGrams = 0;
        
        public static decimal ProteinShakeCalories = 0;
        public static decimal ProteinShakeProteinGrams = 30;
        public static decimal ProteinShakeCarbsGrams = 12;
        public static decimal ProteinShakeFatGrams = 4;
        
        public static decimal Snack1MinCalories = 0;
        public static decimal Snack1MaxCalories = 0;
        public static decimal Snack1MinProteinGrams = 0;
        public static decimal Snack1MaxProteinGrams = 0;
        public static decimal Snack1MinCarbsGrams = 0;
        public static decimal Snack1MaxCarbsGrams = 0;
        public static decimal Snack1MinFatGrams = 0;
        public static decimal Snack1MaxFatGrams = 0;
        
        public static decimal Snack2MinCalories = 0;
        public static decimal Snack2MaxCalories = 0;
        public static decimal Snack2MinProteinGrams = 0;
        public static decimal Snack2MaxProteinGrams = 0;
        public static decimal Snack2MinCarbsGrams = 0;
        public static decimal Snack2MaxCarbsGrams = 0;
        public static decimal Snack2MinFatGrams = 0;
        public static decimal Snack2MaxFatGrams = 0;
        public static DmmMealPlan plan = new DmmMealPlan();

        public static string VegetarianEats = "";
        public static bool IsAnyAllergies = false;
        public static string AllergyText = "";
        public static string CountryText = "";
        public static string FavouriteFood = "";

        public static bool IsAnyFoodYouDontLike = false;
        public static string FoodsYouDontLikeText = "";
        public static bool IsWorkoutDaysChanged = false;
        public static bool IsMealPlanMenuOpened = false;
        public static bool IsMealPlanReceipeOpened = false;
        public App()
        {
            InitializeComponent();
            this.handleGlobalException();

            Microsoft.Maui.Handlers.EntryHandler.Mapper.AppendToMapping(nameof(Entry), (handler, view) =>
            {
                try
                {
                    if (view is Layout.DrMuscleEntry || view is Layout.WorkoutEntry || view is Controls.DrMuscleEntry || view is Controls.DrEntry)
                    {
                        if (handler != null)
                        {
#if __ANDROID__
                        var nativeEditText = handler?.PlatformView;
                        nativeEditText.SetBackgroundColor(Colors.Transparent.ToPlatform());
                        nativeEditText.Background = null;
                        var androidColor = Android.Graphics.Color.ParseColor("#97D1F3");             
                        handler.PlatformView.TextCursorDrawable.SetColorFilter(androidColor, Android.Graphics.PorterDuff.Mode.SrcIn);
                        //handler.PlatformView.SetBackgroundColor(Colors.Transparent.ToPlatform());
#elif __IOS__
                            if (view is Layout.WorkoutEntry || view is Controls.DrEntry)
                            {

                                var toolbar = new UIKit.UIToolbar(new System.Drawing.RectangleF(0.0f, 0.0f, 50.0f, 44.0f));
                                toolbar.BackgroundColor = UIKit.UIColor.LightGray; // Set the color you prefer
                                var doneButton = new UIKit.UIBarButtonItem(UIKit.UIBarButtonSystemItem.Done, delegate
                                {
                                    handler.PlatformView?.ResignFirstResponder();
                                });

                        toolbar.Items = new UIKit.UIBarButtonItem[] {
                        new UIKit.UIBarButtonItem (UIKit.UIBarButtonSystemItem.FlexibleSpace),
                            doneButton
                        };
                        if (handler.PlatformView != null)
                        {
                            handler.PlatformView.InputAccessoryView = toolbar;
                            handler.PlatformView.TintColor = UIKit.UIColor.FromRGB(151, 209, 243);
                        }
                    }
                    if (view is Controls.DrEntry)
                    {
                        if(handler.PlatformView != null)
                        {
                            handler.PlatformView.Layer.BorderWidth = 3;
                            handler.PlatformView.Layer.BorderColor = Color.FromHex("#f1f1f1").ToUIColor().CGColor; 
                        }
                    }
                    else
                    {
                        if(handler.PlatformView != null)
                        {
                            handler.PlatformView.BorderStyle = UIKit.UITextBorderStyle.None;
                        }
                    }
#endif
                        }
                    }

                }
                catch (Exception ex)
                {

                }
            });
            Microsoft.Maui.Handlers.DatePickerHandler.Mapper.AppendToMapping(nameof(DatePicker), (handler, view) =>
            {
                if (handler != null)
                {
#if __ANDROID__
                    var nativeEditText = handler.PlatformView;
                    nativeEditText.SetBackgroundColor(Colors.Transparent.ToPlatform());
                    nativeEditText.Background = null;
                    //handler.PlatformView.SetBackgroundColor(Colors.Transparent.ToPlatform());
#elif __IOS__
                    handler.PlatformView.BorderStyle = UIKit.UITextBorderStyle.None;
#endif
                }
            });
            Microsoft.Maui.Handlers.TimePickerHandler.Mapper.AppendToMapping(nameof(TimePicker), (handler, view) =>
            {
                if (handler != null)
                {
#if __ANDROID__
                    var nativeEditText = handler.PlatformView;
                    nativeEditText.SetBackgroundColor(Colors.Transparent.ToPlatform());
                    nativeEditText.Background = null;
                    //handler.PlatformView.SetBackgroundColor(Colors.Transparent.ToPlatform());
#elif __IOS__
                    handler.PlatformView.BorderStyle = UIKit.UITextBorderStyle.None;
#endif
                }
            });
            Microsoft.Maui.Handlers.EditorHandler.Mapper.AppendToMapping(nameof(ExtendedEditorControl), (handler, view) =>
            {
                try
                {
                    if (handler != null)
                    {
                        if (view is ExtendedEditorControl)
                        {
#if __ANDROID__
                            var nativeEditText = handler.PlatformView;
                            nativeEditText.SetBackgroundResource(Android.Resource.Color.Transparent);
                            nativeEditText.Background = null;
                            // handler.PlatformView.SetBackgroundResource(Android.Resource.Color.Transparent);
#elif __IOS__
                    handler.PlatformView.Layer.BorderWidth = 0;
#endif
                        }
                    }
                }
                catch (Exception ex)
                {

                }
            });
            Microsoft.Maui.Handlers.ButtonHandler.Mapper.AppendToMapping(nameof(DrMuscleButton), (handler, view) =>
            {
                try
                {
                    if (handler != null)
                    {
                        if (view != null && view is DrMuscleButton)
                        {
#if __IOS__
                        if(handler?.PlatformView != null)
                        {
                            var native = handler?.PlatformView as UIButton;
                            if (native != null)
                            {
                                native.TitleLabel.TextAlignment = UIKit.UITextAlignment.Center;
                            }
                        }
#endif
                        }
                    }
                }
                catch (Exception ex)
                {

                }
            });
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull,
                IgnoreReadOnlyProperties = true
            };
            AppDomain.CurrentDomain.UnhandledException += (o, e) =>
            {
                if (e?.ExceptionObject == null)
                    return;
                var exception = e.ExceptionObject as Exception;
                var exceptionDetails = new
                {
                    Message = exception?.Message,
                    StackTrace = exception?.StackTrace,
                    Source = exception?.Source,
                    InnerException = exception?.InnerException?.Message
                };

                SentrySdk.CaptureException(new Exception(JsonSerializer.Serialize(exceptionDetails, options)));
            };
            TaskScheduler.UnobservedTaskException += (o, e) =>
            {
                if (e?.Exception == null)
                    return;

                
                try
                {
                    // Flatten the exception in case of AggregateException
                    var flattenedException = e.Exception.Flatten();

                    var exceptionDetails = new
                    {
                        Message = flattenedException?.Message,
                        StackTrace = flattenedException?.StackTrace,
                        Source = flattenedException?.Source,
                        InnerException = flattenedException?.InnerException?.Message
                    };
                    var serializedException = JsonSerializer.Serialize(exceptionDetails, options);
                    SentrySdk.CaptureException(new Exception(serializedException));
                }
                catch (Exception ex)
                {
                    // Log the serialization failure, but don't let it crash the application.
                    Console.WriteLine($"Serialization failed: {ex.Message}");
                }
            };
            //MainPage = new NavigationPage(new MainPage());
            //MainPage = new NavigationPage(new WalkThroughPage());
            FinishedExercices = new List<ExerciceModel>();
            MainPage = new AppShell();
            
        }
        protected override async void OnStart()
        {
            // uncomment code please

            RollbarHelper.ConfigureRollbar();
            RollbarHelper.RegisterForGlobalExceptionHandling(); // Commented out to avoid duplicate registration

            try
            {
                if (App.minPro == 0)
                {
                    var macrosDist = LocalDBManager.Instance.GetDBSetting("MacrosDistribution");
                    if (macrosDist != null)
                    {
                        var obj = JsonConvert.DeserializeObject<MacrosDistribution>(macrosDist?.Value);
                        App.minPro = obj.MinPro;
                    }
                }
                App.MealTtypes = LocalDBManager.Instance.GetDBSetting("MealTtypes")?.Value;
            }
            catch (Exception ex)
            {

            }

            SentrySdk.ConfigureScope(scope =>
            {
                scope.User = new SentryUser
                {
                    Email = LocalDBManager.Instance.GetDBSetting("email")?.Value
                };
            });

            //AppCenter.LogLevel = LogLevel.None;
            //CrossFirebasePushNotification.Current.OnTokenRefresh += Current_OnTokenRefresh;
            //CrossFirebasePushNotification.Current.OnNotificationReceived += Current_OnNotificationReceived;

            //App.IsFatExist = false;
            CurrentLog.Instance.ShowWelcomePopUp = false;
            DrMuscleRestClient.Instance.StartPost += DrMaxMuscleRestClient_StartPost;
            DrMuscleRestClient.Instance.EndPost += DrMaxMuscleRestClient_EndPost;
            DBSetting dbOnBoardingSeen = LocalDBManager.Instance.GetDBSetting("onboarding_seen");
            DBSetting dbToken = LocalDBManager.Instance.GetDBSetting("token");
            DBSetting dbTokenExpirationDate = LocalDBManager.Instance.GetDBSetting("token_expires_date");
            DBSetting dbFirstname = LocalDBManager.Instance.GetDBSetting("firstname");
            if (LocalDBManager.Instance.GetDBSetting("timer_count") != null)
                LocalDBManager.Instance.SetDBSetting("timer_remaining", LocalDBManager.Instance.GetDBSetting("timer_count").Value);
            else
                LocalDBManager.Instance.SetDBSetting("timer_count", "60");
            try
            {
                if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode") != null)
                {
                    if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("QuickMode", LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value);
                        LocalDBManager.Instance.SetDBSetting("OlderQuickMode", null);
                        LocalDBManager.Instance.ResetReco();
                    }
                }
            }
            catch (Exception ex)
            {

            }
            swapExerciseContexts = SwapExerciseContextList.LoadContexts();
            userWorkoutContext = UserWorkoutContext.LoadContexts();
            workoutListContext = WorkoutListContext.LoadContexts();
            workoutHistoryContext = WorkoutHistoryContext.LoadContexts();
            workoutLogContext = WorkoutLogContext.LoadContexts();
            weightContext = WeightsContext.LoadContexts();
            newRecordModelContext = NewRecordModelContext.LoadContexts();
            // uncomment code please
            //Page startPage = PagesFactory.GetNavigation();

            // uncomment code please
            //if (Device.RuntimePlatform == Device.Android)
            //    DependencyService.Get<IWindowBackgroundColor>().SetBackgroundColor();
            //Type pageType = typeof(MainTabbedPage);
            //page = (MainTabbedPage)Activator.CreateInstance(pageType);
            try
            {
                tabpage = GetTabbedPage<MainTabbedPage>();
                Application.Current.MainPage = new NavigationPage(tabpage);
            }
            catch(Exception ex)
            {

            }
            if (LocalDBManager.Instance.GetDBSetting("AppLanguage") != null)
            {
                // uncomment code please
                //var localize = DependencyService.Get<ILocalize>();
                //if (localize != null)
                //{
                //    ResourceLoader.Instance.SetCultureInfo(new CultureInfo(LocalDBManager.Instance.GetDBSetting("AppLanguage").Value));
                //    localize.SetLocale(new CultureInfo(LocalDBManager.Instance.GetDBSetting("AppLanguage").Value));
                //}
            }

            if (dbOnBoardingSeen == null || dbOnBoardingSeen.Value == "false")
            {
                displayCreateNewAccount = true;
                
                try
                {
                    WalkThroughPage page1 = new WalkThroughPage();
                    await Application.Current.MainPage.Navigation.PushAsync(page1);
                    //= new NavigationPage(page1);
                    //Application.Current.MainPage = new NavigationPage(page1);
                    if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode") != null)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value != null)
                        {
                            LocalDBManager.Instance.SetDBSetting("QuickMode", LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value);
                            LocalDBManager.Instance.SetDBSetting("OlderQuickMode", null);
                        }
                    }
                }
                catch (Exception ex)
                {

                }
            }
            else
            {
                if (dbToken != null && dbTokenExpirationDate != null && DateTime.Now < new DateTime(Convert.ToInt64(dbTokenExpirationDate.Value)))
                {
                    displayCreateNewAccount = true;
                    if (LocalDBManager.Instance.GetDBSetting("SetStyle") == null)
                        LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                    DrMuscleRestClient.Instance.SetToken(LocalDBManager.Instance.GetDBSetting("token").Value);

                    //MainAIPage page = new MainAIPage();
                    MainAIPage._isJustAppOpen = true;
                    //page.OnBeforeShow();
                    //Application.Current.MainPage = new NavigationPage(page);
                    // uncomment code please
                    //MainTabbedPage p = GetTabbedPage<MainTabbedPage>();
                    //Application.Current.MainPage = new NavigationPage(tabpage);


                    if (LocalDBManager.Instance.GetDBSetting("FirstStepCompleted") != null && LocalDBManager.Instance.GetDBSetting("FirstStepCompleted").Value == "true")
                    {
                        IsDemoProgress = true;
                        App.IsIntroBack = true;
                        App.IsNewUser = true;
                        try
                        {
                            MainOnboardingPage mainOnboardingPage = new MainOnboardingPage();
                            mainOnboardingPage.OnBeforeShow();
                            await Application.Current.MainPage.Navigation.PushAsync(mainOnboardingPage);
                            //Application.Current.MainPage = new NavigationPage(mainOnboardingPage);
                            //PagesFactory.PushAsync<MainOnboardingPage>();

                        }
                        catch(Exception ex)
                        {

                        }
                    }
                    else
                        RefreshUsersSettings();
                    try
                    {

                        if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode") != null)
                        {
                            if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value != null)
                            {
                                LocalDBManager.Instance.SetDBSetting("QuickMode", LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value);
                                LocalDBManager.Instance.SetDBSetting("OlderQuickMode", null);
                            }
                        }

                    }
                    catch (Exception ex)
                    {

                    }
                }
                else
                {
                    try
                    {
                        displayCreateNewAccount = true;
                        WelcomePage page = new WelcomePage();
                        page.OnBeforeShow();
                        await Application.Current.MainPage.Navigation.PushAsync(page);
                        //Application.Current.MainPage = new NavigationPage(page);
                        //PagesFactory.PopThenPushAsync<WelcomePage>();
                    }
                    catch(Exception ex)
                    {

                    }
                }

            }
            //MainPage = new NewIntialPage(startPage);

            try
            {
                if (Config.SecondOpenEventTrack == 1)
                {
                    //Send event
                    // uncomment code please
                    DependencyService.Get<DrMaxMuscle.Dependencies.IFirebase>().LogEvent("second_open", "Open");
                    Config.SecondOpenEventTrack = 2;
                }
            }
            catch (Exception ex)
            {

            }
            WelcomeTooltop = new List<bool>();
            WelcomeTooltop.Add(false);
            WelcomeTooltop.Add(false);
            WelcomeTooltop.Add(false);

            BackoffTooltop = new List<bool>();
            BackoffTooltop.Add(false);
            BackoffTooltop.Add(false);
            BackoffTooltop.Add(false);
            if (Config.ShowWelcomePopUp2 || Config.ViewWebHistoryPopup || Config.ShowWelcomePopUp3 || LocalDBManager.Instance.GetDBSetting("email") == null)
                return;
            try
            {
                if (Config.SecondOpenEventTrack == 0)
                    Config.SecondOpenEventTrack = 1;
            }
            catch (Exception ex)
            {

            }

        }
        static readonly Dictionary<Type, Page> pages = new Dictionary<Type, Page>();
        public static T GetTabbedPage<T>(bool cachePages = true) where T : MainTabbedPage
        {
            Type pageType = typeof(T);

            try
            {   
                if (cachePages)
                {
                    //if (!pages.ContainsKey(pageType))
                    //{
                        MainTabbedPage page = (MainTabbedPage)Activator.CreateInstance(pageType);
                        page.OnBeforeShow();
                        pages.Add(pageType, page);
                        return pages[pageType] as T;
                    //}
                    //else
                    //{
                    //    return Activator.CreateInstance(pageType) as T;
                    //}

                }
                else
                {
                    MainTabbedPage page = (MainTabbedPage)Activator.CreateInstance(pageType);
                    page.OnBeforeShow();
                    pages.Add(pageType, page);
                    return pages[pageType] as T;
                }
            }
            catch (Exception ex)
            {
                MainTabbedPage page = (MainTabbedPage)Activator.CreateInstance(pageType);
                page.OnBeforeShow();
                pages.Add(pageType, page);
                return pages[pageType] as T;
                //return Activator.CreateInstance(pageType) as T;
            }
        }
        protected override async void OnSleep()
        {
            IsSleeping = true;

            LocalDBManager.Instance.SetDBSetting("MealTtypes", App.MealTtypes);

            // Android Rest Over Notification
            try
            {
                if (Device.RuntimePlatform == Device.Android && Timer.Instance?.Remaining > 0)
                {
                    RegisterNotification(Timer.Instance.Remaining);
                }
                // Safely dismiss popups
                try
                {
                    await MauiProgram.SafeDismissAllPopups();
                }
                catch (Exception popupEx)
                {
                    Console.WriteLine($"Error dismissing popups: {popupEx.Message}");
                }
            }
            catch (Exception ex)
            {

            }
        }

        private void handleGlobalException()
        { 
            AppDomain.CurrentDomain.UnhandledException += OnAppDomainUnhandledException;
            TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;
        }

        private void OnAppDomainUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e?.ExceptionObject == null)
                return;
            var exception = e.ExceptionObject as Exception;
            var exceptionDetails = new
            {
                Message = exception?.Message,
                StackTrace = exception?.StackTrace,
                Source = exception?.Source,
                InnerException = exception?.InnerException?.Message
            };
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull,
                    IgnoreReadOnlyProperties = true
                };
                var serializedException = JsonSerializer.Serialize(exceptionDetails, options);
                Console.WriteLine("ON APP DOMAIN UNHANDLED EXCEPTION, App.xaml.cs");
                RollbarLocator.RollbarInstance.Critical(e.ExceptionObject);
                SentrySdk.CaptureException(exception);
                SentrySdk.FlushAsync(TimeSpan.FromSeconds(5)).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Serialization failed: {ex.Message}");
                RollbarLocator.RollbarInstance.Error(ex);
                SentrySdk.CaptureException(ex);
                SentrySdk.FlushAsync(TimeSpan.FromSeconds(5)).GetAwaiter().GetResult();
            }
        }

        private void OnUnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        {
            if (e?.Exception == null)
                return;
            var exceptionDetails = new
            {
                Message = e.Exception?.Message,
                StackTrace = e.Exception?.StackTrace,
                Source = e.Exception?.Source,
                InnerException = e.Exception?.InnerException?.Message
            };

            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull,
                    IgnoreReadOnlyProperties = true
                };
                var serializedException = JsonSerializer.Serialize(exceptionDetails, options);
                Console.WriteLine("ON UNOBSERVED TASK EXCEPTION, App.xaml.cs");
                RollbarLocator.RollbarInstance.Critical(e.Exception);
                SentrySdk.CaptureException(new Exception(serializedException));
                SentrySdk.FlushAsync(TimeSpan.FromSeconds(5)).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                // Log the serialization failure, but don't let it crash the application.
                Console.WriteLine($"Serialization failed: {ex.Message}");
                RollbarLocator.RollbarInstance.Error(ex);
                SentrySdk.CaptureException(ex);
                SentrySdk.FlushAsync(TimeSpan.FromSeconds(5)).GetAwaiter().GetResult();
            }
            e.SetObserved();
        }
        
        protected override async void OnResume()
        {
            IsSleeping = false;

            try
            {
                if (App.minPro == 0)
                {
                    var macrosDist = LocalDBManager.Instance.GetDBSetting("MacrosDistribution");
                    if (macrosDist != null)
                    {
                        var obj = JsonConvert.DeserializeObject<MacrosDistribution>(macrosDist?.Value);
                        App.minPro = obj.MinPro;
                    }
                }
                App.MealTtypes = LocalDBManager.Instance.GetDBSetting("MealTtypes")?.Value;

            }
            catch (Exception ex)
            {

            }
            try
            {
                await MauiProgram.SafeDismissAllPopups();
            }
            catch (Exception ex)
            {
            }
            // Handle when your app resumes
        }
        public void RegisterNotification(long time)
        {
            //1455 for Rest over notification
            var bodyText = "Get back to work!";
            if (Timer.Instance.NextRepsCount != 0)
            {
                bodyText = $"Get back to work, next {Timer.Instance.NextRepsCount} reps";
            }
            var dt = DateTime.Now.AddSeconds(time);
            var timeSpan = new TimeSpan(0, dt.Hour, dt.Minute, dt.Second);
            DependencyService.Get<IAlarmAndNotificationService>().ScheduleOnceNotification("Rest over", bodyText, timeSpan, 1455);

        }
        private async void RefreshUsersSettings()
        {
            decimal? sliderVal = null;
            if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("SlierValue")?.Value))
            {
                sliderVal = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("SlierValue")?.Value, System.Globalization.CultureInfo.InvariantCulture);
                await DrMuscleRestClient.Instance.SetUserBarWeight(new UserInfosModel()
                {
                    KgBarWeight = sliderVal,
                    LbBarWeight = sliderVal,
                    MassUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lb"
                });
                LocalDBManager.Instance.SetDBSetting("SlierValue", null);
            }
            var uim = await DrMuscleRestClient.Instance.GetUserInfoWithoutLoader();
            try
            {
                if (uim == null)
                    return;
                LocalDBManager.Instance.SetDBSetting("email", uim.Email);
                LocalDBManager.Instance.SetDBSetting("firstname", !string.IsNullOrEmpty(uim.Firstname) ? ((uim.Firstname.Contains(' ')) ? uim.Firstname.Split(' ')[0] : uim.Firstname) : uim.Firstname);
                LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
                LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
                LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
                LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
                LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
                LocalDBManager.Instance.SetDBSetting("repsminimum", Convert.ToString(uim.RepsMinimum));
                LocalDBManager.Instance.SetDBSetting("repsmaximum", Convert.ToString(uim.RepsMaximum));
                LocalDBManager.Instance.SetDBSetting("onboarding_seen", "true");
                LocalDBManager.Instance.SetDBSetting("timer_vibrate", uim.IsVibrate ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_sound", uim.IsSound ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_autostart", uim.IsAutoStart ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_autoset", uim.IsAutomatchReps ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_fullscreen", uim.IsFullscreen ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_betweenexercises", uim.IsBetweenExercises == null ? "false" : ((bool)uim.IsBetweenExercises ? "true" : "false"));
                LocalDBManager.Instance.SetDBSetting("timer_count", uim.TimeCount.ToString());
                LocalDBManager.Instance.SetDBSetting("timer_remaining", uim.TimeCount.ToString());
                LocalDBManager.Instance.SetDBSetting("Cardio", uim.IsCardio ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("Reminder5th", uim.IsReminder ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("MaxWorkoutDuration", uim.WorkoutDuration.ToString());
                LocalDBManager.Instance.SetDBSetting("LastWorkoutWas", uim.LastWorkoutWas);
                LocalDBManager.Instance.SetDBSetting("RecommendedReminder", uim.IsRecommendedReminder == true ? "true" : uim.IsRecommendedReminder == null ? "null" : "false");
                if (uim.Height != null)
                    LocalDBManager.Instance.SetDBSetting("Height", uim.Height.ToString());

                var updated = LocalDBManager.Instance.GetDBSetting("CaloriesUpdated");
                if (updated != null && updated?.Value == "True")
                {
                    var userIntake = await DrMuscleRestClient.Instance.GetTargetIntakebyUserWithoutLoader();
                    if (userIntake?.TargetIntake != null)
                        LocalDBManager.Instance.SetDBSetting("TargetIntake", userIntake?.TargetIntake.ToString());
                }
                else
                {
                    if (uim.TargetIntake != null)
                        LocalDBManager.Instance.SetDBSetting("TargetIntake", uim.TargetIntake.ToString());
                }

                
                if (uim.LastTargetIntake != null)
                    LocalDBManager.Instance.SetDBSetting("LastTargetIntake", uim.LastTargetIntake.ToString());

                SetupEquipment(uim);
                if (uim.Age != null)
                    LocalDBManager.Instance.SetDBSetting("Age", Convert.ToString(uim.Age));
                if (uim.ReminderTime != null)
                    LocalDBManager.Instance.SetDBSetting("ReminderTime", uim.ReminderTime.ToString());
                if (uim.ReminderDays != null)
                    LocalDBManager.Instance.SetDBSetting("ReminderDays", uim.ReminderDays);
                if (!string.IsNullOrEmpty(uim.SwappedJson))
                {
                    LocalDBManager.Instance.SetDBSetting("swap_exericse_contexts", uim.SwappedJson);
                    ((App)Application.Current).swapExerciseContexts = SwapExerciseContextList.LoadContexts();
                }

                LocalDBManager.Instance.SetDBSetting("DailyReset", Convert.ToString(uim.DailyExerciseCount));
                LocalDBManager.Instance.SetDBSetting("WeeklyReset", Convert.ToString(uim.WeeklyExerciseCount));


                LocalDBManager.Instance.SetDBSetting("IsEmailReminder", uim.IsReminderEmail ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("ReminderHours", uim.ReminderBeforeHours.ToString());
                
                LocalDBManager.Instance.SetDBSetting("IsReferenceSetReps", uim.IsReferenseSet ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("ReferenceSetReps", uim.ReferenceSetReps.ToString());
                if (string.IsNullOrEmpty(uim.BodyPartPrioriy))
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                else
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", uim.BodyPartPrioriy.Trim());
                if (LocalDBManager.Instance.GetDBSetting("ReminderDays") != null && LocalDBManager.Instance.GetDBSetting("ReminderDays").Value != null)
                {
                    var strDays = LocalDBManager.Instance.GetDBSetting("ReminderDays").Value;
                    TimeSpan timePickerSpan;
                    try
                    {
                        timePickerSpan = TimeSpan.Parse(LocalDBManager.Instance.GetDBSetting("ReminderTime").Value);
                    }
                    catch (Exception ex)
                    {
                        return;
                    }
                }
                if (uim.IsPyramid)
                {
                    LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                    LocalDBManager.Instance.SetDBSetting("IsRPyramid", "true");
                }
                else if (uim.IsNormalSet == null || uim.IsNormalSet == true)
                {
                    LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
                    LocalDBManager.Instance.SetDBSetting("IsPyramid", uim.IsNormalSet == null ? "true" : "false");
                }
                else
                {
                    LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                    LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                }
                LocalDBManager.Instance.SetDBSetting("QuickMode", uim.IsQuickMode == true ? "true" : uim.IsQuickMode == null ? "null" : "false");

                if (uim.WarmupsValue != null)
                {
                    LocalDBManager.Instance.SetDBSetting("warmups", Convert.ToString(uim.WarmupsValue));
                }
                if (uim.SetCount != null)
                {
                    LocalDBManager.Instance.SetDBSetting("WorkSetCount", Convert.ToString(uim.SetCount));
                }
                if (uim.Increments != null)
                    LocalDBManager.Instance.SetDBSetting("workout_increments", uim.Increments.Kg.ToString().ReplaceWithDot());
                if (uim.Max != null)
                    LocalDBManager.Instance.SetDBSetting("workout_max", uim.Max.Kg.ToString().ReplaceWithDot());
                if (uim.Min != null)
                    LocalDBManager.Instance.SetDBSetting("workout_min", uim.Min.Kg.ToString().ReplaceWithDot());
                if (uim.BodyWeight != null)
                {
                    LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString().ReplaceWithDot());
                }
                if (uim.WeightGoal != null)
                {
                    LocalDBManager.Instance.SetDBSetting("WeightGoal", uim.WeightGoal.Kg.ToString().ReplaceWithDot());
                }
                LocalDBManager.Instance.SetDBSetting("BackOffSet", uim.IsBackOffSet ? "true" : "false");
                ((App)Application.Current).displayCreateNewAccount = true;


            }
            catch (Exception ex)
            {

            }

            // RegisterDeviceToken();
        }
        private void SetupEquipment(UserInfosModel uim)
        {
            try
            {
                if (LocalDBManager.Instance.GetDBSetting("email")?.Value == null)
                    return;
                LocalDBManager.Instance.SetDBSetting("KgBarWeight", uim.KgBarWeight == null ? "20" : Convert.ToString(uim.KgBarWeight).ReplaceWithDot());
                LocalDBManager.Instance.SetDBSetting("LBBarWeight", uim.LbBarWeight == null ? "45" : Convert.ToString(uim.LbBarWeight).ReplaceWithDot());

                if (uim.EquipmentModel != null)
                {
                    LocalDBManager.Instance.SetDBSetting("Equipment", uim.EquipmentModel.IsEquipmentEnabled ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("ChinUp", uim.EquipmentModel.IsChinUpBarEnabled ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("Dumbbell", uim.EquipmentModel.IsDumbbellEnabled ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("Plate", uim.EquipmentModel.IsPlateEnabled ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("Pully", uim.EquipmentModel.IsPullyEnabled ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("Bands", uim.EquipmentModel.IsBands ? "true" : "false");

                    LocalDBManager.Instance.SetDBSetting("HomeMainEquipment", uim.EquipmentModel.IsHomeEquipmentEnabled ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("HomeChinUp", uim.EquipmentModel.IsHomeChinupBar ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("HomeDumbbell", uim.EquipmentModel.IsHomeDumbbell ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("HomePlate", uim.EquipmentModel.IsHomePlate ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("HomePully", uim.EquipmentModel.IsHomePully ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("HomeBands", uim.EquipmentModel.IsHomeBands ? "true" : "false");


                    LocalDBManager.Instance.SetDBSetting("OtherMainEquipment", uim.EquipmentModel.IsOtherEquipmentEnabled ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("OtherChinUp", uim.EquipmentModel.IsOtherChinupBar ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("OtherDumbbell", uim.EquipmentModel.IsOtherDumbbell ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("OtherPlate", uim.EquipmentModel.IsOtherPlate ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("OtherPully", uim.EquipmentModel.IsOtherPully ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("OtherBands", uim.EquipmentModel.IsOtherBands ? "true" : "false");

                    if (uim.EquipmentModel.Active == "gym")
                        LocalDBManager.Instance.SetDBSetting("GymEquipment", "true");
                    if (uim.EquipmentModel.Active == "home")
                        LocalDBManager.Instance.SetDBSetting("HomeEquipment", "true");
                    if (uim.EquipmentModel.Active == "other")
                        LocalDBManager.Instance.SetDBSetting("OtherEquipment", "true");
                }
                else
                {
                    LocalDBManager.Instance.SetDBSetting("Equipment", "false");
                    LocalDBManager.Instance.SetDBSetting("ChinUp", "true");
                    LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");
                    LocalDBManager.Instance.SetDBSetting("Plate", "true");
                    LocalDBManager.Instance.SetDBSetting("Pully", "true");
                    LocalDBManager.Instance.SetDBSetting("Bands", "true");

                    LocalDBManager.Instance.SetDBSetting("HomeMainEquipment", "false");
                    LocalDBManager.Instance.SetDBSetting("HomeChinUp", "true");
                    LocalDBManager.Instance.SetDBSetting("HomeDumbbell", "true");
                    LocalDBManager.Instance.SetDBSetting("HomePlate", "true");
                    LocalDBManager.Instance.SetDBSetting("HomePully", "true");
                    LocalDBManager.Instance.SetDBSetting("HomeBands", "true");

                    LocalDBManager.Instance.SetDBSetting("OtherEquipment", "false");
                    LocalDBManager.Instance.SetDBSetting("OtherChinUp", "true");
                    LocalDBManager.Instance.SetDBSetting("OtherDumbbell", "true");
                    LocalDBManager.Instance.SetDBSetting("OtherPlate", "true");
                    LocalDBManager.Instance.SetDBSetting("OtherPully", "true");
                    LocalDBManager.Instance.SetDBSetting("OtherBands", "true");

                }


            }
            catch (Exception ex)
            {

            }
        }
        public static async void RegisterDeviceToken()
        {
            if (string.IsNullOrEmpty(Config.RegisteredDeviceToken))
                return;

            var result = await DrMuscleRestClient.Instance.AddDeviceToken(new DeviceModel()
            {
                DeviceToken = Config.RegisteredDeviceToken,
                Platform = Device.RuntimePlatform.Equals(Device.Android) ? "Android" : "iOS"
            });
        }
        private async void DrMaxMuscleRestClient_EndPost()
        {
            try
            {
                //isLoading = false;
                //await Task.Delay(500);
                //bool isLoadingAfter = isLoading;
                //if (isLoadingAfter)
                //    return;

                //if (isLoadingDisplay)
                //{
                    Debug.WriteLine("HideLoading");
                    UserDialogs.Instance.HideHud();
                    isLoadingDisplay = false;
                    isLoading = false;
                //}
            }
            catch (Exception ex)
            {

            }
        }

        private async void DrMaxMuscleRestClient_StartPost()
        {
            isLoading = true;
            if (!isLoadingDisplay)
            {
                Debug.WriteLine("ShowLoading");
                try
                {
                    await Task.Delay(50);
                    UserDialogs.Instance.ShowLoading("Loading...");
                    isLoadingDisplay = true;
                }
                catch (Exception ex)
                {
                    isLoading = false;
                }
            }
        }
        public static (string[] mealPlans, string[] groceryLists, string email, string macrosDistribution, string mealTypes, string plan, string FavouriteDiet, bool isMealPlanLoaded, string macrosdata) GetMealPlansAndGroceryLists()
        {
            var mealPlans = new string[7];
            var groceryLists = new string[7];
            try
            {
                var email = LocalDBManager.Instance.GetDBSetting("email")?.Value;
                var macrosDistribution = LocalDBManager.Instance.GetDBSetting("MacrosDistribution")?.Value;
                var plan = LocalDBManager.Instance.GetDBSetting("Plan")?.Value;
                var mealTypes = LocalDBManager.Instance.GetDBSetting("MealTtypes")?.Value;
                string FavoriteDiet = LocalDBManager.Instance.GetDBSetting("FavoriteDiet")?.Value;
                bool isMealPlanLoaded = App.IsMealPlanLoading;
                var macrosdata = LocalDBManager.Instance.GetDBSetting("Macros")?.Value;

                mealPlans[0] = LocalDBManager.Instance.GetDBSetting("FinalMealPlanDay1")?.Value;
                groceryLists[0] = LocalDBManager.Instance.GetDBSetting("FinalGroceryListDay1")?.Value;

                if (!string.IsNullOrEmpty(mealPlans[0]))
                {
                    for (int i = 1; i < 7; i++)
                    {
                        mealPlans[i] = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay{i + 1}")?.Value;
                        groceryLists[i] = LocalDBManager.Instance.GetDBSetting($"FinalGroceryListDay{i + 1}")?.Value;
                    }
                }

                return (mealPlans, groceryLists, email, macrosDistribution, mealTypes, plan, FavoriteDiet, isMealPlanLoaded, macrosdata);
            }
            catch (Exception ex)
            {
                return (mealPlans, groceryLists, "", "", "", "", "", false, "");
            }
        }
        public static void SetMealPlansAndGroceryLists(string[] mealPlans, string[] groceryLists, string email, string macrosDistribution, string mealTypes, string plan, string FavoriteDiet, bool isMealPlanLoaded, string macrosdata)
        {
            try
            {
                for (int i = 0; i < 7; i++)
                {
                    if (!string.IsNullOrEmpty(mealPlans[i]))
                    {
                        Preferences.Set($"MealPlanDay{i + 1}{email}", mealPlans[i]);
                        Preferences.Set($"GroceryListDay{i + 1}{email}", groceryLists[i]);
                    }
                }
                Preferences.Set($"MacrosDistribution{email}", macrosDistribution);
                Preferences.Set($"MealTtypes{email}", mealTypes);
                Preferences.Set($"Plan{email}", plan);
                Preferences.Set($"FavoriteDiet{email}", FavoriteDiet);
                Preferences.Set($"IsMealPlanLoading{email}", isMealPlanLoaded);
                Preferences.Set($"Macros{email}", macrosdata);

            }
            catch (Exception ex)
            {

            }
        }
        protected override Window CreateWindow(IActivationState activationState)
        {
            if (Windows.Count > 0)
                return Windows[0];

            var window = base.CreateWindow(activationState);
            return window;
        }

        public SwapExerciseContextList SwapExericesContexts
        {
            get { return swapExerciseContexts; }
        }

        public WeightsContext WeightsContextList
        {
            get { return weightContext; }
        }

        public UserWorkoutContext UserWorkoutContexts
        {
            get { return userWorkoutContext; }
        }

        public WorkoutListContext WorkoutListContexts
        {
            get { return workoutListContext; }
        }

        public WorkoutHistoryContext WorkoutHistoryContextList
        {
            get { return workoutHistoryContext; }
        }

        public WorkoutLogContext WorkoutLogContext
        {
            get { return workoutLogContext; }
        }

        public NewRecordModelContext NewRecordModelContext
        {
            get { return newRecordModelContext; }
        }

        public IList<ExerciceModel> FinishedExercices { get; set; }
    }
}