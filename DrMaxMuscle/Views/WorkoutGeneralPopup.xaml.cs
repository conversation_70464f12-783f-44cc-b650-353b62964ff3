﻿using System.Diagnostics;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Message;
using DrMaxMuscle.Screens.Subscription;
using DrMaxMuscle.Screens.User;
using DrMaxMuscle.Utility;
using DrMuscleWebApiSharedModel;
namespace DrMaxMuscle.Views;
public partial class WorkoutGeneralPopup : Popup
{
    TapGestureRecognizer okGuesture;
    string buttonText = "";
    public event EventHandler OkButtonPress;
    public bool _isHide { get; set; }
    public WorkoutGeneralPopup(string image, string title, string subtitle, string buttonText, Thickness? thickness = null, bool isTips = false, bool isSummary = false, string isShowLearnMore = "false", string isShowSettings = "false", string ismealPlan = "false", string isNotNow = "false", string isAutoHide = "false")
    {
        InitializeComponent();
        Debug.WriteLine("Workout General popup calling");

        okGuesture = new TapGestureRecognizer();
        okGuesture.Tapped += DrMuscleButtonCancel_Clicked;
        //OkAction.GestureRecognizers.Add(okGuesture);
        ImgName.Source = image;
        if (thickness != null)
            ImgName.Margin = (Thickness)thickness;
        LblHeading.Text = title;
        LblSubHead.Text = $"{subtitle}";
        OkButton.Text = buttonText;
        this.buttonText = buttonText;
        LblTipText.IsVisible = false;
        var displayInfo = DeviceDisplay.MainDisplayInfo;
        var width = displayInfo.Width / displayInfo.Density;
        mainStack.WidthRequest = width * 0.9;
        if (isSummary)
        {

            //OkButton.Clicked -= OkButton_Clicked;
            //OkAction.GestureRecognizers.Remove(okGuesture);
            SetLoadingSummary(okGuesture);
            LoadDataForWorkout();
            //MessagingCenter.Subscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage", (obj) =>
            //{
            //Device.BeginInvokeOnMainThread(() =>
            //{

            //OkButton.Clicked += OkButton_Clicked;

            //});
            //});
        }

        if (isShowLearnMore == "true")
        {
            BtnLearnMore.IsVisible = true;
            LblSubHead.Margin = new Thickness(15, 0, 15, 5);


        }
        else if (isShowSettings == "true")
        {
            BtnLearnMore.IsVisible = true;
            LblSubHead.Margin = new Thickness(15, 0, 15, 5);
            BtnLearnMore.Text = "Open Settings";

        }
        else if (ismealPlan == "true")
        {
            //BtnCancel.IsVisible = true;
            //LblSubHead.Margin = new Thickness(15, 0, 15, 5);
            BtnLearnMore.IsVisible = false;
        }
        else if (isNotNow == "true")
        {
            // BtnCancel.IsVisible = true;
            BtnLearnMore.IsVisible = false;
            // BtnCancel.Text = "Not now";
        }
        else
        {
            BtnLearnMore.IsVisible = false;
        }
        // Uncomment code please
        //MyParticleCanvas.ParticleColors = AppThemeConstants.CalculateConfettieColors();
        if (isTips)
        {
            SetLoading(title);
            //OkButton.Clicked -= OkButton_Clicked;
            //OkAction.GestureRecognizers.Remove(okGuesture);
            //OkButton.Text = "Customizing workout...";
            //OkAction.IsVisible = false;
            MessagingCenter.Subscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage", (obj) =>
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    LblTipText.IsVisible = false;
                    //OkAction.IsVisible = true;

                    //OkButton.Text = "Start workout";
                    //OkButton.Clicked += OkButton_Clicked;
                    //OkAction.GestureRecognizers.Add(okGuesture);
                });
            });
        }
        
        if (isAutoHide == "true")
        {


        }
    }

    private async void LoadDataForWorkout()
    {
        App.TotalWorkoutsDone = 0;
        var workLog = await DrMuscleRestClient.Instance.GetUserWorkoutStatsWithoughtLoader();
        if (workLog != null)
        {
            App.TotalWorkoutsDone = workLog.TotalWorkoutCompleted.Value;
            App.TotalWeightLifted = workLog.TotalWeight;
            Console.WriteLine("Total workouts = " + App.TotalWorkoutsDone);
        }
    }

    //protected override void OnDisappearing()
    //{
    //    base.OnDisappearing();
    //    _isHide = true;
    //    MessagingCenter.Unsubscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage");
    //}
    private async Task SetLoadingSummary(TapGestureRecognizer okGuesture)
    {
        //await Task.Delay(250);

        //OkButton.Text = "Loading.";

        //await Task.Delay(700);
        //OkButton.Text = "Loading..";

        //await Task.Delay(700);

        //OkButton.Text = "Loading...";
        //await Task.Delay(700);
        OkButton.Text = this.buttonText;
        //OkAction.GestureRecognizers.Add(okGuesture);

    }

    private async void SetLoading(string title)
    {
        //LblHeading.FontAttributes = LblSubHead.FontAttributes;
        //LblHeading.FontSize = LblSubHead.FontSize;
        //LblHeading.TextColor = LblSubHead.TextColor;

        if (Device.RuntimePlatform.Equals(Device.Android))
        {
            LblTipText.Text = "";
            LblTipText.IsVisible = true;
            await Task.Factory.StartNew(async () =>
            {

                MainThread.BeginInvokeOnMainThread(() => {
                    LblTipText.Text = "Loading sets...";
                });


                await Task.Delay(500);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {

                    LblTipText.Text = "Loading reps...";
                });
                await Task.Delay(750);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {

                    LblTipText.Text = "Loading weights...";
                });
                await Task.Delay(500);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {
                    LblTipText.Text = "Loading a big pump...";
                });
                await Task.Delay(500);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {
                    LblTipText.Text = "Let's go!";
                });
            });
        }
        else
        {
            LblTipText.IsVisible = true;

            //ImgLoader.IsVisible = true;
            Device.BeginInvokeOnMainThread(async () =>
            {

                LblTipText.Text = "Loading sets...";
                await Task.Delay(700);
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Loading reps...";
                await Task.Delay(750);
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Loading weights...";
                await Task.Delay(800);
                if (LblTipText.Text == " ")
                    return;
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Loading a big pump...";
                await Task.Delay(800);
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Let's go!";

            });
        }
    }

    async void OkButton_Clicked(System.Object sender, System.EventArgs e)
    {
        Debug.WriteLine("OkButton_Clicked Calling.");
        await this.CloseAsync();
        //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
        //    PopupNavigation.Instance.PopAsync();

        if (OkButtonPress != null)
            OkButtonPress.Invoke(sender, EventArgs.Empty);

        MessagingCenter.Unsubscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage");
    }

    async void DrMuscleButton_Clicked(System.Object sender, System.EventArgs e)
    {
        Debug.WriteLine("DrMuscleButton_Clicked Calling.");
        if (BtnLearnMore.Text.Equals("Open Settings"))
        {
            await this.CloseAsync();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    PopupNavigation.Instance.PopAsync();
            SettingsPage page = new SettingsPage();
            await App.Current.MainPage.Navigation.PushAsync(page);
            //PagesFactory.PushAsync<SettingsPage>();
        }
        else if (BtnLearnMore.Text.Equals("Cancel"))
        {
            await this.CloseAsync();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    PopupNavigation.Instance.PopAsync();
            // PagesFactory.PopAsync();
        }
        else
        {
            if (await CheckTrialUserAsync())
                return;
            await this.CloseAsync();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    PopupNavigation.Instance.PopAsync();
            LearnPage page = new LearnPage();
            await App.Current.MainPage.Navigation.PushAsync(page);
            //PagesFactory.PushAsync<LearnPage>();
        }
        MessagingCenter.Unsubscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage");
    }


/* Unmerged change from project 'DrMaxMuscle (net8.0-ios)'
Before:
    private bool CheckTrialUser()
After:
    private bool CheckTrialUserAsync()
*/
    private async Task<bool> CheckTrialUserAsync()
    {
        try
        {
            if (App.IsFreePlan)
            {
                var ShowWelcomePopUp2 = await HelperClass.DisplayCustomPopup("You discovered a premium feature!", "Upgrading will unlock custom coaching tips based on your goals and progression.",
                "Upgrade", "Maybe later");
                ShowWelcomePopUp2.ActionSelected += (sender, action) =>
                {
                    if (action == PopupAction.OK)
                    {
                        SubscriptionPage page = new SubscriptionPage();
                        page.OnBeforeShow();
                        App.Current.MainPage.Navigation.PushAsync(page);
                        //PagesFactory.PushAsync<SubscriptionPage>();
                    }
                };

                // ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
                // {
                //     Message = "Upgrading will unlock custom coaching tips based on your goals and progression.",
                //     Title = "You discovered a premium feature!",
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = "Upgrade",
                //     CancelText = "Maybe later",
                //     OnAction = async (bool ok) =>
                //     {
                //         if (ok)
                //         {
                //             SubscriptionPage page = new SubscriptionPage();
                //             page.OnBeforeShow();
                //             App.Current.MainPage.Navigation.PushAsync(page);
                //             //PagesFactory.PushAsync<SubscriptionPage>();
                //         }
                //         else
                //         {

                //         }
                //     }
                // };
                // UserDialogs.Instance.Confirm(ShowWelcomePopUp2);
            }
        }
        catch (Exception ex)
        {

        }
        return App.IsFreePlan;
    }

    async void DrMuscleButtonCancel_Clicked(System.Object sender, System.EventArgs e)
    {
        Debug.WriteLine("DrMuscleButtonCancel_Clicked Calling.");
        try
        {
           // MessagingCenter.Unsubscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage");

            
            LocalDBManager.Instance.SetDBSetting("LastWorkoutWas", "good");
            SetupLastWorkoutWas("good");
            await this.CloseAsync();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    PopupNavigation.Instance.PopAsync();
            MessagingCenter.Send<HowWasWorkoutMessage>(new HowWasWorkoutMessage() { HowWasWorkout = "good" }, "HowWasWorkoutMessage");

        }
        catch (Exception ex)
        {

        }
    }

    async void DrMuscleButtonTooHard_Clicked(System.Object sender, System.EventArgs e)
    {
        Debug.WriteLine("DrMuscleButtonTooHard_Clicked Calling.");
        try
        {
            //MessagingCenter.Unsubscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage");

            
        LocalDBManager.Instance.SetDBSetting("LastWorkoutWas", "too hard");
        SetupLastWorkoutWas("too hard");
            await this.CloseAsync();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    PopupNavigation.Instance.PopAsync();
            MessagingCenter.Send<HowWasWorkoutMessage>(new HowWasWorkoutMessage() { HowWasWorkout = "too hard" }, "HowWasWorkoutMessage");
        }
        catch (Exception ex)
        {

        }
    }

    async void DrMuscleButtonEasy_Clicked(System.Object sender, System.EventArgs e)
    {
        Debug.WriteLine("DrMuscleButtonEasy_Clicked Calling.");
        try
            {
           // MessagingCenter.Unsubscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage");

            
        LocalDBManager.Instance.SetDBSetting("LastWorkoutWas", "too easy");
        SetupLastWorkoutWas("too easy");
            await this.CloseAsync();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    PopupNavigation.Instance.PopAsync();
            MessagingCenter.Send<HowWasWorkoutMessage>(new HowWasWorkoutMessage() { HowWasWorkout = "too easy" }, "HowWasWorkoutMessage");
        }
        catch (Exception ex)
        {

        }
    }

    private async void SetupLastWorkoutWas(string workoutwas)
    {
        await DrMuscleRestClient.Instance.SetUserLastWorkoutWas(new UserInfosModel()
        {
            LastWorkoutWas = workoutwas
        });
    }
}
