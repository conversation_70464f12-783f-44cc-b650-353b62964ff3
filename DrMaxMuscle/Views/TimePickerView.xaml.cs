﻿using Acr.UserDialogs;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Layout;
using DrMaxMuscle.Message;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Utility;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;

namespace DrMaxMuscle.Views;

public partial class TimePickerView : PopupPage
{
    bool IsFeet = true;
    IList<int> minutesList = new List<int>();
    IList<int> secondsList = new List<int>();
    WorkoutLogSerieModelRef workout = null;
    public TimePickerView(WorkoutLogSerieModelRef w)
    {
        InitializeComponent();
        minutesList = new List<int>();

        secondsList = new List<int>();
        workout = w;

        for (int i = 0; i < 60; i++)
        {
            minutesList.Add(i);
            secondsList.Add(i);
        }
        PickerMin.ItemsSource = minutesList;
        PickerSec.ItemsSource = secondsList;

        if (w != null && w.Reps > 0)
        {
            workout = w;
            var timeSpan = TimeSpan.FromSeconds(w.Reps);
            PickerMin.SelectedIndex = minutesList.IndexOf(timeSpan.Minutes);
            PickerSec.SelectedIndex = secondsList.IndexOf(timeSpan.Seconds);
        }
    }

    public async void BtnFeetClicked(object sender, EventArgs args)
    {
        //BtnLbs.BackgroundColor = Color.FromHex("#5CD196");

        IsFeet = true;
    }


    public async void BtnDoneClicked(object sender, EventArgs args)
    {


        try
        {
            if (PickerMin.SelectedIndex == 0 && PickerSec.SelectedIndex == 0)
            {
                //await UserDialogs.Instance.AlertAsync(new AlertConfig()
                //{
                //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //    Message = "Please enter at least 00:01.",
                //    Title = "Invalid time"
                //});
                //

                // var response = await UserDialogs.Instance.ConfirmAsync(new ConfirmConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = "Are you sure you want to delete set?",
                //     CancelText = AppResources.Cancel,
                //     OkText = AppResources.Delete
                // });

                var response = await HelperClass.DisplayCustomPopupForResult("","Are you sure you want to delete set?",AppResources.Delete,AppResources.Cancel);


                if (response == PopupAction.OK)
                {
                    await MauiProgram.SafeDismissTopPopup();
                    //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
                    //    await PopupNavigation.Instance.PopAsync();
                    MessagingCenter.Send<DeleteSetMessage>(new DeleteSetMessage() { model = workout, isPermenantDelete = true }, "DeleteSetMessage");
                }
                return;
            }
            var timeSpan = new TimeSpan(0, minutesList[PickerMin.SelectedIndex], secondsList[PickerSec.SelectedIndex]);
            if (workout != null)
            {
                workout.Reps = (int)timeSpan.TotalSeconds;
                if (!workout.IsBackOffSet && !workout.IsWarmups) //&& !workout.IsFinished && !workout.IsEditing
                    MessagingCenter.Send<WeightRepsUpdatedMessage>(new WeightRepsUpdatedMessage() { model = workout }, "WeightRepsUpdatedMessage");
            }
            await MauiProgram.SafeDismissTopPopup();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    await PopupNavigation.Instance.PopAsync();
        }
        catch (Exception ex)
        {
            await MauiProgram.SafeDismissTopPopup();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    await PopupNavigation.Instance.PopAsync();
        }


    }


    async void BtnCancel_Clicked(System.Object sender, System.EventArgs e)
    {
        await MauiProgram.SafeDismissTopPopup();
        //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
        //    await PopupNavigation.Instance.PopAsync();
    }
}
