using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Message;
using DrMaxMuscle.Utility;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;
using System.Text.RegularExpressions;

namespace DrMaxMuscle.Views;

public partial class FeedbackView : PopupPage
{
    public FeedbackView()
    {
        InitializeComponent();
        
    }
    async void SolidButton_Clicked(System.Object sender, System.EventArgs e)
    {
        await MauiProgram.SafeDismissTopPopup();
        //if (PopupNavigation.Instance.PopupStack.Count() > 0)
        //   await PopupNavigation.Instance.PopAsync();
        var page = new FullReview();
        await PopupNavigation.Instance.PushAsync(page);
    }

    async void Feedback_Clicked(System.Object sender, System.EventArgs e)
    {
        await MauiProgram.SafeDismissTopPopup();
        //if (PopupNavigation.Instance.PopupStack.Count() > 0)
        //   await PopupNavigation.Instance.PopAsync();
        await Task.Delay(500);
        await HelperClass.SendMail("Feedback about Dr.Muscle");
    }
}