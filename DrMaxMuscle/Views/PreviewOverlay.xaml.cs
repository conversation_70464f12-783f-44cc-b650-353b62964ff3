﻿using Acr.UserDialogs;
using CommunityToolkit.Maui.Core;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.Demo;
using DrMaxMuscle.Screens.User;
using DrMuscleWebApiSharedModel;
using Newtonsoft.Json;
using System.Globalization;
using DrMaxMuscle.Dependencies;
using RGPopup.Maui.Services;
using DrMaxMuscle.Utility;

namespace DrMaxMuscle.Views;

public partial class PreviewOverlay : Popup
{
    public RegisterModel _registerModel { get; set; }
    bool isAILoaded = false;
    string aiTitle = "";
    string aiDescription = "";

    public PreviewOverlay()
    {
        InitializeComponent();

        var screenWidth = DeviceDisplay.MainDisplayInfo.Width / DeviceDisplay.MainDisplayInfo.Density;
        var screenheight = DeviceDisplay.MainDisplayInfo.Height / DeviceDisplay.MainDisplayInfo.Density;
        string cweight = "", todayweight = "", liftedweight = "";

        this.Size = new Size(screenWidth, screenheight);

        if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
        {
            cweight = "95 kg";
            todayweight = "120 kg";
            liftedweight = "242 kg";
        }
        else
        {
            cweight = "210 lbs";
            todayweight = "265 lbs";
            liftedweight = "535 lbs";
        }


        var tapLinkTermsOfUseGestureRecognizer = new TapGestureRecognizer();
        tapLinkTermsOfUseGestureRecognizer.NumberOfTapsRequired = 1;
        tapLinkTermsOfUseGestureRecognizer.Tapped += (s, e) =>
        {
            Browser.OpenAsync("http://drmuscleapp.com/news/terms/", BrowserLaunchMode.SystemPreferred);
           // Device.OpenUri(new Uri("http://drmuscleapp.com/news/terms/"));
        };

        // uncomment code please
        //DependencyService.Get<IDrMuscleSubcription>().OnMonthlyAccessPurchased += async delegate
        //{
        //    if (Device.RuntimePlatform == Device.Android)
        //    {
        //        await PagesFactory.PushAsyncWithoutBefore<MainAIPage>();
        //    }
        //    else
        //        await PagesFactory.PushAsync<MainAIPage>(); MessagingCenter.Send<SubscriptionSuccessfulMessage>(new SubscriptionSuccessfulMessage(), "SubscriptionSuccessfulMessage");
        //};
        //DependencyService.Get<IDrMuscleSubcription>().OnYearlyAccessPurchased += async delegate
        //{
        //    if (Device.RuntimePlatform == Device.Android)
        //    {
        //        await PagesFactory.PushAsyncWithoutBefore<MainAIPage>();
        //    }
        //    else
        //        await PagesFactory.PushAsync<MainAIPage>(); MessagingCenter.Send<SubscriptionSuccessfulMessage>(new SubscriptionSuccessfulMessage(), "SubscriptionSuccessfulMessage");
        //};


    }

    public void OnBeforeShow()
    {
        //base.OnAppearing();
        Setup();
        AnaliesAIWithChatGPT();
    }

    private async void Setup()
    {

        string goal = "build lean muscle";
        var result = "";
        try
        {
            if (LocalDBManager.Instance.GetDBSetting("Demoreprange")?.Value == "BuildMuscle")
            {
                goal = "build muscle";
                result = "This helps you build muscle.";
                LblGenderGoal4.Text = "Build muscle";
            }
            else if (LocalDBManager.Instance.GetDBSetting("Demoreprange")?.Value == "BuildMuscleBurnFat")
            {
                goal = "build lean muscle";//"Build muscle and burn fat";
                result = "This helps you build muscle and burn fat.";
                LblGenderGoal4.Text = "Build muscle and burn fat";
            }
            else //if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "FatBurning")
            {
                goal = "burn fat";
                result = "This helps you burn fat.";
                LblGenderGoal4.Text = "Burn fat ";
            }
        }
        catch (Exception ex)
        {

        }
        try
        {
            if (LocalDBManager.Instance.GetDBSetting("gender")?.Value.Trim() != "Man")
            {
                ImgGender.Source = "exercise_background";

            }
        }
        catch (Exception ex)
        {

        }

        int lowReps = 0;
        int highreps = 0;
        try
        {
            lowReps = int.Parse(LocalDBManager.Instance.GetDBSetting("repsminimum")?.Value);
            highreps = int.Parse(LocalDBManager.Instance.GetDBSetting("repsmaximum")?.Value);
        }
        catch (Exception)
        {

        }

        string fname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;

        DrMuscleWorkoutsButton.Text = $"{fname}, this program will help you {goal}";


        string cweight = "", todayweight = "", liftedweight = "";

        if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
        {
            cweight = "95 kg";
            todayweight = "120 kg";
            liftedweight = "242 kg";
        }
        else
        {
            cweight = "210 lbs";
            todayweight = "265 lbs";
            liftedweight = "535 lbs";
        }
        var programName = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel")?.Value;
        List<string> listSource = new List<string>();
        if (programName == null)
            programName = "";
        //listSource.Add("View more programs");
        try
        {
            if (programName.ToLower().Contains("gym"))
            {
                if (_registerModel?.EquipmentModel?.IsEquipmentEnabled == true)
                {
                    if (_registerModel?.EquipmentModel?.IsDumbbellEnabled == true)
                        flChipView.Children.Add(CreateRandomBoxview("Dumbbell training "));
                    if (_registerModel?.EquipmentModel?.IsPlateEnabled == true) flChipView.Children.Add(CreateRandomBoxview("Barbell training "));
                }
                else
                {
                    flChipView.Children.Add(CreateRandomBoxview("Barbell training "));
                    flChipView.Children.Add(CreateRandomBoxview("Dumbbell training "));
                }
                flChipView.Children.Add(CreateRandomBoxview("Bodyweight training"));
                if (_registerModel?.IsMobility == true)
                    flChipView.Children.Add(CreateRandomBoxview("Mobility training"));

                if (_registerModel?.IsCardio == true)
                    flChipView.Children.Add(CreateRandomBoxview("Cardio training"));

                flChipView.Children.Add(CreateRandomBoxview("Strength training"));

                flChipView.Children.Add(CreateRandomBoxview("Back-off sets"));
            }
            else if (programName.ToLower().Contains("bands"))
            {
                flChipView.Children.Add(CreateRandomBoxview("Bodyweight training"));
                flChipView.Children.Add(CreateRandomBoxview("Bands training"));

                if (_registerModel?.IsMobility == true)
                    flChipView.Children.Add(CreateRandomBoxview("Mobility training"));

                if (_registerModel?.IsCardio == true)
                    flChipView.Children.Add(CreateRandomBoxview("Cardio training"));
            }
            else if (programName.ToLower().Contains("bodyweight"))
            {
                flChipView.Children.Add(CreateRandomBoxview("Bodyweight training"));
                flChipView.Children.Add(CreateRandomBoxview("Bands training"));

                if (_registerModel?.IsMobility == true)
                    flChipView.Children.Add(CreateRandomBoxview("Mobility training"));

                if (_registerModel?.IsCardio == true)
                    flChipView.Children.Add(CreateRandomBoxview("Cardio training"));
            }
            else if (programName.ToLower().Contains("home"))
            {
                if (_registerModel?.EquipmentModel?.IsHomeEquipmentEnabled == true)
                {
                    if (_registerModel?.EquipmentModel?.IsHomeDumbbell == true)
                        flChipView.Children.Add(CreateRandomBoxview("Dumbbell training "));
                    if (_registerModel?.EquipmentModel?.IsHomePlate == true) flChipView.Children.Add(CreateRandomBoxview("Barbell training "));

                }
                else
                {
                    flChipView.Children.Add(CreateRandomBoxview("Barbell training "));
                    flChipView.Children.Add(CreateRandomBoxview("Dumbbell training "));
                }

                flChipView.Children.Add(CreateRandomBoxview("Bodyweight training"));

                if (_registerModel?.IsMobility == true)
                    flChipView.Children.Add(CreateRandomBoxview("Mobility training"));

                if (_registerModel?.IsCardio == true)
                    flChipView.Children.Add(CreateRandomBoxview("Cardio training"));


                flChipView.Children.Add(CreateRandomBoxview("Strength training"));


                flChipView.Children.Add(CreateRandomBoxview("Back-off sets"));
            }
        }
        catch (Exception ex)
        {

        }


    }

    void WorkoutPageRecognizer_Tapped(object sender, EventArgs e)
    {

    }

    private async Task<string> AnaliesAIWithChatGPT(bool isloader = false, double temperature = 0.7, int maxTokens = 2500, double topP = 1, double frequencyPenalty = 0, double presencePenalty = 0)
    {
        try
        {
            CurrentLog.Instance.AiDescription = "";
            var name = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
            var experience = LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value;
            var iskg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg";
            var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
            var weights = new MultiUnityWeight(value, "kg");
            var goalWeightNum = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value.Replace(",", "."), CultureInfo.InvariantCulture);
            var fetchweights = new MultiUnityWeight((decimal)value, "kg");
            var goalMultiWeight = new MultiUnityWeight((decimal)goalWeightNum, "kg");
            var weight = "";
            var goalWeight = "";
            if (iskg)
            {
                weight = string.Format("{0:0.##} kg", fetchweights.Kg);
                goalWeight = string.Format("{0:0.##} kg", goalMultiWeight.Kg);
            }
            else
            {
                weight = string.Format("{0:0.##} lbs", fetchweights.Lb);
                goalWeight = string.Format("{0:0.##} lbs", goalMultiWeight.Lb);
            }
            var gender = LocalDBManager.Instance.GetDBSetting("gender")?.Value.Trim();
            var age = LocalDBManager.Instance.GetDBSetting("Age")?.Value??"0";
            var ageInt = int.Parse(age);

            var focusText = LocalDBManager.Instance.GetDBSetting("focusText")?.Value;
            if (!string.IsNullOrEmpty(focusText))
            {
                focusText = focusText.Replace("\nStronger sex drive", "");
                focusText = focusText.Replace("Stronger sex drive", "");
            }
            if (string.IsNullOrEmpty(focusText))
                focusText = "Better health";
            var muscleProirity = "";//
            var bodyPart = LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value;
            if (!string.IsNullOrEmpty(bodyPart))
                muscleProirity = $"Muscle priority: {bodyPart}\n";
            var program = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel")?.Value;
            decimal targetIntake = 0;
            try
            {

                var weightinPounds = fetchweights.Lb;
                if (weightinPounds > 150 && Config.UserHeight != 0 && Config.UserHeight > 0 && ageInt != null && ageInt > 0)
                {
                    //9.99*weight (Kg) + 6.25*height (cm) − 4.92*age + 166*sex (M = 1; F = 0) −161
                    targetIntake = Math.Round(((decimal)9.99 * (decimal)weightinPounds) + ((decimal)6.25 * (decimal)Config.UserHeight) - ((decimal)4.92 * (decimal)ageInt) + (166 * (gender == "Man" ? 1 : 0)) - 161, 2);


                    if (goalMultiWeight.Kg > fetchweights.Kg)
                        targetIntake = targetIntake * (decimal)1.15;
                    else
                        targetIntake = targetIntake * (decimal)0.75;
                }
                else
                {
                    if (goalMultiWeight.Kg > fetchweights.Kg)
                        targetIntake = (decimal)Math.Round(15 * Math.Round(fetchweights.Lb, 2), 4);
                    else
                        targetIntake = (decimal)Math.Round(11 * Math.Round(fetchweights.Lb, 2), 4);

                }
            }
            catch (Exception ex)
            {

            }
            var protein = "";

            if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
            {
                protein = Math.Round(fetchweights.Kg * (decimal)1.6) + "-" + Math.Round(fetchweights.Kg * (decimal)2.2) + " g";
            }
            else
            {
                protein = Math.Round(fetchweights.Lb * (decimal)0.7) + "-" + Math.Round(fetchweights.Lb * (decimal)1.0) + " g";
            }
            var query = $"You are a coach in a mobile app. The app creates custom programs and updates them automatically every workout. I just finished creating an account and you're giving me my program.\n\nGender: {gender}\nExperience: {experience}\nAge: {age}\nWeight: {weight}\nTarget weight: {goalWeight}\nGoal: {focusText}\n{muscleProirity}Program: {program}\n\nWhat can you tell me about that? Write a few paragraphs of about 40 words each in the style of John Meadows. Use short sentences and short, common words. Follow this outline:\n\nParagraph 1: Analyze my profile and program.\nParagraph 2: To help reach my goal of... faster, my program updates automatically.\nParagraph 3: Diet tips to reach my goal. Calories ({Math.Round(targetIntake)} a day). Protein ({protein} per day). Mention I can track my body weight in the app to get up-to-date macro recommendations and personalized coaching.\nParagraph 4: Other tips to reach my goal (e.g. consistency, recovery)\nParagraph 5: A quote from Arnold Schwarzenegger. Attribute it. Mention results.\n\nInclude numbers. Number paragraphs with 1, 2, 3..., but don't include headings. Don't use hashtags. Don't mention bodybuilding or bodybuilders.";

            //var query = $"You are a coach in a mobile app. The app creates custom programs and updates them automatically every workout. I just finished creating an account and you're giving me my program.\n\nGender: {gender}\nExperience: {experience}\nAge: {age}\nWeight: {weight}\nTarget weight: {goalWeight}\nGoal: {focusText}\nProgram: {program}\n\nWhat can you tell me about that? Write a few paragraphs of about 40 words each in the style of John Meadows. Use short sentences and short, common words. Follow this outline:\n\nParagraph 1: Analyze my profile and program.\nParagraph 2: Program updates automatically to reach my goals faster (mention my goals).\nParagraph 3: Diet tips to reach my goal. Mention calories and protein, but no specific numbers.\nParagraph 4: Other tips to reach my goal (e.g. consistency, recovery)\nParagraph 5: One famous quote. Attribute it. Tell me I will see results. End on an upbeat note.\n\nInclude numbers. Don't use hashtags. Don't mention bodybuilding or bodybuilders. Number paragraphs.";
            // Create an HTTP client
            var tokenCount = 4097 - query.Length;
            string openaiKey = AppThemeConstants.GPTKey;
            string prompt = query;
            if (isloader)
                UserDialogs.Instance.ShowLoading();
            using (var _httpClient = new HttpClient())
            {
                _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", openaiKey);
                string model = "gpt-3.5-turbo";//"text-davinci-002"; // model for GPT-3.5 Turbo
                var requestUrl = "https://api.openai.com/v1/chat/completions";
                //string model = "text-davinci-003";//"text-davinci-002"; // model for GPT-3.5 Turbo
                //var requestUrl = "https://api.openai.com/v1/completions";
                var requestBody = new
                {
                    messages = new[] { new { role = "user", content = prompt } },
                    model = model,
                    temperature = temperature,
                    max_tokens = tokenCount,
                    top_p = topP,
                    frequency_penalty = frequencyPenalty,
                    presence_penalty = presencePenalty
                };
                var requestBodyJson = Newtonsoft.Json.JsonConvert.SerializeObject(requestBody);
                var requestContent = new StringContent(requestBodyJson, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(requestUrl, requestContent);
                //response.EnsureSuccessStatusCode();
                //UserDialogs.Instance.HideLoading();
                var responseBodyJson = await response.Content.ReadAsStringAsync();

                var chatGPTResponse = JsonConvert.DeserializeObject<ChatGPTResponse>(responseBodyJson);

                var chatResponse = chatGPTResponse.choices[0].message.content;
                var AILoadedText = chatResponse;
                if (string.IsNullOrEmpty(AILoadedText))
                {
                    isAILoaded = false;
                    AILoadedText = "";
                }
                else
                {

                    aiDescription = AILoadedText;
                    if (aiDescription.StartsWith("Paragraph 1: "))
                    {
                        aiDescription = aiDescription.Substring(13);
                        aiDescription = aiDescription.Replace("\nParagraph 2: ", "\n");
                        aiDescription = aiDescription.Replace("\nParagraph 3: ", "\n");
                        aiDescription = aiDescription.Replace("\nParagraph 4: ", "\n");
                        aiDescription = aiDescription.Replace("\nParagraph 5: ", "\n");
                        aiDescription = aiDescription.Replace("\nParagraph 6: ", "\n");
                        aiDescription = aiDescription.Replace("\nParagraph 7: ", "\n");
                        aiDescription = aiDescription.Replace("\nParagraph 8: ", "\n");
                    }
                    if (aiDescription.StartsWith("1. "))
                    {
                        aiDescription = aiDescription.Substring(3);
                        aiDescription = aiDescription.Replace("\n2. ", "\n");
                        aiDescription = aiDescription.Replace("\n3. ", "\n");
                        aiDescription = aiDescription.Replace("\n4. ", "\n");
                        aiDescription = aiDescription.Replace("\n5. ", "\n");
                        aiDescription = aiDescription.Replace("\n6. ", "\n");
                        aiDescription = aiDescription.Replace("\n7. ", "\n");
                        aiDescription = aiDescription.Replace("\n8. ", "\n");
                    }
                    if (aiDescription.StartsWith("(1) "))
                    {
                        aiDescription = aiDescription.Replace("(1) ", "");
                        aiDescription = aiDescription.Replace("(2) ", "");
                        aiDescription = aiDescription.Replace("(3) ", "");
                        aiDescription = aiDescription.Replace("(4) ", "");
                        aiDescription = aiDescription.Replace("(5) ", "");
                        aiDescription = aiDescription.Replace("(6) ", "");
                        aiDescription = aiDescription.Replace("(7) ", "");
                        aiDescription = aiDescription.Replace("(8) ", "");
                    }
                    if (aiDescription.StartsWith("1) "))
                    {
                        aiDescription = aiDescription.Replace("1) ", "");
                        aiDescription = aiDescription.Replace("2) ", "");
                        aiDescription = aiDescription.Replace("3) ", "");
                        aiDescription = aiDescription.Replace("4) ", "");
                        aiDescription = aiDescription.Replace("5) ", "");
                        aiDescription = aiDescription.Replace("6) ", "");
                        aiDescription = aiDescription.Replace("7) ", "");
                        aiDescription = aiDescription.Replace("8) ", "");
                    }

                    if (!aiDescription.Contains("\n\n"))
                    {
                        aiDescription = aiDescription.Replace("\n", "\n\n");
                    }

                    CurrentLog.Instance.AiDescription = aiDescription = aiDescription.Replace("8. ", "");
                }


                return chatResponse;
            }
        }
        catch (Exception ex)
        {
            return "";
            UserDialogs.Instance.HideLoading();
        }
        finally
        {

        }
    }


    async void Close_Tapped(object sender, EventArgs e)
    {
        try
        {
            CurrentLog.Instance.EndExerciseActivityPage = this.GetType();
            this.Close();
            var modalPage1 = new WelcomeAIOverlay();
            await Application.Current.MainPage.Navigation.PushAsync(modalPage1);
            //= new NavigationPage(modalPage1); 
            //PopupNavigation.Instance.PushAsync(modalPage1);
            modalPage1.SetDetails("", CurrentLog.Instance.AiDescription);
            //await BoomSuccessPopup();
            //if (Application.Current.MainPage.Navigation.ModalStack?.Count  > 0)
            //    this.Close();

            //await OpenDemo();
        }
        catch (Exception ex)
        {

        }

    }

    async Task OpenDemo()
    {
        try
        {
            CurrentLog.Instance.CurrentExercise = new ExerciceModel()
            {
                BodyPartId = 7,
                VideoUrl = "https://youtu.be/Plh1CyiPE_Y",
                IsBodyweight = true,
                IsEasy = false,
                IsFinished = false,
                IsMedium = false,
                IsNextExercise = false,
                IsNormalSets = false,
                IsSwapTarget = false,
                IsSystemExercise = true,
                IsTimeBased = false,
                IsUnilateral = false,
                Label = "Crunch",
                RepsMaxValue = null,
                RepsMinValue = null,
                Timer = null,
                Id = 864
            };
            App.IsDemoProgress = true;
            LocalDBManager.Instance.SetDBSetting("DemoProgress", "true");

            NewDemoPage page = new NewDemoPage();
            page.OnBeforeShow();
            await this.CloseAsync();
            //Application.Current.MainPage = new NavigationPage(page);
            await Application.Current.MainPage.Navigation.PushAsync(page);
            //await PagesFactory.PushAsync<Screens.Demo.NewDemoPage>();
        }
        catch (Exception ex)
        {
        }

    }
    void HelpGestureRecognizer_Tapped(System.Object sender, System.EventArgs e)
    {
        try
        {
            Application.Current.MainPage.Navigation.PushAsync(new FAQPage());
        }
        catch (Exception ex)
        {

        }
        //Application.Current.MainPage = new NavigationPage(new FAQPage());
        //PagesFactory.PushAsync<FAQPage>();
    }

    void MoreUserReviewGestureRecognizer_Tapped(System.Object sender, System.EventArgs e)
    {
        Browser.OpenAsync("https://dr-muscle.com/reviews/", BrowserLaunchMode.SystemPreferred);
//        Device.OpenUri(new Uri("https://dr-muscle.com/reviews/"));
    }

    void NewUpdatesGestureRecognizer_Tapped(System.Object sender, System.EventArgs e)
    {
        Browser.OpenAsync("https://dr-muscle.com/timeline/", BrowserLaunchMode.SystemPreferred);
        //Device.OpenUri(new Uri("https://dr-muscle.com/timeline/"));
    }

    void TapMoreExperReviews_Tapped(System.Object sender, System.EventArgs e)
    {
        Browser.OpenAsync("https://dr-muscle.com/reviews/", BrowserLaunchMode.SystemPreferred);
       // Device.OpenUri(new Uri("https://dr-muscle.com/reviews/"));
    }

    private Frame CreateRandomBoxview(string items)
    {
        var view = new Frame();    // Creating New View for design as chip
        view.BackgroundColor = Color.FromHex("#AAE1E1E1");
        view.BorderColor = Colors.Transparent;
        view.Padding = new Thickness(7, 7);
        view.Margin = new Thickness(0,0,5,8);
        view.CornerRadius = 4;
        view.HasShadow = false;


        // creating new child that holds the value of item list and add in View
        var label = new Label();
        label.Text = items;
        label.TextColor = Colors.Black;
        label.HorizontalOptions = LayoutOptions.Center;
        label.VerticalOptions = LayoutOptions.Center;
        label.FontSize = 17;
        view.Content = label;
        return view;
    }

    void programPicker_PropertyChanged(object sender, EventArgs e)
    {
    }

    async void programPicker_Unfocused(object sender, FocusEventArgs e)
    {
        //if (programPicker != null && programPicker.SelectedIndex != -1)
        //{
        //    ConfirmConfig supersetConfig = new ConfirmConfig()
        //    {
        //        Title = "Are you sure?",
        //        Message = $"Your program will change to {programPicker.SelectedItem}",
        //        OkText = "Change program",
        //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //        CancelText = AppResources.Cancel,
        //    };

        //    var x = await UserDialogs.Instance.ConfirmAsync(supersetConfig);
        //    if (x)
        //    {
        //        try
        //        {

        //            var selectedProgram = (string)programPicker.SelectedItem;
        //            var programName = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel")?.Value;
        //            var level = int.Parse($"{programName.Trim().Last()}");
        //            var selectedProgramShortName = "";
        //            if (selectedProgram.Equals("Full-body"))
        //                selectedProgramShortName = "Full body";
        //            else if (selectedProgram.Equals("Upper/Lower-Body Split"))
        //                selectedProgramShortName = "Split body";
        //            else if (selectedProgram.Equals("Push/Pull/Legs Split"))
        //                selectedProgramShortName = "PPL";
        //            else if (selectedProgram.Equals("Powerlifting"))
        //                selectedProgramShortName = "Powerlifting";
        //            else if (selectedProgram.Equals("Buffed with Bands"))
        //                selectedProgramShortName = "Bands only";
        //            else if (selectedProgram.Equals("Bodyweight level 1"))
        //            {
        //                selectedProgramShortName = "Bodyweight";
        //                level = 1;
        //            }
        //            else if (selectedProgram.Equals("Bodyweight level 2"))
        //            {
        //                selectedProgramShortName = "Bodyweight";
        //                level = 2;
        //            }
        //            else if (selectedProgram.Equals("Bodyweight level 3"))
        //            {
        //                selectedProgramShortName = "Bodyweight";
        //                level = 3;
        //            }

        //            var mo = DrMuscle.Constants.AppThemeConstants.GetLevelProgram(level, programName.ToLower().Contains("gym"), selectedProgram.Equals("Full-body"), selectedProgramShortName);
        //            LocalDBManager.Instance.SetDBSetting("recommendedWorkoutId", mo.workoutid.ToString());
        //            LocalDBManager.Instance.SetDBSetting("recommendedWorkoutLabel", mo.workoutName);
        //            LocalDBManager.Instance.SetDBSetting("recommendedProgramId", mo.programid.ToString());
        //            LocalDBManager.Instance.SetDBSetting("recommendedRemainingWorkout", mo.reqWorkout.ToString());

        //            LocalDBManager.Instance.SetDBSetting("recommendedProgramLabel", mo.programName);
        //            PopupNavigation.Instance.PopAsync();

        //        }
        //        catch (Exception ex)
        //        {

        //        }
        //    }
        //    else
        //    {
        //        programPicker.SelectedIndex = -1;
        //    }
        //}
    }

    async void ViewmoreProgram(object sender, EventArgs e)
    {

        var programName = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel")?.Value;
        List<string> listSource = new List<string>();
        btnViewMoreProgram.IsVisible = false;
        workoutFrm.IsVisible = true;


        if (!programName.ToLower().Contains("full-body"))
            await AddOptions("Full-body (2-4x workouts / week)", ChangedProgram);
        if (!programName.ToLower().Contains("up/low"))
            await AddOptions("Upper/lower (3-4x / week)", ChangedProgram);
        if (!programName.ToLower().Contains("push/pull/legs"))
            await AddOptions("Push/pull/legs (6x / week)", ChangedProgram);
        if (!programName.ToLower().Contains("powerlifting"))
            await AddOptions("Powerlifting (2-4x / week)", ChangedProgram);
        if (!programName.ToLower().Contains("bands"))
            await AddOptions("Bands only (2-4x / week)", ChangedProgram);
        if (!programName.ToLower().Contains("bodyweight"))
            await AddOptions("Bodyweight only (2-4x / week)", ChangedProgram);
        if (!programName.ToLower().Contains("abs, legs & glutes"))
            await AddOptions("Abs, Legs & Glutes Focus (2-4x / week)", ChangedProgram);
        if (!programName.ToLower().Contains("muscle split"))
            await AddOptions("Muscle Split (6x / week)", ChangedProgram);

        await Task.Delay(300);
        workoutFrm.VerticalOptions = LayoutOptions.FillAndExpand;
        workoutStack.VerticalOptions = LayoutOptions.FillAndExpand;
        scrollView.ScrollToAsync(workoutFrm, ScrollToPosition.End, true);
    }
    async void ChangedProgram(object sender, EventArgs e)
    {
        try
        {
            var selectedProgram = ((Button)sender).Text;
            var programName = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel")?.Value;
            if (string.IsNullOrEmpty(programName))
                programName = "1";
            var level = int.Parse($"{programName.Trim().Last()}");
            var selectedProgramShortName = "";
            if (selectedProgram.Contains("Full-body"))
                selectedProgramShortName = "Full body";
            else if (selectedProgram.Contains("Upper/lower"))
                selectedProgramShortName = "Split body";
            else if (selectedProgram.Contains("Push/pull/legs"))
                selectedProgramShortName = "PPL";
            else if (selectedProgram.Contains("Powerlifting"))
                selectedProgramShortName = "Powerlifting";
            else if (selectedProgram.Contains("Bands only"))
                selectedProgramShortName = "Bands only";
            else if (selectedProgram.Contains("Abs, Legs & Glutes"))
                selectedProgramShortName = "ALG";
            else if (selectedProgram.Contains("Muscle Split"))
                selectedProgramShortName = "MS";
            else if (selectedProgram.Contains("Bodyweight"))
            {
                selectedProgramShortName = "Bodyweight";
                level = 1;
            }
            else if (selectedProgram.Equals("Bodyweight level 2"))
            {
                selectedProgramShortName = "Bodyweight";
                level = 2;
            }
            else if (selectedProgram.Equals("Bodyweight level 3"))
            {
                selectedProgramShortName = "Bodyweight";
                level = 3;
            }

            var mo = DrMaxMuscle.Constants.AppThemeConstants.GetLevelProgram(level, programName.ToLower().Contains("gym"), selectedProgram.Contains("Full-body"), selectedProgramShortName);
            var x = await HelperClass.DisplayCustomPopupForResult("Are you sure?",$"Your program will change to {mo.programName}","Change program",AppResources.Cancel);


            // ConfirmConfig supersetConfig = new ConfirmConfig()
            // {
            //     Title = "Are you sure?",
            //     Message = $"Your program will change to {mo.programName}",
            //     OkText = "Change program",
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     CancelText = AppResources.Cancel,
            // };

            // var x = await UserDialogs.Instance.ConfirmAsync(supersetConfig);
            if (x == PopupAction.OK)
            {
                LocalDBManager.Instance.SetDBSetting("recommendedWorkoutId", mo.workoutid.ToString());
                LocalDBManager.Instance.SetDBSetting("recommendedWorkoutLabel", mo.workoutName);
                LocalDBManager.Instance.SetDBSetting("recommendedProgramId", mo.programid.ToString());
                LocalDBManager.Instance.SetDBSetting("recommendedRemainingWorkout", mo.reqWorkout.ToString());

                LocalDBManager.Instance.SetDBSetting("recommendedProgramLabel", mo.programName);
                isAILoaded = false;
                aiTitle = "";
                aiDescription = "";
                CurrentLog.Instance.AiDescription = "";
                AnaliesAIWithChatGPT();
                //MessagingCenter.Send<SignupFinishMessage>(new SignupFinishMessage() { IsRefresh = true }, "SignupFinishMessage");
                await DrMuscleRestClient.Instance.SaveWorkoutV3(new SaveWorkoutModel() { WorkoutId = mo.workoutid });
                //Close_Tapped(sender, e);


            }
        }
        catch (Exception ex)
        {

        }
    }

    async Task BoomSuccessPopup()
    {
        try
        {
            var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
            var modalPage = new Views.GeneralPopup("lists.png", "Settings saved", "Do a demo workout to unlock the full experience", "Demo workout", new Thickness(18, 0, 0, 0));
            modalPage.Closed += (sender2, e2) =>
            {
                waitHandle.Set();
            };
            await Application.Current.MainPage.ShowPopupAsync(modalPage);

            await Task.Run(() => waitHandle.WaitOne());
        }
        catch (Exception ex)
        {
        }

    }
    async Task<Button> AddOptions(string title, EventHandler handler)
    {
        var grid = new Grid();
        var gradientBrush = new LinearGradientBrush
        {
            EndPoint = new Point(1,0)
        };
        gradientBrush.GradientStops.Add(new GradientStop(Color.FromHex("#0C2432"), (float)0.0));
        gradientBrush.GradientStops.Add(new GradientStop(Color.FromHex("#195276"), (float)1.0));

        var pancakeView = new Frame() { HeightRequest = 55, Margin = new Thickness(10, 4) ,CornerRadius = 0,BorderColor= Colors.Transparent};
        pancakeView.Background = gradientBrush;
        //pancakeView.OffsetAngle = Device.RuntimePlatform.Equals(Device.Android) ? 45 : 90;
        //pancakeView.BackgroundGradientStops.Add(new Xamarin.Forms.PancakeView.GradientStop { Color = Color.FromHex("#195276"), Offset = 1 });
        //pancakeView.BackgroundGradientStops.Add(new Xamarin.Forms.PancakeView.GradientStop { Color = Color.FromHex("#0C2432"), Offset = 0 });
        grid.Children.Add(pancakeView);


        var btn = new Button()
        {
            Text = title,
            TextColor = Colors.Black,
            BorderWidth=0,
            BackgroundColor = Colors.White,
            FontSize = Device.RuntimePlatform.Equals(Device.Android) ? 15 : 17,
            CornerRadius = 0,
            HeightRequest = 55
        };
        btn.Clicked += handler;
        SetDefaultButtonStyle(btn);
        grid.Children.Add(btn);
        Device.BeginInvokeOnMainThread(() =>
        {
            workoutStack.Children.Add(grid);
        });



        return btn;
    }

    void SetDefaultButtonStyle(Button btn)
    {
        btn.BackgroundColor = Colors.Transparent;
        btn.BorderWidth = 0;
        btn.CornerRadius = 0;
        btn.Margin = new Thickness(25, 2, 25, 2);
        btn.FontAttributes = FontAttributes.Bold;
        btn.BorderColor = Colors.Transparent;
        btn.TextColor = Colors.White;
        btn.HeightRequest = 55;

    }
}