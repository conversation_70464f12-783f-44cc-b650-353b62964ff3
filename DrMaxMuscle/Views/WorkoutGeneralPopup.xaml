﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Views.WorkoutGeneralPopup"
               CanBeDismissedByTappingOutsideOfPopup="False"
               xmlns:t="clr-namespace:DrMaxMuscle.Layout"
               xmlns:constants="clr-namespace:DrMaxMuscle.Constants"
            xmlns:controls="clr-namespace:DrMaxMuscle.Layout"
               xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit">
   
        <Grid x:Name="mainStack"
        BackgroundColor="White"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="Center"
        Padding="0,0,0,0" Margin="0">

            <Frame
            Padding="0,20"
            Margin="0,0,0,0"
            CornerRadius="8"
            HasShadow="False" BackgroundColor="White"
            IsClippedToBounds="True" BorderColor="Transparent"
            HorizontalOptions="FillAndExpand">
                <StackLayout
                Padding="0,20">

                    <BoxView
                    Margin="0,0,0,0"
                    BackgroundColor="Transparent" />
                    <Image
                    Margin="0,10,0,0"
                    x:Name="ImgName"
                    WidthRequest="100"
                    HeightRequest="100"
                    HorizontalOptions="Center"
                    VerticalOptions="Start"
                    Source="truestate.png" />

                    <Label
                    Margin="0,15,0,0"
                    Text="Success!"
                    x:Name="LblHeading"
                    HorizontalOptions="Center"
                    FontSize="26"
                    FontAttributes="Bold"
                    TextColor="Black"
                    HorizontalTextAlignment="Center" />
                    <Label
                    Text="Account created"
                    x:Name="LblSubHead"
                    Margin="15,0,15,10"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    FontSize="17"
                    TextColor="#505050"
                    FontAttributes="Bold" />
                    <t:DrMuscleButton
                    Text="Learn more"
                    x:Name="BtnLearnMore"
                    Clicked="DrMuscleButton_Clicked"
                    BackgroundColor="Transparent"
                    TextColor="#007aff"
                    HeightRequest="55" />
                    <t:DrMuscleButton
                    Text="Too easy"
                    Clicked="DrMuscleButtonEasy_Clicked"
                    BackgroundColor="Transparent"
                        BorderColor="#195377"
                        BorderWidth="1"
                        FontAttributes="Bold"
                        CornerRadius="0"
                        TextColor="#195377"
                    Margin="25,10,25,0"
                    HeightRequest="66" />
                    <!--<Frame
                    Padding="0"
                    x:Name="OkAction"
                    IsClippedToBounds="true"
                    CornerRadius="0"
                    VerticalOptions="EndAndExpand"
                    HorizontalOptions="FillAndExpand"
                    Margin="24,4"
                    Style="{StaticResource GradientFrameStyleBlue}"
                    HeightRequest="66">

                        <Label
                        x:Name="OkButton"
                        Text="Workout good"
                        FontAttributes="Bold"
                        BackgroundColor="Transparent"
                        VerticalTextAlignment="Center"
                        TextColor="White"
                        HorizontalTextAlignment="Center"
                        HorizontalOptions="FillAndExpand"
                        Margin="0,0"
                        HeightRequest="55" />
                    </Frame>-->
                    <t:DrMuscleButton
                        x:Name="OkButton"
                        Text="Workout good"
                        Clicked="DrMuscleButtonCancel_Clicked"
                        FontAttributes="Bold"
                        BackgroundColor="Transparent"
                        TextColor="White"
                        HorizontalOptions="FillAndExpand"
                        Margin="24,4"
                        Style="{StaticResource GradientFrameStyleBlue}"
                        HeightRequest="66" />

                    <t:DrMuscleButton
                        BorderColor="#195377"
BorderWidth="1"
FontAttributes="Bold"
CornerRadius="0"
TextColor="#195377"
                    Text="Too hard"
                    Clicked="DrMuscleButtonTooHard_Clicked"
                    BackgroundColor="Transparent"
                    Margin="25,0"
                    HeightRequest="66" />
                    <Label
                    Text=""
                    x:Name="LblTipText"
                    Margin="15,0,15,0"
                    HeightRequest="55"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"
                    VerticalTextAlignment="Center"
                    HorizontalTextAlignment="Center"
                    FontSize="17"
                    TextColor="#505050"
                    FontAttributes="Bold" />
                </StackLayout>
            </Frame>
            <!--<Image Grid.Row="0" Margin="0,25" Source="SharpCurve.png" HorizontalOptions="FillAndExpand" HeightRequest="120"  VerticalOptions="Start" Aspect="Fill" />-->
            <!--<Image
            Grid.Row="0"
            Margin="0,50,0,20"
            Source=""
            WidthRequest="50"
            HeightRequest="50"
            HorizontalOptions="Center"
            VerticalOptions="Start" />-->
            <!--<forms:ParticleView
            x:Name="MyParticleCanvas"
            FallingParticlesPerSecond="25.0"
            IsActive="True"
            IsRunning="True"
            HasFallingParticles="True"
            VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand"
            InputTransparent="True" />-->
        </Grid>
    
</toolkit:Popup>

