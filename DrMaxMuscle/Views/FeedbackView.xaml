<?xml version="1.0" encoding="utf-8" ?>
<toolkit:PopupPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               xmlns:constants="clr-namespace:DrMaxMuscle.Constants"
               BackgroundColor="#99000000"
                xmlns:t="clr-namespace:DrMaxMuscle.Layout"
               xmlns:toolkit="clr-namespace:RGPopup.Maui.Pages;assembly=RGPopup.Maui"
             x:Class="DrMaxMuscle.Views.FeedbackView">
    <toolkit:PopupPage.Content>
        <Frame Padding="0" CornerRadius="4"
         HasShadow="False"
         IsClippedToBounds="True"
         HorizontalOptions="FillAndExpand"
    VerticalOptions="CenterAndExpand"
               BorderColor="Transparent"
         BackgroundColor="Transparent"
         Margin="20,0,20,0">
            <Grid
    BackgroundColor="Transparent"
    HorizontalOptions="FillAndExpand"
    VerticalOptions="Center"
    Padding="0,0,0,10"
    >
                <Grid.Resources>
                    <ResourceDictionary>
                        <Style TargetType="Button" x:Key="ButtonStyle">
                            <Setter Property="FontSize" Value="Medium" />
                            <Setter Property="TextColor" Value="{x:Static constants:AppThemeConstants.BlueColor}" />
                            <Setter Property="BorderColor" Value="Transparent" />
                            <Setter Property="HorizontalOptions" Value="End" />
                            <Setter Property="VerticalOptions" Value="CenterAndExpand" />
                            <Setter Property="BackgroundColor" Value="Transparent" />
                        </Style>
                    </ResourceDictionary>
                </Grid.Resources>

                <Frame Padding="20" Grid.Row="0" Margin="0,25,0,0" CornerRadius="6" BorderColor="Transparent"
         HasShadow="False"
         IsClippedToBounds="True"
         HorizontalOptions="FillAndExpand">
                    <StackLayout Spacing="10">
                        <Label
                Text="Is your experience 10/10?" Margin="0,5" FontAttributes="Bold" HorizontalOptions="Center" FontSize="18" HorizontalTextAlignment="Center" TextColor="Black" />
                        <!--<Label Text="Our goal is to give you a 10/10 experience."
                       HorizontalOptions="CenterAndExpand" Margin="10,10"
                       HorizontalTextAlignment="Center"
                       Style="{StaticResource LabelStyle}" />-->
                        <Button Text="No, send feedback" x:Name="Feedback"  Clicked="Feedback_Clicked" Style="{StaticResource buttonStyle}" HorizontalOptions="FillAndExpand" Margin="0,0" HeightRequest="60" CornerRadius="0" />
                        <Border
                            Padding="0"
                            Margin="0"
                            HorizontalOptions="FillAndExpand" 
                            Style="{StaticResource GradientBorderStyleBlue}"
                            HeightRequest="60">
                            <Border.StrokeShape>
                                <Rectangle/>
                            </Border.StrokeShape>

                            <t:DrMuscleButton
                x:Name="SolidButton"
                VerticalOptions="EndAndExpand"
                    HeightRequest="60"
                HorizontalOptions="FillAndExpand"
                    Text="Yes, 10/10 experience"
                IsVisible="true"
                Style="{StaticResource highEmphasisButtonStyle}"
                    BackgroundColor="Transparent"
                    BorderColor="Transparent"
                        Clicked="SolidButton_Clicked"
                    TextColor="White"/>
                        </Border>

                    </StackLayout>
                </Frame>
                <Image Grid.Row="0" Source="hearticon.png" WidthRequest="50" HeightRequest="50" HorizontalOptions="Center" VerticalOptions="Start" />

            </Grid>
        </Frame>
    </toolkit:PopupPage.Content>
</toolkit:PopupPage>
