using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Message;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;
using System.Text.RegularExpressions;

namespace DrMaxMuscle.Views;

public partial class MealGeneralPopup : PopupPage
{
    public string Title { get; set; }
    public bool isDisappeared = false;
    public Enums.GeneralPopupEnum GeneralEnum { get; set; }
    public MealGeneralPopup()
    {
        InitializeComponent();
        isDisappeared = false;
    }
    public void SetPopupTitle(string title, Enums.GeneralPopupEnum popupEnum, string subTitle = "", string placeholder = "")
    {
        LblSubTitle.IsVisible = true;
        if (EditorMealInfo != null)
        {
            EditorMealInfo.Placeholder = placeholder;
            EditorMealInfo.HeightRequest = 80;
        }
        if (title == "Change meal plan")
        {
            LblTitle.IsVisible = false;
            BtnSave.Text = "Change";
        }
        else if (title == "Edit meal")
        {
            if (EditorMealInfo != null)
            {
                EditorMealInfo.HeightRequest = 100;
                EditorMealInfo.Placeholder = "";
                EditorMealInfo.Text = placeholder;
            }
            LblSubTitle.IsVisible = false;
            LblTitle.IsVisible = true;
            LblTitle.Text = title;
            SetHeight(placeholder);

            BtnSave.Text = "Change";
        }
        else
        {
            LblTitle.IsVisible = true;
            LblTitle.Text = title;
            BtnSave.Text = "Save";
        }
        LblSubTitle.Text = subTitle;
        GeneralEnum = popupEnum;

    }

    private void SetHeight(string placeholder)
    {
        int numberOfLines = 0;
        try
        {
            numberOfLines = placeholder.Split('\n').Length;
            if (numberOfLines > 2 && EditorMealInfo != null)
                EditorMealInfo.HeightRequest = numberOfLines * (Device.RuntimePlatform == Device.Android ? 27 : 25);
        }
        catch (Exception ex)
        {

        }

    }
    async void BtnSave_Clicked(System.Object sender, System.EventArgs e)
    {
        if (string.IsNullOrEmpty(EditorMealInfo?.Text) || string.IsNullOrWhiteSpace(EditorMealInfo?.Text))
            return;
        isDisappeared = true;
        await MauiProgram.SafeDismissTopPopup();
        //if (PopupNavigation.Instance.PopupStack.Count() > 0)
        //    PopupNavigation.Instance.PopAsync();
        MessagingCenter.Send<GeneralMessage>(new GeneralMessage() { GeneralText = EditorMealInfo?.Text, PopupEnum = GeneralEnum }, "GeneralMessage");

    }

    async void BtnCancel_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            isDisappeared = true;
            await MauiProgram.SafeDismissTopPopup();
            //if (PopupNavigation.Instance.PopupStack.Count() > 0)
            //    PopupNavigation.Instance.PopAsync();
            MessagingCenter.Send<GeneralMessage>(new GeneralMessage() { GeneralText = EditorMealInfo.Text, PopupEnum = GeneralEnum, IsCanceled = true }, "GeneralMessage");

        }
        catch (Exception ex)
        {

        }
    }
    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        try
        {
            if(!isDisappeared)
            MessagingCenter.Send<GeneralMessage>(new GeneralMessage() { GeneralText = EditorMealInfo.Text, PopupEnum = GeneralEnum, IsCanceled = true }, "GeneralMessage");

        }
        catch (Exception ex)
        {

        }
    }
}