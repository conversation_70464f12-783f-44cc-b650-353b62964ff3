﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PopupPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:constants="clr-namespace:DrMaxMuscle.Constants"
    xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
                 CloseWhenBackgroundIsClicked="False"
    xmlns:pages="clr-namespace:RGPopup.Maui.Pages;assembly=RGPopup.Maui"
    xmlns:convertors="clr-namespace:DrMaxMuscle.Convertors"
    xmlns:local="clr-namespace:DrMaxMuscle.Views"
             x:Class="DrMaxMuscle.Views.TimePickerView">
    <pages:PopupPage.Resources>
        <ResourceDictionary>
            <convertors:IntegerDigitVisibleConverter
                x:Key="intDigitConv" />
            <convertors:DecimalDigitVisibleConverter
                x:Key="decDigitConv" />
        </ResourceDictionary>
    </pages:PopupPage.Resources>
    <Border
        Padding="0"
        StrokeShape="RoundRectangle 4,4,4,4"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="CenterAndExpand"
        BackgroundColor="White"
        Margin="20,20,20,0">
        <StackLayout Padding="0,10,0,0">
            <StackLayout.Resources>
                    <ResourceDictionary>
                        <Style
                            TargetType="Button"
                            x:Key="ButtonStyle">
                            <Setter
                                Property="FontSize"
                                Value="Medium" />
                            <Setter
                                Property="TextColor"
                                Value="{x:Static constants:AppThemeConstants.BlueColor}" />
                            <Setter
                                Property="BorderColor"
                                Value="Transparent" />
                            <Setter
                                Property="HorizontalOptions"
                                Value="End" />
                            <Setter
                                Property="VerticalOptions"
                                Value="CenterAndExpand" />
                            <Setter
                                Property="BackgroundColor"
                                Value="Transparent" />
                        </Style>
                    </ResourceDictionary>
                </StackLayout.Resources>
             <Label
                    x:Name="LblTitle"
                    Margin="15,0,10,0"
                    Text="How much time?"
                    FontAttributes="Bold"
                    IsVisible="False"
                    FontSize="Medium"
                    TextColor="Black"
                    HorizontalOptions="FillAndExpand"
                    VerticalOptions="Start" />
            
            <StackLayout>

            <Grid
                x:Name="FeetGrid">
                <Grid.RowDefinitions>
                    
                    <RowDefinition
                        Height="*" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition
                        Width="*" />
                    <ColumnDefinition
                        Width="*" />
                </Grid.ColumnDefinitions>
                <Label
                    Grid.Row="0"
                    Grid.Column="0"
                    HorizontalOptions="End"
                    VerticalOptions="Center"
                    Margin="0,0,10,0"
                    VerticalTextAlignment="Center"
                    Text="Min" />
                <Label
                    Grid.Row="0"
                    Grid.Column="1"
                    HorizontalOptions="End"
                    Margin="0,0,10,0"
                    VerticalOptions="Center"
                    VerticalTextAlignment="Center"
                    Text="Sec" />
                <local:PickerView
                    Grid.Row="0"
                    Grid.Column="0"
                    x:Name="PickerMin"
                    WidthRequest="100"
                    IsVisible="{Binding IntegerDigitLength,
                    Converter={StaticResource intDigitConv}, ConverterParameter=9}"
                    SelectedIndex="4" />
                <local:PickerView
                    Grid.Row="0"
                    Grid.Column="1"
                    x:Name="PickerSec"
                    WidthRequest="100"
                    IsVisible="{Binding IntegerDigitLength,
                    Converter={StaticResource intDigitConv}, ConverterParameter=8}"
                    SelectedIndex="5" />
            </Grid>
                
                </StackLayout>
            <StackLayout
                    Orientation="Horizontal"
                    Margin="10,0,10,10"
                    VerticalOptions="EndAndExpand"
                    HorizontalOptions="End">
                    <Button
                        x:Name="BtnCancel"
                        Text="Cancel"
                        IsVisible="true"
                        Style="{StaticResource ButtonStyle}"
                        HorizontalOptions="End"
                        WidthRequest="100"
                        Clicked="BtnCancel_Clicked" />
                    <Button
                        x:Name="BtnConfirm"
                        Text="Save"
                        Style="{StaticResource ButtonStyle}"
                        HorizontalOptions="End"
                        WidthRequest="80"
                        Clicked="BtnDoneClicked" />
                </StackLayout>
            </StackLayout>
    </Border>
</pages:PopupPage>

