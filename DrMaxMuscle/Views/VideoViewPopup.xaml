﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:PopupPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               xmlns:toolkit="clr-namespace:RGPopup.Maui.Pages;assembly=RGPopup.Maui"
             x:Class="DrMaxMuscle.Views.VideoViewPopup">
     <Frame Padding="0" CornerRadius="4"
             HasShadow="False"
             IsClippedToBounds="True"
             HorizontalOptions="FillAndExpand"
        VerticalOptions="CenterAndExpand"
             BackgroundColor="White"
             Margin="20,10,20,10">
    <StackLayout
        Orientation="Vertical"
        BackgroundColor="White"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="Center"
        Padding="0,0,0,0"
        >
      
        <WebView x:Name="webVideo" BackgroundColor="White" HeightRequest="{OnPlatform Android='250', iOS='250'}" MinimumWidthRequest="300" HorizontalOptions="FillAndExpand" />
        </StackLayout>
          </Frame>
</toolkit:PopupPage>

