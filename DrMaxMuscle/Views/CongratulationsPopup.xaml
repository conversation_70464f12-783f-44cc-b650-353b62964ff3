﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Views.CongratulationsPopup"
               xmlns:constants="clr-namespace:DrMaxMuscle.Constants"
            xmlns:controls="clr-namespace:DrMaxMuscle.Layout"
            xmlns:ctrl="clr-namespace:DrMaxMuscle.Controls"
              xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit">
    <StackLayout
        x:Name="mainStack"
        BackgroundColor="Transparent"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="CenterAndExpand">
        <Frame Padding="0" CornerRadius="4"
         HasShadow="False"
               BorderColor="Transparent"
               x:Name="mainFrame"
         IsClippedToBounds="True"
         
         BackgroundColor="White"
         Margin="0">
            <Grid   
            BackgroundColor="Transparent"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="Center"
            Padding="0,0,0,0">

                <Frame
                    BorderColor="Transparent"
                Padding="0,20"
                Margin="0,10,0,0"
                CornerRadius="8"
                HasShadow="False"
                IsClippedToBounds="True"
                HorizontalOptions="FillAndExpand">
                    <StackLayout
                    Padding="0,20">

                        <BoxView Margin="0,0,0,0" BackgroundColor="Transparent" />
                        <Image Margin="0,0,0,0" x:Name="ImgName" WidthRequest="100" HeightRequest="100" HorizontalOptions="Center" VerticalOptions="Start" Source="truestate.png" />

                        <Label Margin="0,15,0,0" Text="Success!" x:Name="LblHeading" HorizontalOptions="Center" FontSize="26" FontAttributes="Bold" TextColor="Black" HorizontalTextAlignment="Center" />
                        <Label Text="Account created" x:Name="LblSubHead" Margin="15,0,15,10" HorizontalOptions="Center"  HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>
                        <!--How was today's workout?-->
                        <Label Text="" IsVisible="false" Margin="15,0,15,10" HorizontalOptions="Center"  HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>

                        <Grid Margin="0,17,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition
                                Width="10" />
                                <ColumnDefinition
                                Width="*" />
                                <ColumnDefinition
                                Width="*" />
                                <ColumnDefinition
                                Width="10" />
                            </Grid.ColumnDefinitions>
                            <StackLayout
                            Grid.Column="1"
                            HorizontalOptions="FillAndExpand">
                                <Image
                                Source="records.png"
                                Aspect="AspectFit"
                                HeightRequest="32"
                                HorizontalOptions="CenterAndExpand" />
                                <Label
                                x:Name="newRecordsLbl"
                                Text="0"
                                IsVisible="true"
                                HorizontalOptions="Center"
                                FontAttributes="Bold"
                                FontSize="17"
                                Style="{StaticResource LabelStyle}"
                                TextColor="Black" />
                                <Label
                                x:Name="newRecordsLbl1"
                                Text="New records"
                                IsVisible="true"
                                HorizontalOptions="Center"
                                HorizontalTextAlignment="Center"
                                FontSize="17"
                                TextColor="#AA000000"/>

                            </StackLayout>
                            <StackLayout
                            Grid.Column="2"
                            HorizontalOptions="FillAndExpand">
                                <Image
                                x:Name="IconResultImage"
                                Source="chain.png"
                                Aspect="AspectFit"
                                HeightRequest="32"
                                HorizontalOptions="CenterAndExpand" />
                                <Label
                                x:Name="weekStreaklbl"
                                Text="0"
                                IsVisible="true"
                                HorizontalOptions="Center"
                                FontAttributes="Bold"
FontSize="17"
                                Style="{StaticResource LabelStyle}"
                                TextColor="Black" />
                                <Label
                                x:Name="weekStreaklbl1"
                                Text="Weeks streak"
                                IsVisible="true"
                                HorizontalOptions="Center"
                                HorizontalTextAlignment="Center"
                                FontSize="17"
                                TextColor="#AA000000"/>
                            </StackLayout>
                        </Grid>
                        <Grid Margin="0,17,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition
                        Width="10" />
                                <ColumnDefinition
                        Width="*" />
                                <ColumnDefinition
                        Width="*" />
                                <ColumnDefinition
                        Width="10" />
                            </Grid.ColumnDefinitions>

                            <StackLayout
                                Grid.Column="1"
                                HorizontalOptions="FillAndExpand">
                                <Image
                                Source="exercise.png"
                                Aspect="AspectFit"
                                HeightRequest="32"
                                HorizontalOptions="CenterAndExpand" />
                                <Label
                                x:Name="exerciseLbl"
                                Text="0"
                                IsVisible="true"
                                HorizontalOptions="Center"
                                Style="{StaticResource LabelStyle}"
                                FontAttributes="Bold"
FontSize="17"
                                TextColor="Black" />
                                <Label
                                x:Name="exerciseLbl1"
                                Text="Exercises"
                                IsVisible="true"
                                HorizontalOptions="Center"
                                FontSize="17"
                                TextColor="#AA000000">
                                </Label>
                            </StackLayout>
                            <StackLayout
                            Grid.Column="2"
                            HorizontalOptions="FillAndExpand">
                                <Image
                                Source="fire.png"
                                Aspect="AspectFit"
                                HeightRequest="32"
                                HorizontalOptions="CenterAndExpand" />
                                <Label
                                Text="0"
                                x:Name="caloriesLbl"
                                IsVisible="true"
                                HorizontalOptions="Center"
                                Style="{StaticResource LabelStyle}"
                                FontAttributes="Bold"
FontSize="17"
                                TextColor="Black" />
                                <Label
                                x:Name="caloriesLbl1"
                                Text="Calories"
                                HorizontalTextAlignment="Center"
                                IsVisible="true"
                                HorizontalOptions="Center"
                                FontSize="17"
                                TextColor="#AA000000">
                                </Label>

                            </StackLayout>

                        </Grid>
                        <!--<Button Text="Share"
                        Margin="25,10,25,0"
                        Clicked="DrMuscleButtonShareTrial_Clicked"
                        HeightRequest="60">
                            --><!--<Button.ImageSource>
                            </Button.ImageSource>--><!--
                        </Button>-->
                        <ctrl:DrMuscleImageButton
                        Text="Share"
                        Margin="25,10"
                        Source="ic_share_exercise"
                        Clicked="DrMuscleButtonShareTrial_Clicked"
                        HeightRequest="60"/>
                        <Frame
            Padding="0"
            x:Name="OkAction"
            IsClippedToBounds="true"
            CornerRadius="0"
                        VerticalOptions="EndAndExpand"
                        HorizontalOptions="FillAndExpand"
                        Margin="25,-4,25,0"
                        Style="{StaticResource GradientBorderStyleBlue}"
                        HeightRequest="66">

                            <Label
                        x:Name="OkButton"
                        Text="Continue"
                        FontAttributes="Bold"
                        BackgroundColor="Transparent"
                        VerticalTextAlignment="Center"
                        TextColor="White"
                        HorizontalTextAlignment="Center"
                        HorizontalOptions="FillAndExpand"
                        Margin="0,0"
                        HeightRequest="55"
                         />

                        </Frame>

                        <Label Text="" x:Name="LblTipText" Margin="15,0,15,0"
HeightRequest="55"
HorizontalOptions="Center"
                          VerticalOptions="Center" VerticalTextAlignment="Center" HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>

                    </StackLayout>

                </Frame>
                <!--<Image Grid.Row="0" Margin="0,25" Source="SharpCurve.png" HorizontalOptions="FillAndExpand" HeightRequest="120"  VerticalOptions="Start" Aspect="Fill" />-->
                <Image Grid.Row="0" Margin="0,50,0,20"  WidthRequest="50" HeightRequest="50" HorizontalOptions="Center" VerticalOptions="Start" />
                <!--<forms:ParticleView x:Name="MyParticleCanvas"
                              FallingParticlesPerSecond="25.0"
                              IsActive="False"
                              IsRunning="False"
                              HasFallingParticles="True"
                              VerticalOptions="FillAndExpand"
                              HorizontalOptions="FillAndExpand"
                              InputTransparent="True"/>-->
            </Grid>
        </Frame>
        
    </StackLayout>
</toolkit:Popup>