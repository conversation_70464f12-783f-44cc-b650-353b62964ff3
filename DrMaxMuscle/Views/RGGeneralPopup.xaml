﻿<?xml version="1.0" encoding="utf-8" ?>
<popup:Popup
    x:Class="DrMaxMuscle.Views.RGGeneralPopup"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:constants="clr-namespace:DrMaxMuscle.Constants"
    xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
    xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:popup="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:t="clr-namespace:DrMaxMuscle.Layout" Color="Transparent"
    CanBeDismissedByTappingOutsideOfPopup="false" >
    <Frame
        Margin="0,0,00,0"
        Padding="0"
        BackgroundColor="Transparent"
        BorderColor="Transparent"
        CornerRadius="4"
        HasShadow="False"
        HorizontalOptions="FillAndExpand"
        IsClippedToBounds="True"
        VerticalOptions="CenterAndExpand">
        <Grid
            Padding="0,0,0,15"
            BackgroundColor="Transparent"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="Center">

            <Frame
                Margin="0,10,0,0"
                Padding="0,20,0,20"
                BorderColor="Transparent"
                CornerRadius="6"
                HasShadow="False"
                HorizontalOptions="FillAndExpand"
                IsClippedToBounds="True">
                <StackLayout Padding="0,20,0,24">

                    <BoxView Margin="0,0,0,0" BackgroundColor="Transparent" />
                    <ffimageloading:CachedImage
                        x:Name="ImgName"
                        Margin="0,0,0,0"
                        ErrorPlaceholder="backgroundblack.png"
                        HeightRequest="100"
                        HorizontalOptions="Center"
                        Source="truestate.png"
                        VerticalOptions="Start"
                        WidthRequest="100" />

                    <Label
                        x:Name="LblHeading"
                        Margin="0,15,0,0"
                        FontAttributes="Bold"
                        FontSize="26"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="Success!"
                        TextColor="Black" />
                    <Label
                        x:Name="LblSubHead"
                        Margin="15,0,15,30"
                        FontAttributes="Bold"
                        FontSize="17"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="Account created"
                        TextColor="#505050" />
                    <t:DrMuscleButton
                        x:Name="BtnLearnMore"
                        BackgroundColor="Transparent"
                        Clicked="DrMuscleButton_Clicked"
                        HeightRequest="66"
                        Text="Learn more"
                        TextColor="#007aff" />
                    <Frame
                        x:Name="OkAction"
                        Margin="25,0"
                        Padding="0"
                        BorderColor="Transparent"
                        CornerRadius="0"
                        HeightRequest="66"
                        HorizontalOptions="FillAndExpand"
                        IsClippedToBounds="true"
                        Style="{StaticResource GradientFrameStyleBlue}"
                        VerticalOptions="EndAndExpand">

                        <Label
                            x:Name="OkButton"
                            Margin="0,0"
                            BackgroundColor="Transparent"
                            FontAttributes="Bold"
                            HeightRequest="66"
                            HorizontalOptions="FillAndExpand"
                            HorizontalTextAlignment="Center"
                            Text="OK cool"
                            TextColor="White"
                            VerticalTextAlignment="Center" />

                    </Frame>
                    <t:DrMuscleButton
                        x:Name="BtnCancel"
                        BackgroundColor="Transparent"
                        Clicked="DrMuscleButtonCancel_Clicked"
                        HeightRequest="66"
                        IsVisible="false"
                        Text="Cancel"
                        TextColor="#007aff" />

                    <Label
                        x:Name="LblTipText"
                        Margin="15,0,15,0"
                        FontAttributes="Bold"
                        FontSize="17"
                        HeightRequest="66"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text=""
                        TextColor="#505050"
                        VerticalOptions="Center"
                        VerticalTextAlignment="Center" />
                    <Label
                        x:Name="LblCountDown"
                        Margin="15,0,15,0"
                        FontAttributes="Bold"
                        FontSize="15"
                        HeightRequest="30"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        IsVisible="False"
                        Text=""
                        TextColor="#505050"
                        VerticalOptions="Center"
                        VerticalTextAlignment="Center" />
                </StackLayout>

            </Frame>
            <!--<Image Grid.Row="0" Margin="0,25" Source="SharpCurve.png" HorizontalOptions="FillAndExpand" HeightRequest="120"  VerticalOptions="Start" Aspect="Fill" />-->
            <ffimageloading:CachedImage
                Grid.Row="0"
                Margin="0,50,0,20"
                ErrorPlaceholder="backgroundblack.png"
                HeightRequest="50"
                HorizontalOptions="Center"
                VerticalOptions="Start"
                WidthRequest="50" />
            <!--<forms:ParticleView x:Name="MyParticleCanvas"
                  FallingParticlesPerSecond="25.0"
                  IsActive="False"
                  IsRunning="False"
                  HasFallingParticles="True"
                  VerticalOptions="FillAndExpand"
                  HorizontalOptions="FillAndExpand"
                  InputTransparent="True"/>-->
        </Grid>
    </Frame>

</popup:Popup>

