﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup
    x:Class="DrMaxMuscle.Views.NormalExercisePopup"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:constants="clr-namespace:DrMaxMuscle.Constants"
    xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
    xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:local="clr-namespace:DrMaxMuscle.Views"
    xmlns:t="clr-namespace:DrMaxMuscle.Layout"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    Color="Transparent">

    <Frame
        Margin="0,0"
        Padding="20,40"
        BorderColor="Transparent"
        BackgroundColor="Transparent"
        CornerRadius="10"
        HasShadow="False"
        HorizontalOptions="FillAndExpand"
        IsClippedToBounds="True"
        VerticalOptions="FillAndExpand">

        <Grid
            Padding="0,10"
            BackgroundColor="White"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="CenterAndExpand"
            RowSpacing="10">

            <Grid.Resources>
                <ResourceDictionary>
                    <Style x:Key="ButtonStyle" TargetType="Button">
                        <Setter Property="FontSize" Value="Medium" />
                        <Setter Property="TextColor" Value="{x:Static constants:AppThemeConstants.BlueColor}" />
                        <Setter Property="BorderColor" Value="Transparent" />
                        <Setter Property="HorizontalOptions" Value="End" />
                        <Setter Property="VerticalOptions" Value="CenterAndExpand" />
                        <Setter Property="BackgroundColor" Value="Transparent" />
                    </Style>
                </ResourceDictionary>
            </Grid.Resources>

            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" /> <!-- Video -->
                <RowDefinition Height="Auto" /> <!-- Title -->
                <RowDefinition Height="Auto" /> <!-- Description -->
                <RowDefinition Height="Auto" /> <!-- Entry + Picker -->
                <RowDefinition Height="Auto" /> <!-- Buttons -->
            </Grid.RowDefinitions>

            <!-- Video -->
            <ffimageloading:CachedImage
                x:Name="videoPlayer"
                Grid.Row="0"
                Aspect="AspectFit"
                BackgroundColor="White"
                ErrorPlaceholder="backgroundblack.png"
                FadeAnimationForCachedImages="False"
                HeightRequest="200"
                HorizontalOptions="FillAndExpand"
                IsVisible="{Binding IsVideoUrlAvailable}"
                Source="{Binding VideoUrl}">
                <ffimageloading:CachedImage.GestureRecognizers>
                    <TapGestureRecognizer />
                </ffimageloading:CachedImage.GestureRecognizers>
            </ffimageloading:CachedImage>

            <!-- Title -->
            <Label
                x:Name="LblTitle"
                Grid.Row="1"
                Margin="15,15,10,0"
                FontAttributes="Bold"
                FontSize="21"
                HorizontalOptions="CenterAndExpand"
                TextColor="{OnPlatform iOS=#000000, Android=#000000}" />

            <!-- Description -->
            <Label
                x:Name="LblDesc"
                Grid.Row="2"
                Margin="15,0,10,0"
                FontSize="18"
                HorizontalOptions="CenterAndExpand"
                TextColor="{OnPlatform iOS=#000000, Android=#000000}" />

            <!-- Entry + Picker -->
            <Grid Grid.Row="3"
                  Margin="7,15,7,0"
                  BackgroundColor="White"
                  HorizontalOptions="FillAndExpand">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="1" />
                </Grid.RowDefinitions>

                <controls:DrMuscleEntry
                    x:Name="EntryWeight"
                    Grid.Row="0"
                    Margin="15,0,15,0"
                    BackgroundColor="Transparent"
                    Completed="EntryWeight_Completed"
                    HorizontalOptions="FillAndExpand"
                    Keyboard="Numeric"
                    MaxLength="5"
                    Placeholder="Enter weight"
                    Text=""
                    TextColor="Black" />

                <local:PickerView
                    x:Name="PickerCM"
                    Grid.Row="1"
                    Margin="15,-40,15,-40"
                    HeightRequest="250"
                    HorizontalOptions="FillAndExpand"
                    IsVisible="false"
                    SelectedIndex="0" />

                <Border
                    Grid.Row="2"
                    Margin="5,0"
                    Stroke="Transparent"
                    BackgroundColor="#D8D8D8"
                    HeightRequest="0.5"
                    HorizontalOptions="FillAndExpand" />
            </Grid>

            <!-- Buttons -->
            <Grid Grid.Row="4"
                  Margin="10,5"
                  ColumnSpacing="10"
                  HorizontalOptions="End">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <Button
                    x:Name="BtnCancel"
                    Grid.Column="0"
                    BackgroundColor="White"
                    Clicked="BtnCancelClicked"
                    FontAttributes="Bold"
                    Style="{StaticResource ButtonStyle}"
                    Text="Cancel"
                    TextColor="{OnPlatform iOS=#195377, Android=#195377}"
                    WidthRequest="100" />

                <Button
                    x:Name="BtnConfirm"
                    Grid.Column="1"
                    BackgroundColor="White"
                    Clicked="BtnDoneClicked"
                    FontAttributes="Bold"
                    Style="{StaticResource ButtonStyle}"
                    Text="Continue"
                    TextColor="{OnPlatform iOS=#195377, Android=#195377}"
                    Padding="0,0,10,0"
                    WidthRequest="100" />
            </Grid>
        </Grid>
    </Frame>
</toolkit:Popup>
