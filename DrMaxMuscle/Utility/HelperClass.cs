﻿using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Screens.Exercises;
using DrMaxMuscle.Screens.Workouts;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using Microsoft.Maui.Storage;
using Plugin.StoreReview;
#if IOS
using RGPopup.Maui.IOS;
#endif
using RGPopup.Maui.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Utility
{
    public class HelperClass
    {
        #region private fields
        private static bool _isRatePopupOpened = false;
        #endregion

        /// <summary>
        /// Share the app link.
        /// </summary>
        /// <returns></returns>
        public async static Task ShareApp(string compaign = "sidebar", string firebaseEventName = "App_Share", string additionInfomation = "")
        {
            try
            {
                var firstname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;

                if (Device.RuntimePlatform.Equals(Device.Android))
                {
                    if (!string.IsNullOrEmpty(additionInfomation))
                    {
                        await Share.RequestAsync(new ShareTextRequest
                        {
                            Uri = $"{additionInfomation}\n\nLearn more: https://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign={compaign}&utm_content={firstname?.Replace(" ", "_")}",
                            Subject = $"{firstname} is inviting you to try Dr.Muscle, the app that gets you in shape faster using artificial intelligence"
                        });
                    }
                    else
                    {
                        await Share.RequestAsync(new ShareTextRequest
                        {
                            Uri = $"https://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign={compaign}&utm_content={firstname?.Replace(" ", "_")}",
                            Subject = $"{firstname} is inviting you to try Dr.Muscle, the app that gets you in shape faster using artificial intelligence"
                        });
                    }
                }
                else
                {
                    if (!string.IsNullOrEmpty(additionInfomation))
                        await Share.RequestAsync($"{firstname} is inviting you to try Dr.Muscle, the app that gets you in shape faster using artificial intelligence\n\n{additionInfomation}\n\nLearn more: https://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign={compaign}&utm_content={firstname?.Replace(" ", "_")}");
                    else
                        await Share.RequestAsync($"{firstname} is inviting you to try Dr.Muscle, the app that gets you in shape faster using artificial intelligence \nhttps://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign={compaign}&utm_content={firstname?.Replace(" ", "_")}");
                }
                var _firebase = (IFirebase)MauiProgram.ServiceProvider.GetService(typeof(IFirebase));
                _firebase.LogEvent(firebaseEventName, $"Compaign {compaign}");
            }
            catch (Exception ex)
            {

            }
        }

        /// <summary>
        /// To share image with some caption.
        /// </summary>
        /// <param name="imageStream">Image as stream</param>
        /// <param name="compaign">Caption to send</param>
        public static void ShareImage(Stream? imageStream, string compaign = "exercise", string firebaseEventName = "App_Share", string additionMessage = "")
        {
            try
            {
                var firstname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
                var appUrl = $"https://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign={compaign}&utm_content={firstname?.Replace(" ", "_")}";

                string message = string.IsNullOrEmpty(additionMessage) ? appUrl : $"{appUrl}\n\n{additionMessage}";
                DependencyService.Get<IShareService>().Share("Dr.Muscle Stats", message, imageStream);
                DependencyService.Get<IFirebase>().LogEvent(firebaseEventName, $"Image and Compaign {compaign}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ShareImage: {ex.Message}");
            }
        }

        /// <summary>
        /// Used to send mail to the support team (<EMAIL>) from user. It is async method so need to await.
        /// </summary>
        /// <param name="subject">Define the predefined subject with the mail will be sent</param>
        /// <param name="body">It is optional part that represents some predefine body text</param>
        /// <returns></returns>
        public async static Task SendMail(string subject, string body = null, string firebaseEventName = "App_Email")
        {
            try
            {
                if (await Launcher.CanOpenAsync(new Uri($"mailto:<EMAIL>?subject={subject}&body={body}")))
                {
                    await Launcher.OpenAsync(new Uri($"mailto:<EMAIL>?subject={subject}&body={body}"));
                    DependencyService.Get<IFirebase>().LogEvent(firebaseEventName, $"Subject : {subject}");
                }
                else
                {
                    //string mailtoUri = $"mailto:<EMAIL>?subject={(!string.IsNullOrEmpty(subject)? Uri.EscapeDataString(subject): subject)}&body={(!string.IsNullOrEmpty(body) ? Uri.EscapeDataString(body) : body)}";
                    //string webUrl = $"https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su={(!string.IsNullOrEmpty(subject) ? Uri.EscapeDataString(subject) : subject)}&body={(!string.IsNullOrEmpty(body) ? Uri.EscapeDataString(body) : body)}";

                    //await Browser.OpenAsync(webUrl, new BrowserLaunchOptions
                    //{
                    //    LaunchMode = BrowserLaunchMode.SystemPreferred
                    //});


                    #if __ANDROID__
                    
                        MainActivity.OpenGmailApp(subject, body);

                    #elif __IOS__
                        string mailtoUri = $"mailto:<EMAIL>?subject={(!string.IsNullOrEmpty(subject)? Uri.EscapeDataString(subject): subject)}&body={(!string.IsNullOrEmpty(body) ? Uri.EscapeDataString(body) : body)}";
                        string webUrl = $"https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su={(!string.IsNullOrEmpty(subject) ? Uri.EscapeDataString(subject) : subject)}&body={(!string.IsNullOrEmpty(body) ? Uri.EscapeDataString(body) : body)}";

                        await Browser.OpenAsync(webUrl, new BrowserLaunchOptions
                        {
                            LaunchMode = BrowserLaunchMode.SystemPreferred
                        });
                    #endif
                }
            }
            catch (Exception ex)
            {

            }
            //var _firebase = (IFirebase)MauiProgram.ServiceProvider.GetService(typeof(IFirebase));
            //_firebase.LogEvent(firebaseEventName, $"Subject : {subject}");
        }

        /// <summary>
        /// Opens the store to provide rating and review.
        /// </summary>
        /// <returns></returns>
        public async static Task RateApp(string firebaseEventName = "App_Rate")
        {
            if (_isRatePopupOpened)
                return;

            try
            {
                DependencyService.Get<IFirebase>().LogEvent(firebaseEventName, "Rate_App");
                

                _isRatePopupOpened = true;
#if RELEASE
                if (DeviceInfo.Platform.Equals(DevicePlatform.Android))
                    await CrossStoreReview.Current.RequestReview(false);
                else
                DependencyService.Get<IAppSettingsHelper>().RateApp();
                    // MainThread.BeginInvokeOnMainThread(() =>
                    // {
                    //     Browser.OpenAsync("https://apps.apple.com/us/app/dr-muscle-ai-personal-trainer/id1073943857", BrowserLaunchMode.SystemPreferred);
                    // });
#elif DEBUG
                if (DeviceInfo.Platform.Equals(DevicePlatform.Android))
                    await CrossStoreReview.Current.RequestReview(false);
                else
                DependencyService.Get<IAppSettingsHelper>().RateApp();
                    // MainThread.BeginInvokeOnMainThread(() =>
                    // {
                    //     Browser.OpenAsync("https://apps.apple.com/us/app/dr-muscle-ai-personal-trainer/id1073943857", BrowserLaunchMode.SystemPreferred);
                    // });

#endif
                _isRatePopupOpened = false;
            }
            catch (Exception ex)
            {
                _isRatePopupOpened = false;
                System.Diagnostics.Debug.WriteLine($"Exception While rating app : {ex.Message}");
            }
        }
        public static string GetYouTubeVideoId(string url)
        {
            // Extracts the video ID from a YouTube URL
            Uri uri = new Uri(url);
            string host = uri.Host;
            string videoId = "";

            if (host.Contains("youtu.be"))
            {
                videoId = uri.Segments[1];
            }
            else if (host.Contains("youtube.com"))
            {
                string query = uri.Query;
                if (!string.IsNullOrEmpty(query))
                {
                    var queryParams = System.Web.HttpUtility.ParseQueryString(query);
                    videoId = queryParams["v"];
                }
            }

            return videoId;
        }
        public static String GetBandsColor(double weight, bool isKg)
        {
            try
            {
                var finals = weight;
                var availableBands = "";
                var bandsItems = new List<BandsModel>();
                // calculating total weight and the difference
                if (isKg)
                {
                    availableBands = LocalDBManager.Instance.GetDBSetting("BandsKg").Value;

                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("BandsKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("HomeBandsKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("OtherBandsKg").Value;
                    }
                }
                else
                {
                    availableBands = LocalDBManager.Instance.GetDBSetting("BandsLb").Value;
                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("BandsLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("HomeBandsLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("OtherBandsLb").Value;
                    }
                }
                var keyVal = availableBands;

                string[] items = keyVal.Split('|');
                foreach (var item in items)
                {
                    string[] pair = item.Split('_');
                    var model = new BandsModel();
                    if (pair.Length == 4)
                    {
                        model.BandColor = pair[0].FirstCharToUpper();
                        model.Key = pair[1];
                        try
                        {
                            model.Weight = double.Parse(pair[1]);
                        }
                        catch (Exception ex)
                        {
                            model.Weight = 0;
                        }
                        model.Value = Int32.Parse(pair[2]);
                        if (model.Value > 1)
                            model.Value = 1;
                        model.IsSystemPlates = pair[3] == "True" ? true : false;
                        if (model.Value != 0)
                            bandsItems.Add(model);
                    }
                }
                bandsItems.Sort(delegate (BandsModel c1, BandsModel c2) { return c2.Weight.CompareTo(c1.Weight); });

                var matchBands = bandsItems.FirstOrDefault(x => x.Weight == weight);
                if (matchBands != null)
                    return matchBands.BandColor;

            }
            catch (Exception ex)
            {

            }
            return "";
        }

        public static List<BandsModel> GetAvailableBands(bool isKg)
        {
            try
            {

                var availableBands = "";
                var bandsItems = new List<BandsModel>();
                // calculating total weight and the difference
                if (isKg)
                {
                    availableBands = LocalDBManager.Instance.GetDBSetting("BandsKg").Value;

                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("BandsKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("HomeBandsKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("OtherBandsKg").Value;
                    }
                }
                else
                {
                    availableBands = LocalDBManager.Instance.GetDBSetting("BandsLb").Value;
                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("BandsLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("HomeBandsLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("OtherBandsLb").Value;
                    }
                }
                var keyVal = availableBands;

                string[] items = keyVal.Split('|');
                foreach (var item in items)
                {
                    string[] pair = item.Split('_');
                    var model = new BandsModel();
                    model.isAvailable = true;
                    if (pair.Length == 4)
                    {
                        model.BandColor = pair[0].FirstCharToUpper();
                        model.Key = pair[1];
                        try
                        {
                            model.Weight = double.Parse(pair[1]);
                        }
                        catch (Exception ex)
                        {
                            model.Weight = 0;
                        }
                        model.Value = Int32.Parse(pair[2]);
                        if (model.Value > 1)
                            model.Value = 1;
                        model.IsSystemPlates = pair[3] == "True" ? true : false;
                        if (model.Value != 0)
                            bandsItems.Add(model);
                    }
                }
                bandsItems.Sort(delegate (BandsModel c1, BandsModel c2) { return c1.Weight.CompareTo(c2.Weight); });
                return bandsItems;



            }
            catch (Exception ex)
            {

            }
            return new List<BandsModel>();
        }

        public static async Task<bool> ShowCompleteDialg()
        {
            return await Microsoft.Maui.ApplicationModel.MainThread.InvokeOnMainThreadAsync(async () => {
                TaskCompletionSource<bool> _popupResult = new TaskCompletionSource<bool>();
                try
                {
                    CompletePopup completePopup = new CompletePopup();

                    completePopup.OkButtonPress += (sender, args) =>
                    {
                        if (!_popupResult.Task.IsCompleted)
                        {
                            _popupResult.SetResult(true);
                        }
                    };

                    await Application.Current.MainPage.ShowPopupAsync(completePopup);
                }
                catch(ObjectDisposedException ex)
                {
                }
                catch (InvalidOperationException ex)
                {
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error occurred during ShowCompleteDialg : {ex.Message}");
                }
                return await _popupResult.Task;
            });
        }

        public static async Task<CustomPopup> DisplayCustomPopup(string title,string message,string yes,string no,bool isVertical = false)
        {
            return await Microsoft.Maui.ApplicationModel.MainThread.InvokeOnMainThreadAsync(async () => {
                CustomPopup exitPopUp = new CustomPopup(title, message, yes, no, isVertical);
                try
                {
                    await Application.Current.MainPage.ShowPopupAsync(exitPopUp);
                }
                catch (ObjectDisposedException ex)
                {
                }
                catch (InvalidOperationException ex)
                {
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error occurred during DisplayCustomPopup : {ex.Message}");
                }

                return exitPopUp;
            });
        }

        
        public static async Task<PopupAction> DisplayCustomPopupForResult(string title, string message, string yes, string no, bool isVertical = false)
        {
            return await Microsoft.Maui.ApplicationModel.MainThread.InvokeOnMainThreadAsync(async () => {
                TaskCompletionSource<PopupAction> _popupResult = new TaskCompletionSource<PopupAction>();
                CustomPopup exitPopUp = new CustomPopup(title, message, yes, no, isVertical);
                try
                {
                    await Application.Current.MainPage.ShowPopupAsync(exitPopUp);
                    // Add null check and ensure single completion
                    if (exitPopUp.Handler?.PlatformView != null)
                    {
                        // Add null check and ensure single completion
                        exitPopUp.ActionSelected += (sender, action) =>
                        {
                            if (!_popupResult.Task.IsCompleted)
                            {
                                _popupResult.SetResult(action);
                            }
                        };
                    }
                }
                catch (ObjectDisposedException ex)
                {
                }
                catch (InvalidOperationException ex)
                {
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error occurred during DisplayCustomPopup : {ex.Message}");
                }
                return await _popupResult.Task;
            });
        }


        public static async Task PopToPage<T>(INavigation navigation)
        {
            try
            {
                
                var pages = navigation.NavigationStack.ToList();

            //Then we invert it because it's from first to last and we need in the inverse order
            pages.Reverse();
            if (pages.Count > 0 && ( pages[0] is ChooseWorkoutExerciseOrder || pages[0] is ChooseYourCustomExercisePage || pages[0] is KenkoChooseYourWorkoutExercisePage))
            {
                App.ShowTopButtonWorkout = false;
            }
            else
            {
                App.ShowTopButtonWorkout = true;
            }
            //Then we discard the current page
            if (pages.Count > 0)
                pages.RemoveAt(0);

                
                var toRemove = new List<Page>();
                Page typePage = null;

                foreach (var page in pages)
                {
                    if (page.GetType() == typeof(T))
                    {
                        typePage = page;
                        break;
                    }

                    toRemove.Add(page);
                }

               
                if (typePage == null)
                {
                    Console.WriteLine($"Page of type {typeof(T).Name} not found in navigation stack.");
                    return;
                }

               
                foreach (var rvPage in toRemove)
                {
                     if (navigation != null && navigation.NavigationStack != null && navigation.NavigationStack.Contains(rvPage))
                    {
                        navigation.RemovePage(rvPage);
                    }
                }

             
                if (typePage is KenkoChooseYourWorkoutExercisePage kenkoPage)
                {
                    kenkoPage.OnBeforeShow();
                }
                else if (typePage is AllExercisePage allExercisePage)
                {
                    allExercisePage.OnBeforeShow();
                }
                else if (typePage is ChooseYourCustomWorkoutPage customWorkoutPage)
                {
                    customWorkoutPage.workouts = new List<WorkoutTemplateModel>();
                    customWorkoutPage.workoutGroups = new List<WorkoutTemplateGroupModel>();
                }

                if (navigation.NavigationStack.Any())
                    await navigation.PopAsync();
            }
            catch (ArgumentOutOfRangeException ex)
            {
                Console.WriteLine($"Error during PopToPage: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error in PopToPage: {ex.Message}");
            }
            //
            if (navigation != null && navigation.NavigationStack != null && navigation.NavigationStack.Any())
                await navigation.PopAsync();
        }

    }
}

