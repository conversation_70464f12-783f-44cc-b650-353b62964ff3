﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle
{
    public class PlateModel : INotifyPropertyChanged
    {
        private string _key;
        private int _value;
        public int Id { get; set; }
        public string Key
        {
            get
            {
                return _key;
            }
            set
            {
                _key = value;
                OnPropertyChanged(nameof(Key));
                OnPropertyChanged(nameof(Label));
            }
        }
        public int Value
        {
            get
            {
                return _value;
            }
            set
            {
                _value = value;
                OnPropertyChanged(nameof(Value));
                OnPropertyChanged(nameof(Label));
            }
        }
        public double Weight { get; set; }
        public bool IsSystemPlates { get; set; }
        public int CalculatedPlatesCount { get; set; }
        public int NotAvailablePlatesCount { get; set; }
        public bool isAvailable { get; set; }
        public string WeightType { get; set; }
        public string Label
        {
            get
            { return Id == -1 ? Key : $"{Key} {WeightType} x {Value}"; }
        }
        public override string ToString()
        {
            return $"{Key}_{Value}";
        }
        public event PropertyChangedEventHandler PropertyChanged;

        public virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChangedEventHandler handler = PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class DumbellModel : INotifyPropertyChanged
    {
        private string _key;
        private int _value;
        public int Id { get; set; }
        public string Key
        {
            get
            {
                return _key;
            }
            set
            {
                _key = value;
                OnPropertyChanged(nameof(Key));
                OnPropertyChanged(nameof(Label));
            }
        }
        public int Value
        {
            get
            {
                return _value;
            }
            set
            {
                _value = value;
                OnPropertyChanged(nameof(Value));
                OnPropertyChanged(nameof(Label));
            }
        }
        public double Weight { get; set; }
        public bool IsSystemPlates { get; set; }
        public int CalculatedPlatesCount { get; set; }
        public int NotAvailablePlatesCount { get; set; }
        public bool isAvailable { get; set; }
        public string WeightType { get; set; }
        public string Label
        {
            get
            { return Id == -1 ? Key : $"{Key} {WeightType} x {Value}"; }
        }
        public override string ToString()
        {
            return $"{Key}_{Value}";
        }
        public event PropertyChangedEventHandler PropertyChanged;

        public virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChangedEventHandler handler = PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class PulleyModel : INotifyPropertyChanged
    {
        private string _key;
        private int _value;
        public int Id { get; set; }
        public string Key
        {
            get
            {
                return _key;
            }
            set
            {
                _key = value;
                OnPropertyChanged(nameof(Key));
                OnPropertyChanged(nameof(Label));
            }
        }
        public int Value
        {
            get
            {
                return _value;
            }
            set
            {
                _value = value;
                OnPropertyChanged(nameof(Value));
                OnPropertyChanged(nameof(Label));
            }
        }
        public double Weight { get; set; }
        public bool IsSystemPlates { get; set; }
        public int CalculatedPlatesCount { get; set; }
        public int NotAvailablePlatesCount { get; set; }
        public bool isAvailable { get; set; }
        public string WeightType { get; set; }
        public string Label
        {
            get
            { return Id == -1 ? Key : $"{Key} {WeightType} x {Value}"; }
        }
        public override string ToString()
        {
            return $"{Key}_{Value}";
        }
        public event PropertyChangedEventHandler PropertyChanged;

        public virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChangedEventHandler handler = PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class BandsModel : INotifyPropertyChanged
    {
        private string _bandColor;
        private string _key;
        private int _value;
        public int Id { get; set; }
        public string Key
        {
            get
            {
                return _key;
            }
            set
            {
                _key = value;
                OnPropertyChanged(nameof(Key));
                OnPropertyChanged(nameof(Label));
            }
        }

        public string BandColor
        {
            get
            {
                return _bandColor;
            }
            set
            {
                _bandColor = value;
                OnPropertyChanged(nameof(BandColor));
                OnPropertyChanged(nameof(Label));
            }
        }

        public int Value
        {
            get
            {
                return _value;
            }
            set
            {
                _value = value;
                OnPropertyChanged(nameof(Value));
                OnPropertyChanged(nameof(Label));
            }
        }
        public double Weight { get; set; }
        public bool IsSystemPlates { get; set; }
        public int CalculatedBandsCount { get; set; }
        public int NotAvailableBandsCount { get; set; }
        public bool isAvailable { get; set; }
        public string WeightType { get; set; }
        public string Label
        {
            get
            { return Id == -1 ? Key : $"{Key} {WeightType} ({BandColor?.ToLower()}) x {Value}"; }
        }
        public override string ToString()
        {
            return $"{Key}_{Value}";
        }
        public event PropertyChangedEventHandler PropertyChanged;

        public virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChangedEventHandler handler = PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
