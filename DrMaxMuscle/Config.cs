﻿using Plugin.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle
{
    public class Config
    {
        public static string RegisteredDeviceToken
        {
            get
            {
                return GetData("RegisteredDeviceToken", "");
            }
            set
            {
                SetData("RegisteredDeviceToken", value);
            }
        }
        public static int SecondOpenEventTrack
        {
            get
            {
                return GetData("SecondOpenEventTrack", 0);
            }

            set
            {
                if (value is int)
                {
                    SetData("SecondOpenEventTrack", value);
                }
            }
        }

        public static bool IsAnalysis
        {
            get
            {
                return GetData("IsAnalysis", false);
            }
            set
            {
                SetData("IsAnalysis", value);
            }
        }

        public static bool ShowWelcomePopUp2
        {
            get
            {
                try
                {
                    return GetData("ShowWelcomePopUp2", false);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error retrieving ShowWelcomePopUp2: {ex.Message}");
                    return false;
                }
            }
            set
            {
                try
                {
                    SetData("ShowWelcomePopUp2", value);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error saving ShowWelcomePopUp2: {ex.Message}");
                }
            }
        }

        public static bool ShowExercisePopup
        {
            get
            {
                return GetData("ShowExercisePopup", false);
            }
            set
            {
                SetData("ShowExercisePopup", value);
            }
        }

        public static bool ShowGymPopup
        {
            get
            {
                return GetData("ShowGymPopup", false);
            }
            set
            {
                SetData("ShowGymPopup", value);
            }
        }

        public static bool ShowHomeGymPopup
        {
            get
            {
                return GetData("ShowHomeGymPopup", false);
            }
            set
            {
                SetData("ShowHomeGymPopup", value);
            }
        }
        public static bool ShowBodyweightPopup
        {
            get
            {
                return GetData("ShowBodyweightPopup", false);
            }
            set
            {
                SetData("ShowBodyweightPopup", value);
            }
        }

        public static bool ShowCustomPopup
        {
            get
            {
                return GetData("ShowCustomPopup", false);
            }
            set
            {
                SetData("ShowCustomPopup", value);
            }
        }

        public static bool ShowLearnPopup
        {
            get
            {
                return GetData("ShowLearnPopup", false);
            }
            set
            {
                SetData("ShowLearnPopup", value);
            }
        }

        public static bool ShowChatPopup
        {
            get
            {
                return GetData("ShowChatPopup", false);
            }
            set
            {
                SetData("ShowChatPopup", value);
            }
        }

        public static bool ShowSettingsPopup
        {
            get
            {
                return GetData("ShowSettingsPopup", false);
            }
            set
            {
                SetData("ShowSettingsPopup", value);
            }
        }

        public static bool ShowBackoffPopup
        {
            get
            {
                return GetData("ShowBackoffPopup", false);
            }
            set
            {
                SetData("ShowBackoffPopup", value);
            }
        }

        public static bool AddExercisesPopUp
        {
            get
            {
                return GetData("AddExercisesPopUp", false);
            }
            set
            {
                SetData("AddExercisesPopUp", value);
            }
        }

        public static bool IsOneRMApplied
        {
            get
            {
                return GetData("IsOneRMApplied", false);
            }
            set
            {
                SetData("IsOneRMApplied", value);
            }
        }

        public static bool ShowWarmups
        {
            get
            {
                return GetData("ShowWarmups", false);
            }
            set
            {
                SetData("ShowWarmups", value);
            }
        }

        public static bool RepRangeOutsidePopup
        {
            get
            {
                return GetData("RepRangeOutsidePopup", false);
            }
            set
            {
                SetData("RepRangeOutsidePopup", value);
            }
        }

        public static bool ShowWelcomePopUp3
        {
            get
            {
                return GetData("ShowWelcomePopUp3", false);
            }
            set
            {
                SetData("ShowWelcomePopUp3", value);
            }
        }

        public static bool ShowWelcomePopUp4
        {
            get
            {
                return GetData("ShowWelcomePopUp4", false);
            }

            set
            {
                SetData("ShowWelcomePopUp4", value);
            }
        }

        public static bool ShowAllSetPopup
        {
            get
            {
                return GetData("ShowAllSetPopup", false);
            }

            set
            {
                SetData("ShowAllSetPopup", value);
            }
        }

        public static bool MobilityWelcomePopup
        {
            get
            {
                return GetData("MobilityWelcomePopup", false);
            }

            set
            {
                SetData("MobilityWelcomePopup", value);
            }
        }

        public static bool ShowWelcomePopUp5
        {
            get
            {
                return GetData("ShowWelcomePopUp5", false);
            }
            set
            {
                SetData("ShowWelcomePopUp5", value);
            }
        }

        public static bool ShowEasyExercisePopUp
        {
            get
            {
                return GetData("ShowEasyExercisePopUp", false);
            }

            set
            {
                SetData("ShowEasyExercisePopUp", value);
            }
        }

        public static bool ShowRIRPopUp
        {
            get
            {
                return GetData("ShowRIRPopUp", false);
            }

            set
            {
                SetData("ShowRIRPopUp", value);
            }
        }
        //
        public static bool Superset_warning_shown
        {
            get
            {
                return GetData("Superset_warning_shown", false);
            }

            set
            {
                SetData("Superset_warning_shown", value);
            }
        }

        public static int ShowPlateCalculatorPopup
        {
            get
            {
                return GetData("ShowPlateCalculatorPopup", 0);
            }

            set
            {
                SetData("ShowPlateCalculatorPopup", value);
            }
        }

        public static int ShowSupersetPopup
        {
            get
            {
                return GetData("ShowSupersetPopup", 0);
            }

            set
            {
                SetData("ShowSupersetPopup", value);
            }
        }
        //
        //public static decimal DownRecordPercentage
        //{
        //    get
        //    {
        //        return GetData("DownRecordPercentage", 0);
        //    }

        //    set
        //    {
        //        SetData("DownRecordPercentage", value);
        //    }
        //}

        //public static string DownRecordExplainer
        //{
        //    get
        //    {
        //        return GetData("DownRecordExplainer", "");
        //    }

        //    set
        //    {
        //        SetData("DownRecordExplainer", value);
        //    }
        //}
        public static bool ShowExplainRIRPopUp
        {
            get
            {
                return GetData("ShowExplainRIRPopUp", false);
            }

            set
            {
                SetData("ShowExplainRIRPopUp", value);
            }
        }

        public static bool ShowPlateTooltip
        {
            get
            {
                return GetData("ShowPlateTooltip", false);
            }

            set
            {
                SetData("ShowPlateTooltip", value);
            }
        }

        public static bool ShowChallenge
        {
            get
            {
                return GetData("ShowChallenge", false);
            }

            set
            {
                SetData("ShowChallenge", value);
            }
        }

        public static bool ShowDeload
        {
            get
            {
                return GetData("ShowDeload", false);
            }

            set
            {
                SetData("ShowDeload", value);
            }
        }

        public static bool ShowTimer
        {
            get
            {
                return GetData("ShowTimer", false);
            }

            set
            {
                SetData("ShowTimer", value);
            }
        }

        public static bool ShowEditWorkout
        {
            get
            {
                return GetData("ShowEditWorkout", false);
            }

            set
            {
                SetData("ShowEditWorkout", value);
            }
        }

        public static bool ShowDragnDrop
        {
            get
            {
                return GetData("ShowDragnDrop", false);
            }

            set
            {
                SetData("ShowDragnDrop", value);
            }
        }

        public static bool ShowBarSliderTooltip
        {
            get
            {
                return GetData("ShowBarSliderTooltip", false);
            }

            set
            {
                SetData("ShowBarSliderTooltip", value);
            }
        }

        public static bool ShowBarPlatesTooltip
        {
            get
            {
                return GetData("ShowBarPlatesTooltip", false);
            }

            set
            {
                SetData("ShowBarPlatesTooltip", value);
            }
        }


        public static bool RateAlreadyGiven
        {
            get
            {
                return GetData("RateGiven", false);
            }

            set
            {
                SetData("RateGiven", value);
            }
        }

        public static bool SurprisePopup
        {
            get
            {
                return GetData("SurprisePopup", false);
            }

            set
            {
                SetData("SurprisePopup", value);
            }
        }

        public static int ShowTipsNumber
        {
            get
            {
                return GetData("ShowTipsNumber", 0);
            }

            set
            {
                SetData("ShowTipsNumber", (int)value);
            }
        }

        public static int ShowBestNumber
        {
            get
            {
                return GetData("ShowBestNumber", 0);
            }

            set
            {
                SetData("ShowBestNumber", value);
            }
        }

        public static int ShowLeastNumber
        {
            get
            {
                return GetData("ShowLeastNumber", 0);
            }

            set
            {
                SetData("ShowLeastNumber", value);
            }
        }

        public static int ShowInspireNumber
        {
            get
            {
                return GetData("ShowInspireNumber", 0);
            }

            set
            {
                SetData("ShowInspireNumber", value);
            }
        }
        public static int ShowPart1Number
        {
            get
            {
                return GetData("ShowPart1Number", 0);
            }

            set
            {
                SetData("ShowPart1Number", value);
            }
        }
        public static int ShowPart2Number
        {
            get
            {
                return GetData("ShowPart2Number", 0);
            }

            set
            {
                SetData("ShowPart2Number", value);
            }
        }

        public static int ShowProgressNumber
        {
            get
            {
                return GetData("ShowProgressNumber", 0);
            }

            set
            {
                SetData("ShowProgressNumber", value);
            }
        }

        public static int ShowTitleNumber
        {
            get
            {
                return GetData("ShowTitleNumber", 0);
            }

            set
            {
                SetData("ShowTitleNumber", value);
            }
        }

        public static int ShowWorkoutImagesNumber
        {
            get
            {
                return GetData("ShowWorkoutImagesNumber", 0);
            }

            set
            {
                SetData("ShowWorkoutImagesNumber", value);
            }
        }

        public static bool ViewWebHistoryPopup
        {
            get
            {
                return GetData("ViewWebHistoryPopup", false);
            }

            set
            {
                SetData("ViewWebHistoryPopup", value);
            }
        }

        public static DateTime LastOpenCongratsPopupDate
        {
            get
            {
                return GetData("LastOpenCongratsPopupDate", DateTime.Now.Date);
            }

            set
            {
                SetData("LastOpenCongratsPopupDate", value);
            }
        }

        public static bool FirstChallenge
        {
            get
            {
                return GetData("FirstChallenge", false);
            }

            set
            {
                SetData("FirstChallenge", value);
            }
        }

        public static bool FirstDeload
        {
            get
            {
                return GetData("FirstDeload", false);
            }

            set
            {
                SetData("FirstDeload", value);
            }
        }

        public static bool FirstLightSession
        {
            get
            {
                return GetData("FirstLightSession", false);
            }

            set
            {
                SetData("FirstLightSession", value);
            }
        }

        public static bool IsSwappedFirstTime
        {
            get
            {
                return GetData("IsSwappedFirstTime", false);
            }

            set
            {
                SetData("IsSwappedFirstTime", value);
            }
        }

        public static bool IsFirstExerciseCreatedPopup
        {
            get
            {
                return GetData("IsFirstExerciseCreatedPopup", false);
            }
            set
            {
                SetData("IsFirstExerciseCreatedPopup", value);
            }
        }

        public static bool IsFirstWorkoutEditedPopup
        {
            get
            {
                return GetData("IsFirstWorkoutEditedPopup", false);
            }
            set
            {
                SetData("IsFirstWorkoutEditedPopup", value);
            }
        }

        public static bool IsFirstFavoritePopup
        {
            get
            {
                return GetData("IsFirstFavoritePopup", false);
            }
            set
            {
                SetData("IsFirstFavoritePopup", value);
            }
        }

        public static bool IsExistingSubscriber
        {
            get
            {
                return GetData("IsExistingSubscriber", false);
            }
            set
            {
                SetData("IsExistingSubscriber", value);
            }
        }

        public static string UserEmail
        {
            get
            {
                return GetData("UserEmail", "");
            }
            set
            {
                SetData("UserEmail", value);
            }
        }

        public static bool IsAppUsed
        {
            get
            {
                return GetData("IsAppUsed", false);
            }
            set
            {
                SetData("IsAppUsed", value);
            }
        }

        public static bool IsMealEntered
        {
            get
            {
                return GetData("IsMealEntered", true);
            }
            set
            {
                SetData("IsMealEntered", value);
            }
        }

        public static bool IsMeal1
        {
            get
            {
                return GetData("IsMeal1", false);
            }
            set
            {
                SetData("IsMeal1", value);
            }
        }

        public static bool IsMeal2
        {
            get
            {
                return GetData("IsMeal2", false);
            }
            set
            {
                SetData("IsMeal2", value);
            }
        }

        public static bool IsMeal3
        {
            get
            {
                return GetData("IsMeal3", false);
            }
            set
            {
                SetData("IsMeal3", value);
            }
        }

        public static string UserId
        {
            get
            {
                return GetData("UserId", "");
            }
            set
            {
                SetData("UserId", value);
            }
        }

        public static string CurrentWeight
        {
            get
            {
                return GetData("CurrentWeight", "");
            }
            set
            {
                SetData("CurrentWeight", value);
            }
        }

        public static string MassUnit
        {
            get
            {
                return GetData("MassUnit", "lb");
            }
            set
            {
                SetData("MassUnit", value);
            }
        }

        public static string MonthAgoWeight
        {
            get
            {
                return GetData("MonthAgoWeight", "");
            }
            set
            {
                SetData("MonthAgoWeight", value);
            }
        }

        public static string PredictedWeight
        {
            get
            {
                return GetData("PredictedWeight", "");
            }
            set
            {
                SetData("PredictedWeight", value);
            }
        }

        public static string LastEatMeal
        {
            get
            {
                return GetData("LastEatMeal", "");
            }
            set
            {
                SetData("LastEatMeal", value);
            }
        }

        public static decimal NextMealHours
        {
            get
            {
                return GetData("NextMealHours", 0);
            }
            set
            {
                SetData("NextMealHours", value);
            }
        }



        public static DateTime AccountCreationDate
        {
            get
            {
                return GetData("AccountCreationDate", DateTime.Now.Date);
            }

            set
            {
                SetData("AccountCreationDate", value);
            }
        }

        public static string LastMealPlanOrderDate
        {
            get
            {
                return GetData("LastMealPlanOrderDate", null);
            }

            set
            {
                SetData("LastMealPlanOrderDate", value);
            }
        }

        public static long LastBodyWeightUpdate
        {
            get
            {
                return GetData("LastBodyWeightUpdate", default(long));
            }

            set
            {
                SetData("LastBodyWeightUpdate", value);
            }
        }

        public static string Firstname
        {
            get
            {
                return GetData("Firstname", "");
            }

            set
            {
                SetData("Firstname", value);
            }
        }

        public static float UserHeight
        {
            get
            {
                return GetData("UserHeight", (float)0);
            }
            set
            {
                SetData("UserHeight", value);
            }
        }

        public static int UserAge
        {
            get
            {
                return GetData("UserAge", 0);
            }
            set
            {
                SetData("UserAge", value);
            }
        }

        public static string UserGender
        {
            get
            {
                return GetData("UserGender", "");
            }
            set
            {
                SetData("UserGender", value);
            }
        }   

         public static string TitleList
        {
            get
            {
                return GetData("TitleList", "");
            }
            set
            {
                SetData("TitleList", value);
            }
        }

        public static string BestTitleList
        {
            get
            {
                return GetData("BestTitleList", "");
            }
            set
            {
                SetData("BestTitleList", value);
            }
        }

        public static string LeastTitleList
        {
            get
            {
                return GetData("LeastTitleList", "");
            }
            set
            {
                SetData("LeastTitleList", value);
            }
        }

        public static string Part1List
        {
            get
            {
                return GetData("Part1List", "");
            }
            set
            {
                SetData("Part1List", value);
            }
        }

        public static string Part2List
        {
            get
            {
                return GetData("Part2List", "");
            }
            set
            {
                SetData("Part2List", value);
            }
        }

        public static string InspireList
        {
            get
            {
                return GetData("InspireList", "");
            }
            set
            {
                SetData("InspireList", value);
            }
        }
        public static string CheckmarkMessage
        {
            get
            {
                return GetData("CheckmarkMessage", "");
            }
            set
            {
                SetData("CheckmarkMessage", value);
            }
        }

        public static void SetData(string key, dynamic val)
        {
            try
            {
#if ANDROID
                CrossSettings.Current.AddOrUpdateValue(key, val);
#elif IOS
                Preferences.Set(key, val);
#endif
            }
            catch (Exception ex)
            {
                // Optional: fallback or logging
                System.Diagnostics.Debug.WriteLine($"[SetData] Failed to set {key} with value {val}: {ex.Message}");

#if IOS
                // Try safe type-based fallback
                try
                {
                    if (val is int intVal)
                        Preferences.Set(key, intVal);
                    else if (val is bool boolVal)
                        Preferences.Set(key, boolVal);
                    else if (val is double doubleVal)
                        Preferences.Set(key, doubleVal);
                    else if (val is float floatVal)
                        Preferences.Set(key, floatVal);
                    else if (val is long longVal)
                        Preferences.Set(key, longVal);
                    else if (val is string strVal)
                        Preferences.Set(key, strVal);
                    else
                        Preferences.Set(key, val.ToString()); // Last resort
                }
                catch (Exception innerEx)
                {
                    // Final catch to ensure no crash
                    System.Diagnostics.Debug.WriteLine($"[SetData-Fallback] Still failed: {innerEx.Message}");
                }
#endif
            }
        }
        public static dynamic GetData(string key, dynamic val)
        {
            try
            {
#if ANDROID
                return CrossSettings.Current.GetValueOrDefault(key, val);
#elif IOS
            return Preferences.Get(key,val);
#endif
            }
            catch (Exception ex)
            {
                if (val is int || val is double)
                    return 0;
                return val;
            }

        }
    }
}
