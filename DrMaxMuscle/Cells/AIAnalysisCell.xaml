﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             x:Class="DrMaxMuscle.Cells.AIAnalysisCell">
    <controls:CustomFrame
    x:Name="WeightProgress2"
    Margin="10,11,10,10"
    Padding="0,10,10,10"
    CornerRadius="12"
    BorderColor="Transparent"
HasShadow="true">
<controls:CustomFrame.Shadow>
    <Shadow Brush="Black"
              Offset="0,0"
              Radius="5"
              Opacity="0.2" />    
</controls:CustomFrame.Shadow>
        <controls:CustomFrame.Triggers>
            <DataTrigger
            Binding="{Binding IsNewRecordAvailable, Mode=OneWay}"
            Value="True"
            TargetType="Frame">
                <Setter
                Property="Margin"
                Value="10,11,10,10" />
            </DataTrigger>
            <DataTrigger
            Binding="{Binding IsNewRecordAvailable, Mode=OneWay}"
            Value="False"
            TargetType="Frame">
                <Setter
                Property="Margin"
                Value="10,1,10,10" />
            </DataTrigger>
        </controls:CustomFrame.Triggers>
        <StackLayout
        Padding="10,15,10,15">

            <Grid
            Margin="0,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition
                    Height="*" />
                    <RowDefinition
                    Height="Auto" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition
                    Width="40" />
                    <ColumnDefinition
                    Width="*" />
                </Grid.ColumnDefinitions>

                <Image
                x:Name="iconImage"
                Source="{Binding StrengthImage}"
                Margin="{OnPlatform Android='0,-6,0,0', iOS='0,-8,0,0'}"
                Grid.Row="0"
                WidthRequest="27"
                VerticalOptions="Start"
                HeightRequest="27" />
                <StackLayout
                Grid.Column="1"
                Grid.Row="0"
                Grid.RowSpan="2">
                    <Label
                    x:Name="LblStrengthUp"
                    Text="{Binding Question, Mode=OneWay}"
                    Margin="0,-8,0,9"
                    TextColor="Black"
                    FontAttributes="Bold"
                    FontSize="19" />
                    <Label
                    x:Name="LblStrengthUpText"
                    Margin="0,-2,0,0"
                    Text="{Binding Part1}"
                    FontSize="17"
                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                    TextColor="#AA000000">
                        <Label.FormattedText>
                            <FormattedString>
                                <Span
                                Text="{Binding Part1, Mode=OneWay}" />
                                <Span
                                Text="{Binding Part2, Mode=OneWay}"
                                TextColor="{x:Static app:AppThemeConstants.BlueLightColor}">
                                    <Span.GestureRecognizers>
                                        <TapGestureRecognizer
                                        Tapped="TapGestureRecognizer_OnTapped"></TapGestureRecognizer>
                                    </Span.GestureRecognizers>
                                </Span>
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>
                    <controls:ExtendedLabelLink
        x:Name="lblLongText"
        FontSize="17"
                        IsVisible="False"
        LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
        TextColor="#AA000000"
        Text="{Binding Part1, Mode=OneWay}"
        
        Margin="4,0" />
                </StackLayout>
            </Grid>
            <Grid
            x:Name="gridChatButtons"
            IsVisible="False"
            HorizontalOptions="FillAndExpand"
            Margin="1,20,-9,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition
                        Width=".5*" />
                    <ColumnDefinition
                        Width=".5*" />
                </Grid.ColumnDefinitions>
                <t:DrMuscleButton
                Text="SHARE"
                FontSize="13"
                FontAttributes="Bold"
                Grid.Column="0"
                HorizontalOptions="Center"
                IsVisible="false"
                x:Name="BtnProgressAIChat"
                Clicked="OpenChat_Clicked"
                Style="{StaticResource buttonLinkStyle}"
                VerticalOptions="Center"
                TextColor="{x:Static app:AppThemeConstants.BlueColor}" />

                <t:DrMuscleButton
                x:Name="BtnMoreTips"
                FontSize="13"
                FontAttributes="Bold"
                Grid.Column="0"
                HorizontalOptions="Center"
                Text="MORE TIPS"
                Clicked="MoreTips_Clicked"
                Style="{StaticResource buttonLinkStyle}"
                VerticalOptions="Center"
                IsVisible="False"
                TextColor="{x:Static app:AppThemeConstants.BlueColor}" />

                <Border
                Padding="0"
                Margin="0"
                
                    Stroke="Transparent" StrokeShape="RoundRectangle 6,6,6,6" Style="{StaticResource GradientBorderStyleBlue}"
                VerticalOptions="Center"
                x:Name="BtnHelpWithGoal"
                Grid.Column="1"
                HorizontalOptions="FillAndExpand"
                HeightRequest="45">

                        <t:DrMuscleButton
                            Margin="0,-2,0,0"
                    FontSize="13"
                            HeightRequest="45"
                    FontAttributes="Bold"
                    HorizontalOptions="FillAndExpand"
                    Text="HELP WITH GOAL"
                    Clicked="HelpWithGoal_Clicked"
                    Style="{StaticResource buttonLinkStyle}"
                    VerticalOptions="Center"
                    TextColor="White" />
                    
                </Border>

                <Border
                Padding="0"
                Margin="0"
                Stroke="Transparent" StrokeShape="RoundRectangle 6,6,6,6" Style="{StaticResource GradientBorderStyleBlue}"
                VerticalOptions="Center"
                x:Name="BtnInspiredMe"
                Grid.Column="1"
                HorizontalOptions="FillAndExpand"
                HeightRequest="45">
                    
                        <t:DrMuscleButton
                    FontSize="13"
                            HeightRequest="45"
                            Margin="0,-2,0,0"
                    FontAttributes="Bold"
                    HorizontalOptions="FillAndExpand"
                    Text="INSPIRE ME"
                    Clicked="InspireMe_Clicked"
                    Style="{StaticResource buttonLinkStyle}"
                    VerticalOptions="Center"
                    TextColor="White" />
                    
                </Border>

                <Border
                Padding="0"
                Margin="0"
                Stroke="Transparent" StrokeShape="RoundRectangle 6,6,6,6" Style="{StaticResource GradientBorderStyleBlue}"
                IsVisible="false"
                VerticalOptions="Center"
                x:Name="BtnShare"
                Grid.Column="1"
                HorizontalOptions="FillAndExpand"
                HeightRequest="45">
                    
                        <t:DrMuscleButton
                    VerticalOptions="Center"
                            Margin="0,-2,0,0"
                    HeightRequest="45"
                    FontSize="13"
                    CornerRadius="6"
                    HorizontalOptions="FillAndExpand"
                    Text="SHARE"
                    IsVisible="true"
                    Style="{StaticResource highEmphasisButtonStyle}"
                    BackgroundColor="Transparent"
                    BorderColor="Transparent"
                    TextColor="White"
                    Clicked="BtnShare_Clicked"/>
                    
                </Border>

            </Grid>
        </StackLayout>
    </controls:CustomFrame>
</ContentView>
