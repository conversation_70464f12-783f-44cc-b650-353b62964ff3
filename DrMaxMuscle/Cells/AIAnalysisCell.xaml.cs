using DrMaxMuscle.Helpers;
using DrMaxMuscle.Message;
using DrMaxMuscle.Screens.Subscription;
using DrMaxMuscle.Utility;

namespace DrMaxMuscle.Cells;

public partial class AIAnalysisCell : ContentView
{
	public AIAnalysisCell()
	{
		InitializeComponent();
	}
    private void TapGestureRecognizer_OnTapped(object sender, EventArgs e)
    {
        try
        {
            SubscriptionPage subscription = new SubscriptionPage();
            subscription.OnBeforeShow();
            Navigation.PushAsync(subscription);
        }
        catch(Exception ex)
        {

        }
    }

    protected override void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();
        BotModel model = this.BindingContext as BotModel;
        if (model == null)
            return;
        BtnInspiredMe.IsVisible = false;
        BtnHelpWithGoal.IsVisible = false;
        BtnShare.IsVisible = false;
        if (model != null && !string.IsNullOrEmpty(model.LbsLifted) && model.LbsLifted.Contains("5"))
        {
            LblStrengthUpText.IsVisible = false;
            lblLongText.IsVisible = true;
            lblLongText.Text = model.Part1;
        } else
        {
            LblStrengthUpText.IsVisible = true;
            lblLongText.IsVisible = false;
        }

        if (model != null && !string.IsNullOrEmpty(model.Part2) && model.Part2.Contains("Learn more."))
        {
            gridChatButtons.IsVisible = false;
        }
        else
        {
            //model.LevelUpText.Contains("2") means it is card of "inner champion"
            if (model != null && !string.IsNullOrEmpty(model.LevelUpText) && model.LevelUpText.Contains("2"))
            {
                gridChatButtons.IsVisible = true;
                BtnInspiredMe.IsVisible = true;
                BtnProgressAIChat.IsVisible = true;
                return;
            }
            //model.LevelUpText.Contains("3") means it is card of "progress report"
            else if (model != null && !string.IsNullOrEmpty(model.LevelUpText) && model.LevelUpText.Contains("3"))
            {
                iconImage.Margin = new Thickness(0, -8, 0, 0);
                iconImage.WidthRequest = 18;
                iconImage.HeightRequest = 23;
                BtnInspiredMe.IsVisible = false;
                gridChatButtons.IsVisible = true;
                BtnHelpWithGoal.IsVisible = false;
                BtnShare.IsVisible = true;
                BtnProgressAIChat.IsVisible = false;
                BtnMoreTips.IsVisible = true;
                LblStrengthUp.Text = "Progress Report";
                return;
            }
            //model.LevelUpText.Contains("4") means it is for "tips to achieve goal"
            else if (model != null && !string.IsNullOrEmpty(model.LevelUpText) && model.LevelUpText.Contains("4"))
            {
                gridChatButtons.IsVisible = true;
                BtnHelpWithGoal.IsVisible = true;
                BtnProgressAIChat.IsVisible = true;
                BtnInspiredMe.IsVisible = false;
                BtnShare.IsVisible = false;
                BtnMoreTips.IsVisible = false;
                return;
            }
        }
    }

    private async void HelpWithGoal_Clicked(object sender, EventArgs args)
    {
        try
        {
            ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
        await Task.Delay(300);
        MessagingCenter.Send<HelpWithGoalChatMessage>(new HelpWithGoalChatMessage(), "HelpWithGoalChatMessage");
        }
        catch (Exception ex)
        {
        }
    }

    private async void MoreTips_Clicked(object sender, EventArgs args)
    {
        try
        {
            ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
            await Task.Delay(300);
            MessagingCenter.Send<HelpWithGoalChatMessage>(new HelpWithGoalChatMessage(), "FullReportChatMessage");

        }
        catch (Exception ex)
        {
        }
    }

    private async void InspireMe_Clicked(object sender, EventArgs args)
    {
        try 
        { 
            ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
            await Task.Delay(300);
            MessagingCenter.Send<HelpWithGoalChatMessage>(new HelpWithGoalChatMessage() { IsInspireMe = true }, "HelpWithGoalChatMessage");

        }
        catch (Exception ex)
        {
        }
    }

    async void OpenChat_Clicked(object sender, EventArgs args)
    {

        BotModel bindingContext = this.BindingContext as BotModel;
        await HelperClass.ShareApp("AI_Analysis", "Share_AI_Analysis", bindingContext.Part1);
        // try { 
        // ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];

        // }
        // catch (Exception ex)
        // {
        // }
    }

    async void BtnShare_Clicked(System.Object sender, System.EventArgs e)
    {
        BotModel bindingContext = this.BindingContext as BotModel;
        await HelperClass.ShareApp("Progress_Report", "Share_Progress_Report", bindingContext.Part1);
    }
}