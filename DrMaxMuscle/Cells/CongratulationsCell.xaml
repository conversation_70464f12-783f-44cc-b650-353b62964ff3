<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             x:Class="DrMaxMuscle.Cells.CongratulationsCell">
    <controls:CustomFrame
    Margin="10,10,10,5"
    CornerRadius="12"
    x:Name="FrmContainer"
    Padding="20,12,20,2"
    HorizontalOptions="Center"
                        BorderColor="Transparent"
HasShadow="true"
    BackgroundColor="Transparent">
    <controls:CustomFrame.Shadow>
    <Shadow Brush="Black"
              Offset="0,0"
              Radius="5"
              Opacity="0.2" />
</controls:CustomFrame.Shadow>
        <Label
        x:Name="LblAnswer"
        FontSize="Medium"
        Text="{Binding Question}"
        IsVisible="true"
        HorizontalOptions="Center"
        HorizontalTextAlignment="Center"
            FontAttributes="Bold"
        Style="{StaticResource LabelStyle}"
        TextColor="Black"
        Margin="0" />

    </controls:CustomFrame>
</ContentView>
