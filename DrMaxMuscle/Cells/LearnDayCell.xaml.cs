using DrMaxMuscle.Views;
using RGPopup.Maui.Services;

namespace DrMaxMuscle.Cells;

public partial class LearnDayCell : ContentView
{
	public LearnDayCell()
	{
		InitializeComponent();
        FrmContainer.Opacity = 0;
        FrmContainer.Opacity = 0;

    }
    protected override async void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();
        await FrmContainer.FadeTo(1, 500, Easing.CubicInOut);
        await LblAnswer.FadeTo(1, 500);

    }

    void TapGestureRecognizer_Tapped(System.Object sender, System.EventArgs e)
    {
        // uncomment code please
        PopupNavigation.Instance.PushAsync(new ReminderPopup());
    }
}