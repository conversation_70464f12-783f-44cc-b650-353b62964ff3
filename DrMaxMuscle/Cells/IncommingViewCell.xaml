<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             x:Class="DrMaxMuscle.Cells.IncommingViewCell">
    <Grid
    Rotation="180"
    FlowDirection="LeftToRight"
    ColumnSpacing="5"
    RowSpacing="0"
    Padding="5">
        <Grid.ColumnDefinitions>
            <ColumnDefinition
            Width="40">
            </ColumnDefinition>
            <ColumnDefinition
            Width="*">
            </ColumnDefinition>
            <ColumnDefinition
            Width="20">
            </ColumnDefinition>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition
            Height="auto">
            </RowDefinition>
            <RowDefinition
            Height="*">
            </RowDefinition>
        </Grid.RowDefinitions>
        <ffimageloading:CachedImage
        Grid.Row="0"
        Grid.Column="0"
        Grid.RowSpan="2"
        x:Name="imgInProfilePic"
        HorizontalOptions="Center"
        VerticalOptions="Start"
        WidthRequest="35"
        HeightRequest="35"
        DownsampleToViewSize="true"
        LoadingPlaceholder="backgroundblack.png"
        Source="{Binding ProfileUrl}"
            ErrorPlaceholder="backgroundblack.png">
        </ffimageloading:CachedImage>
        <Frame
        Padding="0"
        Grid.Row="0"
        CornerRadius="{OnPlatform Android='50',iOS= '17'}"
        Grid.Column="0"
        Grid.RowSpan="2"
        HorizontalOptions="Center"
        VerticalOptions="Start"
        WidthRequest="33"
        HeightRequest="33"
        HasShadow="false"
        x:Name="FrmProfile">
            <Label
            x:Name="LblProfileText"
            HorizontalOptions="Center"
            VerticalOptions="Center"
            HorizontalTextAlignment="Center"
            VerticalTextAlignment="Center"
            Text=""
            TextColor="White"
            FontSize="20" />
        </Frame>

        <StackLayout
        Grid.Row="1"
        Margin="-40,10,0,0"
        Grid.Column="1"
        VerticalOptions="End"
        Orientation="Horizontal"
        IsClippedToBounds="true"
        HorizontalOptions="Start">

            <Border StrokeThickness="0" StrokeShape="RoundRectangle 0,12,12,12" 
                    VerticalOptions="Start" Stroke="Transparent"
                    Grid.Row="1"
                    Grid.Column="1"
                    HorizontalOptions="End"
                    Margin="0,5,15,5" 
                    Padding="15"
                    Style="{StaticResource GradientBorderStyleBlue}">  
                <Border.Shadow>
                    <Shadow Brush="Black"
                        Opacity="0.5"
                        Radius="5"
                        Offset="2,2"/>
                </Border.Shadow>

                <StackLayout
                         Spacing="4"
                         Padding="0"
                         Margin="0">
                    <controls:ExtendedLightBlueLabel
                                VerticalTextAlignment="End"
                                x:Name="lblOutMessage"
                                HorizontalOptions="End"
                                HorizontalTextAlignment="Start"
                                TextColor="White"
                                FontSize="17"
                                LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                                Text="{Binding Message}">
                    </controls:ExtendedLightBlueLabel>
                    <Label
                            FontSize="Micro"
                            HorizontalOptions="End"
                            HorizontalTextAlignment="End"
                            Text="{Binding TImeAgo}"
                            VerticalOptions="End"
                            VerticalTextAlignment="End"
                            TextColor="LightGray">
                    </Label>
                </StackLayout>
                
            </Border>


            <!--<Frame
            VerticalOptions="Start"
            Grid.Row="1"
            Grid.Column="1"
            IsClippedToBounds="False"
            HorizontalOptions="End"
            Margin="0,5,15,5" 
            Padding="15"
            Style="{StaticResource GradientFrameStyleBlue}"
            CornerRadius="12" >


                <StackLayout
                 Spacing="4"
                 Padding="0"
                 Margin="0">
                    <controls:ExtendedLightBlueLabel
                VerticalTextAlignment="End"
                x:Name="lblOutMessage"
                HorizontalOptions="End"
                HorizontalTextAlignment="Start"
                TextColor="White"
                FontSize="17"
                LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                Text="{Binding Message}">
                    </controls:ExtendedLightBlueLabel>
                    <Label
                    FontSize="Micro"
                    HorizontalOptions="End"
                    HorizontalTextAlignment="End"
                    Text="{Binding TImeAgo}"
                    VerticalOptions="End"
                    VerticalTextAlignment="End"
                    TextColor="LightGray">
                    </Label>
                </StackLayout>
               
            </Frame>-->

        </StackLayout>
        <StackLayout
        Grid.Row="0"
        Margin="0,8,0,0"
        Grid.Column="1"
        VerticalOptions="Center"
        Orientation="Horizontal"
        HorizontalOptions="Start">
            <Label
            x:Name="nameLabel"
            FontAttributes="Bold"
            HorizontalOptions="End"
            HorizontalTextAlignment="End"
            Text="{Binding Nickname}"
            TextColor="{x:Static app:AppThemeConstants.OffBlackColor}">
                <Label.GestureRecognizers>
                    <TapGestureRecognizer
                    Tapped="Username_Tapped" />
                </Label.GestureRecognizers>
            </Label>

        </StackLayout>
    </Grid>
</ContentView>
