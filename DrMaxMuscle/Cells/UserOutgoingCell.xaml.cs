using Acr.UserDialogs;
using CommunityToolkit.Maui.Alerts;
using CommunityToolkit.Maui.Core;
using DrMaxMuscle.Resx;
namespace DrMaxMuscle.Cells;

public partial class UserOutgoingCell : ContentView
{
    public UserOutgoingCell()
    {
        InitializeComponent();
    }
    protected override void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();
        try
        {
            Device.BeginInvokeOnMainThread(() =>
            {
                var message = (DrMaxMuscle.Helpers.Messages)this.BindingContext;
                if (message == null)
                    return;
                if (message.UserId.ToLower().Equals("<EMAIL>"))
                {
                    //imgOutProfilePic.IsVisible = true;
                    if (message.IsFromAI)
                    {
                        //imgOutProfilePic.Source = Device.RuntimePlatform == Device.Android ? "Icon" : "roundedicon";
                        //nameLabel.Text = "Dr. Muscle AI";
                    }
                    else
                    {
                        //imgOutProfilePic.Source = "victoriaProfileRound.png";//"adminprofile.png";
                        //nameLabel.Text = "Victoria from Dr. Muscle";
                    }

                }
                else
                {
                    if (!string.IsNullOrEmpty(message.ProfileUrl) && message.ProfileUrl.ToLower().Contains("facebook") || message.ProfileUrl.ToLower().Contains("google"))
                    {
                        //imgOutProfilePic.IsVisible = true;
                        //imgOutProfilePic.Source = message.ProfileUrl;
                    }
                    else
                    {

                    }
                    if (message.IsFromAI)
                    {
                        //imgOutProfilePic.IsVisible = true
                        //imgOutProfilePic.Source = Device.RuntimePlatform == Device.Android ? "Icon" : "roundedicon";
                        //nameLabel.Text = "Dr. Muscle AI";
                    }
                }
            });
        }
        catch (Exception ex)
        {

        }
    }

    async void Username_Tapped(object sender, EventArgs e)
    {
        var message = (DrMaxMuscle.Helpers.Messages)this.BindingContext;
        if (message == null)
            return;
        bool IsAdmin = LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>") || LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>");

        if (!message.UserId.ToLower().Equals("<EMAIL>") && IsAdmin)
        {
            //Clipboard.SetTextAsync(message.UserId);
            //Plugin.Toast.CrossToastPopUp.Current.ShowToastMessage("Copied to clipboard", Plugin.Toast.Abstractions.ToastLength.Short);
            bool isMuted = App.MutedUserList.Contains(message.UserId);
            if (!message.UserId.ToLower().Equals("<EMAIL>") && IsAdmin)
            {

                ActionSheetConfig config = new ActionSheetConfig();
                //config.AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray);
                config.Add($"Get {message.UserId}", async () =>
                {
                    Clipboard.SetTextAsync(message.UserId);
                    CancellationTokenSource cancellationTokenSource = new CancellationTokenSource();
                    var toast = Toast.Make("Email id copied to clipboard", ToastDuration.Short, 14);

                    await toast.Show(cancellationTokenSource.Token);
                    //Plugin.Toast.CrossToastPopUp.Current.ShowToastMessage("Email id copied to clipboard", Plugin.Toast.Abstractions.ToastLength.Short);
                });
                config.Add(isMuted ? $"Unmute {message.UserId}" : $"Mute {message.UserId}", () =>
                {
                    var unmuteUserMessage = new Message.MuteUnmuteUserMessage();
                    unmuteUserMessage.IsMuted = isMuted;
                    unmuteUserMessage.UserId = message.UserId;
                    MessagingCenter.Send(unmuteUserMessage, "MuteUnmuteUserMessage");
                });
                config.Add("Delete message", () =>
                {
                    var deleteMessage = new Message.DeleteChatMessage();
                    deleteMessage.FullMessage = message;
                    MessagingCenter.Send(deleteMessage, "DeleteChatMessage");

                });
                config.SetCancel(AppResources.Cancel, null);

                config.SetTitle(isMuted ? $"Get email or unmute {message.UserId}?" : $"Get email or mute {message.UserId}?");
                UserDialogs.Instance.ActionSheet(config);

            }
        }
    }

}
