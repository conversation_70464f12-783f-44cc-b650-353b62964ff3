<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             x:Class="DrMaxMuscle.Cells.StatsCell">
    <Grid
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Padding="10,10,10,0"
    >
        <Grid.RowDefinitions>
            <RowDefinition
            Height="*" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition
            Width="*" />
            <ColumnDefinition
            Width="*" />
        </Grid.ColumnDefinitions>
        <StackLayout
        Grid.Row="0"
        HorizontalOptions="CenterAndExpand"
        Orientation="Horizontal"
        Grid.Column="0">
            <Label
            Text="{Binding StrengthPerText}"
            TextColor="{Binding StrengthTextColor}"
             FontSize="Medium"
            FontAttributes="Bold"
            HorizontalOptions="Center"
            HorizontalTextAlignment="Center" />
            <ffimageloading:CachedImage
            HorizontalOptions="Center"
            Aspect="AspectFit"
            Source="{Binding StrengthImage}" 
                ErrorPlaceholder="backgroundblack.png"/>

        </StackLayout>

        <StackLayout
        Grid.Row="0"
        
        Orientation="Horizontal"
        HorizontalOptions="Center"
        Grid.Column="1">
            <Label
            Text="{Binding SetsPerText}"
            TextColor="{Binding SetTextColor}"
            FontSize="Medium"
            FontAttributes="Bold"
            HorizontalOptions="CenterAndExpand"
            HorizontalTextAlignment="Center" />
            <ffimageloading:CachedImage
            HorizontalOptions="Center"
            Aspect="AspectFit"
            Source="{Binding SetsImage}"
                ErrorPlaceholder="backgroundblack.png"/>

        </StackLayout>
    </Grid>
</ContentView>
