using DrMaxMuscle.Helpers;
using DrMaxMuscle.Message;
using DrMaxMuscle.Utility;

namespace DrMaxMuscle.Cells;

public partial class NewRecordCell : ContentView
{
	public NewRecordCell()
	{
		InitializeComponent();
	}
    protected override void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();
        try
        {
            var botModel = (BotModel)this.BindingContext;
            if (string.IsNullOrEmpty(botModel?.Question))
            {
                //LblAnswer.IsVisible = false;
                LblAnswer.Text = "Congratulations!";
            }

        }
        catch (Exception ex)
        {

        }
    }

    async void BtnShareApp_Clicked(System.Object sender, System.EventArgs e)
    {
        //get the image
        // uncomment code please
        var ImageStream = await SLWorkoutStats.CaptureAsync();
        HelperClass.ShareImage(await ImageStream.OpenReadAsync(), "workout_summary", "share_workout_summary");
    }

    async void BtnAIChat_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
            await Task.Delay(300);
            MessagingCenter.Send<HelpWithGoalChatMessage>(new HelpWithGoalChatMessage(), "AnalyzeMyCurrentStatsAndGiveMeYourTopRecommendation");
        }
        catch (Exception ex)
        {
        }
    }
}