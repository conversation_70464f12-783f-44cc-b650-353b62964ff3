﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Cells.LevelUpCell">
    <Grid
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Padding="10,20,10,0"
    >
        <Grid.RowDefinitions>
            <RowDefinition
            Height="*" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition
            Width="*" />
            <ColumnDefinition
            Width="*" />
            <ColumnDefinition
            Width="*" />
        </Grid.ColumnDefinitions>
        <StackLayout
                        Grid.Column="0"
                        HorizontalOptions="FillAndExpand">
            <Image
                            x:Name="IconResultImage"
                            Source="chain.png"
                            Aspect="AspectFit"
                            HeightRequest="32"
                            HorizontalOptions="CenterAndExpand" />

            <Label
                            x:Name="lblResult4"
                            Text="{Binding ChainCount}"
                            IsVisible="true"
                            HorizontalOptions="Center"
                            FontAttributes="Bold"
                            FontSize="17"
                            Style="{StaticResource LabelStyle}"
                            TextColor="Black" />
            <Label
                            x:Name="lblResult44"
                            Text="Weeks streak"
                            IsVisible="true"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            FontSize="17"
                            LineHeight="{OnPlatform Android='1.3'}" 
                            TextColor="Black" >
                <Label.Triggers>
                    <DataTrigger TargetType="Label" Binding="{Binding ChainCount}" Value="1" >
                        <Setter Property="Text" Value="Week streak" />
                    </DataTrigger>
                    <DataTrigger TargetType="Label" Binding="{Binding ChainCount}" Value="0" >
                        <Setter Property="Text" Value="Week streak" />
                    </DataTrigger>
                </Label.Triggers>
            </Label>
        </StackLayout>
       

        <StackLayout
        Grid.Row="0"
        IsVisible="{Binding IsLastVisible}"
        HorizontalOptions="FillAndExpand"
        Grid.Column="1">
            <Image
                            Source="workoutdone.png"
                            Aspect="AspectFit"
                            HeightRequest="32"
                            HorizontalOptions="CenterAndExpand" />
            <Label
            Text="{Binding LevelUpMessage}"
            FontAttributes="Bold"
            FontSize="17"
            Style="{StaticResource LabelStyle}"
            HorizontalOptions="Center"
            HorizontalTextAlignment="Center"
            TextColor="Black"/>
            <Label
            Text="{Binding LevelUpText}"
            
            FontSize="17"
            LineHeight="{OnPlatform Android='1.3'}" 
            TextColor="Black"
            HorizontalOptions="CenterAndExpand"
            HorizontalTextAlignment="Center" />
        </StackLayout>

        <StackLayout
    Grid.Row="0"
    HorizontalOptions="FillAndExpand"
    Grid.Column="2">
            <Image
        Source="flexed_biceps.png"
        Aspect="AspectFit"
        HeightRequest="32"
        HorizontalOptions="CenterAndExpand" />
            <Label
        Text="{Binding LbsLifted}"
        FontAttributes="Bold"
        FontSize="17"
        Style="{StaticResource LabelStyle}"
        HorizontalOptions="Center"
        HorizontalTextAlignment="Center"
        TextColor="Black" />
            <Label
        Text="{Binding LbsLiftedText}"
        FontSize="17"
        LineHeight="{OnPlatform Android='1.3'}" 
        TextColor="Black"
        HorizontalOptions="CenterAndExpand"
        HorizontalTextAlignment="Center" />
        </StackLayout>
    </Grid>
</ContentView>
