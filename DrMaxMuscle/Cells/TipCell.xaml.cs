namespace DrMaxMuscle.Cells;

public partial class TipCell : ContentView
{
	public TipCell()
	{
		InitializeComponent();
	}
    protected override async void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();

    }

    void LblStrengthUpText_BindingContextChanged(System.Object sender, System.EventArgs e)
    {
        // uncomment code please
        //ForceUpdateSize();
    }
}