﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             xmlns:effects="clr-namespace:DrMaxMuscle.Effects"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
xmlns:converters="clr-namespace:DrMaxMuscle.Convertors"
                                                             xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             x:Class="DrMaxMuscle.Cells.SetBindingNextItem">
    <StackLayout Spacing="0">
        <StackLayout.Resources>
            <converters:BoolInverter
                        x:Key="BoolInverterConverter" />
        </StackLayout.Resources>
        <!--Next cell-->

        <StackLayout VerticalOptions="FillAndExpand" Padding="15,10,15,0"
            Margin="4,0,4,0"
            IsVisible="{Binding IsNext}"
                         x:Name="NextCellGrid"
            IsClippedToBounds="true">
                <StackLayout.GestureRecognizers>
                    <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped" />
                </StackLayout.GestureRecognizers>

            <ffimageloading:CachedImage x:Name="videoPlayer" Source="{Binding VideoUrl}" FadeAnimationEnabled="False"  HeightRequest="200" Aspect="AspectFit" FadeAnimationForCachedImages="False" HorizontalOptions="FillAndExpand" BackgroundColor="White" IsVisible="{Binding IsVideoUrlAvailable}">
                <ffimageloading:CachedImage.Triggers>
                <!-- Check if VideoUrl is not null -->
                <DataTrigger TargetType="ffimageloading:CachedImage" Binding="{Binding VideoUrl}" Value="{x:Null}">
                        <Setter Property="Source" Value="backgroundblack" />
                    </DataTrigger>
            </ffimageloading:CachedImage.Triggers>    
                <ffimageloading:CachedImage.GestureRecognizers>
                        <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped_1" />
                    </ffimageloading:CachedImage.GestureRecognizers>
                    </ffimageloading:CachedImage>


                <Grid IsVisible="{Binding IsHeaderCell}" ColumnSpacing="0" Padding="0,10,0,11">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="25" />
                        <ColumnDefinition Width="60" />
                        <ColumnDefinition Width="0.77*" />
                        <ColumnDefinition Width="25" />
                        <ColumnDefinition Width="0.77*" />
                    </Grid.ColumnDefinitions>
                    <Label Text="SET" Grid.Column="1" FontSize="18" TextColor="#FFFFFF" FontAttributes="Bold"
                           HorizontalOptions="FillAndExpand" HorizontalTextAlignment="Center" />
                    <Label Text="REPS" x:Name="repsTypeLabel" Grid.Column="2" FontSize="18" TextColor="#FFFFFF"
                           FontAttributes="Bold" HorizontalOptions="FillAndExpand" HorizontalTextAlignment="Center" />
                    <Label Text="LBS" x:Name="massUnitLabel" Grid.Column="4" FontSize="18" TextColor="#FFFFFF"
                           FontAttributes="Bold" HorizontalOptions="FillAndExpand" HorizontalTextAlignment="Center" />
                </Grid>


                <Grid

                    IsClippedToBounds="True"
                    ColumnSpacing="0"
                    RowSpacing="0"
                    VerticalOptions="FillAndExpand"

                    HorizontalOptions="FillAndExpand">

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="25" />
                        <ColumnDefinition Width="60" />
                        <ColumnDefinition Width="0.77*" />
                        <ColumnDefinition Width="25" />
                        <ColumnDefinition Width="0.77*" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition
                            Height="0.85*" />
                        <RowDefinition
                            Height="*" />
                        <RowDefinition
                            Height="0.85*" />
                    </Grid.RowDefinitions>

                    <ffimageloading:CachedImage
                        Source="deleteset_yellow"
                        FadeAnimationEnabled="False"
                        Margin="{OnPlatform Android='0,3,0,5', iOS='0,5,0,5'}"
                        HeightRequest="20"
                        WidthRequest="20"
                        Aspect="AspectFit"
                        Grid.Row="1"
                        Grid.Column="0"
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                    </ffimageloading:CachedImage>
                    <t:DrMuscleButton
                        Margin="0"
                        Grid.Row="1"
                        Grid.Column="0"
                        BackgroundColor="Transparent"
                        Clicked="DeleteSetTapGestureRecognizer_Tapped">
                    </t:DrMuscleButton>

                    <Label
                        Text="{Binding SetNo}"
                        Grid.Row="1"
                        Grid.Column="1"
                        Margin="{OnPlatform Android='0,-4,0,0', iOS='0'}"
                        VerticalOptions="Center"
                        VerticalTextAlignment="Center"
                        HorizontalOptions="FillAndExpand"
                        HorizontalTextAlignment="Center"
                        FontSize="30"
                        TextColor="#FFFFFF" />
                    <ImageButton
                        Grid.Row="0"
                        Grid.Column="2"
                        x:Name="RepsMore"
                        Aspect="AspectFit"
                        BackgroundColor="Transparent"
                        Margin="5,3,5,4"
                        Source="light_blue_arrow.png"
                        Padding="0,0,0,0"
                        BorderColor="Transparent"
                        BorderWidth="0"
                        HeightRequest="62"
                        Clicked="RepsMore_Clicked"
                        VerticalOptions="Center" />
                    <StackLayout x:Name="StackReps" Grid.Row="1" Spacing="0" Margin="5,3,5,3"
                                 Grid.Column="2" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand">
                        <StackLayout.Triggers>
                            <DataTrigger
                                    TargetType="StackLayout"
                                    Binding="{Binding BodypartId}"
                                    Value="12">
                                    <Setter
                                        Property="IsVisible"
                                        Value="False" />
                                </DataTrigger>

                        </StackLayout.Triggers>
                    <Frame
                        HasShadow="False"
                        CornerRadius="5"
                        BorderColor="Transparent"
                        BackgroundColor="{OnPlatform Android='#4D0C2432', iOS='#660C2432'}"
                        Padding="0">
                        <StackLayout
                            VerticalOptions="FillAndExpand"
                            HorizontalOptions="FillAndExpand">
                            <t:WorkoutEntry
                            Text="{Binding Reps}"
                            x:Name="RepsEntry"
                            HorizontalTextAlignment="Center"
                            VerticalOptions="Center"
                            Keyboard="Numeric"
                            HorizontalOptions="FillAndExpand"
                            FontSize="30"
                            MaxLength="4"
                            HeightRequest="{OnPlatform iOS='84', Android='87'}"
                            TextChanged="RepsEntry_TextChanged"
                            TextColor="White">
                            <t:WorkoutEntry.Triggers>
                                <DataTrigger
                                    TargetType="t:WorkoutEntry"
                                    Binding="{Binding IsMaxChallenge}"
                                    Value="true">
                                    <Setter
                                        Property="IsVisible"
                                        Value="false" />
                                </DataTrigger>
                            </t:WorkoutEntry.Triggers>
                        </t:WorkoutEntry>
                            <Label
                            Text="{Binding Reps, StringFormat='Max ({0})'}"
                            HorizontalTextAlignment="Center"
                            VerticalOptions="Center"
                            IsVisible="false"
                            HorizontalOptions="FillAndExpand"
                            FontSize="21"
                            HeightRequest="{OnPlatform iOS='84', Android='87'}"
                            TextColor="White">
                                <Label.Triggers>
                                    <DataTrigger
                                    TargetType="Label"
                                    Binding="{Binding IsMaxChallenge}"
                                    Value="true">
                                        <Setter
                                        Property="IsVisible"
                                        Value="true" />
                                    </DataTrigger>
                                </Label.Triggers>
                            </Label>
                        </StackLayout>
                    </Frame>

                    </StackLayout>
                    <!--Cardio for reps-->
                    <StackLayout Grid.Row="1" Spacing="0" Margin="5,3,5,3" BackgroundColor="Transparent"
                                 Grid.Column="2" IsVisible="false" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand">
                        <StackLayout.Triggers>
                            <DataTrigger
                                    TargetType="StackLayout"
                                    Binding="{Binding BodypartId}"
                                    Value="12">
                                    <Setter
                                        Property="IsVisible"
                                        Value="True" />
                                </DataTrigger>

                        </StackLayout.Triggers>
                        <Grid BackgroundColor="Transparent">
                        <Frame
                          HasShadow="False"
                          CornerRadius="5"
                          BorderColor="Transparent"
                          BackgroundColor="{OnPlatform Android='#4D0C2432', iOS='#660C2432'}"
                          Padding="0">
                            <StackLayout
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand">
                                <t:WorkoutEntry Text="{Binding RepsCardio}"
                                                x:Name="RepsCardioEntry"
                                                HorizontalTextAlignment="Center"
                                                VerticalOptions="Center"
                                                Keyboard="Numeric"
                                                HorizontalOptions="FillAndExpand"
                                                FontSize="24"
                                                MaxLength="6"
                                                HeightRequest="{OnPlatform iOS='84', Android='87'}"
                                                TextChanged="RepsEntry_TextChanged"
                                                TextColor="White">
                                    <t:WorkoutEntry.Triggers>
                                        <DataTrigger
                                    TargetType="t:WorkoutEntry"
                                    Binding="{Binding IsMaxChallenge}"
                                    Value="true">
                                            <Setter
                                        Property="IsVisible"
                                        Value="false" />
                                        </DataTrigger>
                                    </t:WorkoutEntry.Triggers>
                                </t:WorkoutEntry>
                                <Label
                                    Text="{Binding RepsCardio, StringFormat='Max ({0})'}"
                                    HorizontalTextAlignment="Center"
                                    VerticalOptions="Center"
                                    IsVisible="false"
                                    HorizontalOptions="FillAndExpand"
                                    FontSize="17"
                                    HeightRequest="{OnPlatform iOS='84', Android='87'}"
                                    BackgroundColor="{OnPlatform Android='#4D0C2432', iOS='#660C2432'}"
                                    TextColor="White">
                                    <Label.Triggers>
                                        <DataTrigger
                                            TargetType="Label"
                                            Binding="{Binding IsMaxChallenge}"
                                            Value="true">
                                            <Setter
                                                Property="IsVisible"
                                                Value="true" />
                                        </DataTrigger>
                                    </Label.Triggers>
                                </Label>
                            </StackLayout>
                        </Frame>
                        
                        <Label
                            HorizontalTextAlignment="Center"
                            VerticalOptions="FillAndExpand"
                            HorizontalOptions="FillAndExpand"
                            FontSize="21"
                            BackgroundColor="Transparent"
                            >
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped_3" />
                            </Label.GestureRecognizers>
                        </Label>
                            </Grid>
                    </StackLayout>
                    <Label
                        Text="*"
                        Grid.Row="1"
                        Margin="0,2,0,0"
                        Grid.Column="3"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        VerticalTextAlignment="Center"
                        VerticalOptions="Center"
                        FontSize="25"
                        TextColor="White" />
                    <ImageButton
                        x:Name="RepsLess"
                        Grid.Row="2"
                        Grid.Column="2"
                        Aspect="AspectFit"
                        Source="light_blue_arrow_down.png"
                        Padding="0,0,0,0"
                        BackgroundColor="Transparent"
                        BorderColor="Transparent"
                        BorderWidth="0"
                        Margin="{OnPlatform Android='5,2,5,0', iOS='5,0,5,0'}"
                        HeightRequest="72"
                        Clicked="RepsLess_Clicked"
                        VerticalOptions="Start" />
                    <controls:AutoSizeLabel
                        Grid.Row="1"
                        Grid.Column="2"
                        Margin="{OnPlatform Android='5,0,5,8', iOS='5,0,5,8'}"
                        x:Name="PerSideText"
                        HorizontalOptions="Center"
                        VerticalOptions="End"
                        VerticalTextAlignment="End"
                        HorizontalTextAlignment="End"
                        Text="per side"
                        MaxLines="1"
                        FontSize="17"
                        TextColor="#AAFFFFFF">

                    </controls:AutoSizeLabel>
                    <ImageButton
                        x:Name="WeightMore"
                        Grid.Row="0"
                        Grid.Column="4"
                        Aspect="AspectFit"
                        Source="light_blue_arrow.png"
                        IsVisible="{Binding IsBodyweight, Converter={StaticResource BoolInverterConverter}}"
                        Padding="0,0,0,0"
                        

                        BackgroundColor="Transparent"
                        BorderColor="Transparent"
                        Margin="5,3,5,4"
                        HeightRequest="62"
                        Clicked="WeightMore_Clicked"
                        VerticalOptions="Start" />

                    <StackLayout
                        Grid.Row="1"
                        Grid.Column="4"
                        Margin="5,3,5,3"
                        HorizontalOptions="FillAndExpand"
                        VerticalOptions="FillAndExpand"
                        Spacing="0"
                        x:Name="StackWeight"
                        effects:TooltipEffect.Text="I adjusted your weight"
                        effects:TooltipEffect.BackgroundColor="{x:Static app:AppThemeConstants.ReysBlueColor}"
                        effects:TooltipEffect.TextColor="White"
                        effects:TooltipEffect.Position="Top"
                        effects:TooltipEffect.HasTooltip="True">
                        <StackLayout.Triggers>
                            <DataTrigger
                                    TargetType="StackLayout"
                                    Binding="{Binding BodypartId}"
                                    Value="12">
                                    <Setter
                                        Property="IsVisible"
                                        Value="False" />
                                </DataTrigger>

                        </StackLayout.Triggers>
                    <Frame
                      HasShadow="False"
                      CornerRadius="5"
                      BorderColor="Transparent"
                      BackgroundColor="{OnPlatform Android='#4D0C2432', iOS='#660C2432'}"
                      Padding="0">
                        <t:WorkoutEntry
                            x:Name="WeightEntry"
                            Keyboard="Numeric"
                            VerticalTextAlignment="Center"
                            VerticalOptions="Center"
                            Text="{Binding WeightSingal}"
                            HorizontalOptions="FillAndExpand"
                            HorizontalTextAlignment="Center"
                            TextChanged="WeightEntry_TextChanged"
                            HeightRequest="{OnPlatform iOS='84', Android='87'}"
                            FontSize="30"
                            ios:Entry.AdjustsFontSizeToFitWidth="true"
                            TextColor="#FFFFFF">
                            <t:WorkoutEntry.Triggers>
                                <DataTrigger
                                    TargetType="t:WorkoutEntry"
                                    Binding="{Binding IsBodyweight}"
                                    Value="true">
                                    <Setter Property="IsReadOnly" Value="True" />
                                </DataTrigger>
                            </t:WorkoutEntry.Triggers>
                        </t:WorkoutEntry>
                    </Frame>
                </StackLayout>
                    <StackLayout
                        Grid.Row="1"
                        Grid.Column="4"
                        Margin="5,3,5,3"
                        HorizontalOptions="FillAndExpand"
                        VerticalOptions="FillAndExpand"
                        Spacing="0"
                        x:Name="StackSpeed"
                        IsVisible="false"
                        >
                        <StackLayout.Triggers>
                            <DataTrigger
                                    TargetType="StackLayout"
                                    Binding="{Binding BodypartId}"
                                    Value="12">
                                    <Setter
                                        Property="IsVisible"
                                        Value="True" />
                                </DataTrigger>

                        </StackLayout.Triggers>
                    <Frame
                      HasShadow="False"
                      CornerRadius="5"
                      BorderColor="Transparent"
                      BackgroundColor="{OnPlatform Android='#4D0C2432', iOS='#660C2432'}"
                      Padding="0">
                        <t:WorkoutEntry
                            x:Name="SpeedEntry"
                            Margin="4,0"
                            Keyboard="Numeric"
                            VerticalTextAlignment="Center"
                            VerticalOptions="Center"
                            Text="{Binding Speed}"
                            HorizontalOptions="FillAndExpand"
                            HorizontalTextAlignment="Center"
                            TextChanged="SpeedEntry_TextChanged"
                            HeightRequest="{OnPlatform iOS='84', Android='87'}"
                            FontSize="30"
                            ios:Entry.AdjustsFontSizeToFitWidth="true"
                            TextColor="#FFFFFF">

                        </t:WorkoutEntry>
                    </Frame>
                </StackLayout>
                    <ImageButton
                        Grid.Row="2"
                        Grid.Column="4"
                        x:Name="WeightLess"
                        Source="light_blue_arrow_down.png"
                        Aspect="AspectFit"
                        IsVisible="{Binding IsBodyweight, Converter={StaticResource BoolInverterConverter}}"
                        BackgroundColor="Transparent"
                        Margin="{OnPlatform Android='5,2,5,0', iOS='5,0,5,0'}"
                        Padding="0,0,0,0"
                        BorderColor="Transparent"
                        BorderWidth="0"
                        HeightRequest="72"
                        Clicked="WeightLess_Clicked"
                        VerticalOptions="Start" />
                    <controls:AutoSizeLabel
                        Grid.Row="1"
                        Grid.Column="4"
                        Margin="{OnPlatform Android='5,0,5,8', iOS='5,0,5,8'}"
                        x:Name="WeightText"
                        HorizontalOptions="Center"
                        VerticalOptions="End"
                        VerticalTextAlignment="End"
                        HorizontalTextAlignment="End"
                        Text="per hand"
                        MaxLines="1"
                        FontSize="17"
                        TextColor="#AAFFFFFF">

                    </controls:AutoSizeLabel>
                    <Grid.Triggers>
                        <DataTrigger TargetType="Grid" Binding="{Binding IsSetupNotCompleted}" Value="true">
                            <Setter Property="HeightRequest" Value="0" />
                        </DataTrigger>
                    </Grid.Triggers>
                </Grid>


                <Frame Padding="10"
                       HorizontalOptions="FillAndExpand"  BackgroundColor="{OnPlatform Android='#4D0C2432', iOS='#660C2432'}"
                       HasShadow="False"
                            CornerRadius="6" BorderColor="Transparent">
                    <Frame.Triggers>
                        <MultiTrigger TargetType="Frame">
                             <MultiTrigger.Conditions>
                                <BindingCondition Binding="{Binding Source={x:Reference LblCoachTips},
                                       Path=Text.Length}"
                     Value="0"/>
                                <BindingCondition Binding="{Binding Source={x:Reference lblLasttimeset},
                                       Path=Text.Length}"
                     Value="0"/>
                                 <BindingCondition Binding="{Binding IsReferenceSet}" Value="False"/>
                            </MultiTrigger.Conditions>
                            <Setter
                                Property="IsVisible"
                                Value="False" />
                        </MultiTrigger>


                    </Frame.Triggers>
                        <StackLayout Spacing="0">
                    <StackLayout HorizontalOptions="Center" Orientation="Horizontal" IsVisible="{Binding IsReferenceSet}" Spacing="6">
                            <Label
                                Text="Reference set"
                                FontAttributes="Italic"
                                FontSize="21"
                                HorizontalTextAlignment="Center"
                                TextColor="White"
                                 />

                            <ffimageloading:CachedImage
                                Source="orange"
                                Aspect="AspectFit"
                                WidthRequest="40"
                                FadeAnimationEnabled="False"
                                HeightRequest="22"
                                Margin="{OnPlatform iOS='-7,0,0,1', Android='-7,1,0,-3'}"
                                VerticalOptions="Center"
                                ErrorPlaceholder="backgroundblack">
                                <ffimageloading:CachedImage.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="ShowAlert_Tapped" />
                                </ffimageloading:CachedImage.GestureRecognizers>


                            </ffimageloading:CachedImage>
                        </StackLayout>
                        <StackLayout HorizontalOptions="Center" Orientation="Horizontal" IsVisible="{Binding SetTitle, Converter={StaticResource StringBoolConverter}}" Spacing="6">
                            <Label

                                x:Name="LblCoachTips"
                                Text="{Binding SetTitle}"
                                FontAttributes="Italic"
                                FontSize="21"
                                
                                HorizontalTextAlignment="Center"
                                TextColor="White"
                                effects:TooltipEffect.Text="Warm-up and sets instructions"
                                effects:TooltipEffect.BackgroundColor="{x:Static app:AppThemeConstants.ReysBlueColor}"
                                effects:TooltipEffect.TextColor="White"
                                effects:TooltipEffect.Position="Top"
                                effects:TooltipEffect.HasTooltip="True"
                                effects:TooltipEffect.HasShowTooltip="{Binding ShowPlusTooltip}" />

                            <ffimageloading:CachedImage
                                Source="{Binding HeaderImage}"
                                FadeAnimationEnabled="False"
                                x:Name="IconOrange"
                                Aspect="AspectFit"
                                IsVisible="{Binding HeaderImage, Converter={StaticResource StringBoolConverter}}"
                                WidthRequest="40"
                                HeightRequest="22"
                                Margin="{OnPlatform iOS='-7,0,0,1', Android='-7,1,0,-3'}"
                                VerticalOptions="Center"
                                ErrorPlaceholder="backgroundblack">
                                <ffimageloading:CachedImage.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="ShowAlert_Tapped" />
                                </ffimageloading:CachedImage.GestureRecognizers>


                            </ffimageloading:CachedImage>
                        </StackLayout>
                           
                        <Label Text="{Binding LastTimeSet}"
                               x:Name="lblLasttimeset"
                               Margin="0,0,0,0"
                               FontAttributes="Italic"
                               FontSize="21"
                               HorizontalOptions="Center"
                               IsVisible="{Binding LastTimeSet, Converter={StaticResource StringBoolConverter}}"
                               HorizontalTextAlignment="Center"
                               TextColor="White" />
                             
                    </StackLayout>
                </Frame>


                <Frame
                    x:Name="BtnSaveSet"
                    Margin="2,11,2,20"
                    IsClippedToBounds="true"
                    BorderColor="Transparent"
                    CornerRadius="6"
                    HorizontalOptions="FillAndExpand"
                    HeightRequest="72"
                    effects:TooltipEffect.Text="Tap here when you are done"
                    effects:TooltipEffect.BackgroundColor="{x:Static app:AppThemeConstants.ReysBlueColor}"
                    effects:TooltipEffect.TextColor="White"
                    effects:TooltipEffect.Position="Top"
                    effects:TooltipEffect.HasTooltip="True"
                    effects:TooltipEffect.HasShowTooltip="False"
                    Style="{StaticResource GradientFrameStyleGreen}">
                    <Label
                        x:Name="BtnFinishSet"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        Text=""
                        IsEnabled="False"
                        TextColor="#0C2432"
                        FontSize="21"
                        FontAttributes="Bold"
                        effects:TooltipEffect.Text="Tap here when you are done"
                        effects:TooltipEffect.BackgroundColor="{x:Static app:AppThemeConstants.ReysBlueColor}"
                        effects:TooltipEffect.TextColor="White"
                        effects:TooltipEffect.Position="Top"
                        effects:TooltipEffect.HasTooltip="True">

                    </Label>
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer x:Name="savesetGesture"
                            Tapped="SaveSet_Clicked"
                            CommandParameter="{Binding .}" />
                    </Frame.GestureRecognizers>
                    <Frame.Triggers>
                        <DataTrigger TargetType="Frame" Binding="{Binding IsLastSet}" Value="False">
                            <Setter Property="Margin" Value="2,13,2,0" />
                        </DataTrigger>
                        <DataTrigger TargetType="Frame" Binding="{Binding IsExerciseFinished}"
                                     Value="True">
                            <Setter Property="Margin" Value="2,13,2,20" />
                        </DataTrigger>


                    </Frame.Triggers>
                </Frame>

                <FlexLayout
                    Direction="Row"
                    AlignContent="Start"

                    JustifyContent="SpaceBetween"
                    
                    Margin="0,-10,0,20"
                    AlignItems="Start" Wrap="Wrap"
                    HorizontalOptions="FillAndExpand"
                    IsVisible="false"
                    HeightRequest="60">
                    <FlexLayout.Triggers>
                        <MultiTrigger
                            TargetType="FlexLayout">
                            <MultiTrigger.Conditions>
                                <BindingCondition
                                    Binding="{Binding IsLastSet}"
                                    Value="True" />
                                <!--<BindingCondition
                                    Binding="{Binding IsFinished}"
                                    Value="false" />-->
                            </MultiTrigger.Conditions>
                            <Setter
                                Property="IsVisible"
                                Value="true" />
                        </MultiTrigger>
                    </FlexLayout.Triggers>
                    <t:DrMuscleButton
                        HorizontalOptions="FillAndExpand"
                        FlexLayout.Grow="1"
                        Text="Add set"
                        x:Name="btnAddSet"
                        TextColor="White"
                        Clicked="btnAddSet_Clicked"
                        CommandParameter="{Binding .}"
                        BackgroundColor="Transparent"
                        BorderColor="#ECFF92"
                        BorderWidth="1"
                        CornerRadius="6"
                        HeightRequest="60"
                        Margin="3,0,3,0">
                        <!--<t:DrMuscleButton.Triggers>
                            <DataTrigger
                                TargetType="t:DrMuscleButton" Binding="{Binding IsLastSet}"
                                        Value="True">
                                <Setter
                                    Property="IsVisible"
                                    Value="true" />
                            </DataTrigger>
                        </t:DrMuscleButton.Triggers>-->
                    </t:DrMuscleButton>
                    <t:DrMuscleButton
                        IsVisible="true"
                        x:Name="UnFinishedExercises"
                        FlexLayout.Grow="1"
                        Text="Finish exercise"
                        HorizontalOptions="FillAndExpand"
                        TextColor="White"
                        Clicked="UnFinishedExercise_Clicked"
                        CommandParameter="{Binding .}"
                        BackgroundColor="Transparent"
                        BorderColor="#ECFF92"
                        BorderWidth="1"
                        CornerRadius="6"
                        HeightRequest="60"
                        Margin="3,0,3,0">
                        <t:DrMuscleButton.Triggers>

                            <MultiTrigger
                                TargetType="t:DrMuscleButton">
                                <MultiTrigger.Conditions>
                                    <BindingCondition
                                        Binding="{Binding IsLastSet}"
                                        Value="True" />
                                    <BindingCondition
                                        Binding="{Binding IsFinished}"
                                        Value="false" />
                                    <BindingCondition
                                        Binding="{Binding IsExerciseFinished}"
                                        Value="True" />
                                </MultiTrigger.Conditions>
                                <Setter
                                    Property="Text"
                                    Value="Save" />
                            </MultiTrigger>
                            <DataTrigger TargetType="t:DrMuscleButton"  Binding="{Binding IsExerciseFinished}"
                                        Value="True">
                                <Setter Property="IsVisible" Value="false" />

                            </DataTrigger>
                        </t:DrMuscleButton.Triggers>

                    </t:DrMuscleButton>

                </FlexLayout>

            </StackLayout>
            
        
<!--
       
   


       -->

<!--Normal cell-->
       <Grid BackgroundColor="Transparent" IsVisible="{Binding IsNext, Converter={StaticResource BoolInverterConverter}}">
            
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped_2" />

            </Grid.GestureRecognizers>
        
            <StackLayout VerticalOptions="FillAndExpand" Spacing="1" Padding="15,10,15,0"
                         Margin="4,0,4,0">
                <StackLayout.Resources>
                    <converters:BoolInverter
                        x:Key="BoolInverterConverter" />
                </StackLayout.Resources>
                <ffimageloading:CachedImage x:Name="videoPlayerNormal" Source="{Binding VideoUrl}" FadeAnimationEnabled="False" HeightRequest="200" Aspect="AspectFit"  DownsampleToViewSize="True" HorizontalOptions="FillAndExpand" BackgroundColor="White" IsVisible="{Binding IsVideoUrlAvailable}">
                    <ffimageloading:CachedImage.Triggers>
                        <!-- Check if VideoUrl is not null -->
                        <DataTrigger TargetType="ffimageloading:CachedImage" Binding="{Binding VideoUrl}" Value="{x:Null}">
                            <Setter Property="Source" Value="backgroundblack" />
                            <Setter Property="DownsampleToViewSize" Value="False" />
                        </DataTrigger>
                    </ffimageloading:CachedImage.Triggers>
                    <ffimageloading:CachedImage.GestureRecognizers>
                        <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped_1"/>
                    </ffimageloading:CachedImage.GestureRecognizers>
                    </ffimageloading:CachedImage>
               <Grid IsVisible="{Binding IsHeaderCell}" ColumnSpacing="0" Padding="0,10,0,11">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="25"  />
                        <ColumnDefinition Width="60"  />
                        <ColumnDefinition Width="0.77*" />
                        <ColumnDefinition Width="25" />
                        <ColumnDefinition Width="0.77*" />

                    </Grid.ColumnDefinitions>
                    <Label Text="SET" Grid.Column="1" FontSize="18" TextColor="#FFFFFF" FontAttributes="Bold" HorizontalOptions="FillAndExpand" HorizontalTextAlignment="Center"/>
                    <Label Text="REPS" x:Name="repsTypeLabelNormal"  Grid.Column="2" FontSize="18" TextColor="#FFFFFF" FontAttributes="Bold" HorizontalOptions="FillAndExpand"  HorizontalTextAlignment="Center"/>
                    <Label Text="LBS" x:Name="massUnitLabelNormal" Grid.Column="4" FontSize="18" TextColor="#FFFFFF" FontAttributes="Bold" HorizontalOptions="FillAndExpand"  HorizontalTextAlignment="Center"/>
                </Grid>
               

                    <Grid
                        
                        IsClippedToBounds="True"
                        ColumnSpacing="0"
                        RowSpacing="0"
                        VerticalOptions="FillAndExpand"
                        HorizontalOptions="FillAndExpand">

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition
                                Width="25" />
                            <ColumnDefinition
                                Width="60" />
                            <ColumnDefinition
                                Width="0.77*" />
                            <ColumnDefinition
                                Width="25" />
                            <ColumnDefinition
                                Width="0.77*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition
                                Height="*" />
                           
                        </Grid.RowDefinitions>

                        <ffimageloading:CachedImage
                            Source="done2"
                            Margin="0,5,0,5"
                            FadeAnimationEnabled="False"
                            HeightRequest="20"
                            WidthRequest="20"
                            Aspect="AspectFit"
                            Grid.Row="0"
                            Grid.Column="0"
                            HorizontalOptions="Center"
                            VerticalOptions="Center"
                            ErrorPlaceholder="backgroundblack"
                            IsVisible="{Binding IsFinished}" />
                        <ffimageloading:CachedImage
                            Source="deleteset"
                            Margin="0,5,0,5"
                            HeightRequest="20"
                            FadeAnimationEnabled="False"
                            WidthRequest="20"
                            Aspect="AspectFit"
                            ErrorPlaceholder="backgroundblack"
                            Grid.Row="0"
                            Grid.Column="0"
                            HorizontalOptions="Center"
                            VerticalOptions="Center"
                            IsVisible="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}" >
                            </ffimageloading:CachedImage>
                       <t:DrMuscleButton
                            Margin="0"
                            Grid.Row="0"
                            Grid.Column="0"
                            BackgroundColor="Transparent"
                            Clicked="DeleteSetTapGestureRecognizer_TappedNormal"
                            IsVisible="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}" >
                            </t:DrMuscleButton>
                        <Label
                            Text="{Binding SetNo}"
                            Grid.Row="0"
                            Grid.Column="1"
                            VerticalOptions="Center"
                            VerticalTextAlignment="Center"
                            HorizontalOptions="FillAndExpand"
                            HorizontalTextAlignment="Center"
                            FontSize="21"
                            TextColor="#AAFFFFFF" />

                        <StackLayout Grid.Column="2" Grid.Row="0"  Margin="10,3,10,3">
                            <StackLayout.Triggers>
                            <DataTrigger
                                    TargetType="StackLayout"
                                    Binding="{Binding BodypartId}"
                                    Value="12">
                                    <Setter
                                        Property="IsVisible"
                                        Value="False" />
                                </DataTrigger>

                        </StackLayout.Triggers>

                        <Frame
                         Grid.Row="0"
                         Grid.Column="2"
                         HasShadow="False"
                         CornerRadius="10"
                         BorderColor="Transparent"
                         BackgroundColor="{Binding BackColor}"
                         Padding="0"
                         HeightRequest="50"
                         WidthRequest="100">
                            <t:WorkoutEntry
                                Grid.Row="0"
                                Grid.Column="2"
                                Text="{Binding Reps}"
                                x:Name="RepsEntryNormal"
                                HorizontalTextAlignment="Center"
                                VerticalOptions="Center"
                                Keyboard="Numeric"
                                HorizontalOptions="FillAndExpand"
                                FontSize="21"
                                MaxLength="4"
                                TextChanged="RepsEntryNormal_TextChanged"
                                IsReadOnly="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}"
                                TextColor="#AAFFFFFF" >
                                <t:WorkoutEntry.Triggers>
                                    <MultiTrigger
                                    TargetType="t:WorkoutEntry">
                                        <MultiTrigger.Conditions>
                                            <BindingCondition
                                            Binding="{Binding IsMaxChallenge}"
                                            Value="True" />
                                            <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="True" />
                                        </MultiTrigger.Conditions>
                                        <Setter
                                        Property="IsVisible"
                                        Value="true" />
                                    </MultiTrigger>
                                </t:WorkoutEntry.Triggers>
                            </t:WorkoutEntry>
                        </Frame>
                        <Label
                            Text="Max"
                            HorizontalTextAlignment="Center"
                            VerticalOptions="Center"
                            HorizontalOptions="FillAndExpand"
                            FontSize="21"
                            IsVisible="false"
                            TextColor="#AAFFFFFF"         >
                            <Label.Triggers>
                               
                                <MultiTrigger
                                    TargetType="Label">
                                    <MultiTrigger.Conditions>
                                        <BindingCondition
                                            Binding="{Binding IsMaxChallenge}"
                                            Value="True" />
                                        <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="True" />
                                    </MultiTrigger.Conditions>
                                    <Setter
                                        Property="IsVisible"
                                        Value="false" />
                                </MultiTrigger>
                            </Label.Triggers>
                        </Label>
                               </StackLayout>
<!--Cardio for reps-->
                        <StackLayout Grid.Column="2" Grid.Row="0" IsVisible="false" Margin="10,3,10,4"  >
                            <StackLayout.Triggers>
                            <DataTrigger
                                    TargetType="StackLayout"
                                    Binding="{Binding BodypartId}"
                                    Value="12">
                                    <Setter
                                        Property="IsVisible"
                                        Value="True" />
                                </DataTrigger>

                        </StackLayout.Triggers>
                        <Grid >
                            <Frame
                           Grid.Row="0"
                           Grid.Column="2"
                           HasShadow="False"
                           CornerRadius="10"
                           BorderColor="Transparent"
                           BackgroundColor="{Binding BackColor}"
                           Padding="0"
                           HeightRequest="50"
                           WidthRequest="100">
                                <t:WorkoutEntry
                            Text="{Binding RepsCardio}"                            
                            x:Name="RepsCardioEntry2"
                            HorizontalTextAlignment="Center"
                            VerticalOptions="Center"
                            Keyboard="Numeric"
                            HorizontalOptions="FillAndExpand"
                            FontSize="21"
                            MaxLength="6"
                            TextChanged="RepsEntry_TextChanged"
                            TextColor="#AAFFFFFF">
                                    <!--<t:WorkoutEntry.Triggers>
                                <DataTrigger
                                    TargetType="t:WorkoutEntry"
                                    Binding="{Binding IsMaxChallenge}"
                                    Value="true">
                                    <Setter
                                        Property="IsVisible"
                                        Value="false" />
                                </DataTrigger>
                            </t:WorkoutEntry.Triggers>-->
                                </t:WorkoutEntry>
                            </Frame>
                            <t:WorkoutEntry
                            Text="Max"
                            HorizontalTextAlignment="Center"
                            x:Name="RepsCardioEntry3"
                            VerticalOptions="Center"
                            Keyboard="Numeric"
                            IsVisible="false"
                            HorizontalOptions="FillAndExpand"
                            FontSize="21"
                            TextChanged="RepsEntry_TextChanged"
                            BackgroundColor="{Binding BackColor}"
                            TextColor="#AAFFFFFF">
                            <!--<t:WorkoutEntry.Triggers>
                                <DataTrigger
                                    TargetType="t:WorkoutEntry"
                                    Binding="{Binding IsMaxChallenge}"
                                    Value="true">
                                    <Setter Property="IsReadOnly" Value="True" />
                                    <Setter
                                        Property="IsVisible"
                                        Value="true" />
                                </DataTrigger>
                            </t:WorkoutEntry.Triggers>-->
                        </t:WorkoutEntry>
                        <Label
                            HorizontalTextAlignment="Center"
                            VerticalOptions="FillAndExpand"
                            HorizontalOptions="FillAndExpand"
                            FontSize="21"
                            BackgroundColor="Transparent"
                            >
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped_3" />
                            </Label.GestureRecognizers>
                        </Label>
                            </Grid>
                        </StackLayout><Label
                            Text="*"
                            Grid.Row="0"
                            Margin="0,2,0,0"
                            Grid.Column="3"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            VerticalTextAlignment="Center"
                            VerticalOptions="Center"
                            FontSize="21"
                            TextColor="#AAFFFFFF" />

                    <Frame Grid.Row="0"
                           Grid.Column="4"
                           HasShadow="False"
                           CornerRadius="10"
                           BorderColor="Transparent"
                           BackgroundColor="{Binding BackColor}"
                           Padding="0"
                           WidthRequest="100"
                           HeightRequest="50">
                        <Grid>
                            <t:WorkoutEntry x:Name="WeightEntryNormal"
                                            Grid.Row="0"
                                            Grid.Column="4"
                                            Keyboard="Numeric"
                                            VerticalTextAlignment="Center"
                                            VerticalOptions="Center"
                                            Text="{Binding WeightSingal}"
                                            HorizontalOptions="FillAndExpand"
                                            HorizontalTextAlignment="Center"
                                            TextChanged="WeightEntryNormal_TextChanged"
                                            IsReadOnly="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}"
                                            FontSize="21"
                                            TextColor="#AAFFFFFF">
                                <t:WorkoutEntry.Triggers>
                                    <DataTrigger TargetType="t:WorkoutEntry"
                                                 Binding="{Binding IsBodyweight}"
                                                 Value="true">
                                        <Setter Property="IsReadOnly"
                                                Value="True" />
                                    </DataTrigger>
                                    <DataTrigger TargetType="t:WorkoutEntry"
                                                 Binding="{Binding BodypartId}"
                                                 Value="12">
                                        <Setter Property="IsVisible"
                                                Value="false" />
                                    </DataTrigger>
                                </t:WorkoutEntry.Triggers>
                            </t:WorkoutEntry>
                            <t:WorkoutEntry x:Name="SpeedEntryNormal"
                                            Grid.Row="0"
                                            Grid.Column="4"
                                            Margin="10,3,10,4"
                                            Keyboard="Numeric"
                                            VerticalTextAlignment="Center"
                                            VerticalOptions="Center"
                                            IsVisible="false"
                                            Text="{Binding Speed}"
                                            HorizontalOptions="FillAndExpand"
                                            HorizontalTextAlignment="Center"
                                            TextChanged="SpeedEntryNormal_TextChanged"
                                            IsReadOnly="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}"
                                            FontSize="21"
                                            TextColor="#AAFFFFFF">
                                <t:WorkoutEntry.Triggers>
                                    <DataTrigger TargetType="t:WorkoutEntry"
                                                 Binding="{Binding BodypartId}"
                                                 Value="12">
                                        <Setter Property="IsVisible"
                                                Value="True" />
                                    </DataTrigger>
                                </t:WorkoutEntry.Triggers>
                            </t:WorkoutEntry>
                        </Grid>
                    </Frame>
                </Grid>
                    <Frame Padding="10"  
                          HasShadow="False"
                            CornerRadius="6" BorderColor="Transparent"
                            Margin="0,20,0,5" HorizontalOptions="FillAndExpand" BackgroundColor="{Binding BackColor}" IsVisible="false">
                        <Frame.Triggers>
                            <MultiTrigger
                                TargetType="Frame">
                                <MultiTrigger.Conditions>
                                    <BindingCondition
                                        Binding="{Binding IsLastSet}"
                                        Value="True" />
                                    <BindingCondition
                                        Binding="{Binding IsFinished}"
                                        Value="True" />
                                    <BindingCondition   
                                        Binding="{Binding IsExerciseFinished}"
                                        Value="False" />
                                </MultiTrigger.Conditions>
                                <Setter
                                    Property="IsVisible"
                                    Value="true" />
                            </MultiTrigger>
                                
                        </Frame.Triggers>
                        <Label
                           
                            
                            
                            Text="All sets done—congrats!"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            FontAttributes="Italic"
                            FontSize="21"
                            TextColor="White">
                            
                        </Label>
                    </Frame>
                <Frame
                            x:Name="FinishExerciseNormal"
                            BorderColor="Transparent"
                            Margin="3,10,3,0"
                            IsVisible="false"
                            IsClippedToBounds="true"
                            CornerRadius="6"
                            HorizontalOptions="FillAndExpand"
                            HeightRequest="72"
                            Padding="0"
                            Style="{StaticResource GradientFrameStyleGreen}">
                    <t:DrMuscleButton
                                Text="Finish exercise"
                                TextColor="#0C2432"
                                Clicked="FinishedExercise_ClickedNormal"
                                CommandParameter="{Binding .}"
                                BackgroundColor="Transparent"
                                FontSize="21"
                                FontAttributes="Bold">
                                <t:DrMuscleButton.Triggers>

                                    <DataTrigger
                                        TargetType="t:DrMuscleButton"
                                        Binding="{Binding IsFirstSide}"
                                        Value="true">
                                        <Setter
                                            Property="Text"
                                            Value="Finish side 1" />
                                    </DataTrigger>
                                    <DataTrigger
                                        TargetType="t:DrMuscleButton"
                                        Binding="{Binding IsFirstSide}"
                                        Value="false">
                                        <Setter
                                            Property="Text"
                                            Value="Finish exercise" />
                                    </DataTrigger>
                                    <DataTrigger
                                        TargetType="t:DrMuscleButton"
                                        Binding="{Binding IsExerciseFinished}"
                                        Value="true">
                                        <Setter
                                            Property="Text"
                                            Value="Save" />
                                    </DataTrigger>
                                </t:DrMuscleButton.Triggers>
                            </t:DrMuscleButton>
                            <Frame.Triggers>
                                <MultiTrigger
                                    TargetType="Frame">
                                    <MultiTrigger.Conditions>
                                        <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                                        <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="True" />
                                    </MultiTrigger.Conditions>
                                    <Setter
                                        Property="IsVisible"
                                        Value="true" />
                                </MultiTrigger>
                            <DataTrigger
                                        TargetType="Frame"
                                        Binding="{Binding IsExerciseFinished}"
                                        Value="true">
                                    </DataTrigger>
                            </Frame.Triggers>
                        </Frame>
                        <Grid
                            
                            
                            ColumnSpacing="8"
                            Margin="0,10,0,20"
                            HorizontalOptions="FillAndExpand"
                             HeightRequest="60" IsVisible="False">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.Triggers>
                                <MultiTrigger
                                    TargetType="Grid">
                                    <MultiTrigger.Conditions>
                                        <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />                                                                             
                                        <!--<BindingCondition
                                            Binding="{Binding IsExerciseFinished}"
                                            Value="False" />-->
                                    </MultiTrigger.Conditions>
                                    <Setter
                                        Property="IsVisible"
                                        Value="true" />
                                </MultiTrigger>
                            <MultiTrigger
                                    TargetType="Grid">
                                    <MultiTrigger.Conditions>
                                        <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                                        <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="True" />
                                    </MultiTrigger.Conditions>
                                    <Setter
                                        Property="Margin"
                                        Value="0,10,0,20" />
                                </MultiTrigger>
                            </Grid.Triggers>
                            <t:DrMuscleButton
                            Grid.Column="0"
                            IsVisible="true"
                            Text="Add set"
                            TextColor="White"
                            Clicked="AddSet_ClickedNormal"
                            CommandParameter="{Binding .}"
                            BackgroundColor="Transparent"
                            HeightRequest="60"
                            BorderColor="#ECFF92"
                            BorderWidth="1"
                            CornerRadius="6"
                            Margin="3,0,3,0">
                            <t:DrMuscleButton.Triggers>
                                <MultiTrigger
                                    TargetType="t:DrMuscleButton">
                                    <MultiTrigger.Conditions>
                                        <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                                         <BindingCondition
                                            Binding="{Binding IsFirstSetFinished}"
                                            Value="True" />
                                        <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="True" />
                                        
                                    </MultiTrigger.Conditions>
                                    <Setter
                                        Property="Grid.ColumnSpan"
                                        Value="2" />
                                    
                                    
                                </MultiTrigger>

                            </t:DrMuscleButton.Triggers>
                        </t:DrMuscleButton>
<t:DrMuscleButton
                            Grid.Column="1"
                            IsVisible="false"
                            Text="Skip exercise"
                            Margin="3,0,3,0"
                            TextColor="White"
                            Clicked="SkipExercise_ClickedNormal"
                            CommandParameter="{Binding .}"
                            BackgroundColor="Transparent"
                            HeightRequest="60"
                            BorderColor="#ECFF92"
                            BorderWidth="1"
                            CornerRadius="6"
                            >
                            <t:DrMuscleButton.Triggers>
                                <MultiTrigger
                                    TargetType="t:DrMuscleButton">
                                    <MultiTrigger.Conditions>
                                         <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                                        <BindingCondition
                                            Binding="{Binding IsFirstSetFinished}"
                                            Value="False" />
                                        <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="False" />
                                    </MultiTrigger.Conditions>
                                    <Setter
                                        Property="IsVisible"
                                        Value="true" />
                                    <Setter Property="FlexLayout.Grow" Value="1" />
                                </MultiTrigger>
                               
                            </t:DrMuscleButton.Triggers>

                        </t:DrMuscleButton>
                             <t:DrMuscleButton
                            Grid.Column="1"
                            IsVisible="false"
                            Text="Finish exercise"
                            TextColor="White"
                            Clicked="UnFinishedExercise_Clicked1Normal"
                            CommandParameter="{Binding .}"
                            BackgroundColor="Transparent"
                            HeightRequest="60"
                            BorderColor="#ECFF92"
                             Margin="3,0,3,0"
                            BorderWidth="1"
                            CornerRadius="6">
                            <t:DrMuscleButton.Triggers>
                                <MultiTrigger
                                    TargetType="t:DrMuscleButton">
                                    <MultiTrigger.Conditions>
                                        <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                                        <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="false" />
                                        <BindingCondition
                                            Binding="{Binding IsExerciseFinished}"
                                            Value="True" />

                                    </MultiTrigger.Conditions>
                                    <Setter
                                        Property="Text"
                                        Value="Save" />
                                </MultiTrigger>
                                <MultiTrigger
                                    TargetType="t:DrMuscleButton">
                                    <MultiTrigger.Conditions>
                                        <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                                         <BindingCondition
                                            Binding="{Binding IsFirstSetFinished}"
                                            Value="True" />
                                        <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="False" />
                                        <BindingCondition
                                            Binding="{Binding IsFirstSide}"
                                            Value="False" />
                                        <BindingCondition
                                            Binding="{Binding IsExerciseFinished}"
                                            Value="True" />
                                    </MultiTrigger.Conditions>
                                    <Setter
                                        Property="IsVisible"
                                        Value="true" />
                                    <Setter
                                        Property="Text"
                                        Value="Save" />
                                    <Setter Property="FlexLayout.Grow" Value="1" />
                                     
                                </MultiTrigger>
                                <MultiTrigger
                                    TargetType="t:DrMuscleButton">
                                    <MultiTrigger.Conditions>
                                        <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                                         <BindingCondition
                                            Binding="{Binding IsFirstSetFinished}"
                                            Value="True" />
                                        <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="False" />
                                        <BindingCondition
                                            Binding="{Binding IsFirstSide}"
                                            Value="False" />
                                        <BindingCondition
                                            Binding="{Binding IsExerciseFinished}"
                                            Value="False" />
                                    </MultiTrigger.Conditions>
                                    <Setter
                                        Property="IsVisible"
                                        Value="true" />
                                    <Setter
                                        Property="Text"
                                        Value="Finish exercise" />
                                    <Setter Property="FlexLayout.Grow" Value="1" />
                                     
                                </MultiTrigger>
                                <MultiTrigger
                                    TargetType="t:DrMuscleButton">
                                    <MultiTrigger.Conditions>
                                        <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                                         <BindingCondition
                                            Binding="{Binding IsFirstSetFinished}"
                                            Value="True" />
                                        <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="False" />
                                        <BindingCondition
                                            Binding="{Binding IsFirstSide}"
                                            Value="True" />
                                    </MultiTrigger.Conditions>
                                    <Setter
                                        Property="IsVisible"
                                        Value="true" />
                                    <Setter Property="FlexLayout.Grow" Value="1" />
                                    <Setter
                                        Property="Text"
                                        Value="Finish side 1" />
                                    
                                </MultiTrigger>
                            </t:DrMuscleButton.Triggers>

                        </t:DrMuscleButton>

                            
                        </Grid>
               
            </StackLayout>
        </Grid>



   </StackLayout>
</ContentView>
