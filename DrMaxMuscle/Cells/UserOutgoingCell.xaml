<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             x:Class="DrMaxMuscle.Cells.UserOutgoingCell">
    <Grid
    Rotation="180"
    FlowDirection="LeftToRight"
    ColumnSpacing="5"
    RowSpacing="0"
    Padding="5">
        <Grid.ColumnDefinitions>
            <ColumnDefinition
            Width="20">
            </ColumnDefinition>
            <ColumnDefinition
            Width="*">
            </ColumnDefinition>
            <ColumnDefinition
            Width="40">
            </ColumnDefinition>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition
            Height="auto">
            </RowDefinition>
            <RowDefinition
            Height="*">
            </RowDefinition>
        </Grid.RowDefinitions>
        <ffimageloading:CachedImage
        Grid.Row="0"
        IsVisible="false"
        Grid.Column="2"
        Grid.RowSpan="2"
        x:Name="imgOutProfilePic"
        HorizontalOptions="Center"
        VerticalOptions="Start"
        WidthRequest="35"
        HeightRequest="35"
        DownsampleToViewSize="true"
        LoadingPlaceholder="backgroundblack.png"
            ErrorPlaceholder="backgroundblack.png"
        Source="{Binding ProfileUrl}">
        </ffimageloading:CachedImage>

        <Border StrokeThickness="0" StrokeShape="RoundRectangle 12,0,12,12"
                Background="White" 
                VerticalOptions="Start"
                Grid.Row="1"
                Grid.Column="1"
                HorizontalOptions="End"
                Margin="0,0,-40,0"
                Padding="15">
            <Border.Shadow>
                <Shadow Brush="DarkGray"
                    Opacity="0.5"
                    Radius="5"
                    Offset="2,2"/>
            </Border.Shadow>

            <StackLayout
Padding="0"
Spacing="4"
Margin="0">
                <controls:ExtendedLabel
    Grid.Row="1"
    Grid.Column="1"
    x:Name="lblInMessage"
    HorizontalOptions="Start"
    HorizontalTextAlignment="{OnPlatform Android = 'Start',iOS='End'}"
    VerticalOptions="End"
    VerticalTextAlignment="End"
    TextColor="Black"
    FontSize="17"
    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
    Text="{Binding Message}" >
                </controls:ExtendedLabel>
                <Label
    x:Name="timeStampLabel"
    FontSize="Micro"
    HorizontalOptions="End"
    HorizontalTextAlignment="End"
    VerticalTextAlignment="End"
    VerticalOptions="End"
    Text="{Binding TImeAgo}"
    TextColor="Gray"/>
            </StackLayout>
            
        </Border>

        <!--<Frame
            HasShadow="True"
            BorderColor="White"
            BackgroundColor="White"
        VerticalOptions="Start"
        Grid.Row="1"
        Grid.Column="1"
        IsClippedToBounds="False"
        HorizontalOptions="End"
        Margin="0,0,-40,0"
        Padding="15"
        CornerRadius="12"  >
           
            <StackLayout
            Padding="0"
            Spacing="4"
            Margin="0">
                <controls:ExtendedLabel
                Grid.Row="1"
                Grid.Column="1"
                x:Name="lblInMessage"
                HorizontalOptions="Start"
                HorizontalTextAlignment="{OnPlatform Android = 'Start',iOS='End'}"
                VerticalOptions="End"
                VerticalTextAlignment="End"
                TextColor="Black"
                FontSize="17"
                LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                Text="{Binding Message}" >
                </controls:ExtendedLabel>
                <Label
                x:Name="timeStampLabel"
                FontSize="Micro"
                HorizontalOptions="End"
                HorizontalTextAlignment="End"
                VerticalTextAlignment="End"
                VerticalOptions="End"
                Text="{Binding TImeAgo}"
                TextColor="Gray"/>
            </StackLayout>
        </Frame>-->

    </Grid>
</ContentView>
