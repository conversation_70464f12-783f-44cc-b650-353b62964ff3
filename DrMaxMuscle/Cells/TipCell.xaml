<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             x:Class="DrMaxMuscle.Cells.TipCell">
    <controls:CustomFrame
                    
                    Margin="10,1,10,10"
                    Padding="10,10,10,10"
                    CornerRadius="12"
                        BorderColor="Transparent"
HasShadow="true">
<controls:CustomFrame.Shadow>
    <Shadow Brush="Black"
              Offset="0,0"
              Radius="5"
              Opacity="0.2" />     
</controls:CustomFrame.Shadow>

        <StackLayout Padding="10,15,10,15">
            <Grid RowSpacing="0"
                                >
                <Grid.RowDefinitions>
                    <RowDefinition
                                        Height="Auto" />
                    <RowDefinition
                                        Height="Auto" />

                    <RowDefinition
                                        Height="Auto" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>

                    <ColumnDefinition
                                        Width="*" />
                </Grid.ColumnDefinitions>

                <StackLayout
                                    Grid.Column="0"
                                    Grid.Row="0"
                                    >
                    <Label
                                        Text="{Binding Question}"
                                        
        FontAttributes="Bold"
FontSize="20"
                    Margin="0,0,0,9"
        Style="{StaticResource LabelStyle}"
        TextColor="Black"
                                         />
                    <Label
                    Text="{Binding Answer}"
                    FontSize="17"
                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                    TextColor="#AA000000"/>
                </StackLayout>
            </Grid>
        </StackLayout>
    </controls:CustomFrame>
</ContentView>
