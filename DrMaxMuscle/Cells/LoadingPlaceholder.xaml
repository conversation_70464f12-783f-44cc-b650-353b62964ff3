<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             x:Class="DrMaxMuscle.Cells.LoadingPlaceholder">
    <!-- Skeleton Loader Card -->
    <controls:CustomFrame 
        Padding="22,26"
        CornerRadius="12"
        HasShadow="True"
        BackgroundColor="White">
        <Grid RowSpacing="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid Grid.Row="0" ColumnSpacing="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width=".2*"/>
                    <ColumnDefinition Width=".8*"/>
                </Grid.ColumnDefinitions>
                <BoxView
                   Grid.Column="0"
                   x:Name="profile"
                   HorizontalOptions="Start"
                   HeightRequest="30"
                   WidthRequest="30"
                   CornerRadius="5"
                   Color="LightGray"/>
                <BoxView 
                    Grid.Column="1"
                    x:Name="title"
                    HeightRequest="7"
                    HorizontalOptions="FillAndExpand"
                    VerticalOptions="CenterAndExpand"
                    Color="LightGray"
                    />
            </Grid>
            <!-- Second Line -->
            <BoxView
               Grid.Row="1"
               x:Name="line1"
               HeightRequest="4"
               HorizontalOptions="FillAndExpand"
               Color="LightGray"
               />

            <BoxView
               Grid.Row="2"
               x:Name="line2"
               HeightRequest="4"
               HorizontalOptions="FillAndExpand"
               Color="LightGray"
               />
            <!-- Third Line -->
            <BoxView
               Grid.Row="3"
               x:Name="line3"
               HeightRequest="4"
               HorizontalOptions="FillAndExpand"
               Color="LightGray"
               />
            <Grid 
               Grid.Row="4" 
               ColumnSpacing="4">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width=".5*"/>
                    <ColumnDefinition Width=".5*"/>
                </Grid.ColumnDefinitions>
                <BoxView
                   Grid.Column="0"
                   x:Name="btn1"
                   HorizontalOptions="CenterAndExpand"
                   VerticalOptions="CenterAndExpand"
                   HeightRequest="4"
                   CornerRadius="5"
                   WidthRequest="80"
                   Color="LightGray"/>
                <BoxView
                   Grid.Column="1"
                   x:Name="btn2"
                   HorizontalOptions="FillAndExpand"
                   VerticalOptions="CenterAndExpand"
                   HeightRequest="30"
                   CornerRadius="5"
                   Color="LightGray"/>
            </Grid>
        </Grid>
    </controls:CustomFrame>
</ContentView>
