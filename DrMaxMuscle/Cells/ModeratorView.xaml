<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
            xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
            xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             x:Class="DrMaxMuscle.Cells.ModeratorView">
    <Grid
    Rotation="180"
    FlowDirection="LeftToRight"
    ColumnSpacing="5"
    RowSpacing="0"
    Margin="0,10,10,10"
    Padding="5">
        <Grid.ColumnDefinitions>
            <ColumnDefinition
            Width="40">
            </ColumnDefinition>
            <ColumnDefinition
            Width="*">
            </ColumnDefinition>
            <ColumnDefinition
            Width="20">
            </ColumnDefinition>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition
            Height="auto">
            </RowDefinition>
            <RowDefinition
            Height="*">
            </RowDefinition>
        </Grid.RowDefinitions>
        <Image
            
        Grid.Row="0"
        Grid.Column="0"
        Grid.RowSpan="2"
        x:Name="imgInProfilePic"
            Aspect="AspectFit"
        HorizontalOptions="Center"
        VerticalOptions="Start"
        WidthRequest="35"
            Source="icon_1.png"
        HeightRequest="35">
        </Image>
        <Frame
        Padding="0"
            BackgroundColor="Transparent"
        Grid.Row="0"
        Grid.Column="0"
        Grid.RowSpan="2"
        HorizontalOptions="Center"
        VerticalOptions="Start"
        WidthRequest="35"
        HeightRequest="35"
        HasShadow="false"
        x:Name="FrmProfile">
            <Label
            x:Name="LblProfileText"
            HorizontalOptions="Center"
            VerticalOptions="Center"
            HorizontalTextAlignment="Center"
            VerticalTextAlignment="Center"
            Text=""
            TextColor="White"
            FontSize="20" />
        </Frame>

        <Border StrokeThickness="0" StrokeShape="RoundRectangle 0,12,12,12"  Style="{StaticResource GradientBorderStyleBlue}"
                VerticalOptions="Start" Stroke="Transparent"
                Grid.Row="1"
                Grid.Column="1"
                HorizontalOptions="End"
                Margin="{OnPlatform Android='-40,10,15,0', iOS='-40,20,25,0'}"
                Padding="15" >
            <Border.Shadow>
                <Shadow Brush="Black"
            Opacity="0.5"
            Radius="5"
            Offset="2,2"/>
            </Border.Shadow>

            <VerticalStackLayout
                    Spacing="4"
                    Padding="0"
                    Margin="0">
                <controls:ExtendedLightBlueLabel
                    VerticalTextAlignment="End"
                    x:Name="lblOutMessage"
                    HorizontalOptions="End"
                    HorizontalTextAlignment="Start"
                    TextColor="White"
                    FontSize="17"
                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                    Text="{Binding Message}">
                </controls:ExtendedLightBlueLabel>

                <Label
                    FontSize="Micro"
                    HorizontalOptions="End"
                    HorizontalTextAlignment="End"
                    Text="{Binding TImeAgo}"
                    VerticalOptions="End"
                    VerticalTextAlignment="End"
                    TextColor="LightGray">
                </Label>
            </VerticalStackLayout>
            
        </Border>



        <!--<HorizontalStackLayout
        Grid.Row="1"
        Margin="-40,10,0,0"
        Grid.Column="1"
        VerticalOptions="End"
        IsClippedToBounds="true"
        HorizontalOptions="Start">-->
            <!--<Frame
            VerticalOptions="Start"
            Grid.Row="1"
            Grid.Column="1"
            IsClippedToBounds="False"
            HorizontalOptions="End"
            Margin="{OnPlatform Android='-40,10,15,0', iOS='-40,20,25,0'}"
            Padding="15"
            CornerRadius="12"
                BorderColor="Transparent"
                HasShadow="True"
            Style="{StaticResource GradientFrameStyleBlue}">

                <VerticalStackLayout
                Spacing="4"
                Padding="0"
                Margin="0">
                    <controls:ExtendedLightBlueLabel
                    VerticalTextAlignment="End"
                    x:Name="lblOutMessage"
                    HorizontalOptions="End"
                    HorizontalTextAlignment="Start"
                    TextColor="White"
                    FontSize="17"
                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                    Text="{Binding Message}">

                        
                    </controls:ExtendedLightBlueLabel>

                    <Label
                    FontSize="Micro"
                    HorizontalOptions="End"
                    HorizontalTextAlignment="End"
                    Text="{Binding TImeAgo}"
                    VerticalOptions="End"
                    VerticalTextAlignment="End"
                    TextColor="LightGray">
                    </Label>
                </VerticalStackLayout>

                --><!--<pancakeView:PancakeView.Shadow>
                    <pancakeView:DropShadow
                    Color="{OnPlatform Android='#D1D5D8',iOS='Gray'}"
                    Opacity="0.5"
                    BlurRadius="{x:OnPlatform Android='3',iOS='3'}" />
                </pancakeView:PancakeView.Shadow>--><!--
            </Frame>-->

            <!--Share button to share AI Response.-->
            <ImageButton
                 Grid.Row="1"
 Grid.Column="2"
            x:Name="ImageButtonShare"
            Source="ic_share"
            HorizontalOptions="End"
            VerticalOptions="Center"
            Padding="5"
                Margin="{OnPlatform Android='0,10,0,0', iOS= '0,15,0,0'}"
            HeightRequest="{OnPlatform Android='36', iOS= '30'}"
            WidthRequest="{OnPlatform Android='36', iOS= '30'}"
            BackgroundColor="Transparent"
            Clicked="ImageButtonShare_Clicked"
            IsVisible="False"/>
        <!--</HorizontalStackLayout>-->
        <HorizontalStackLayout
        Grid.Row="0"
        Margin="0,8,0,0"
        Grid.Column="1"
        VerticalOptions="Center"
        HorizontalOptions="Start">
            <Label
            x:Name="nameLabel"
            FontAttributes="Bold"
            HorizontalOptions="End"
            HorizontalTextAlignment="End"
            Text="{Binding Nickname}"
            TextColor="{x:Static app:AppThemeConstants.OffBlackColor}">
                <Label.GestureRecognizers>
                    <TapGestureRecognizer
                    Tapped="Username_Tapped" />
                </Label.GestureRecognizers>
            </Label>

        </HorizontalStackLayout>
    </Grid>
</ContentView>
