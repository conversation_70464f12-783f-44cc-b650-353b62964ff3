﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             x:Class="DrMaxMuscle.Cells.SummaryRest">
    <controls:CustomFrame
                    x:Name="WeightProgress2"
                    Margin="10,1,10,10"
                    Padding="10,10,10,10"
                    CornerRadius="12"
                    BorderColor="Transparent"
HasShadow="true">
<controls:CustomFrame.Shadow>
    <Shadow Brush="Black"
              Offset="0,0"
              Radius="5"
              Opacity="0.2" />
</controls:CustomFrame.Shadow>
        <StackLayout Spacing="0">
            <Label
        x:Name="LblAnswer"
        Text="{Binding Question}"
        IsVisible="true"
        FontAttributes="Bold"
FontSize="20"
        Style="{StaticResource LabelStyle}"
        TextColor="Black"
 Padding="10,10,0,5"
        Margin="0" />
            <Grid
                Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Padding="10,15,10,15">
                <Grid.RowDefinitions>
                    <RowDefinition
            Height="*" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition
            Width="*" />
                    <ColumnDefinition
            Width="*" />
                    <ColumnDefinition
            Width="*" />
                </Grid.ColumnDefinitions>

               
                <StackLayout
        Grid.Row="0"
        HorizontalOptions="FillAndExpand"
        Grid.Column="0">
                    <Image
            Source="nextworkout.png"
            Aspect="AspectFit"
            HeightRequest="32"
            HorizontalOptions="CenterAndExpand" />
                    <Label
            Text="{Binding LbsLiftedText}"
            x:Name="LblBodyweight"
            FontAttributes="Bold"
FontSize="17"
            Style="{StaticResource LabelStyle}"
            HorizontalOptions="Center"
            HorizontalTextAlignment="Center"
            TextColor="Black" />
                    <Label
            Text=""
            FontSize="17"
            TextColor="#AA000000"
            HorizontalOptions="CenterAndExpand"
            HorizontalTextAlignment="Center" />
                </StackLayout>
                <StackLayout
        Grid.Row="0"
        HorizontalOptions="FillAndExpand"
        Grid.Column="1">
                    <Image
            Source="restrecovery.png"
            Aspect="AspectFit"
            HeightRequest="32"
            HorizontalOptions="CenterAndExpand" />
                    <Label
            Text="{Binding SinceTime}"
            FontAttributes="Bold"
FontSize="17"
            Style="{StaticResource LabelStyle}"
            HorizontalOptions="Center"
            HorizontalTextAlignment="Center"
            TextColor="Black" />
                    <Label
            Text="{Binding LastWorkoutText}"
            FontSize="17"
            TextColor="#AA000000"
            HorizontalOptions="Center"
            HorizontalTextAlignment="Center" />
                </StackLayout>
                
                <StackLayout
            Grid.Row="0"
            IsVisible="{Binding IsLastVisible}"
            HorizontalOptions="FillAndExpand"
            Grid.Column="2">
                    <Image
                Source="workoutdone.png"
                Aspect="AspectFit"
                HeightRequest="32"
                HorizontalOptions="CenterAndExpand" />
                    <Label
                Text="{Binding LevelUpMessage}"
                FontAttributes="Bold"
FontSize="17"
                Style="{StaticResource LabelStyle}"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                TextColor="Black"/>
                    <Label
                Text="{Binding LevelUpText}"
                FontSize="17"
                TextColor="#AA000000"
                HorizontalOptions="CenterAndExpand"
                HorizontalTextAlignment="Center" />
                </StackLayout>

            </Grid>
        </StackLayout>
    </controls:CustomFrame>
</ContentView>
