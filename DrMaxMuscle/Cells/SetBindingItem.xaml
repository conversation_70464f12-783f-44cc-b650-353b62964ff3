﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:converters="clr-namespace:DrMaxMuscle.Convertors"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             x:Class="DrMaxMuscle.Cells.SetBindingItem">
     <StackLayout VerticalOptions="FillAndExpand" Spacing="1" Padding="15,10,15,10"
                         Margin="4,0,4,0">
                <StackLayout.Resources>
                    <converters:BoolInverter
                        x:Key="BoolInverterConverter" />
                </StackLayout.Resources>
        <ffimageloading:CachedImage x:Name="videoPlayerNormal" Source="{Binding VideoUrl}" ErrorPlaceholder="backgroundblack.png" HeightRequest="200" Aspect="AspectFit"  DownsampleToViewSize="True" HorizontalOptions="FillAndExpand" BackgroundColor="White" IsVisible="{Binding IsVideoUrlAvailable}">
                    </ffimageloading:CachedImage>
            </StackLayout>
</ContentView>
