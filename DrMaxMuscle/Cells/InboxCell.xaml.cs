using DrMaxMuscle.Constants;
using DrMaxMuscle.Helpers;

namespace DrMaxMuscle.Cells;

public partial class InboxCell : ContentView
{
    public InboxCell()
    {
        InitializeComponent();
    }
    protected override void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();
        try
        {

            var message = (DrMaxMuscle.Helpers.Messages)this.BindingContext;
            if (message == null)
                return;
            if (message.UserId.ToLower().Equals("<EMAIL>"))
            {
                imgInProfilePicFrame.IsVisible = true;
                imgInProfilePic.IsVisible = true;
                FrmProfile.IsVisible = false;
                imgInProfilePic.Source = Device.RuntimePlatform == Device.iOS ? "victoriaprofileround.png" : "victoriaprofileround.png";//"adminprofile.png";
                LblName.Text = $"Victoria, {message.NormalUSerName}";
                LblName.FontAttributes = FontAttributes.Bold;
                ContentGrid.BackgroundColor = Colors.Transparent;
                if (message.IsV1User)
                {
                    ContentGrid.BackgroundColor = AppThemeConstants.GreenTransparentColor;
                }
                else
                {
                    ContentGrid.BackgroundColor = Colors.Transparent;
                }
            }
            else
            {

                if (message.IsV1User)
                {
                    ContentGrid.BackgroundColor = AppThemeConstants.GreenTransparentColor;
                }
                else
                {
                    ContentGrid.BackgroundColor = Colors.Transparent;
                }
                imgInProfilePic.Source = null;
                imgInProfilePicFrame.IsVisible = false;
                imgInProfilePic.IsVisible = false;
                FrmProfile.IsVisible = true;
                LblName.FontAttributes = FontAttributes.Bold;
                if (message.IsBothReplied)
                    LblName.Text = $"{message.Nickname}, Victoria";
                else
                    LblName.Text = $"{message.Nickname}";
                LblProfileText.Text = message.Nickname.Length > 0 ? message.Nickname.Substring(0, 1).ToUpper() : "";
                //Color backColor = AppThemeConstants.RandomColor;
                //if (AppThemeConstants.ProfileColor.ContainsKey(message.UserId))
                //{
                //    FrmProfile.BackgroundColor = AppThemeConstants.ProfileColor[message.UserId];
                //}
                //else
                //{
                //    AppThemeConstants.ProfileColor.Add(message.UserId, backColor);
                //    FrmProfile.BackgroundColor = backColor;
                //}
            }

        }
        catch (Exception ex)
        {

        }
    }
}
