<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"

             x:Class="DrMaxMuscle.Cells.LinkCell">
    <Frame
    Margin="10,10,40,5"
    CornerRadius="12"
    x:Name="FrmContainer"
    Padding="20,12,20,12"
    HorizontalOptions="Start"
    BorderColor="#ffffff"
    HasShadow="False"
    Opacity="0"
    BackgroundColor="#ffffff">
        <controls:ExtendedLabelLink
        x:Name="LblAnswer"
        FontSize="17"
        LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
        TextColor="#26262B"
        Text="{Binding Question}"
        HorizontalOptions="Start"
        Margin="4,0" />
    </Frame>
</ContentView>
