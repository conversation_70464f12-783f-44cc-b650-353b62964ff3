﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Maui.Controls;

namespace DrMaxMuscle.Layout
{
    public class DrMuscleButton : Button
    {
        public DrMuscleButton() : base()
        {
            Clicked += DrM<PERSON><PERSON>Button_Clicked;
        }

        private void DrMuscleButton_Clicked(object sender, EventArgs e)
        {
            if (sender != null && sender is DrMuscleButton button && button.Handler != null && button.Handler.PlatformView != null)
            {
            }
            //Analytics.TrackEvent($"Button [{this.Text}] clicked");
        }

        protected override void OnHandlerChanged()
        {
            base.OnHandlerChanged();

            if (Handler == null) // The button is being removed
            {
                Clicked -= DrMuscleButton_Clicked; // Unsubscribe to prevent memory leaks
            }
        }
    }
}
