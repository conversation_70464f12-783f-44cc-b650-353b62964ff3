﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DrMaxMuscle.Resx {
    using System;
    using System.Reflection;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class AppResources {
        
        private static System.Resources.ResourceManager resourceMan;
        
        private static System.Globalization.CultureInfo resourceCulture;
        
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal AppResources() {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static System.Resources.ResourceManager ResourceManager {
            get {
                if (object.Equals(null, resourceMan)) {
                    System.Resources.ResourceManager temp = new System.Resources.ResourceManager("DrMaxMuscle.Resx.AppResources", typeof(AppResources).GetTypeInfo().Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        internal static string WelcomeTo {
            get {
                return ResourceManager.GetString("WelcomeTo", resourceCulture);
            }
        }
        
        internal static string DrMuslce {
            get {
                return ResourceManager.GetString("DrMuslce", resourceCulture);
            }
        }
        
        internal static string IHelpYouTransformYourBody {
            get {
                return ResourceManager.GetString("IHelpYouTransformYourBody", resourceCulture);
            }
        }
        
        internal static string ByLiftingWeightsUsingScience {
            get {
                return ResourceManager.GetString("ByLiftingWeightsUsingScience", resourceCulture);
            }
        }
        
        internal static string AndASmartProgramThatLevels {
            get {
                return ResourceManager.GetString("AndASmartProgramThatLevels", resourceCulture);
            }
        }
        
        internal static string IMLikeAPersonalTrainer {
            get {
                return ResourceManager.GetString("IMLikeAPersonalTrainer", resourceCulture);
            }
        }
        
        internal static string AlwaysUptoDateAndAvailableAnytimeAnywhere {
            get {
                return ResourceManager.GetString("AlwaysUptoDateAndAvailableAnytimeAnywhere", resourceCulture);
            }
        }
        
        internal static string HelpMeCustomizeYourProgramAreYouA {
            get {
                return ResourceManager.GetString("HelpMeCustomizeYourProgramAreYouA", resourceCulture);
            }
        }
        
        internal static string UpWithYouAutomatically {
            get {
                return ResourceManager.GetString("UpWithYouAutomatically", resourceCulture);
            }
        }
        
        internal static string AreYouMaleorWoman {
            get {
                return ResourceManager.GetString("AreYouMaleorWoman", resourceCulture);
            }
        }
        
        internal static string Man {
            get {
                return ResourceManager.GetString("Man", resourceCulture);
            }
        }
        
        internal static string Woman {
            get {
                return ResourceManager.GetString("Woman", resourceCulture);
            }
        }
        
        internal static string AlreadyHaveAnAccount {
            get {
                return ResourceManager.GetString("AlreadyHaveAnAccount", resourceCulture);
            }
        }
        
        internal static string ByContinuingYouAgreeToOur {
            get {
                return ResourceManager.GetString("ByContinuingYouAgreeToOur", resourceCulture);
            }
        }
        
        internal static string TermsOfUseLower {
            get {
                return ResourceManager.GetString("TermsOfUseLower", resourceCulture);
            }
        }
        
        internal static string And {
            get {
                return ResourceManager.GetString("And", resourceCulture);
            }
        }
        
        internal static string PrivacyPolicy {
            get {
                return ResourceManager.GetString("PrivacyPolicy", resourceCulture);
            }
        }
        
        internal static string ImNotLikeOtherApps {
            get {
                return ResourceManager.GetString("ImNotLikeOtherApps", resourceCulture);
            }
        }
        
        internal static string GotIt {
            get {
                return ResourceManager.GetString("GotIt", resourceCulture);
            }
        }
        
        internal static string CustomizingYourProgram {
            get {
                return ResourceManager.GetString("CustomizingYourProgram", resourceCulture);
            }
        }
        
        internal static string GotItYourProgramStart {
            get {
                return ResourceManager.GetString("GotItYourProgramStart", resourceCulture);
            }
        }
        
        internal static string LessThan1Year {
            get {
                return ResourceManager.GetString("LessThan1Year", resourceCulture);
            }
        }
        
        internal static string YearOrMore {
            get {
                return ResourceManager.GetString("YearOrMore", resourceCulture);
            }
        }
        
        internal static string YouHaveSetA {
            get {
                return ResourceManager.GetString("YouHaveSetA", resourceCulture);
            }
        }
        
        internal static string NewRecord {
            get {
                return ResourceManager.GetString("NewRecord", resourceCulture);
            }
        }
        
        internal static string PleaseEnterYourFirstnameSoICan {
            get {
                return ResourceManager.GetString("PleaseEnterYourFirstnameSoICan", resourceCulture);
            }
        }
        
        internal static string TapToEnterYourFirstName {
            get {
                return ResourceManager.GetString("TapToEnterYourFirstName", resourceCulture);
            }
        }
        
        internal static string Next {
            get {
                return ResourceManager.GetString("Next", resourceCulture);
            }
        }
        
        internal static string GotItPleaseChooseAGoalDontWorryYouCanForMan {
            get {
                return ResourceManager.GetString("GotItPleaseChooseAGoalDontWorryYouCanForMan", resourceCulture);
            }
        }
        
        internal static string FocusOnBuildingMuscle {
            get {
                return ResourceManager.GetString("FocusOnBuildingMuscle", resourceCulture);
            }
        }
        
        internal static string BuildMuscleAndBurnFat {
            get {
                return ResourceManager.GetString("BuildMuscleAndBurnFat", resourceCulture);
            }
        }
        
        internal static string FocusOnBurningFat {
            get {
                return ResourceManager.GetString("FocusOnBurningFat", resourceCulture);
            }
        }
        
        internal static string GotItPleaseChooseAGoalDontWorryLiftingWeightsForWoman {
            get {
                return ResourceManager.GetString("GotItPleaseChooseAGoalDontWorryLiftingWeightsForWoman", resourceCulture);
            }
        }
        
        internal static string FocusOnToningUp {
            get {
                return ResourceManager.GetString("FocusOnToningUp", resourceCulture);
            }
        }
        
        internal static string ToneUpAndSlimDown {
            get {
                return ResourceManager.GetString("ToneUpAndSlimDown", resourceCulture);
            }
        }
        
        internal static string FocusOnSlimmingDown {
            get {
                return ResourceManager.GetString("FocusOnSlimmingDown", resourceCulture);
            }
        }
        
        internal static string BuildMuscle {
            get {
                return ResourceManager.GetString("BuildMuscle", resourceCulture);
            }
        }
        
        internal static string BuildMuscleBurnFat {
            get {
                return ResourceManager.GetString("BuildMuscleBurnFat", resourceCulture);
            }
        }
        
        internal static string FatBurning {
            get {
                return ResourceManager.GetString("FatBurning", resourceCulture);
            }
        }
        
        internal static string YouSaidBigBigMenOftenWantSayTheyWantToGetRid {
            get {
                return ResourceManager.GetString("YouSaidBigBigMenOftenWantSayTheyWantToGetRid", resourceCulture);
            }
        }
        
        internal static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
        
        internal static string NoChooseOtherGoal {
            get {
                return ResourceManager.GetString("NoChooseOtherGoal", resourceCulture);
            }
        }
        
        internal static string YouSaidMidSizeMidSizeMenOthenSayTheyWantToGetFit {
            get {
                return ResourceManager.GetString("YouSaidMidSizeMidSizeMenOthenSayTheyWantToGetFit", resourceCulture);
            }
        }
        
        internal static string YouSaidSkinnySkinnyMenOftenHaveAHardTimeGainingWeight {
            get {
                return ResourceManager.GetString("YouSaidSkinnySkinnyMenOftenHaveAHardTimeGainingWeight", resourceCulture);
            }
        }
        
        internal static string YesIHaveAHardTimeGaining {
            get {
                return ResourceManager.GetString("YesIHaveAHardTimeGaining", resourceCulture);
            }
        }
        
        internal static string NoIDontHaveAHardTime {
            get {
                return ResourceManager.GetString("NoIDontHaveAHardTime", resourceCulture);
            }
        }
        
        internal static string NotSureIveNeverLiftedBefore {
            get {
                return ResourceManager.GetString("NotSureIveNeverLiftedBefore", resourceCulture);
            }
        }
        
        internal static string GotItSkinnyMenOftenSayTheyWantToPutOnLeanMassWhileKeepingMyAbsDefined {
            get {
                return ResourceManager.GetString("GotItSkinnyMenOftenSayTheyWantToPutOnLeanMassWhileKeepingMyAbsDefined", resourceCulture);
            }
        }
        
        internal static string YouSaidManWhatsYourBodyType {
            get {
                return ResourceManager.GetString("YouSaidManWhatsYourBodyType", resourceCulture);
            }
        }
        
        internal static string Skinny {
            get {
                return ResourceManager.GetString("Skinny", resourceCulture);
            }
        }
        
        internal static string Midsize {
            get {
                return ResourceManager.GetString("Midsize", resourceCulture);
            }
        }
        
        internal static string Big {
            get {
                return ResourceManager.GetString("Big", resourceCulture);
            }
        }
        
        internal static string DoYouUseLbsOrKgs {
            get {
                return ResourceManager.GetString("DoYouUseLbsOrKgs", resourceCulture);
            }
        }
        
        internal static string Lbs {
            get {
                return ResourceManager.GetString("Lbs", resourceCulture);
            }
        }
        
        internal static string Kg {
            get {
                return ResourceManager.GetString("Kg", resourceCulture);
            }
        }
        
        internal static string YouSaidFullFiguredFullFiguredWomenOftenHaveAHardTimeLosingWeight {
            get {
                return ResourceManager.GetString("YouSaidFullFiguredFullFiguredWomenOftenHaveAHardTimeLosingWeight", resourceCulture);
            }
        }
        
        internal static string YesICanGainWeightEasily {
            get {
                return ResourceManager.GetString("YesICanGainWeightEasily", resourceCulture);
            }
        }
        
        internal static string NoIDontGainWeightThatEasily {
            get {
                return ResourceManager.GetString("NoIDontGainWeightThatEasily", resourceCulture);
            }
        }
        
        internal static string ThankYouFullFiguredWomenAlsoOftenSayTheyWantToGetFItAndStrong {
            get {
                return ResourceManager.GetString("ThankYouFullFiguredWomenAlsoOftenSayTheyWantToGetFItAndStrong", resourceCulture);
            }
        }
        
        internal static string YouSaidMidSizeMidSizeWomenOftenSAyTheyWantToGetFitAndStrong {
            get {
                return ResourceManager.GetString("YouSaidMidSizeMidSizeWomenOftenSAyTheyWantToGetFitAndStrong", resourceCulture);
            }
        }
        
        internal static string YouSaidThinThinWomenOftenSayTheyWantToGetFitAndStrongW {
            get {
                return ResourceManager.GetString("YouSaidThinThinWomenOftenSayTheyWantToGetFitAndStrongW", resourceCulture);
            }
        }
        
        internal static string YouSaidWomanPleaseTellMeAboutYourBodyType {
            get {
                return ResourceManager.GetString("YouSaidWomanPleaseTellMeAboutYourBodyType", resourceCulture);
            }
        }
        
        internal static string Thin {
            get {
                return ResourceManager.GetString("Thin", resourceCulture);
            }
        }
        
        internal static string FullFigured {
            get {
                return ResourceManager.GetString("FullFigured", resourceCulture);
            }
        }
        
        internal static string ThankYouYourSuggestedProgramIs {
            get {
                return ResourceManager.GetString("ThankYouYourSuggestedProgramIs", resourceCulture);
            }
        }
        
        internal static string UpperLowerBodySplitLevel1More1Year {
            get {
                return ResourceManager.GetString("UpperLowerBodySplitLevel1More1Year", resourceCulture);
            }
        }
        
        internal static string MondayUpperBody1More1Year {
            get {
                return ResourceManager.GetString("MondayUpperBody1More1Year", resourceCulture);
            }
        }
        
        internal static string TuesdayLowerBodyMore1Year {
            get {
                return ResourceManager.GetString("TuesdayLowerBodyMore1Year", resourceCulture);
            }
        }
        
        internal static string WednesdayOffMore1Year {
            get {
                return ResourceManager.GetString("WednesdayOffMore1Year", resourceCulture);
            }
        }
        
        internal static string ThursdayUpperBodyMore1Year {
            get {
                return ResourceManager.GetString("ThursdayUpperBodyMore1Year", resourceCulture);
            }
        }
        
        internal static string FridayOrSaturdayLowerBodyMore1Year {
            get {
                return ResourceManager.GetString("FridayOrSaturdayLowerBodyMore1Year", resourceCulture);
            }
        }
        
        internal static string SundayOffMore1Year {
            get {
                return ResourceManager.GetString("SundayOffMore1Year", resourceCulture);
            }
        }
        
        internal static string WorkOutYourUpperAndYourLowerBody2xWeekForBestResultsMore1Year {
            get {
                return ResourceManager.GetString("WorkOutYourUpperAndYourLowerBody2xWeekForBestResultsMore1Year", resourceCulture);
            }
        }
        
        internal static string FullBodyLevel1 {
            get {
                return ResourceManager.GetString("FullBodyLevel1", resourceCulture);
            }
        }
        
        internal static string MondayFullBody {
            get {
                return ResourceManager.GetString("MondayFullBody", resourceCulture);
            }
        }
        
        internal static string TuesdayOff {
            get {
                return ResourceManager.GetString("TuesdayOff", resourceCulture);
            }
        }
        
        internal static string WednesdayFullBody {
            get {
                return ResourceManager.GetString("WednesdayFullBody", resourceCulture);
            }
        }
        
        internal static string ThursdayOff {
            get {
                return ResourceManager.GetString("ThursdayOff", resourceCulture);
            }
        }
        
        internal static string FridayOrSaturdayFullBody {
            get {
                return ResourceManager.GetString("FridayOrSaturdayFullBody", resourceCulture);
            }
        }
        
        internal static string SundayOff {
            get {
                return ResourceManager.GetString("SundayOff", resourceCulture);
            }
        }
        
        internal static string WorkOutYourFullBody3xWeekForBestResults {
            get {
                return ResourceManager.GetString("WorkOutYourFullBody3xWeekForBestResults", resourceCulture);
            }
        }
        
        internal static string YouCanChangeWorkoutDays {
            get {
                return ResourceManager.GetString("YouCanChangeWorkoutDays", resourceCulture);
            }
        }
        
        internal static string WhereDoYouWorkOut {
            get {
                return ResourceManager.GetString("WhereDoYouWorkOut", resourceCulture);
            }
        }
        
        internal static string Gym {
            get {
                return ResourceManager.GetString("Gym", resourceCulture);
            }
        }
        
        internal static string Home {
            get {
                return ResourceManager.GetString("Home", resourceCulture);
            }
        }
        
        internal static string Continue {
            get {
                return ResourceManager.GetString("Continue", resourceCulture);
            }
        }
        
        internal static string YourWorkoutPlanIs {
            get {
                return ResourceManager.GetString("YourWorkoutPlanIs", resourceCulture);
            }
        }
        
        internal static string LogInWithFacebook {
            get {
                return ResourceManager.GetString("LogInWithFacebook", resourceCulture);
            }
        }
        
        internal static string LogInWithEmail {
            get {
                return ResourceManager.GetString("LogInWithEmail", resourceCulture);
            }
        }
        
        internal static string TapToEnterYourEmail {
            get {
                return ResourceManager.GetString("TapToEnterYourEmail", resourceCulture);
            }
        }
        
        internal static string TapToEnterYourPassword {
            get {
                return ResourceManager.GetString("TapToEnterYourPassword", resourceCulture);
            }
        }
        
        internal static string SixCharactersOrLonger {
            get {
                return ResourceManager.GetString("SixCharactersOrLonger", resourceCulture);
            }
        }
        
        internal static string LogIn {
            get {
                return ResourceManager.GetString("LogIn", resourceCulture);
            }
        }
        
        internal static string ForgotPassword {
            get {
                return ResourceManager.GetString("ForgotPassword", resourceCulture);
            }
        }
        
        internal static string MadeAMistakeStartOver {
            get {
                return ResourceManager.GetString("MadeAMistakeStartOver", resourceCulture);
            }
        }
        
        internal static string CreateNewAccount {
            get {
                return ResourceManager.GetString("CreateNewAccount", resourceCulture);
            }
        }
        
        internal static string TermsOfUse {
            get {
                return ResourceManager.GetString("TermsOfUse", resourceCulture);
            }
        }
        
        internal static string AnErrorOccursWhenSigningIn {
            get {
                return ResourceManager.GetString("AnErrorOccursWhenSigningIn", resourceCulture);
            }
        }
        
        internal static string UnableToLogIn {
            get {
                return ResourceManager.GetString("UnableToLogIn", resourceCulture);
            }
        }
        
        internal static string EmailAndPasswordDoNotMatch {
            get {
                return ResourceManager.GetString("EmailAndPasswordDoNotMatch", resourceCulture);
            }
        }
        
        internal static string PasswordReset {
            get {
                return ResourceManager.GetString("PasswordReset", resourceCulture);
            }
        }
        
        internal static string EnterYourEmail {
            get {
                return ResourceManager.GetString("EnterYourEmail", resourceCulture);
            }
        }
        
        internal static string Ok {
            get {
                return ResourceManager.GetString("Ok", resourceCulture);
            }
        }
        
        internal static string PleaseCheckYourEmail {
            get {
                return ResourceManager.GetString("PleaseCheckYourEmail", resourceCulture);
            }
        }
        
        internal static string ToRestYourPassword {
            get {
                return ResourceManager.GetString("ToRestYourPassword", resourceCulture);
            }
        }
        
        internal static string CanYouTryAnotherLoginEmail {
            get {
                return ResourceManager.GetString("CanYouTryAnotherLoginEmail", resourceCulture);
            }
        }
        
        internal static string EmailNotFound {
            get {
                return ResourceManager.GetString("EmailNotFound", resourceCulture);
            }
        }
        
        internal static string EmailPasswordEmptyError {
            get {
                return ResourceManager.GetString("EmailPasswordEmptyError", resourceCulture);
            }
        }
        
        internal static string InvalidEmailError {
            get {
                return ResourceManager.GetString("InvalidEmailError", resourceCulture);
            }
        }
        
        internal static string InvalidEmailAddress {
            get {
                return ResourceManager.GetString("InvalidEmailAddress", resourceCulture);
            }
        }
        
        internal static string PasswordLengthError {
            get {
                return ResourceManager.GetString("PasswordLengthError", resourceCulture);
            }
        }
        
        internal static string PleaseCheckInternetConnection {
            get {
                return ResourceManager.GetString("PleaseCheckInternetConnection", resourceCulture);
            }
        }
        
        internal static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        internal static string ConnectWithFacebook {
            get {
                return ResourceManager.GetString("ConnectWithFacebook", resourceCulture);
            }
        }
        
        internal static string CreateAccountWithWmail {
            get {
                return ResourceManager.GetString("CreateAccountWithWmail", resourceCulture);
            }
        }
        
        internal static string CreateAccount {
            get {
                return ResourceManager.GetString("CreateAccount", resourceCulture);
            }
        }
        
        internal static string TapToCreateYourPassword {
            get {
                return ResourceManager.GetString("TapToCreateYourPassword", resourceCulture);
            }
        }
        
        internal static string WelcomeToLower {
            get {
                return ResourceManager.GetString("WelcomeToLower", resourceCulture);
            }
        }
        
        internal static string SaveYourCustomProgramAndProgression {
            get {
                return ResourceManager.GetString("SaveYourCustomProgramAndProgression", resourceCulture);
            }
        }
        
        internal static string GymFullBody {
            get {
                return ResourceManager.GetString("GymFullBody", resourceCulture);
            }
        }
        
        internal static string GymUpperBody {
            get {
                return ResourceManager.GetString("GymUpperBody", resourceCulture);
            }
        }
        
        internal static string HomeFullBody {
            get {
                return ResourceManager.GetString("HomeFullBody", resourceCulture);
            }
        }
        
        internal static string HomeUpperBody {
            get {
                return ResourceManager.GetString("HomeUpperBody", resourceCulture);
            }
        }
        
        internal static string NotSetUp {
            get {
                return ResourceManager.GetString("NotSetUp", resourceCulture);
            }
        }
        
        internal static string GymFullBodyLevel1 {
            get {
                return ResourceManager.GetString("GymFullBodyLevel1", resourceCulture);
            }
        }
        
        internal static string GymUpLowSplitLevel1 {
            get {
                return ResourceManager.GetString("GymUpLowSplitLevel1", resourceCulture);
            }
        }
        
        internal static string HomeFullBodyLevel1 {
            get {
                return ResourceManager.GetString("HomeFullBodyLevel1", resourceCulture);
            }
        }
        
        internal static string HomeUpLowSplitLevel1 {
            get {
                return ResourceManager.GetString("HomeUpLowSplitLevel1", resourceCulture);
            }
        }
        
        internal static string SearchExercises {
            get {
                return ResourceManager.GetString("SearchExercises", resourceCulture);
            }
        }
        
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        internal static string ChooseExercises {
            get {
                return ResourceManager.GetString("ChooseExercises", resourceCulture);
            }
        }
        
        internal static string ChooseWorkouts {
            get {
                return ResourceManager.GetString("ChooseWorkouts", resourceCulture);
            }
        }
        
        internal static string Custom {
            get {
                return ResourceManager.GetString("Custom", resourceCulture);
            }
        }
        
        internal static string ChooseWorkout {
            get {
                return ResourceManager.GetString("ChooseWorkout", resourceCulture);
            }
        }
        
        internal static string HomeGym {
            get {
                return ResourceManager.GetString("HomeGym", resourceCulture);
            }
        }
        
        internal static string SaveWorkout {
            get {
                return ResourceManager.GetString("SaveWorkout", resourceCulture);
            }
        }
        
        internal static string Bodyweight {
            get {
                return ResourceManager.GetString("Bodyweight", resourceCulture);
            }
        }
        
        internal static string ChooseOrder {
            get {
                return ResourceManager.GetString("ChooseOrder", resourceCulture);
            }
        }
        
        internal static string SaveProgram {
            get {
                return ResourceManager.GetString("SaveProgram", resourceCulture);
            }
        }
        
        internal static string ChoosePrograms {
            get {
                return ResourceManager.GetString("ChoosePrograms", resourceCulture);
            }
        }
        
        internal static string up {
            get {
                return ResourceManager.GetString("up", resourceCulture);
            }
        }
        
        internal static string down {
            get {
                return ResourceManager.GetString("down", resourceCulture);
            }
        }
        
        internal static string BodyweightWorkouts24xWk {
            get {
                return ResourceManager.GetString("BodyweightWorkouts24xWk", resourceCulture);
            }
        }
        
        internal static string CreateNewProgram {
            get {
                return ResourceManager.GetString("CreateNewProgram", resourceCulture);
            }
        }
        
        internal static string NameYourProgram {
            get {
                return ResourceManager.GetString("NameYourProgram", resourceCulture);
            }
        }
        
        internal static string CreateNew {
            get {
                return ResourceManager.GetString("CreateNew", resourceCulture);
            }
        }
        
        internal static string CreateNewWorkout {
            get {
                return ResourceManager.GetString("CreateNewWorkout", resourceCulture);
            }
        }
        
        internal static string NameYourWorkout {
            get {
                return ResourceManager.GetString("NameYourWorkout", resourceCulture);
            }
        }
        
        internal static string MyWorkouts {
            get {
                return ResourceManager.GetString("MyWorkouts", resourceCulture);
            }
        }
        
        internal static string MyPrograms {
            get {
                return ResourceManager.GetString("MyPrograms", resourceCulture);
            }
        }
        
        internal static string TapToCreateNewCustomWorkout___ {
            get {
                return ResourceManager.GetString("TapToCreateNewCustomWorkout...", resourceCulture);
            }
        }
        
        internal static string CreateWorkoutsToCreateACustomProgram {
            get {
                return ResourceManager.GetString("CreateWorkoutsToCreateACustomProgram", resourceCulture);
            }
        }
        
        internal static string Rename {
            get {
                return ResourceManager.GetString("Rename", resourceCulture);
            }
        }
        
        internal static string EnterNewName {
            get {
                return ResourceManager.GetString("EnterNewName", resourceCulture);
            }
        }
        
        internal static string DeleteWorkout {
            get {
                return ResourceManager.GetString("DeleteWorkout", resourceCulture);
            }
        }
        
        internal static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        internal static string PermanentlyDelete {
            get {
                return ResourceManager.GetString("PermanentlyDelete", resourceCulture);
            }
        }
        
        internal static string EnterProgramName {
            get {
                return ResourceManager.GetString("EnterProgramName", resourceCulture);
            }
        }
        
        internal static string Create {
            get {
                return ResourceManager.GetString("Create", resourceCulture);
            }
        }
        
        internal static string EnterWorkoutName {
            get {
                return ResourceManager.GetString("EnterWorkoutName", resourceCulture);
            }
        }
        
        internal static string FullBodyWorkouts23xWk {
            get {
                return ResourceManager.GetString("FullBodyWorkouts23xWk", resourceCulture);
            }
        }
        
        internal static string UpLowSplitWorkouts45xWk {
            get {
                return ResourceManager.GetString("UpLowSplitWorkouts45xWk", resourceCulture);
            }
        }
        
        internal static string TodaYExercises {
            get {
                return ResourceManager.GetString("TodaYExercises", resourceCulture);
            }
        }
        
        internal static string FinishAndSaveWorkout {
            get {
                return ResourceManager.GetString("FinishAndSaveWorkout", resourceCulture);
            }
        }
        
        internal static string ChooseExercise {
            get {
                return ResourceManager.GetString("ChooseExercise", resourceCulture);
            }
        }
        
        internal static string ShowWelcomePopUp2Messagge {
            get {
                return ResourceManager.GetString("ShowWelcomePopUp2Messagge", resourceCulture);
            }
        }
        
        internal static string ShowWelcomePopUp2Title {
            get {
                return ResourceManager.GetString("ShowWelcomePopUp2Title", resourceCulture);
            }
        }
        
        internal static string RemindMe {
            get {
                return ResourceManager.GetString("RemindMe", resourceCulture);
            }
        }
        
        internal static string AreYouSureYouAreFinishedAndWantToSaveTodaysWorkout {
            get {
                return ResourceManager.GetString("AreYouSureYouAreFinishedAndWantToSaveTodaysWorkout", resourceCulture);
            }
        }
        
        internal static string FinishAndSave {
            get {
                return ResourceManager.GetString("FinishAndSave", resourceCulture);
            }
        }
        
        internal static string Congratulations {
            get {
                return ResourceManager.GetString("Congratulations", resourceCulture);
            }
        }
        
        internal static string YouAre1WorkoutCloserToNewExercisesYourProgramWillLevelUpIn {
            get {
                return ResourceManager.GetString("YouAre1WorkoutCloserToNewExercisesYourProgramWillLevelUpIn", resourceCulture);
            }
        }
        
        internal static string WorkoutsFullStop {
            get {
                return ResourceManager.GetString("WorkoutsFullStop", resourceCulture);
            }
        }
        
        internal static string ResetExercise {
            get {
                return ResourceManager.GetString("ResetExercise", resourceCulture);
            }
        }
        
        internal static string AreYouSureYouWantToResetThisExerciseAndDeleteAllItsHistoryThisCannotBeUndone {
            get {
                return ResourceManager.GetString("AreYouSureYouWantToResetThisExerciseAndDeleteAllItsHistoryThisCannotBeUndone", resourceCulture);
            }
        }
        
        internal static string Reset {
            get {
                return ResourceManager.GetString("Reset", resourceCulture);
            }
        }
        
        internal static string TapToEnterNewName {
            get {
                return ResourceManager.GetString("TapToEnterNewName", resourceCulture);
            }
        }
        
        internal static string ChooseYourExercise {
            get {
                return ResourceManager.GetString("ChooseYourExercise", resourceCulture);
            }
        }
        
        internal static string NewExercise {
            get {
                return ResourceManager.GetString("NewExercise", resourceCulture);
            }
        }
        
        internal static string LetsNameYourNewExercise {
            get {
                return ResourceManager.GetString("LetsNameYourNewExercise", resourceCulture);
            }
        }
        
        internal static string TapHereToEnterName {
            get {
                return ResourceManager.GetString("TapHereToEnterName", resourceCulture);
            }
        }
        
        internal static string RenameExercise {
            get {
                return ResourceManager.GetString("RenameExercise", resourceCulture);
            }
        }
        
        internal static string DeleteExercise {
            get {
                return ResourceManager.GetString("DeleteExercise", resourceCulture);
            }
        }
        
        internal static string TapToCreateNewCustomExercise {
            get {
                return ResourceManager.GetString("TapToCreateNewCustomExercise", resourceCulture);
            }
        }
        
        internal static string IsThisABodyweightExercise {
            get {
                return ResourceManager.GetString("IsThisABodyweightExercise", resourceCulture);
            }
        }
        
        internal static string YesBodyweight {
            get {
                return ResourceManager.GetString("YesBodyweight", resourceCulture);
            }
        }
        
        internal static string IsThisAnEasyExerciseUsedForRecovery {
            get {
                return ResourceManager.GetString("IsThisAnEasyExerciseUsedForRecovery", resourceCulture);
            }
        }
        
        internal static string YesEasy {
            get {
                return ResourceManager.GetString("YesEasy", resourceCulture);
            }
        }
        
        internal static string TapToEnterName {
            get {
                return ResourceManager.GetString("TapToEnterName", resourceCulture);
            }
        }
        
        internal static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        internal static string Exercises {
            get {
                return ResourceManager.GetString("Exercises", resourceCulture);
            }
        }
        
        internal static string MyExercises {
            get {
                return ResourceManager.GetString("MyExercises", resourceCulture);
            }
        }
        
        internal static string ChooseYourSwapExercise {
            get {
                return ResourceManager.GetString("ChooseYourSwapExercise", resourceCulture);
            }
        }
        
        internal static string AddMyOwn {
            get {
                return ResourceManager.GetString("AddMyOwn", resourceCulture);
            }
        }
        
        internal static string LearnMoreAboutDeloads {
            get {
                return ResourceManager.GetString("LearnMoreAboutDeloads", resourceCulture);
            }
        }
        
        internal static string NextExercise {
            get {
                return ResourceManager.GetString("NextExercise", resourceCulture);
            }
        }
        
        internal static string MAXSTRENGTHESTIMATELAST3WORKOUTS {
            get {
                return ResourceManager.GetString("MAXSTRENGTHESTIMATELAST3WORKOUTS", resourceCulture);
            }
        }
        
        internal static string YourStrengthHasGoneUp {
            get {
                return ResourceManager.GetString("YourStrengthHasGoneUp", resourceCulture);
            }
        }
        
        internal static string YourStrengthHasGoneUpAndYouHaveSetaNewRecord {
            get {
                return ResourceManager.GetString("YourStrengthHasGoneUpAndYouHaveSetaNewRecord", resourceCulture);
            }
        }
        
        internal static string TodaysMaxEstimate {
            get {
                return ResourceManager.GetString("TodaysMaxEstimate", resourceCulture);
            }
        }
        
        internal static string PreviousMaxEstimate {
            get {
                return ResourceManager.GetString("PreviousMaxEstimate", resourceCulture);
            }
        }
        
        internal static string Attention {
            get {
                return ResourceManager.GetString("Attention", resourceCulture);
            }
        }
        
        internal static string YourStrengthHasGoneDown {
            get {
                return ResourceManager.GetString("YourStrengthHasGoneDown", resourceCulture);
            }
        }
        
        internal static string IWillLowerYourWeightsToHelpYouRecoverTheNextTimeYou {
            get {
                return ResourceManager.GetString("IWillLowerYourWeightsToHelpYouRecoverTheNextTimeYou", resourceCulture);
            }
        }
        
        internal static string DeloadSuccessful {
            get {
                return ResourceManager.GetString("DeloadSuccessful", resourceCulture);
            }
        }
        
        internal static string IHaveLowedYourWeightsToHelpYouRecoverInTheShortTermAndProgressLongTerm {
            get {
                return ResourceManager.GetString("IHaveLowedYourWeightsToHelpYouRecoverInTheShortTermAndProgressLongTerm", resourceCulture);
            }
        }
        
        internal static string WellDone {
            get {
                return ResourceManager.GetString("WellDone", resourceCulture);
            }
        }
        
        internal static string YourStrengthHasNotChangedButYouHaveDoneMoreSetsThisIsGood {
            get {
                return ResourceManager.GetString("YourStrengthHasNotChangedButYouHaveDoneMoreSetsThisIsGood", resourceCulture);
            }
        }
        
        internal static string YourStrengthHasDecreasedSlightlyButYouHaveDoneMoreSetsOverallThisIsProgress_ {
            get {
                return ResourceManager.GetString("YourStrengthHasDecreasedSlightlyButYouHaveDoneMoreSetsOverallThisIsProgress.", resourceCulture);
            }
        }
        
        internal static string IMadeThisExericseEasyToHelpYouRecoverTheNextTimeYouTrain {
            get {
                return ResourceManager.GetString("IMadeThisExericseEasyToHelpYouRecoverTheNextTimeYouTrain", resourceCulture);
            }
        }
        
        internal static string ShowWelcomePopUp5Message {
            get {
                return ResourceManager.GetString("ShowWelcomePopUp5Message", resourceCulture);
            }
        }
        
        internal static string ShowWelcomePopUp5Title {
            get {
                return ResourceManager.GetString("ShowWelcomePopUp5Title", resourceCulture);
            }
        }
        
        internal static string DoThisTodayToBuildMuscleFaster {
            get {
                return ResourceManager.GetString("DoThisTodayToBuildMuscleFaster", resourceCulture);
            }
        }
        
        internal static string BeginExercise {
            get {
                return ResourceManager.GetString("BeginExercise", resourceCulture);
            }
        }
        
        internal static string DoThisTodayToBuildMuscleAndBurnFatFaster {
            get {
                return ResourceManager.GetString("DoThisTodayToBuildMuscleAndBurnFatFaster", resourceCulture);
            }
        }
        
        internal static string DoThisTodayToProgressFaster {
            get {
                return ResourceManager.GetString("DoThisTodayToProgressFaster", resourceCulture);
            }
        }
        
        internal static string DoThisTodayToGetFitAndStrongFaster {
            get {
                return ResourceManager.GetString("DoThisTodayToGetFitAndStrongFaster", resourceCulture);
            }
        }
        
        internal static string DoThisTodayToGetFitAndLeanFaster {
            get {
                return ResourceManager.GetString("DoThisTodayToGetFitAndLeanFaster", resourceCulture);
            }
        }
        
        internal static string DoThisTodayToGetFitAndBurnFatFaster {
            get {
                return ResourceManager.GetString("DoThisTodayToGetFitAndBurnFatFaster", resourceCulture);
            }
        }
        
        internal static string ShowEasyExercisePopUpTitle {
            get {
                return ResourceManager.GetString("ShowEasyExercisePopUpTitle", resourceCulture);
            }
        }
        
        internal static string ShowEasyExercisePopUpMessage {
            get {
                return ResourceManager.GetString("ShowEasyExercisePopUpMessage", resourceCulture);
            }
        }
        
        internal static string WarmUp {
            get {
                return ResourceManager.GetString("WarmUp", resourceCulture);
            }
        }
        
        internal static string RepsAt {
            get {
                return ResourceManager.GetString("RepsAt", resourceCulture);
            }
        }
        
        internal static string Rest {
            get {
                return ResourceManager.GetString("Rest", resourceCulture);
            }
        }
        
        internal static string WorkSets {
            get {
                return ResourceManager.GetString("WorkSets", resourceCulture);
            }
        }
        
        internal static string ShowWelcomePopUp3Message {
            get {
                return ResourceManager.GetString("ShowWelcomePopUp3Message", resourceCulture);
            }
        }
        
        internal static string ShowWelcomePopUp3Title {
            get {
                return ResourceManager.GetString("ShowWelcomePopUp3Title", resourceCulture);
            }
        }
        
        internal static string DoThisNow {
            get {
                return ResourceManager.GetString("DoThisNow", resourceCulture);
            }
        }
        
        internal static string Reps {
            get {
                return ResourceManager.GetString("Reps", resourceCulture);
            }
        }
        
        internal static string Saveset {
            get {
                return ResourceManager.GetString("Saveset", resourceCulture);
            }
        }
        
        internal static string Superset {
            get {
                return ResourceManager.GetString("Superset", resourceCulture);
            }
        }
        
        internal static string FinishExercise {
            get {
                return ResourceManager.GetString("FinishExercise", resourceCulture);
            }
        }
        
        internal static string ASuperSetIsWhenYouAlternateSetsOfDifferentExercisesYourSets {
            get {
                return ResourceManager.GetString("ASuperSetIsWhenYouAlternateSetsOfDifferentExercisesYourSets", resourceCulture);
            }
        }
        
        internal static string WhatIsASuperset {
            get {
                return ResourceManager.GetString("WhatIsASuperset", resourceCulture);
            }
        }
        
        internal static string SetsLeftLift {
            get {
                return ResourceManager.GetString("SetsLeftLift", resourceCulture);
            }
        }
        
        internal static string times {
            get {
                return ResourceManager.GetString("times", resourceCulture);
            }
        }
        
        internal static string NowPleaseTellMeHowHardThatWas {
            get {
                return ResourceManager.GetString("NowPleaseTellMeHowHardThatWas", resourceCulture);
            }
        }
        
        internal static string ThatWasVeryVeryHard {
            get {
                return ResourceManager.GetString("ThatWasVeryVeryHard", resourceCulture);
            }
        }
        
        internal static string ICouldHaveDone12MoreRep {
            get {
                return ResourceManager.GetString("ICouldHaveDone12MoreRep", resourceCulture);
            }
        }
        
        internal static string ICouldHaveDone34MoreReps {
            get {
                return ResourceManager.GetString("ICouldHaveDone34MoreReps", resourceCulture);
            }
        }
        
        internal static string IcouldHaveDone56MoreReps {
            get {
                return ResourceManager.GetString("IcouldHaveDone56MoreReps", resourceCulture);
            }
        }
        
        internal static string ICouldHaveDone7PMoreReps {
            get {
                return ResourceManager.GetString("ICouldHaveDone7PMoreReps", resourceCulture);
            }
        }
        
        internal static string PleaseAnswer {
            get {
                return ResourceManager.GetString("PleaseAnswer", resourceCulture);
            }
        }
        
        internal static string ImSorryIDidNotGetYourAnswerINeedToKnow {
            get {
                return ResourceManager.GetString("ImSorryIDidNotGetYourAnswerINeedToKnow", resourceCulture);
            }
        }
        
        internal static string TryAgain {
            get {
                return ResourceManager.GetString("TryAgain", resourceCulture);
            }
        }
        
        internal static string GotItExclamation {
            get {
                return ResourceManager.GetString("GotItExclamation", resourceCulture);
            }
        }
        
        internal static string YouSaid {
            get {
                return ResourceManager.GetString("YouSaid", resourceCulture);
            }
        }
        
        internal static string IWillAdjustAccordingly {
            get {
                return ResourceManager.GetString("IWillAdjustAccordingly", resourceCulture);
            }
        }
        
        internal static string Lift {
            get {
                return ResourceManager.GetString("Lift", resourceCulture);
            }
        }
        
        internal static string time {
            get {
                return ResourceManager.GetString("time", resourceCulture);
            }
        }
        
        internal static string Sets {
            get {
                return ResourceManager.GetString("Sets", resourceCulture);
            }
        }
        
        internal static string set {
            get {
                return ResourceManager.GetString("set", resourceCulture);
            }
        }
        
        internal static string AlmostDoneYouCanDoThis {
            get {
                return ResourceManager.GetString("AlmostDoneYouCanDoThis", resourceCulture);
            }
        }
        
        internal static string AllSetsDoneCongrats {
            get {
                return ResourceManager.GetString("AllSetsDoneCongrats", resourceCulture);
            }
        }
        
        internal static string TapFinishExerciseToContinue {
            get {
                return ResourceManager.GetString("TapFinishExerciseToContinue", resourceCulture);
            }
        }
        
        internal static string ShowWelcomePopUp4Message {
            get {
                return ResourceManager.GetString("ShowWelcomePopUp4Message", resourceCulture);
            }
        }
        
        internal static string ShowWelcomePopUp4Title {
            get {
                return ResourceManager.GetString("ShowWelcomePopUp4Title", resourceCulture);
            }
        }
        
        internal static string Chart {
            get {
                return ResourceManager.GetString("Chart", resourceCulture);
            }
        }
        
        internal static string Logs {
            get {
                return ResourceManager.GetString("Logs", resourceCulture);
            }
        }
        
        internal static string History {
            get {
                return ResourceManager.GetString("History", resourceCulture);
            }
        }
        
        internal static string Last3Workouts {
            get {
                return ResourceManager.GetString("Last3Workouts", resourceCulture);
            }
        }
        
        internal static string LastMonth {
            get {
                return ResourceManager.GetString("LastMonth", resourceCulture);
            }
        }
        
        internal static string Last3Months {
            get {
                return ResourceManager.GetString("Last3Months", resourceCulture);
            }
        }
        
        internal static string Last6Months {
            get {
                return ResourceManager.GetString("Last6Months", resourceCulture);
            }
        }
        
        internal static string LastYear {
            get {
                return ResourceManager.GetString("LastYear", resourceCulture);
            }
        }
        
        internal static string AllTime {
            get {
                return ResourceManager.GetString("AllTime", resourceCulture);
            }
        }
        
        internal static string Total {
            get {
                return ResourceManager.GetString("Total", resourceCulture);
            }
        }
        
        internal static string PerRepOnAverage {
            get {
                return ResourceManager.GetString("PerRepOnAverage", resourceCulture);
            }
        }
        
        internal static string MY1RMPROGRESSION {
            get {
                return ResourceManager.GetString("MY1RMPROGRESSION", resourceCulture);
            }
        }
        
        internal static string OverTheLast4WeeksYouHaveTrainedTheFollowingExercisesAtLeast3Times {
            get {
                return ResourceManager.GetString("OverTheLast4WeeksYouHaveTrainedTheFollowingExercisesAtLeast3Times", resourceCulture);
            }
        }
        
        internal static string AverageOfAllRecentExercises {
            get {
                return ResourceManager.GetString("AverageOfAllRecentExercises", resourceCulture);
            }
        }
        
        internal static string ForTheseExercisesYourCurrentAverage1RMIs {
            get {
                return ResourceManager.GetString("ForTheseExercisesYourCurrentAverage1RMIs", resourceCulture);
            }
        }
        
        internal static string YourPrevious1RMWas {
            get {
                return ResourceManager.GetString("YourPrevious1RMWas", resourceCulture);
            }
        }
        
        internal static string ChangeIs {
            get {
                return ResourceManager.GetString("ChangeIs", resourceCulture);
            }
        }
        
        internal static string SignUpToContinueUsing {
            get {
                return ResourceManager.GetString("SignUpToContinueUsing", resourceCulture);
            }
        }
        
        internal static string DrMuscleAfterYourFreeTrial {
            get {
                return ResourceManager.GetString("DrMuscleAfterYourFreeTrial", resourceCulture);
            }
        }
        
        internal static string SignUpMonthly {
            get {
                return ResourceManager.GetString("SignUpMonthly", resourceCulture);
            }
        }
        
        internal static string SignUpAnnual {
            get {
                return ResourceManager.GetString("SignUpAnnual", resourceCulture);
            }
        }
        
        internal static string RestorePurchase {
            get {
                return ResourceManager.GetString("RestorePurchase", resourceCulture);
            }
        }
        
        internal static string EmailSupport {
            get {
                return ResourceManager.GetString("EmailSupport", resourceCulture);
            }
        }
        
        internal static string OnceYouConfirmYourSubscriptionPurchase {
            get {
                return ResourceManager.GetString("OnceYouConfirmYourSubscriptionPurchase", resourceCulture);
            }
        }
        
        internal static string OnceYourSubscriptionIsActiveYourITunesAccountWill {
            get {
                return ResourceManager.GetString("OnceYourSubscriptionIsActiveYourITunesAccountWill", resourceCulture);
            }
        }
        
        internal static string ByTappingContinueYourPaymentWillBeChargedToYourGooglePlayAccount {
            get {
                return ResourceManager.GetString("ByTappingContinueYourPaymentWillBeChargedToYourGooglePlayAccount", resourceCulture);
            }
        }
        
        internal static string YourSubscriptionWillRenewAutomatically {
            get {
                return ResourceManager.GetString("YourSubscriptionWillRenewAutomatically", resourceCulture);
            }
        }
        
        internal static string YouAlreadyHaveAccess {
            get {
                return ResourceManager.GetString("YouAlreadyHaveAccess", resourceCulture);
            }
        }
        
        internal static string ThankYou {
            get {
                return ResourceManager.GetString("ThankYou", resourceCulture);
            }
        }
        
        internal static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        internal static string Restore {
            get {
                return ResourceManager.GetString("Restore", resourceCulture);
            }
        }
        
        internal static string Swap {
            get {
                return ResourceManager.GetString("Swap", resourceCulture);
            }
        }
        
        internal static string Loading {
            get {
                return ResourceManager.GetString("Loading", resourceCulture);
            }
        }
        
        internal static string Welcome {
            get {
                return ResourceManager.GetString("Welcome", resourceCulture);
            }
        }
        
        internal static string WehaveSentYourAccountDetailsAndTipsToYourEmail {
            get {
                return ResourceManager.GetString("WehaveSentYourAccountDetailsAndTipsToYourEmail", resourceCulture);
            }
        }
        
        internal static string SinceYouAreNewLetsTryAWorkoutDontWorryYouCanResetItLater {
            get {
                return ResourceManager.GetString("SinceYouAreNewLetsTryAWorkoutDontWorryYouCanResetItLater", resourceCulture);
            }
        }
        
        internal static string TryAWorkout {
            get {
                return ResourceManager.GetString("TryAWorkout", resourceCulture);
            }
        }
        
        internal static string ConnectionError {
            get {
                return ResourceManager.GetString("ConnectionError", resourceCulture);
            }
        }
        
        internal static string YourProgram {
            get {
                return ResourceManager.GetString("YourProgram", resourceCulture);
            }
        }
        
        internal static string TodaysWorkout {
            get {
                return ResourceManager.GetString("TodaysWorkout", resourceCulture);
            }
        }
        
        internal static string WorkoutsBeforeYouLevelUp {
            get {
                return ResourceManager.GetString("WorkoutsBeforeYouLevelUp", resourceCulture);
            }
        }
        
        internal static string YourProgramNotSetUp {
            get {
                return ResourceManager.GetString("YourProgramNotSetUp", resourceCulture);
            }
        }
        
        internal static string TodaysWorkoutNotSetUp {
            get {
                return ResourceManager.GetString("TodaysWorkoutNotSetUp", resourceCulture);
            }
        }
        
        internal static string YourProgramIs {
            get {
                return ResourceManager.GetString("YourProgramIs", resourceCulture);
            }
        }
        
        internal static string TodaysWorkoutIs {
            get {
                return ResourceManager.GetString("TodaysWorkoutIs", resourceCulture);
            }
        }
        
        internal static string TodaysWorkoutTitle {
            get {
                return ResourceManager.GetString("TodaysWorkoutTitle", resourceCulture);
            }
        }
        
        internal static string ManageWorkouts {
            get {
                return ResourceManager.GetString("ManageWorkouts", resourceCulture);
            }
        }
        
        internal static string ManageExercises {
            get {
                return ResourceManager.GetString("ManageExercises", resourceCulture);
            }
        }
        
        internal static string LetsSetUpYour {
            get {
                return ResourceManager.GetString("LetsSetUpYour", resourceCulture);
            }
        }
        
        internal static string WhatsYourBodyWeight {
            get {
                return ResourceManager.GetString("WhatsYourBodyWeight", resourceCulture);
            }
        }
        
        internal static string _in {
            get {
                return ResourceManager.GetString("in", resourceCulture);
            }
        }
        
        internal static string HowMuchCanYou {
            get {
                return ResourceManager.GetString("HowMuchCanYou", resourceCulture);
            }
        }
        
        internal static string VeryVeryVeryEasily6TimesIwillImproveOnYourGuessAfterYourFirstWorkout {
            get {
                return ResourceManager.GetString("VeryVeryVeryEasily6TimesIwillImproveOnYourGuessAfterYourFirstWorkout", resourceCulture);
            }
        }
        
        internal static string VeryEasily6TimesIWillImproveOnYourGuessAfterYourFirstWorkout {
            get {
                return ResourceManager.GetString("VeryEasily6TimesIWillImproveOnYourGuessAfterYourFirstWorkout", resourceCulture);
            }
        }
        
        internal static string TapToEnterYourWeight {
            get {
                return ResourceManager.GetString("TapToEnterYourWeight", resourceCulture);
            }
        }
        
        internal static string HowMany {
            get {
                return ResourceManager.GetString("HowMany", resourceCulture);
            }
        }
        
        internal static string CanYouDo {
            get {
                return ResourceManager.GetString("CanYouDo", resourceCulture);
            }
        }
        
        internal static string TapToEnterHowMany {
            get {
                return ResourceManager.GetString("TapToEnterHowMany", resourceCulture);
            }
        }
        
        internal static string SetupComplete {
            get {
                return ResourceManager.GetString("SetupComplete", resourceCulture);
            }
        }
        
        internal static string SetupCompleteExerciseNow {
            get {
                return ResourceManager.GetString("SetupCompleteExerciseNow", resourceCulture);
            }
        }
        
        internal static string SelectLanguage {
            get {
                return ResourceManager.GetString("SelectLanguage", resourceCulture);
            }
        }
        
        internal static string Change {
            get {
                return ResourceManager.GetString("Change", resourceCulture);
            }
        }
        
        internal static string HomeScreen {
            get {
                return ResourceManager.GetString("HomeScreen", resourceCulture);
            }
        }
        
        internal static string TrainingLogAndCharts {
            get {
                return ResourceManager.GetString("TrainingLogAndCharts", resourceCulture);
            }
        }
        
        internal static string SubscriptionInfo {
            get {
                return ResourceManager.GetString("SubscriptionInfo", resourceCulture);
            }
        }
        
        internal static string Settings {
            get {
                return ResourceManager.GetString("Settings", resourceCulture);
            }
        }
        
        internal static string LogOut {
            get {
                return ResourceManager.GetString("LogOut", resourceCulture);
            }
        }
        
        internal static string REPRANGE {
            get {
                return ResourceManager.GetString("REPRANGE", resourceCulture);
            }
        }
        
        internal static string YouProgressFasterWhenYouChangeRepsOftenChooseARangeYourRepsWillChangeAutomaticallyEveryWorkout {
            get {
                return ResourceManager.GetString("YouProgressFasterWhenYouChangeRepsOftenChooseARangeYourRepsWillChangeAutomaticall" +
                        "yEveryWorkout", resourceCulture);
            }
        }
        
        internal static string LearnMore {
            get {
                return ResourceManager.GetString("LearnMore", resourceCulture);
            }
        }
        
        internal static string FiveToTwelveReps {
            get {
                return ResourceManager.GetString("FiveToTwelveReps", resourceCulture);
            }
        }
        
        internal static string EightToFifteenReps {
            get {
                return ResourceManager.GetString("EightToFifteenReps", resourceCulture);
            }
        }
        
        internal static string TwelveToTwentyReps {
            get {
                return ResourceManager.GetString("TwelveToTwentyReps", resourceCulture);
            }
        }
        
        internal static string Min {
            get {
                return ResourceManager.GetString("Min", resourceCulture);
            }
        }
        
        internal static string Max {
            get {
                return ResourceManager.GetString("Max", resourceCulture);
            }
        }
        
        internal static string SaveCustomReps {
            get {
                return ResourceManager.GetString("SaveCustomReps", resourceCulture);
            }
        }
        
        internal static string SETSTYLE {
            get {
                return ResourceManager.GetString("SETSTYLE", resourceCulture);
            }
        }
        
        internal static string RestPauseSetsAreHarderButTheyHalveWorkoutTime {
            get {
                return ResourceManager.GetString("RestPauseSetsAreHarderButTheyHalveWorkoutTime", resourceCulture);
            }
        }
        
        internal static string NormalSets {
            get {
                return ResourceManager.GetString("NormalSets", resourceCulture);
            }
        }
        
        internal static string RestPauseSets {
            get {
                return ResourceManager.GetString("RestPauseSets", resourceCulture);
            }
        }
        
        internal static string UNITS {
            get {
                return ResourceManager.GetString("UNITS", resourceCulture);
            }
        }
        
        internal static string BACKGROUNDIMAGE {
            get {
                return ResourceManager.GetString("BACKGROUNDIMAGE", resourceCulture);
            }
        }
        
        internal static string Male {
            get {
                return ResourceManager.GetString("Male", resourceCulture);
            }
        }
        
        internal static string Female {
            get {
                return ResourceManager.GetString("Female", resourceCulture);
            }
        }
        
        internal static string NoImage {
            get {
                return ResourceManager.GetString("NoImage", resourceCulture);
            }
        }
        
        internal static string LANGUAGE {
            get {
                return ResourceManager.GetString("LANGUAGE", resourceCulture);
            }
        }
        
        internal static string VIBRATE {
            get {
                return ResourceManager.GetString("VIBRATE", resourceCulture);
            }
        }
        
        internal static string SOUND {
            get {
                return ResourceManager.GetString("SOUND", resourceCulture);
            }
        }
        
        internal static string AUTOSTART {
            get {
                return ResourceManager.GetString("AUTOSTART", resourceCulture);
            }
        }
        
        internal static string AUTOMATCHREPS {
            get {
                return ResourceManager.GetString("AUTOMATCHREPS", resourceCulture);
            }
        }
        
        internal static string AutomaticallyChangeTimerDurationToMatchRecommendedRepsAndOptimizeMuscleHypertrophy {
            get {
                return ResourceManager.GetString("AutomaticallyChangeTimerDurationToMatchRecommendedRepsAndOptimizeMuscleHypertroph" +
                        "y", resourceCulture);
            }
        }
        
        internal static string START {
            get {
                return ResourceManager.GetString("START", resourceCulture);
            }
        }
        
        internal static string STOP {
            get {
                return ResourceManager.GetString("STOP", resourceCulture);
            }
        }
        
        internal static string LowRepsBuildMoreStrengthHighRepsAreEasierOnYourJoints {
            get {
                return ResourceManager.GetString("LowRepsBuildMoreStrengthHighRepsAreEasierOnYourJoints", resourceCulture);
            }
        }
        
        internal static string AllRepsBuildMuscle {
            get {
                return ResourceManager.GetString("AllRepsBuildMuscle", resourceCulture);
            }
        }
        
        internal static string SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5 {
            get {
                return ResourceManager.GetString("SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsI" +
                        "s5", resourceCulture);
            }
        }
        
        internal static string LessThan5Reps {
            get {
                return ResourceManager.GetString("LessThan5Reps", resourceCulture);
            }
        }
        
        internal static string PleaseIncreaseMaxRepsToIncreaseMinimumRepsFurther {
            get {
                return ResourceManager.GetString("PleaseIncreaseMaxRepsToIncreaseMinimumRepsFurther", resourceCulture);
            }
        }
        
        internal static string SetsOfMoreThan30RepsAreRatherPainfulTakeALongTimeToDoAndDoNotBuildMuscleFasterForBestResultsMaxRepsIs30 {
            get {
                return ResourceManager.GetString("SetsOfMoreThan30RepsAreRatherPainfulTakeALongTimeToDoAndDoNotBuildMuscleFasterFor" +
                        "BestResultsMaxRepsIs30", resourceCulture);
            }
        }
        
        internal static string MoreThan30Reps {
            get {
                return ResourceManager.GetString("MoreThan30Reps", resourceCulture);
            }
        }
        
        internal static string PleaseDecreaseMinimumRepsToDecreaseMaxRepsFurther {
            get {
                return ResourceManager.GetString("PleaseDecreaseMinimumRepsToDecreaseMaxRepsFurther", resourceCulture);
            }
        }
        
        internal static string SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime {
            get {
                return ResourceManager.GetString("SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime", resourceCulture);
            }
        }
        
        internal static string ChooseEnvironment {
            get {
                return ResourceManager.GetString("ChooseEnvironment", resourceCulture);
            }
        }
        
        internal static string Month {
            get {
                return ResourceManager.GetString("Month", resourceCulture);
            }
        }
        
        internal static string Year {
            get {
                return ResourceManager.GetString("Year", resourceCulture);
            }
        }
        
        internal static string Version {
            get {
                return ResourceManager.GetString("Version", resourceCulture);
            }
        }
        
        internal static string Build {
            get {
                return ResourceManager.GetString("Build", resourceCulture);
            }
        }
        
        internal static string WhatAreTheSmallestWeightIncrementsAvailableToU {
            get {
                return ResourceManager.GetString("WhatAreTheSmallestWeightIncrementsAvailableToU", resourceCulture);
            }
        }
        
        internal static string TapToEnterYourIncrements {
            get {
                return ResourceManager.GetString("TapToEnterYourIncrements", resourceCulture);
            }
        }
        
        internal static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        internal static string Increments {
            get {
                return ResourceManager.GetString("Increments", resourceCulture);
            }
        }
        
        internal static string TapToSet {
            get {
                return ResourceManager.GetString("TapToSet", resourceCulture);
            }
        }
        
        internal static string PleaseEntryYourIncrements {
            get {
                return ResourceManager.GetString("PleaseEntryYourIncrements", resourceCulture);
            }
        }
        
        internal static string FeelStrongToday {
            get {
                return ResourceManager.GetString("FeelStrongToday", resourceCulture);
            }
        }
        
        internal static string TryAChallengeYouWillDoAsManyRepsAsYouCan {
            get {
                return ResourceManager.GetString("TryAChallengeYouWillDoAsManyRepsAsYouCan", resourceCulture);
            }
        }
        
        internal static string Challenge {
            get {
                return ResourceManager.GetString("Challenge", resourceCulture);
            }
        }
        
        internal static string maxLowecase {
            get {
                return ResourceManager.GetString("maxLowecase", resourceCulture);
            }
        }
        
        internal static string GiveMeAChallenge {
            get {
                return ResourceManager.GetString("GiveMeAChallenge", resourceCulture);
            }
        }
        
        internal static string Weight {
            get {
                return ResourceManager.GetString("Weight", resourceCulture);
            }
        }
        
        internal static string SaveIncrements {
            get {
                return ResourceManager.GetString("SaveIncrements", resourceCulture);
            }
        }
        
        internal static string FinishAndSaveWorkoutQuestion {
            get {
                return ResourceManager.GetString("FinishAndSaveWorkoutQuestion", resourceCulture);
            }
        }
        
        internal static string CheckYourMail {
            get {
                return ResourceManager.GetString("CheckYourMail", resourceCulture);
            }
        }
        
        internal static string YourProgramIsReady {
            get {
                return ResourceManager.GetString("YourProgramIsReady", resourceCulture);
            }
        }
        
        internal static string BackupAutomaticallyAccessAnywhere {
            get {
                return ResourceManager.GetString("BackupAutomaticallyAccessAnywhere", resourceCulture);
            }
        }
        
        internal static string PleaseChooseAGoal {
            get {
                return ResourceManager.GetString("PleaseChooseAGoal", resourceCulture);
            }
        }
        
        internal static string DontWorryYouCanCustomizeLater {
            get {
                return ResourceManager.GetString("DontWorryYouCanCustomizeLater", resourceCulture);
            }
        }
        
        internal static string DontWorryLiftingWightsWontMakeyouBulky {
            get {
                return ResourceManager.GetString("DontWorryLiftingWightsWontMakeyouBulky", resourceCulture);
            }
        }
        
        internal static string BigMenOftenSay {
            get {
                return ResourceManager.GetString("BigMenOftenSay", resourceCulture);
            }
        }
        
        internal static string TheyWantToGetRidOfThisBodyFatAndLoseMyGut {
            get {
                return ResourceManager.GetString("TheyWantToGetRidOfThisBodyFatAndLoseMyGut", resourceCulture);
            }
        }
        
        internal static string MidsizeMenOftenSay {
            get {
                return ResourceManager.GetString("MidsizeMenOftenSay", resourceCulture);
            }
        }
        
        internal static string TheyWantToGetFitStrongAndMoreMuscularGainLeanMassAndHaveAVisibleSetOf {
            get {
                return ResourceManager.GetString("TheyWantToGetFitStrongAndMoreMuscularGainLeanMassAndHaveAVisibleSetOf", resourceCulture);
            }
        }
        
        internal static string SkinnyMenOften {
            get {
                return ResourceManager.GetString("SkinnyMenOften", resourceCulture);
            }
        }
        
        internal static string HaveAHardTimeGainingWeightSomeSayIEatConstantlyAndWorkMyButtOff {
            get {
                return ResourceManager.GetString("HaveAHardTimeGainingWeightSomeSayIEatConstantlyAndWorkMyButtOff", resourceCulture);
            }
        }
        
        internal static string SkinnyMenAlsoOftenSay {
            get {
                return ResourceManager.GetString("SkinnyMenAlsoOftenSay", resourceCulture);
            }
        }
        
        internal static string TheyWantToPutOnLeanMassWhileKeepingmyAbsDefinedGainHealthy {
            get {
                return ResourceManager.GetString("TheyWantToPutOnLeanMassWhileKeepingmyAbsDefinedGainHealthy", resourceCulture);
            }
        }
        
        internal static string WhatsYourBodyType {
            get {
                return ResourceManager.GetString("WhatsYourBodyType", resourceCulture);
            }
        }
        
        internal static string AreYouABeginnerWithNoEquipment {
            get {
                return ResourceManager.GetString("AreYouABeginnerWithNoEquipment", resourceCulture);
            }
        }
        
        internal static string IWillSimplyYourAccountSetupAndGiveYouBodyWeightExercisesOnly {
            get {
                return ResourceManager.GetString("IWillSimplyYourAccountSetupAndGiveYouBodyWeightExercisesOnly", resourceCulture);
            }
        }
        
        internal static string YesIMBeginner {
            get {
                return ResourceManager.GetString("YesIMBeginner", resourceCulture);
            }
        }
        
        internal static string NoImMoreAdvanced {
            get {
                return ResourceManager.GetString("NoImMoreAdvanced", resourceCulture);
            }
        }
        
        internal static string HowLongHaveYouBeenWorkingOut {
            get {
                return ResourceManager.GetString("HowLongHaveYouBeenWorkingOut", resourceCulture);
            }
        }
        
        internal static string YourProgramStartsAtYourLevelItLevelsUpWithAsYouProgress {
            get {
                return ResourceManager.GetString("YourProgramStartsAtYourLevelItLevelsUpWithAsYouProgress", resourceCulture);
            }
        }
        
        internal static string OneToThreeYears {
            get {
                return ResourceManager.GetString("OneToThreeYears", resourceCulture);
            }
        }
        
        internal static string MoreThan3Years {
            get {
                return ResourceManager.GetString("MoreThan3Years", resourceCulture);
            }
        }
        
        internal static string HomeGymBasicEqipment {
            get {
                return ResourceManager.GetString("HomeGymBasicEqipment", resourceCulture);
            }
        }
        
        internal static string HomeBodtweightOnly {
            get {
                return ResourceManager.GetString("HomeBodtweightOnly", resourceCulture);
            }
        }
        
        internal static string WhatWeightIncrementsDoYouUse {
            get {
                return ResourceManager.GetString("WhatWeightIncrementsDoYouUse", resourceCulture);
            }
        }
        
        internal static string IfYouAreNotSureEnter1YouCanChangeLater {
            get {
                return ResourceManager.GetString("IfYouAreNotSureEnter1YouCanChangeLater", resourceCulture);
            }
        }
        
        internal static string YourProgramLevelsUpAutomatically {
            get {
                return ResourceManager.GetString("YourProgramLevelsUpAutomatically", resourceCulture);
            }
        }
        
        internal static string IUpdateItEveryTimeYouWorkOutBuild {
            get {
                return ResourceManager.GetString("IUpdateItEveryTimeYouWorkOutBuild", resourceCulture);
            }
        }
        
        internal static string IUpdateItEveryTimeYouWorkOutBuildNBuildFat {
            get {
                return ResourceManager.GetString("IUpdateItEveryTimeYouWorkOutBuildNBuildFat", resourceCulture);
            }
        }
        
        internal static string IUpdateItEveryTimeYouWorkOutBurnFatFaster {
            get {
                return ResourceManager.GetString("IUpdateItEveryTimeYouWorkOutBurnFatFaster", resourceCulture);
            }
        }
        
        internal static string WarningIMNOtLikeOtherAppsIGuideYouInRealTimeBased {
            get {
                return ResourceManager.GetString("WarningIMNOtLikeOtherAppsIGuideYouInRealTimeBased", resourceCulture);
            }
        }
        
        internal static string IUnderstand {
            get {
                return ResourceManager.GetString("IUnderstand", resourceCulture);
            }
        }
        
        internal static string SuggestedProgram {
            get {
                return ResourceManager.GetString("SuggestedProgram", resourceCulture);
            }
        }
        
        internal static string FullFiguredOften {
            get {
                return ResourceManager.GetString("FullFiguredOften", resourceCulture);
            }
        }
        
        internal static string HaveAHardTimeLosingWeightGetFatLookingAtFood {
            get {
                return ResourceManager.GetString("HaveAHardTimeLosingWeightGetFatLookingAtFood", resourceCulture);
            }
        }
        
        internal static string FullFiguredWomenAlsoOftenSay {
            get {
                return ResourceManager.GetString("FullFiguredWomenAlsoOftenSay", resourceCulture);
            }
        }
        
        internal static string TheyWantToGetFitAndStrongWhileDroppingBodyFatShapeArms {
            get {
                return ResourceManager.GetString("TheyWantToGetFitAndStrongWhileDroppingBodyFatShapeArms", resourceCulture);
            }
        }
        
        internal static string ThankYouTitle {
            get {
                return ResourceManager.GetString("ThankYouTitle", resourceCulture);
            }
        }
        
        internal static string MidsizeWomenOftenSay {
            get {
                return ResourceManager.GetString("MidsizeWomenOftenSay", resourceCulture);
            }
        }
        
        internal static string TheyWantToGetFitAndStrongLeanerAndComfortableInMyBody {
            get {
                return ResourceManager.GetString("TheyWantToGetFitAndStrongLeanerAndComfortableInMyBody", resourceCulture);
            }
        }
        
        internal static string TheyWantToGetFitAndStrongWhileMaintaingLeanPhysiqueAddSizeToLegsBootyDenseLookingMuscleOverall {
            get {
                return ResourceManager.GetString("TheyWantToGetFitAndStrongWhileMaintaingLeanPhysiqueAddSizeToLegsBootyDenseLooking" +
                        "MuscleOverall", resourceCulture);
            }
        }
        
        internal static string PleaseTellMeAboutYourBodyType {
            get {
                return ResourceManager.GetString("PleaseTellMeAboutYourBodyType", resourceCulture);
            }
        }
        
        internal static string Setup {
            get {
                return ResourceManager.GetString("Setup", resourceCulture);
            }
        }
        
        internal static string Video {
            get {
                return ResourceManager.GetString("Video", resourceCulture);
            }
        }
        
        internal static string FirstTimeHereTryAWorkoutDontWorryYouCanResetItLater {
            get {
                return ResourceManager.GetString("FirstTimeHereTryAWorkoutDontWorryYouCanResetItLater", resourceCulture);
            }
        }
        
        internal static string UpdateReps {
            get {
                return ResourceManager.GetString("UpdateReps", resourceCulture);
            }
        }
        
        internal static string EnterWeights {
            get {
                return ResourceManager.GetString("EnterWeights", resourceCulture);
            }
        }
        
        internal static string LanguageLowercase {
            get {
                return ResourceManager.GetString("LanguageLowercase", resourceCulture);
            }
        }
        
        internal static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        internal static string Program {
            get {
                return ResourceManager.GetString("Program", resourceCulture);
            }
        }
        
        internal static string WelcomeBack {
            get {
                return ResourceManager.GetString("WelcomeBack", resourceCulture);
            }
        }
        
        internal static string Today {
            get {
                return ResourceManager.GetString("Today", resourceCulture);
            }
        }
        
        internal static string CurrentMaxEstimate {
            get {
                return ResourceManager.GetString("CurrentMaxEstimate", resourceCulture);
            }
        }
        
        internal static string PreviousMaxEstimateHomeScreen {
            get {
                return ResourceManager.GetString("PreviousMaxEstimateHomeScreen", resourceCulture);
            }
        }
        
        internal static string Progress {
            get {
                return ResourceManager.GetString("Progress", resourceCulture);
            }
        }
        
        internal static string LastWorkout {
            get {
                return ResourceManager.GetString("LastWorkout", resourceCulture);
            }
        }
        
        internal static string WorkoutsDone {
            get {
                return ResourceManager.GetString("WorkoutsDone", resourceCulture);
            }
        }
        
        internal static string Lifted {
            get {
                return ResourceManager.GetString("Lifted", resourceCulture);
            }
        }
        
        internal static string StartTodaysWorkout {
            get {
                return ResourceManager.GetString("StartTodaysWorkout", resourceCulture);
            }
        }
        
        internal static string DayAgo {
            get {
                return ResourceManager.GetString("DayAgo", resourceCulture);
            }
        }
        
        internal static string AMonthAgo {
            get {
                return ResourceManager.GetString("AMonthAgo", resourceCulture);
            }
        }
        
        internal static string AYearAgo {
            get {
                return ResourceManager.GetString("AYearAgo", resourceCulture);
            }
        }
        
        internal static string TodayLowercase {
            get {
                return ResourceManager.GetString("TodayLowercase", resourceCulture);
            }
        }
        
        internal static string UpNext {
            get {
                return ResourceManager.GetString("UpNext", resourceCulture);
            }
        }
        
        internal static string StartCapitalized {
            get {
                return ResourceManager.GetString("StartCapitalized", resourceCulture);
            }
        }
        
        internal static string EnterNewReps {
            get {
                return ResourceManager.GetString("EnterNewReps", resourceCulture);
            }
        }
        
        internal static string MaxStrengthProgression {
            get {
                return ResourceManager.GetString("MaxStrengthProgression", resourceCulture);
            }
        }
        
        internal static string VolumeSetsProgression {
            get {
                return ResourceManager.GetString("VolumeSetsProgression", resourceCulture);
            }
        }
        
        internal static string FullscreenUppercase {
            get {
                return ResourceManager.GetString("FullscreenUppercase", resourceCulture);
            }
        }
        
        internal static string Skip {
            get {
                return ResourceManager.GetString("Skip", resourceCulture);
            }
        }
        
        internal static string Hide {
            get {
                return ResourceManager.GetString("Hide", resourceCulture);
            }
        }
        
        internal static string Seconds {
            get {
                return ResourceManager.GetString("Seconds", resourceCulture);
            }
        }
        
        internal static string Restfor {
            get {
                return ResourceManager.GetString("Restfor", resourceCulture);
            }
        }
        
        internal static string WorkSetsNoColon {
            get {
                return ResourceManager.GetString("WorkSetsNoColon", resourceCulture);
            }
        }
        
        internal static string MaxStrength {
            get {
                return ResourceManager.GetString("MaxStrength", resourceCulture);
            }
        }
        
        internal static string WorkoutDone {
            get {
                return ResourceManager.GetString("WorkoutDone", resourceCulture);
            }
        }
        
        internal static string TryAWorkoutToSeeYourProgressInThisChart {
            get {
                return ResourceManager.GetString("TryAWorkoutToSeeYourProgressInThisChart", resourceCulture);
            }
        }
        
        internal static string GetReadyFor {
            get {
                return ResourceManager.GetString("GetReadyFor", resourceCulture);
            }
        }
        
        internal static string StrengthAndSetsLast3Weeks {
            get {
                return ResourceManager.GetString("StrengthAndSetsLast3Weeks", resourceCulture);
            }
        }
        
        internal static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        internal static string VideoAndInstruction {
            get {
                return ResourceManager.GetString("VideoAndInstruction", resourceCulture);
            }
        }
        
        internal static string ResetHistory {
            get {
                return ResourceManager.GetString("ResetHistory", resourceCulture);
            }
        }
        
        internal static string SettingsUppercase {
            get {
                return ResourceManager.GetString("SettingsUppercase", resourceCulture);
            }
        }
        
        internal static string UseCustomReps {
            get {
                return ResourceManager.GetString("UseCustomReps", resourceCulture);
            }
        }
        
        internal static string UseCustomSetStyle {
            get {
                return ResourceManager.GetString("UseCustomSetStyle", resourceCulture);
            }
        }
        
        internal static string UseCustomIncrements {
            get {
                return ResourceManager.GetString("UseCustomIncrements", resourceCulture);
            }
        }
        
        internal static string IncrementsCapital {
            get {
                return ResourceManager.GetString("IncrementsCapital", resourceCulture);
            }
        }
        
        internal static string MoreUppercase {
            get {
                return ResourceManager.GetString("MoreUppercase", resourceCulture);
            }
        }
        
        internal static string TryaWorkoutToSee {
            get {
                return ResourceManager.GetString("TryaWorkoutToSee", resourceCulture);
            }
        }
        
        internal static string YourProgressInThisChart {
            get {
                return ResourceManager.GetString("YourProgressInThisChart", resourceCulture);
            }
        }
        
        internal static string MaxStrengthCapital {
            get {
                return ResourceManager.GetString("MaxStrengthCapital", resourceCulture);
            }
        }
        
        internal static string WorkSetsCapital {
            get {
                return ResourceManager.GetString("WorkSetsCapital", resourceCulture);
            }
        }
        
        internal static string MinValueShouldNotGreaterThenMax {
            get {
                return ResourceManager.GetString("MinValueShouldNotGreaterThenMax", resourceCulture);
            }
        }
        
        internal static string Bar {
            get {
                return ResourceManager.GetString("Bar", resourceCulture);
            }
        }
        
        internal static string Plates {
            get {
                return ResourceManager.GetString("Plates", resourceCulture);
            }
        }
        
        internal static string PlatesCapital {
            get {
                return ResourceManager.GetString("PlatesCapital", resourceCulture);
            }
        }
        
        internal static string Equipment {
            get {
                return ResourceManager.GetString("Equipment", resourceCulture);
            }
        }
        
        internal static string EnterNewCount {
            get {
                return ResourceManager.GetString("EnterNewCount", resourceCulture);
            }
        }
        
        internal static string TapToEnterNewPlates {
            get {
                return ResourceManager.GetString("TapToEnterNewPlates", resourceCulture);
            }
        }
        
        internal static string EditPlateCount {
            get {
                return ResourceManager.GetString("EditPlateCount", resourceCulture);
            }
        }
        
        internal static string AddPlateWeight {
            get {
                return ResourceManager.GetString("AddPlateWeight", resourceCulture);
            }
        }
        
        internal static string EnterNewWeightIn {
            get {
                return ResourceManager.GetString("EnterNewWeightIn", resourceCulture);
            }
        }
        
        internal static string EditPlateWeight {
            get {
                return ResourceManager.GetString("EditPlateWeight", resourceCulture);
            }
        }
        
        internal static string DeletePlates {
            get {
                return ResourceManager.GetString("DeletePlates", resourceCulture);
            }
        }
        
        internal static string AddPlateCount {
            get {
                return ResourceManager.GetString("AddPlateCount", resourceCulture);
            }
        }
        
        internal static string ChatBeta {
            get {
                return ResourceManager.GetString("ChatBeta", resourceCulture);
            }
        }
        
        internal static string CongYouHaveBeenWorkingOutFor {
            get {
                return ResourceManager.GetString("CongYouHaveBeenWorkingOutFor", resourceCulture);
            }
        }
        
        internal static string HowsYourExperienceWithDrMuscle {
            get {
                return ResourceManager.GetString("HowsYourExperienceWithDrMuscle", resourceCulture);
            }
        }
        
        internal static string GreatYouHaveBeenWorkingOutFor {
            get {
                return ResourceManager.GetString("GreatYouHaveBeenWorkingOutFor", resourceCulture);
            }
        }
        
        internal static string Days {
            get {
                return ResourceManager.GetString("Days", resourceCulture);
            }
        }
        
        internal static string GreatYourFreeTrialEndsIn {
            get {
                return ResourceManager.GetString("GreatYourFreeTrialEndsIn", resourceCulture);
            }
        }
        
        internal static string WouldYouLikeToLearnMoreAboutSigningUp {
            get {
                return ResourceManager.GetString("WouldYouLikeToLearnMoreAboutSigningUp", resourceCulture);
            }
        }
        
        internal static string months {
            get {
                return ResourceManager.GetString("months", resourceCulture);
            }
        }
        
        internal static string GreatExclamation {
            get {
                return ResourceManager.GetString("GreatExclamation", resourceCulture);
            }
        }
        
        internal static string RateUsOnStore {
            get {
                return ResourceManager.GetString("RateUsOnStore", resourceCulture);
            }
        }
        
        internal static string MaybeLater {
            get {
                return ResourceManager.GetString("MaybeLater", resourceCulture);
            }
        }
        
        internal static string InviteAFriendToTryDrMuscleForFree {
            get {
                return ResourceManager.GetString("InviteAFriendToTryDrMuscleForFree", resourceCulture);
            }
        }
        
        internal static string GreatNewWorkoutApp {
            get {
                return ResourceManager.GetString("GreatNewWorkoutApp", resourceCulture);
            }
        }
        
        internal static string SendUsAQuickEmail {
            get {
                return ResourceManager.GetString("SendUsAQuickEmail", resourceCulture);
            }
        }
        
        internal static string WeBelieveYourExperienceShouldBeSolidHowCanWeImprove {
            get {
                return ResourceManager.GetString("WeBelieveYourExperienceShouldBeSolidHowCanWeImprove", resourceCulture);
            }
        }
        
        internal static string SendEmail {
            get {
                return ResourceManager.GetString("SendEmail", resourceCulture);
            }
        }
        
        internal static string BadSorryToHearThat {
            get {
                return ResourceManager.GetString("BadSorryToHearThat", resourceCulture);
            }
        }
        
        internal static string WeBelieveYourExperienceShouldBeSolidSendQuickEmailHowCanWeImprove {
            get {
                return ResourceManager.GetString("WeBelieveYourExperienceShouldBeSolidSendQuickEmailHowCanWeImprove", resourceCulture);
            }
        }
        
        internal static string GoodButCouldBeImproved {
            get {
                return ResourceManager.GetString("GoodButCouldBeImproved", resourceCulture);
            }
        }
        
        internal static string Bad {
            get {
                return ResourceManager.GetString("Bad", resourceCulture);
            }
        }
        
        internal static string SlideToAdjustBarWeight {
            get {
                return ResourceManager.GetString("SlideToAdjustBarWeight", resourceCulture);
            }
        }
        
        internal static string TwoWorkSetsPerExercise {
            get {
                return ResourceManager.GetString("TwoWorkSetsPerExercise", resourceCulture);
            }
        }
        
        internal static string ThirtyMinMode {
            get {
                return ResourceManager.GetString("ThirtyMinMode", resourceCulture);
            }
        }
        
        internal static string QUICKMODE {
            get {
                return ResourceManager.GetString("QUICKMODE", resourceCulture);
            }
        }
        
        internal static string GroupChatBeta {
            get {
                return ResourceManager.GetString("GroupChatBeta", resourceCulture);
            }
        }
        
        internal static string Workouts {
            get {
                return ResourceManager.GetString("Workouts", resourceCulture);
            }
        }
        
        internal static string GroupChatIsPayingSubscribeOnly {
            get {
                return ResourceManager.GetString("GroupChatIsPayingSubscribeOnly", resourceCulture);
            }
        }
        
        internal static string Send {
            get {
                return ResourceManager.GetString("Send", resourceCulture);
            }
        }
        
        internal static string AreYouSureYouWantToExit {
            get {
                return ResourceManager.GetString("AreYouSureYouWantToExit", resourceCulture);
            }
        }
        
        internal static string Exit {
            get {
                return ResourceManager.GetString("Exit", resourceCulture);
            }
        }
        
        internal static string ChooseAnotherWorkout {
            get {
                return ResourceManager.GetString("ChooseAnotherWorkout", resourceCulture);
            }
        }
        
        internal static string EnterUnlockCode {
            get {
                return ResourceManager.GetString("EnterUnlockCode", resourceCulture);
            }
        }
        
        internal static string InvalidCode {
            get {
                return ResourceManager.GetString("InvalidCode", resourceCulture);
            }
        }
        
        internal static string UnlockProgram {
            get {
                return ResourceManager.GetString("UnlockProgram", resourceCulture);
            }
        }
        
        internal static string UnlockCode {
            get {
                return ResourceManager.GetString("UnlockCode", resourceCulture);
            }
        }
        
        internal static string UnlockAnotherProgram {
            get {
                return ResourceManager.GetString("UnlockAnotherProgram", resourceCulture);
            }
        }
        
        internal static string TryCodeForSurprise {
            get {
                return ResourceManager.GetString("TryCodeForSurprise", resourceCulture);
            }
        }
        
        internal static string Support {
            get {
                return ResourceManager.GetString("Support", resourceCulture);
            }
        }
        
        internal static string TapHereFor11Chat {
            get {
                return ResourceManager.GetString("TapHereFor11Chat", resourceCulture);
            }
        }
        
        internal static string HumanSupport {
            get {
                return ResourceManager.GetString("HumanSupport", resourceCulture);
            }
        }
        
        internal static string ChatWithSupport {
            get {
                return ResourceManager.GetString("ChatWithSupport", resourceCulture);
            }
        }
        
        internal static string GroupChat {
            get {
                return ResourceManager.GetString("GroupChat", resourceCulture);
            }
        }
        
        internal static string RestPauseSetsAreHarderButMakeYourWorkouts59Faster {
            get {
                return ResourceManager.GetString("RestPauseSetsAreHarderButMakeYourWorkouts59Faster", resourceCulture);
            }
        }
        
        internal static string Featured {
            get {
                return ResourceManager.GetString("Featured", resourceCulture);
            }
        }
        
        internal static string Caution {
            get {
                return ResourceManager.GetString("Caution", resourceCulture);
            }
        }
        
        internal static string YouHaveBeenWorkingOut {
            get {
                return ResourceManager.GetString("YouHaveBeenWorkingOut", resourceCulture);
            }
        }
        
        internal static string DaysInARowISuggestTalkingADayOffAreYouSureYouWantToWorkOutToday {
            get {
                return ResourceManager.GetString("DaysInARowISuggestTalkingADayOffAreYouSureYouWantToWorkOutToday", resourceCulture);
            }
        }
        
        internal static string WorkOut {
            get {
                return ResourceManager.GetString("WorkOut", resourceCulture);
            }
        }
        
        internal static string WelcomeBackExclamination {
            get {
                return ResourceManager.GetString("WelcomeBackExclamination", resourceCulture);
            }
        }
        
        internal static string YourLastWorkoutWas {
            get {
                return ResourceManager.GetString("YourLastWorkoutWas", resourceCulture);
            }
        }
        
        internal static string DaysAgoYouMayNeedToAdjustYourWeightsLetsSee {
            get {
                return ResourceManager.GetString("DaysAgoYouMayNeedToAdjustYourWeightsLetsSee", resourceCulture);
            }
        }
        
        internal static string WorkOutNow {
            get {
                return ResourceManager.GetString("WorkOutNow", resourceCulture);
            }
        }
        
        internal static string TheLastTimeYouDid {
            get {
                return ResourceManager.GetString("TheLastTimeYouDid", resourceCulture);
            }
        }
        
        internal static string was {
            get {
                return ResourceManager.GetString("was", resourceCulture);
            }
        }
        
        internal static string DaysAgoYouShouldBeFullyRecoveredDoExtraSet {
            get {
                return ResourceManager.GetString("DaysAgoYouShouldBeFullyRecoveredDoExtraSet", resourceCulture);
            }
        }
        
        internal static string AddOneSet {
            get {
                return ResourceManager.GetString("AddOneSet", resourceCulture);
            }
        }
        
        internal static string DaysAgoDoALightSessionToRecover {
            get {
                return ResourceManager.GetString("DaysAgoDoALightSessionToRecover", resourceCulture);
            }
        }
        
        internal static string LightSession {
            get {
                return ResourceManager.GetString("LightSession", resourceCulture);
            }
        }
        
        internal static string GoodMorning {
            get {
                return ResourceManager.GetString("GoodMorning", resourceCulture);
            }
        }
        
        internal static string GoodAfternoon {
            get {
                return ResourceManager.GetString("GoodAfternoon", resourceCulture);
            }
        }
        
        internal static string GoodEvening {
            get {
                return ResourceManager.GetString("GoodEvening", resourceCulture);
            }
        }
        
        internal static string Hi {
            get {
                return ResourceManager.GetString("Hi", resourceCulture);
            }
        }
        
        internal static string WelcomeToDrMuscleIMCarlAndIWillHelp {
            get {
                return ResourceManager.GetString("WelcomeToDrMuscleIMCarlAndIWillHelp", resourceCulture);
            }
        }
        
        internal static string ThatsMeGettingMyPhDInExerciseStatics {
            get {
                return ResourceManager.GetString("ThatsMeGettingMyPhDInExerciseStatics", resourceCulture);
            }
        }
        
        internal static string IHaveBeenACoachAllMyLifeAndATrainerForTheCandadianForcesIHaveHelped {
            get {
                return ResourceManager.GetString("IHaveBeenACoachAllMyLifeAndATrainerForTheCandadianForcesIHaveHelped", resourceCulture);
            }
        }
        
        internal static string ThisAppIsLikeATrainerInYourPhoneThatGuidesYou {
            get {
                return ResourceManager.GetString("ThisAppIsLikeATrainerInYourPhoneThatGuidesYou", resourceCulture);
            }
        }
        
        internal static string LetsCustomizeYourProgramCanIAskIfYouAreAManOrWoman {
            get {
                return ResourceManager.GetString("LetsCustomizeYourProgramCanIAskIfYouAreAManOrWoman", resourceCulture);
            }
        }
        
        internal static string ManOrWoman {
            get {
                return ResourceManager.GetString("ManOrWoman", resourceCulture);
            }
        }
        
        internal static string OkAManMenOftenSayIWantToGainLeanMassAndHaveAVisibleSetOfAbs {
            get {
                return ResourceManager.GetString("OkAManMenOftenSayIWantToGainLeanMassAndHaveAVisibleSetOfAbs", resourceCulture);
            }
        }
        
        internal static string BuildingMuscle {
            get {
                return ResourceManager.GetString("BuildingMuscle", resourceCulture);
            }
        }
        
        internal static string BuildingMuscleAndBurningFat {
            get {
                return ResourceManager.GetString("BuildingMuscleAndBurningFat", resourceCulture);
            }
        }
        
        internal static string BurningFat {
            get {
                return ResourceManager.GetString("BurningFat", resourceCulture);
            }
        }
        
        internal static string OkAWomanWomanOftenSayIWantToGetFit {
            get {
                return ResourceManager.GetString("OkAWomanWomanOftenSayIWantToGetFit", resourceCulture);
            }
        }
        
        internal static string GettingStronger {
            get {
                return ResourceManager.GetString("GettingStronger", resourceCulture);
            }
        }
        
        internal static string OverallFitness {
            get {
                return ResourceManager.GetString("OverallFitness", resourceCulture);
            }
        }
        
        internal static string GotItAreYouABeginnerWithNoEquipment {
            get {
                return ResourceManager.GetString("GotItAreYouABeginnerWithNoEquipment", resourceCulture);
            }
        }
        
        internal static string BurningFatGotItAreYouBegginerWithNoEquipment {
            get {
                return ResourceManager.GetString("BurningFatGotItAreYouBegginerWithNoEquipment", resourceCulture);
            }
        }
        
        internal static string BuildingMuscleBuriningFatGotItAreYouBeginner {
            get {
                return ResourceManager.GetString("BuildingMuscleBuriningFatGotItAreYouBeginner", resourceCulture);
            }
        }
        
        internal static string BuildingMuscleGotItAreYouABeginnerWithNoEquipment {
            get {
                return ResourceManager.GetString("BuildingMuscleGotItAreYouABeginnerWithNoEquipment", resourceCulture);
            }
        }
        
        internal static string OkHowLongHaveYouBeenWorkingOutFor {
            get {
                return ResourceManager.GetString("OkHowLongHaveYouBeenWorkingOutFor", resourceCulture);
            }
        }
        
        internal static string AllRightPleaseWait {
            get {
                return ResourceManager.GetString("AllRightPleaseWait", resourceCulture);
            }
        }
        
        internal static string YourProgramIsReadyExclamation {
            get {
                return ResourceManager.GetString("YourProgramIsReadyExclamation", resourceCulture);
            }
        }
        
        internal static string InternetConnectionProblem {
            get {
                return ResourceManager.GetString("InternetConnectionProblem", resourceCulture);
            }
        }
        
        internal static string SelectExercisesAndTimeframes {
            get {
                return ResourceManager.GetString("SelectExercisesAndTimeframes", resourceCulture);
            }
        }
        
        internal static string trained {
            get {
                return ResourceManager.GetString("trained", resourceCulture);
            }
        }
        
        internal static string SetsTotal {
            get {
                return ResourceManager.GetString("SetsTotal", resourceCulture);
            }
        }
        
        internal static string MaxStrenthRMRecord {
            get {
                return ResourceManager.GetString("MaxStrenthRMRecord", resourceCulture);
            }
        }
        
        internal static string RecentExercisesinFourWeek {
            get {
                return ResourceManager.GetString("RecentExercisesinFourWeek", resourceCulture);
            }
        }
        
        internal static string AverageMaxStrength {
            get {
                return ResourceManager.GetString("AverageMaxStrength", resourceCulture);
            }
        }
        
        internal static string WorkSetsLastSevenDays {
            get {
                return ResourceManager.GetString("WorkSetsLastSevenDays", resourceCulture);
            }
        }
        
        internal static string SaveWarmUps {
            get {
                return ResourceManager.GetString("SaveWarmUps", resourceCulture);
            }
        }
        
        internal static string WarmUpSets {
            get {
                return ResourceManager.GetString("WarmUpSets", resourceCulture);
            }
        }
        
        internal static string UseCustomWarmUps {
            get {
                return ResourceManager.GetString("UseCustomWarmUps", resourceCulture);
            }
        }
        
        internal static string ViewOnTheWeb {
            get {
                return ResourceManager.GetString("ViewOnTheWeb", resourceCulture);
            }
        }
        
        internal static string ViewAnalyzeAndDownloadData {
            get {
                return ResourceManager.GetString("ViewAnalyzeAndDownloadData", resourceCulture);
            }
        }
        
        internal static string OpenWebApp {
            get {
                return ResourceManager.GetString("OpenWebApp", resourceCulture);
            }
        }
        
        internal static string WebApp {
            get {
                return ResourceManager.GetString("WebApp", resourceCulture);
            }
        }
        
        internal static string MaxWeight {
            get {
                return ResourceManager.GetString("MaxWeight", resourceCulture);
            }
        }
        
        internal static string MinWeight {
            get {
                return ResourceManager.GetString("MinWeight", resourceCulture);
            }
        }
        
        internal static string DaysAgo {
            get {
                return ResourceManager.GetString("DaysAgo", resourceCulture);
            }
        }
        
        internal static string ThinWomenOftenSay {
            get {
                return ResourceManager.GetString("ThinWomenOftenSay", resourceCulture);
            }
        }
        
        internal static string More {
            get {
                return ResourceManager.GetString("More", resourceCulture);
            }
        }
        
        internal static string AttentionTodayIsADeload {
            get {
                return ResourceManager.GetString("AttentionTodayIsADeload", resourceCulture);
            }
        }
        
        internal static string FreeOnSupport {
            get {
                return ResourceManager.GetString("FreeOnSupport", resourceCulture);
            }
        }
        
        internal static string SignUptoUnlock {
            get {
                return ResourceManager.GetString("SignUptoUnlock", resourceCulture);
            }
        }
        
        internal static string ThisIsBeginningWithSupport {
            get {
                return ResourceManager.GetString("ThisIsBeginningWithSupport", resourceCulture);
            }
        }
        
        internal static string ISuggestForRecovery {
            get {
                return ResourceManager.GetString("ISuggestForRecovery", resourceCulture);
            }
        }
    }
}
