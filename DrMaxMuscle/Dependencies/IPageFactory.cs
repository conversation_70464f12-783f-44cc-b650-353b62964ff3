﻿using DrMaxMuscle.Screens.User;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Dependencies
{
    public interface IPageFactory
    {
        WelcomePage CreateWelcomePage();
    }

    public class PageFactory : IPageFactory
    {
        private readonly LocalDBManager _dbManager;

        public PageFactory(LocalDBManager dbManager)
        {
            _dbManager = dbManager;
        }

        public WelcomePage CreateWelcomePage()
        {
            return new WelcomePage();
        }
    }
}
