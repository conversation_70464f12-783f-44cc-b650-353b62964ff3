﻿using Acr.UserDialogs;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Utility;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Microsoft.Maui.Networking;
using RGPopup.Maui.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle
{
    internal class DrMuscleRestClient
    {
        public int ApiVersion { get { return 1; } }
        public string BaseUrl;
        private static DrMuscleRestClient _instance;


        public async Task<List<HistoryModel>> GetHistoryAllTime(GetUserWorkoutLogAverageForExerciseRequest request)
        {
            return await PostJson<List<HistoryModel>>("api/Exercise/GetWorkoutHistoryForAlltime", request);
        }

        public async Task<List<HistoryModel>> GetPastDateHistory(GetUserWorkoutLogAverageForExerciseRequest request)
        {
            return await PostJson<List<HistoryModel>>("api/Exercise/GetWorkoutHistoryForDate", request);
        }

        public async Task<List<HistoryModel>> GetHistoryAllTimeWithoutLoader(GetUserWorkoutLogAverageForExerciseRequest request)
        {
            return await PostJsonWithoughtLoader<List<HistoryModel>>("api/Exercise/GetWorkoutHistoryForAlltime", request);
        }
        public async Task<List<HistoryModel>> GetHistoryByUserId(GetUserWorkoutLogAverageForExerciseRequest request)
        {
            return await PostJson<List<HistoryModel>>("api/Exercise/GetWorkoutHistoryForAlltimeById", request);
        }

        public async Task<List<HistoryModel>> GetHistory()
        {
            return await PostJson<List<HistoryModel>>("api/Exercise/GetWorkoutHistory", null);
        }

        public async Task<List<HistoryModel>> GetHistoryWithoutLoader()
        {
            return await PostJsonWithoughtLoader<List<HistoryModel>>("api/Exercise/GetWorkoutHistory", null);
        }
        public async Task<HistoryExerciseModel> GetCompeletedWorkoutWeightsLifted()
        {
            return await PostJson<HistoryExerciseModel>("api/Exercise/GetCompeletedWorkoutWeightsLifted", null);
        }

        public delegate void OnStartPost();
        public event OnStartPost StartPost;

        public async Task<List<HistoryModel>> GetExerciseHistory(long id)
        {
            return await PostJson<List<HistoryModel>>("api/Exercise/GetExerciseWorkoutHistory", id);
        }

        public async Task<List<HistoryModel>> GetLastExerciseHistory(long id)
        {
            return await PostJson<List<HistoryModel>>("api/Exercise/GetLastExerciseWorkoutHistory", id);
        }

        public async Task<List<HistoryModel>> GetLastExerciseHistoryWithoutLoader(long id)
        {
            return await PostJsonWithoughtLoader<List<HistoryModel>>("api/Exercise/GetLastExerciseWorkoutHistory", id);
        }

        public async Task<GetExercisesLogResponseModel> GetExercisesLog(DateTime since)
        {
            return await PostJson<GetExercisesLogResponseModel>("api/Exercise/GetExercisesLog", since);
        }

        public async Task<BooleanModel> IsValidFeatureProgramCode(FeaturedProgramModel code)
        {
            return await PostJson<BooleanModel>("api/Exercise/IsValidFeatureProgramCode", code);
        }

        public async Task<UnlockCodeResponseModel> IsValidFeatureProgramCodeV2(FeaturedProgramModel code)
        {
            return await PostJson<UnlockCodeResponseModel>("api/Exercise/IsValidFeatureProgramCodeV2", code);
        }

        public async Task<BooleanModel> ForgotPassword(ForgotPasswordModel model)
        {
            return await PostJson<BooleanModel>("api/Account/ForgotPassword", model);
        }

        public async Task<BooleanModel> AddDeviceToken(DeviceModel deviceModel)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/AddDeviceToken", deviceModel);
        }

        public async Task<BooleanModel> RemoveDeviceToken(DeviceModel deviceModel)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/RemoveDeviceToken", deviceModel);
        }

        public async Task<BooleanModel> SaveWorkout(SaveWorkoutModel model)
        {
            return await PostJson<BooleanModel>("api/Workout/SaveWorkoutPro", model);
        }

        public async Task<BooleanModel> SaveWorkoutV2(SaveWorkoutModel model)
        {
            return await PostJson<BooleanModel>("api/Workout/SaveWorkoutV2Pro", model);
        }
        //
        public async Task<BooleanModel> SaveWorkoutV3(SaveWorkoutModel model)
        {
            return await PostJson<BooleanModel>("api/Workout/SaveWorkoutV3Pro", model);
        }

        public async Task<BooleanModel> SaveWorkoutV3WithoutLoader(SaveWorkoutModel model)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Workout/SaveWorkoutV3Pro", model);
        }
        public async Task<GetUserProgramInfoResponseModel> SaveGetWorkoutInfo(SaveWorkoutModel model)
        {
            return await PostJson<GetUserProgramInfoResponseModel>("api/Workout/SaveGetWorkoutInfoPro", model);
        }

        public async Task<GetUserProgramInfoResponseModel> SaveGetWorkoutInfoWithoutLoader(SaveWorkoutModel model)
        {
            return await PostJsonWithoughtLoader<GetUserProgramInfoResponseModel>("api/Workout/SaveGetWorkoutInfoPro", model);
        }

        public async Task<GetUserProgramInfoResponseModel> SaveGetWorkoutInfoProToSkip(SaveWorkoutModel model)
        {
            return await PostJson<GetUserProgramInfoResponseModel>("api/Workout/SaveGetWorkoutInfoProToSkip", model);
        }

        public async Task<GetUserProgramInfoResponseModel> SaveGetWorkoutInfoWithoutLoaderTimeOut(SaveWorkoutModel model)
        {
            return await PostJsonWithoughtLoaderTimeout<GetUserProgramInfoResponseModel>("api/Workout/SaveGetWorkoutInfoPro", model, 1);
        }
        public async Task<BooleanModel> CreateNewWorkoutTemplateOrder(WorkoutTemplateGroupModel model)
        {
            return await PostJson<BooleanModel>("api/Workout/CreateNewWorkoutTemplateGroup", model);
        }

        public async Task<BooleanModel> RestoreUserWorkoutTemplate(WorkoutTemplateGroupModel model)
        {
            return await PostJson<BooleanModel>("api/Workout/RestoreUserWorkoutTemplate", model);
        }

        public async Task<GetUserWorkoutLogAverageResponse> GetUserWorkoutLogAverageForExercise(GetUserWorkoutLogAverageForExerciseRequest request)
        {
            return await PostJson<GetUserWorkoutLogAverageResponse>("/api/WorkoutLog/GetUserWorkoutLogAverageForExercise", request);
        }

        public delegate void OnEndPost();


        public async Task<GetUserWorkoutTemplateResponseModel> GetCustomWorkoutsForUser()
        {
            return await PostJson<GetUserWorkoutTemplateResponseModel>("api/Workout/GetCustomWorkoutsForUser", null);
        }

        public async Task<GetUserWorkoutTemplateResponseModel> GetUserWorkout()
        {
            return await PostJson<GetUserWorkoutTemplateResponseModel>("api/Workout/GetUserWorkout", null);
        }

        public async Task<WorkoutTemplateModel> GetUserCustomizedCurrentWorkout(long workoutid)
        {
            return await PostJson<WorkoutTemplateModel>("api/Workout/GetUserCustomizedCurrentWorkout", workoutid);
        }
        public async Task<WorkoutTemplateModel> GetUserCustomizedCurrentWorkoutWithoutLoader(long workoutid)
        {
            return await PostJsonWithoughtLoader<WorkoutTemplateModel>("api/Workout/GetUserCustomizedCurrentWorkout", workoutid);
        }
        public async Task<GetUserWorkoutTemplateResponseModel> GetCustomizedUserWorkout(EquipmentModel model)
        {
            return await PostJson<GetUserWorkoutTemplateResponseModel>("api/Workout/GetCustomizedUserWorkout", model);
        }
        public event OnEndPost EndPost;

        public delegate void OnUnauthorized();
        //As a user doing a bodyweight exercise for the first time, I want the app to ask me how many times I can do it "easily", so that I don't get injured the first time I do it
        public async Task<GetUserWorkoutTemplateGroupResponseModel> GetUserWorkoutGroup()
        {
            return await PostJson<GetUserWorkoutTemplateGroupResponseModel>("api/Workout/GetUserWorkoutTemplateGroup", null);
        }

        public async Task<GetUserWorkoutTemplateGroupResponseModel> GetUserFeaturedProgramGroup()
        {
            return await PostJson<GetUserWorkoutTemplateGroupResponseModel>("api/Workout/GetFeaturedProgramForUserV2", null);
        }

        public async Task<BooleanModel> UpdateSettingsForProgram(long programId)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Workout/UpdateSettingsForProgram", programId);
        }

        public async Task<BooleanModel> AddUpdateWorkoutSettings(WorkoutTemplateSettingsModel workoutTemplateSettings)
        {
            return await PostJson<BooleanModel>("api/Workout/AddUpdateWorkoutSettings", workoutTemplateSettings);
        }

        public async Task<WorkoutTemplateSettingsModel> GetWorkoutSettings(WorkoutTemplateSettingsModel workoutTemplateSettings)
        {
            return await PostJson<WorkoutTemplateSettingsModel>("api/Workout/GetWorkoutSettings", workoutTemplateSettings);
        }
        public async Task<GetUserWorkoutTemplateGroupResponseModel> GetSystemWorkoutGroup()
        {
            return await PostJson<GetUserWorkoutTemplateGroupResponseModel>("api/Workout/GetSystemWorkoutTemplateGroup", null);
        }

        public async Task<GetUserWorkoutTemplateGroupResponseModel> GetOnlySystemWorkoutGroup()
        {
            return await PostJson<GetUserWorkoutTemplateGroupResponseModel>("api/Workout/GetOnlySystemWorkoutTemplateGroup", null);
        }

        public async Task<GetUserWorkoutTemplateGroupResponseModel> GetCustomizedSystemWorkoutGroup(EquipmentModel model)
        {
            return await PostJson<GetUserWorkoutTemplateGroupResponseModel>("api/Workout/GetCustomizedSystemWorkoutGroup", model);
        }

        public async Task<GetUserWorkoutTemplateGroupResponseModel> GetOnlyCustomizedSystemWorkoutGroup(EquipmentModel model)
        {
            return await PostJson<GetUserWorkoutTemplateGroupResponseModel>("api/Workout/GetOnlyCustomizedSystemWorkoutGroup", model);
        }

        public event OnUnauthorized Unauthorized;

        public async Task<RecommendationModel> GetRecommendationForExercise(GetRecommendationForExerciseModel model)
        {
            return await PostJson<RecommendationModel>("api/Exercise/GetRecommendationForExercise", model);
        }

        public async Task<RecommendationModel> GetRecommendationRestPauseForExercise(GetRecommendationForExerciseModel model)
        {
            return await PostJson<RecommendationModel>("api/Exercise/GetRecommendationRestPauseForExercise", model);
        }

        public async Task<RecommendationModel> GetRecommendationRestPause2ForExercise(GetRecommendationForExerciseModel model)
        {
            return await PostJson<RecommendationModel>("api/Exercise/GetRecommendationRestPause2ForExercise", model);
        }

        public async Task<RecommendationModel> GetRecommendationRestPauseRIRForExercise(GetRecommendationForExerciseModel model)
        {
            return await PostJson<RecommendationModel>("api/Exercise/GetRecommendationRestPauseRIRForExercise", model);
        }

        public async Task<RecommendationModel> GetRecommendationNormalRIRForExercise(GetRecommendationForExerciseModel model)
        {
            return await PostJson<RecommendationModel>("api/Exercise/GetRecommendationNormalRIRForExercise", model);
        }

        public async Task<RecommendationModel> GetRecommendationRestPauseRIRForExerciseWithoutLoader(GetRecommendationForExerciseModel model)
        {
            return await PostJsonWithoughtLoader<RecommendationModel>("api/Exercise/GetRecommendationRestPauseRIRForExercise", model);
        }

        public async Task<RecommendationModel> GetRecommendationNormalRIRForExerciseWithoutLoader(GetRecommendationForExerciseModel model)
        {
            return await PostJsonWithoughtLoader<RecommendationModel>("api/Exercise/GetRecommendationNormalRIRForExercise", model);
        }

        //======Without Deloads start
        public async Task<RecommendationModel> GetRecommendationRestPauseRIRForExerciseWithoutDeload(GetRecommendationForExerciseModel model)
        {
            return await PostJson<RecommendationModel>("api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew", model);
        }

        public async Task<RecommendationModel> GetRecommendationNormalRIRForExerciseWithoutDeload(GetRecommendationForExerciseModel model)
        {
            return await PostJson<RecommendationModel>("api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew", model);
        }

        // public async Task<RecommendationModel> GetRecommendationRestPauseRIRForExerciseWithoutLoaderWithoutDeload(GetRecommendationForExerciseModel model)
        // {
        //     return await PostJsonWithoughtLoader<RecommendationModel>("api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew", model);
        // }
        //
        // public async Task<RecommendationModel> GetRecommendationNormalRIRForExerciseWithoutLoaderWithoutDeload(GetRecommendationForExerciseModel model)
        // {
        //     return await PostJsonWithoughtLoader<RecommendationModel>("api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew", model);
        // }
        public async Task<List<RecommendationModel>> GetAllRecommendationNew(List<GetRecommendationForExerciseModel> model)
        {
            return await PostJsonWithoughtLoader<List<RecommendationModel>>("api/Exercise/GetAllRecommendationNew", model, 1);
        }
        public async Task<RecommendationModel> GetRecommendationRestPauseRIRForExerciseWithoutLoaderWithoutDeloadNoTimeOut(GetRecommendationForExerciseModel model)
        {
            return await PostJsonWithoughtLoader<RecommendationModel>("api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew", model, 1);
        }

        public async Task<RecommendationModel> GetRecommendationNormalRIRForExerciseWithoutLoaderWithoutDeloadNoTimeOut(GetRecommendationForExerciseModel model)
        {
            return await PostJsonWithoughtLoader<RecommendationModel>("api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew", model, 1);
        }

        public async Task<RecommendationModel> GetRecommendationRestPauseRIRForExerciseWithoutLoaderWithoutDeload(GetRecommendationForExerciseModel model)
        {
            return await PostJsonWithoughtLoaderTimeout<RecommendationModel>("api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew", model, 4);
        }

        public async Task<RecommendationModel> GetRecommendationNormalRIRForExerciseWithoutLoaderWithoutDeload(GetRecommendationForExerciseModel model)
        {
            return await PostJsonWithoughtLoaderTimeout<RecommendationModel>("api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew", model, 4);
        }
        //======Without Deloads end
        public async Task<RecommendationModel> GetRecommendationRestPauseRIRForExerciseWithSwap(GetRecommendationForExerciseModel model)
        {
            return await PostJson<RecommendationModel>("api/Exercise/GetRecommendationRestPauseRIRForExerciseWithSwap", model);
        }

        public async Task<RecommendationModel> GetRecommendationRestPauseRIRForExerciseWithSwapWithoutLoader(GetRecommendationForExerciseModel model)
        {
            return await PostJsonWithoughtLoader<RecommendationModel>("api/Exercise/GetRecommendationRestPauseRIRForExerciseWithSwap", model);
        }

        public async Task<RecommendationModel> GetRecommendationNormalRIRForExerciseWithSwap(GetRecommendationForExerciseModel model)
        {
            return await PostJson<RecommendationModel>("api/Exercise/GetRecommendationNormalRIRForExerciseWithSwap", model);
        }

        public async Task<RecommendationModel> GetRecommendationNormalRIRForExerciseWithSwapWithoutLoader(GetRecommendationForExerciseModel model)
        {
            return await PostJsonWithoughtLoader<RecommendationModel>("api/Exercise/GetRecommendationNormalRIRForExerciseWithSwap", model);
        }

        public async Task<BooleanModel> AddUpdateExerciseSettings(ExerciseSettingsModel exerciseSettingsModel)
        {
            return await PostJson<BooleanModel>("api/Exercise/AddUpdateExerciseSettingsPyramid", exerciseSettingsModel);
        }

        public async Task<BooleanModel> AddUpdateExerciseSettingsV2(ExerciseSettingsModel exerciseSettingsModel)
        {
            return await PostJson<BooleanModel>("api/Exercise/AddUpdateExerciseSettingsV2", exerciseSettingsModel);
        }

        public async Task<BooleanModel> AddUpdateUserDefaultExerciseSettings(ExerciseSettingsModel exerciseSettingsModel)
        {
            return await PostJson<BooleanModel>("api/Exercise/AddUpdateUserDefaultExerciseSettingsPyramid", exerciseSettingsModel);
        }

        public async Task<BooleanModel> AddUpdateExerciseUserLightSessionWithBodyPart(LightSessionModel lightSessionModel)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Exercise/AddUpdateExerciseUserLightSessionWithBodyPart", lightSessionModel);
        }

        public async Task<BooleanModel> AddUpdateExerciseUserLightSession(LightSessionModel lightSessionModel)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Exercise/AddUpdateExerciseUserLightSession", lightSessionModel);
        }
        public async Task<ExerciseSettingsModel> GetExerciseSettings(ExerciseSettingsModel exerciseSettingsModel)
        {
            return await PostJson<ExerciseSettingsModel>("api/Exercise/GetExerciseSettingsPyramid", exerciseSettingsModel);
        }
        public async Task<GetUserExerciseResponseModel> GetFavoriteExercises()
        {
            return await PostJsonWithoughtLoader<GetUserExerciseResponseModel>("api/Exercise/GetFavoriteExercises", null);
        }
        public async Task<GetUserExerciseResponseModel> GetFavoriteExercisesWithLoader()
        {
            return await PostJson<GetUserExerciseResponseModel>("api/Exercise/GetFavoriteExercises", null);
        }
        public async Task<List<OneRMModel>> GetOneRMForExercise(GetOneRMforExerciseModel model)
        {
            return await PostJson<List<OneRMModel>>("api/Exercise/GetOneRMForExercise", model);
        }

        public async Task<List<OneRMModel>> GetOneRMForExerciseWithoutLoader(GetOneRMforExerciseModel model)
        {
            return await PostJsonWithoughtLoader<List<OneRMModel>>("api/Exercise/GetOneRMForExercise", model);
        }
        //
        public async Task<List<List<OneRMModel>>> GetLastThreeBestOneRMForExercises()
        {
            return await PostJsonWithoughtLoader<List<List<OneRMModel>>>("api/Exercise/GetLastThreeBestOneRMForExercises", null);
        }

        public async Task<BooleanModel> SetUserCreationDate(DateTime setDate)
        {
            return await PostJson<BooleanModel>("api/Account/SetCreationDate", setDate);
        }


        public async Task<UserInfosModel> GetUserInfo()
        {
            return await PostJson<UserInfosModel>("api/Account/GetUserInfoPyramid", null);
        }

        public async Task<UserInfosModel> GetUserInfoWithoutLoader()
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/GetUserInfoPyramid", null);
        }

        public async Task<UserInfosModel> GetUserInfoBeforeDemo()
        {
            return await PostJson<UserInfosModel>("api/Account/GetUserBeforeDemoInfo", null);
        }

        public async Task<UserInfosModel> GetTargetIntake()
        {
            return await PostJson<UserInfosModel>("api/Account/GetTargetIntake", null);
        }
        public async Task<UserTargetIntake> GetTargetIntakebyUserWithoutLoader()
        {
            return await PostJsonWithoughtLoader<UserTargetIntake>("api/Account/GetTargetIntakebyUser", null);
        }

        public async Task<UserInfosModel> GetTargetIntakeWithoutLoader()
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/GetTargetIntake", null);
        }
        public async Task<UserWeight> GetTargetIntakeListWithoutLoader()
        {
            return await PostJsonWithoughtLoader<UserWeight>("api/Account/GetTargetIntakeList", null);
        }
        public async Task<UserInfosModel> SetUserBodyWeight(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserBodyWeight", userInfosModel);
        }

        //
        public async Task<UserInfosModel> SetUserStartBodyWeight(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserStartBodyWeight", userInfosModel);
        }
        public async Task<UserInfosModel> SetUserBarWeight(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserBarWeight", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserBodyWeightWithoutLoader(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserBodyWeight", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserWeightGoal(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserWeightGoal", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserAge(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserAge", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserWorkoutDuration(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserWorkoutDuration", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserAB(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserAB", userInfosModel);
        }
        public async Task<UserInfosModel> SetUserHeight(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserHeight", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserHeightWithoutLoader(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserHeight", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserFirstname(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserFirstname", userInfosModel);
        }



        public async Task<UserInfosModel> SetUserIncrements(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserIncrements", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserIncrementsOnly(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserIncrementsOnly", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserEquipmentSettings(EquipmentModel equipModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserEquipmentSettings", equipModel);
        }

        public async Task<UserInfosModel> SetUserEquipmentPlateSettings(EquipmentModel equipModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserEquipmentPlateSettings", equipModel);
        }

        public async Task<UserInfosModel> SetUserEquipmentDumbbellSettings(EquipmentModel equipModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserEquipmentDumbbellSettings", equipModel);
        }

        public async Task<UserInfosModel> SetUserEquipmentPulleySettings(EquipmentModel equipModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserEquipmentPulleySettings", equipModel);
        }

        public async Task<UserInfosModel> SetUserEquipmentBandsSettings(EquipmentModel equipModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserEquipmentBandsSettings", equipModel);
        }

        public async Task<UserInfosModel> SetUserEquipmentSettingsWithLoader(EquipmentModel equipModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserEquipmentSettings", equipModel);
        }

        public async Task<UserInfosModel> SetUserSetCount(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserSetCount", userInfosModel);
        }
        public async Task<UserInfosModel> SetCustomWarmups(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetCustomWarmups", userInfosModel);
        }

        public async Task<UserInfosModel> SetReminderHoursbeforeWorkout(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserHoursBeforeReminder", userInfosModel);
        }

        public async Task<UserModel> SetUserEmailReminderTime(UserModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserModel>("api/Account/SetUserEmailReminderTime", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserQuickMode(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserQuickMode", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserLastWorkoutWas(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserLastWorkoutWas", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserRecommendedReminder(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserRecommendedReminder", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserBodypartPriority(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserBodypartPriority", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserReminderTime(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserReminderTime", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserReminderTimeWithoutLoader(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserReminderTime", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserLastChallengeDate(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserLastChallengeDate", userInfosModel);
        }
        public async Task<UserInfosModel> SetUserSetStyle(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserSetStyleWithPyramid", userInfosModel);
        }
        //IsPyramid
        public async Task<UserInfosModel> SetUserPyramidSetStyle(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserPyramidSetStyle", userInfosModel);
        }
        public async Task<UserInfosModel> SetUserMassUnit(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserMassUnit", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserMobility(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserMobility", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserExerciseQuickMode(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserExerciseQuickMode", userInfosModel);
        }
        public async Task<UserInfosModel> SetUserReferenceSetReps(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserReferenceSetReps", userInfosModel);
        }
        public async Task<UserInfosModel> SetUserReferenceSetRepsToggle(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserReferenceSetRepsToggle", userInfosModel);
        }
        public async Task<UserInfosModel> SetUserWorkoutReminderEmail(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserWorkoutReminderEmail", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserMobilityWithoutLoader(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserMobility", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserMobilityLevel(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserMobilityLevelReps", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserMobilityLevelWithoutLoader(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserMobilityLevel", userInfosModel);
        }

        public async Task<UserInfosModel> SetSwappedJson(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetSwappedJson", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserReminder(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserReminder", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserReminderWithoutLoader(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserReminder", userInfosModel);
        }
        public async Task<UserInfosModel> SetUserCardio(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserCardio", userInfosModel);
        }
        public async Task<UserInfosModel> SetUserBackOffSet(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserBackOffSet", userInfosModel);
        }

        public async Task<UserInfosModel> SetUser1By1Side(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUser1By1Side", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserStrength(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserStrength", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserExerciseCount(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserExerciseCount", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserTimeCount(UserInfosModel userInfosModel)
        {
            return await PostJson<UserInfosModel>("api/Account/SetUserTimeCount", userInfosModel);
        }
        public async Task<UserInfosModel> SetUserTimeCountWithoutLoader(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserTimeCount", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserTimerOptions(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserTimerOptions", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserTimerOptionsV2(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserTimerOptionsV2", userInfosModel);
        }

        public async Task<UserInfosModel> SetUserTimerOptionsV3(UserInfosModel userInfosModel)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/SetUserTimerOptionsV3", userInfosModel);
        }

        public async Task<BooleanModel> SendMessage(ChatModel model)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/SendMessage", model);
        }
        //
        public async Task<BooleanModel> SendAdminMessage(ChatModel model)
        {
            return await PostJson<BooleanModel>("api/Account/SendAdminMessage", model);
        }

        public async Task<BooleanModel> SendAdminMessageWithoutLoader(ChatModel model)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/SendAdminMessage", model);
        }

        public async Task<GroupChatModel> SendGroupMessage(GroupChatModel model)
        {
            return await PostJsonWithoughtLoader<GroupChatModel>("api/Account/SendGroupMessage", model);
        }

        public async Task<List<ChatRoomModel>> FetchInbox(bool isLoader)
        {
            if (isLoader)
                return await PostJson<List<ChatRoomModel>>("api/Account/FetchInbox", null);
            else
                return await PostJsonWithoughtLoader<List<ChatRoomModel>>("api/Account/FetchInbox", null);

        }
        public async Task<List<ChatRoomModel>> FetchInboxByType(bool isLoader, bool isRead)
        {
            if (isLoader)
                return await PostJson<List<ChatRoomModel>>("api/Account/FetchInboxByType", isRead ? 1 : 0);
            else
                return await PostJsonWithoughtLoader<List<ChatRoomModel>>("api/Account/FetchInboxByType", isRead ? 1 : 0);

        }
        public async Task<List<ChatModel>> FetchChatBoxWithoutLoader(ChatModel model)
        {
            return await PostJsonWithoughtLoader<List<ChatModel>>("api/Account/FetchChatBox", model);
        }
        public async Task<List<ChatModel>> FetchChatBox(ChatModel model)
        {
            return await PostJson<List<ChatModel>>("api/Account/FetchChatBox", model);
        }

        public async Task<List<GroupChatModel>> FetchGroupMessages(GroupChatModel model)
        {
            return await PostJsonWithoughtLoader<List<GroupChatModel>>("api/Account/FetchGroupMessages", model);
        }

        public async Task<BooleanModel> DeleteGroupChatMessage(GroupChatModel model)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/DeleteChatMessage", model);
        }

        public async Task<BooleanModel> MuteGroupChatUser(GroupChatModel model)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/SetMuteUser", model);
        }

        public async Task<BooleanModel> UnmuteGroupChatUser(GroupChatModel model)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/SetUnmuteUser", model);
        }

        public async Task<List<string>> GetMutedUsers()
        {
            return await PostJsonWithoughtLoader<List<string>>("api/Account/GetMutedUserList", null);
        }

        public async Task<BooleanModel> AddSatisfactionSurvey(SatisfactionSurveyModel model)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/AddSatisfactionSurvey", model);
        }
        public async Task<HistoryExerciseModel> GetUserWorkoutStatsWithoughtLoader()
        {
            return await PostJsonWithoughtLoader<HistoryExerciseModel>("/api/WorkoutLog/GetUserWorkoutStats", null);
        }

        //Chat started

        private string _token;
        public static DrMuscleRestClient Instance
        {
            get
            {
                if (_instance == null)
                    _instance = new DrMuscleRestClient();
                return _instance;
            }
        }

        public void SetToken(string token)
        {
            _token = token;
        }

        public async Task<BooleanModel> AddNewExerciseLog(NewExerciceLogModel model)
        {
            return await PostJson<BooleanModel>("api/Exercise/AddNewExerciseLog", model);
        }
        public async Task<BooleanModel> AddNewExerciseLogWithMoreSet(NewExerciceLogModel model)
        {
            return await PostJson<BooleanModel>("api/Exercise/AddNewExerciseLogWithMoreSet", model);
        }
        //

        public async Task<BooleanModel> AddWorkoutLogSerie(WorkoutLogSerieModel model)
        {
            return await PostJson<BooleanModel>("api/Exercise/AddWorkoutLogSerieNew", model);
        }



        public async Task<BooleanModel> AddWorkoutLogSerieList(List<WorkoutLogSerieModel> model)
        {
            return await PostJson<BooleanModel>("api/Exercise/AddWorkoutLogSerieListNew", model);
        }

        public async Task<BooleanModel> AddWorkoutLogSerieListwithoutLoader(List<WorkoutLogSerieModel> model)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Exercise/AddWorkoutLogSerieListNew", model, 0);
        }

        public async Task<BooleanModel> RenameWorkoutTemplate(WorkoutTemplateModel model)
        {
            return await PostJson<BooleanModel>("api/Workout/RenameWorkoutTemplate", model);
        }

        public async Task<GetUserWorkoutLogAverageResponse> GetUserWorkoutLogAverageForExerciseForPeriod(GetUserWorkoutLogAverageForExerciseRequest request)
        {
            return await PostJson<GetUserWorkoutLogAverageResponse>("/api/WorkoutLog/GetUserWorkoutLogAverageForExerciseForPeriod", request);
        }

        public async Task<GetUserWorkoutLogDate> GetUserWorkoutLogDate()
        {
            return await PostJsonWithoughtLoader<GetUserWorkoutLogDate>("/api/WorkoutLog/GetUserWorkoutLogDate", null);
        }



        private DrMuscleRestClient()
        {
            ResetBaseUrl();
        }

        public void ResetBaseUrl()
        {
            if (LocalDBManager.Instance.GetDBSetting("Environment") == null)
            {
                BaseUrl = "https://drmuscle.azurewebsites.net/";
                return;
            }
            if (LocalDBManager.Instance.GetDBSetting("Environment").Value == "Production")
                BaseUrl = "https://drmuscle.azurewebsites.net/";
            else
            {
                //BaseUrl = "http://*************:45455/";
                BaseUrl = "https://drmuscle2.azurewebsites.net/";
                //BaseUrl = "http://*************:45455/";
            }
        }

        public async Task<LoginSuccessResult> Login(LoginModel model)
        {
            HttpResponseMessage response;
            LoginSuccessResult token = null;
            using (var client = new HttpClient())
            {
                try
                {
                    client.BaseAddress = new Uri(BaseUrl);
                    var body = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("grant_type","password"),
                        new KeyValuePair<string, string>("password",model.Password),
                        new KeyValuePair<string, string>("username",model.Username)
                    };
                    var content = new FormUrlEncodedContent(body);
                    StartPost?.Invoke();
                    response = await client.PostAsync("token", content);

                    string raw = await response.Content.ReadAsStringAsync();
                    token = JsonConvert.DeserializeObject<LoginSuccessResult>(raw);
                    SetToken(token.access_token);

                }
                catch (Exception error)
                {
                    token = null;
                    response = new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest);
#if DEBUG
                    response.Content = new StringContent(string.Concat(error.Message, error.StackTrace));
#else
                    response.Content = new StringContent("Oops, an error occured, please try again");
#endif
                }
                finally
                {
                    EndPost?.Invoke();

                }
                return token;
            }
        }

        public async Task<LoginSuccessResult> LoginWithoutLoader(LoginModel model)
        {
            HttpResponseMessage response;
            LoginSuccessResult token = null;
            using (var client = new HttpClient())
            {
                try
                {
                    client.BaseAddress = new Uri(BaseUrl);
                    var body = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("grant_type","password"),
                        new KeyValuePair<string, string>("password",model.Password),
                        new KeyValuePair<string, string>("username",model.Username)
                    };
                    var content = new FormUrlEncodedContent(body);
                    //StartPost?.Invoke();
                    response = await client.PostAsync("token", content);

                    string raw = await response.Content.ReadAsStringAsync();
                    token = JsonConvert.DeserializeObject<LoginSuccessResult>(raw);
                    SetToken(token.access_token);

                }
                catch (Exception error)
                {
                    token = null;
                    response = new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest);
#if DEBUG
                    response.Content = new StringContent(string.Concat(error.Message, error.StackTrace));
#else
                    response.Content = new StringContent("Oops, an error occured, please try again");
#endif
                }
                finally
                {
                    //EndPost?.Invoke();

                }
                return token;
            }
        }

        public async Task<LoginSuccessResult> GoogleLogin(string GoogleToken, string email, string name, string bodyWeight, string massUnit, string userId ="")
        {
            HttpResponseMessage response;
            LoginSuccessResult token = null;
            using (var client = new HttpClient())
            {
                try
                {
                    client.BaseAddress = new Uri(BaseUrl);
                    var body = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("grant_type","google"),
                        new KeyValuePair<string, string>("accesstoken", GoogleToken),
                        new KeyValuePair<string, string>("provider", "google"),
                        new KeyValuePair<string, string>("email", email),
                        new KeyValuePair<string, string>("name", name),
                        new KeyValuePair<string, string>("bodyweight", bodyWeight),
                        new KeyValuePair<string, string>("massunit", massUnit),
                        new KeyValuePair<string, string>("userid", userId)
                    };
                    var content = new FormUrlEncodedContent(body);
                    StartPost?.Invoke();
                    response = await client.PostAsync("token", content);

                    string raw = await response.Content.ReadAsStringAsync();
                    token = JsonConvert.DeserializeObject<LoginSuccessResult>(raw);
                    SetToken(token.access_token);

                }
                catch (Exception error)
                {
                    token = null;
                    response = new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest);
#if DEBUG
                    response.Content = new StringContent(string.Concat(error.Message, error.StackTrace));
#else
                    response.Content = new StringContent("Oops, an error occured, please try again");
#endif
                }
                finally
                {
                    EndPost?.Invoke();

                }
                return token;
            }
        }

        public async Task<LoginSuccessResult> FacebookLogin(string FacebookToken, string bodyWeight, string massUnit)
        {
            HttpResponseMessage response;
            LoginSuccessResult token = null;
            using (var client = new HttpClient())
            {
                try
                {
                    client.BaseAddress = new Uri(BaseUrl);
                    var body = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("grant_type","facebook"),
                        new KeyValuePair<string, string>("accesstoken", FacebookToken),
                        new KeyValuePair<string, string>("provider", "facebook"),
                        new KeyValuePair<string, string>("bodyweight", bodyWeight),
                        new KeyValuePair<string, string>("massunit", massUnit)
                    };
                    var content = new FormUrlEncodedContent(body);
                    StartPost?.Invoke();
                    response = await client.PostAsync("token", content);

                    string raw = await response.Content.ReadAsStringAsync();
                    token = JsonConvert.DeserializeObject<LoginSuccessResult>(raw);
                    SetToken(token.access_token);

                }
                catch (Exception error)
                {
                    token = null;
                    response = new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest);
#if DEBUG
                    response.Content = new StringContent(string.Concat(error.Message, error.StackTrace));
#else
                    response.Content = new StringContent("Oops, an error occured, please try again");
#endif
                }
                finally
                {
                    EndPost?.Invoke();

                }
                return token;
            }
        }

        public async Task<BooleanModel> DeleteWorkoutTemplateModel(WorkoutTemplateModel model)
        {
            return await PostJson<BooleanModel>("api/Workout/DeleteWorkoutTemplate", model);
        }

        public async Task<BooleanModel> IsNewExercise(ExerciceModel model)
        {
            return await PostJson<BooleanModel>("api/Exercise/IsNewExercice", model);
        }

        public async Task<NewExerciseLogResponseModel> IsNewExerciseWithSessionInfo(ExerciceModel model)
        {
            return await PostJson<NewExerciseLogResponseModel>("api/Exercise/IsNewExerciseWithSessionInfo", model);
        }

        public async Task<NewExerciseLogResponseModel> IsNewExerciseWithSessionInfoWithoutLoader(ExerciceModel model)
        {
            return await PostJsonWithoughtLoader<NewExerciseLogResponseModel>("api/Exercise/IsNewExerciseWithSessionInfo", model);
        }

        public async Task<NewExerciseLogResponseModel> IsNewExerciseWithSessionInfoWithoutLoaderTimeOut(ExerciceModel model)
        {
            return await PostJsonWithoughtLoaderTimeout<NewExerciseLogResponseModel>("api/Exercise/IsNewExerciseWithSessionInfo", model, 4);
        }

        public async Task<BooleanModel> ResetPassword(ResetPasswordModel model)
        {
            return await PostJson<BooleanModel>("api/Account/ResetPassword", model);
        }

        public async Task<ExerciceModel> GetExercise(GetExerciseRequest model)
        {
            return await PostJson<ExerciceModel>("api/Exercise/GetExercise", model);
        }

        public async Task<ExerciceModel> CreateNewExercise(AddUserExerciseModel model)
        {
            return await PostJson<ExerciceModel>("api/Exercise/AddUserExercise", model);
        }

        public async Task<BooleanModel> RegisterUser(RegisterModel model)
        {
            return await PostJson<BooleanModel>("api/Account/Register", model);
        }

        //
        public async Task<BooleanModel> RegisterUserBeforeDemo(RegisterModel model)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/RegisterUserBeforeDemo", model);
        }

        public async Task<UserInfosModel> RegisterUserAfterDemo(RegisterModel model)
        {
            return await PostJsonWithoughtLoader<UserInfosModel>("api/Account/RegisterUserAfterDemoV2", model);
        }
        //
        public async Task<bool> RegisterUserSendEmail(RegisterModel model)
        {
            return await PostJsonWithoughtLoader<bool>("api/Account/RegisterUserSendEmail", model);
        }
        public async Task<UserInfosModel> RegisterWithUser(RegisterModel model)
        {
            return await PostJson<UserInfosModel>("api/Account/RegisterWithUser", model);
        }

        public async Task<BooleanModel> IsV1User()
        {
            return await PostJson<BooleanModel>("api/Account/IsV1User", null);
        }

        public async Task<BooleanModel> IsV1UserWithoutLoader()
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/IsV1User", null);
        }
        public async Task<BooleanModel> IsV1UserWithoutLoaderWithEmail(string email)
        {
            return await PostJsonWithoughtLoader<BooleanModel>($"api/Account/IsV1UserEmail?email={email}", email);
        }

        public async Task<List<UserWeight>> GetUserWeights()
        {
            return await PostJsonWithoughtLoader<List<UserWeight>>("api/Account/GetUserWeights", null);
        }


        public async Task<List<UserWeight>> GetUserWeightsV2()
        {
            return await PostJsonWithoughtLoader<List<UserWeight>>("api/Account/GetUserWeightsV2", null);
        }
        public async Task<List<UserWeight>> GetUserWeightsWithLoader()
        {
            return await PostJson<List<UserWeight>>("api/Account/GetUserWeights", null);
        }
        //
        public async Task<bool> UpdateUserWeightHistory(UserWeight weight)
        {
            return await PostJson<bool>("api/Account/UpdateUserWeightHistory", weight);
        }
        public async Task<bool> UpdateUserWeightHistoryWithOutLoader(UserWeight weight)
        {
            return await PostJsonWithoughtLoader<bool>("api/Account/UpdateUserWeightHistory", weight);
        }
        public async Task<bool> UpdateUserCaloriesWithOutLoader(CaloriesModel calory)
        {
            return await PostJsonWithoughtLoader<bool>("api/Account/UpdateUserCalories", calory);
        }

        public async Task<bool> DeleteUserWeightHistory(UserWeight weight)
        {
            return await PostJson<bool>("api/Account/DeleteUserWeightHistory", weight);
        }

        public async Task<DmmMeal> AddUserMealAsync(DmmMeal model)
        {
            return await PostJson<DmmMeal>("api/Account/PostDmmMeal", model);
        }

        public async Task<MealPlanModel> AddMealPlanAsync(DmmMealPlan model)
        {
            return await PostJson<MealPlanModel>("api/Account/PostDmmMealPlan", model);
        }

        public async Task<BooleanModel> IsMonthlyUser(bool isSilent = false)
        {
            if (isSilent)
                return await PostJsonWithoughtLoader<BooleanModel>("api/Account/IsMonthly", null);
            else
                return await PostJson<BooleanModel>("api/Account/IsMonthly", null);
        }

        public async Task<BooleanModel> IsV1UserWithoutLoaderQuick()
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/IsV1UserQuick", null);
        }

        public async Task<BooleanModel> SubscriptionDetail(SubscriptionModel model)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/SubscriptionDetail", model);
        }
        public async Task<BooleanModel> SubscriptionDetailWithId(SubscriptionModel model)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/SubscriptionDetailWithId", model);
        }

        public async Task<SubscriptionSourceModel> SubscriptionSource()
        {
            return await PostJsonWithoughtLoader<SubscriptionSourceModel>("api/Account/SubscriptionSource", null);
        }

        public async Task<BooleanModel> SubscriptionDetailIfNotExist(SubscriptionModel model)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/SubscriptionDetailIfNotExist", model);
        }

        public async Task<BooleanModel> EditWorkoutLogSeries(WorkoutLogSerieModel workoutLogSerie)
        {
            return await PostJson<BooleanModel>("api/WorkoutLog/EditUserWorkoutLogSerieNew", workoutLogSerie);
        }
        public async Task<BooleanModel> EditWorkoutLogSeriesWithoutLoader(WorkoutLogSerieModel workoutLogSerie)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/WorkoutLog/EditUserWorkoutLogSerieNew", workoutLogSerie);
        }
        public async Task<BooleanModel> AddEditUserWorkoutLogSerieListWithoutLoader(List<WorkoutLogSerieModel> workoutLogSeries)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/WorkoutLog/AddEditUserWorkoutLogSerieList", workoutLogSeries);
        }
        ///This will only update 1RM, take care before calling it
        public async Task<BooleanModel> UpdateWorkoutLogSerieUserById()
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/WorkoutLog/UpdateWorkoutLogSerieUserById", null);
        }

        public async Task<BooleanModel> DeleteWorkoutLogSeries(WorkoutLogSerieModel workoutLogSerie)
        {
            return await PostJson<BooleanModel>("api/WorkoutLog/DeleteUserWorkoutLogSerie", workoutLogSerie);
        }

        public async Task<BooleanModel> DeleteUserWorkoutLogExercise(DeleteWorkoutLogExerciseModel workoutLogSerie)
        {
            return await PostJson<BooleanModel>("api/WorkoutLog/DeleteUserWorkoutLogExercise", workoutLogSerie);
        }

        public async Task<BooleanModel> DeleteWorkoutLog(DeleteWorkoutLogExerciseModel workoutLogSerie)
        {
            return await PostJson<BooleanModel>("api/WorkoutLog/DeleteWorkoutLog", workoutLogSerie);
        }

        private async Task<T> PostJson<T>(string route, object model)
        {
            return await PostJson<T>(route, model, 1);
        }

        private async Task<T> PostJsonWithoughtLoader<T>(string route, object model)
        {
            return await PostJsonWithoughtLoader<T>(route, model, 4);
        }



        private async Task<T> PostJson<T>(string route, object model, int attempNr)
        {
            //Analytics.TrackEvent("PostJson", new Dictionary<string, string>() { { "route", route } });
            HttpResponseMessage httpResponse;
            using (var client = new HttpClient())
            {

                StartPost?.Invoke();

                try
                {
                    client.BaseAddress = new Uri(BaseUrl);
                    if (!string.IsNullOrEmpty(_token))
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", _token);

                    HttpContent content = new StringContent(JsonConvert.SerializeObject(model),
                                                            Encoding.UTF8,
                                                            "application/json");
                    System.Diagnostics.Debug.WriteLine($"From With Loader: {route}");
                    System.Diagnostics.Debug.WriteLine(JsonConvert.SerializeObject(model));
                    client.Timeout = TimeSpan.FromSeconds(100);
                    if (route.Contains("GetLogAverageWithSetsV2"))
                    {
                        client.Timeout = TimeSpan.FromSeconds(200);
                    }
                    else if (route.Contains("FetchInbox") ||
                             route.Contains("FetchChatBox") || route.Contains("GetCustomizedUserWorkout"))
                    {
                        //client.Timeout = TimeSpan.FromSeconds(26);
                    }
                    else
                    {
                        //client.Timeout = TimeSpan.FromSeconds(18);
                    }

                    httpResponse = await client.PostAsync(route, content);

                    //httpResponse = await client.PostAsJsonAsync(route, model);
                }
                catch (Exception error)
                {
                    if (attempNr > 0)
                        return await PostJson<T>(route, model, attempNr - 1);

                    var result = await HelperClass.DisplayCustomPopupForResult("Loading error","Slow or no connection. Please check and try again.","Retry loading","Cancel");

                    if(result == PopupAction.OK){
                        attempNr = 4;
                        return await PostJson<T>(route, model, attempNr - 1);
                    }
                    // ConfirmConfig ShowAlertPopUp = new ConfirmConfig()
                    // {
                    //     Title = "Loading error",
                    //     Message = "Slow or no connection. Please check and try again.",
                    //     AndroidStyleId = DependencyService.Get<IStyles>()
                    //         .GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     OkText = "Retry loading",
                    //     CancelText = "Cancel",

                    // };
                    // var results = await UserDialogs.Instance.ConfirmAsync(ShowAlertPopUp);
                    // if (results)
                    // {
                    //     attempNr = 4;
                    //     return await PostJson<T>(route, model, attempNr - 1);
                    // }
                    httpResponse = new HttpResponseMessage(HttpStatusCode.BadRequest);
#if DEBUG
                    httpResponse.Content = new StringContent(string.Concat(error.Message, error.StackTrace));
#else
                    httpResponse.Content = new StringContent("Oops, an error occured, please try again");
#endif
                }

                EndPost?.Invoke();
                switch (httpResponse.StatusCode)
                {
                    case HttpStatusCode.OK:
                        break;
                    case HttpStatusCode.Unauthorized:
                        Unauthorized?.Invoke();
                        break;
                    default:

                        break;
                }

                ApiResponse apiResponse;

                try
                {
                    string rawResponse = await httpResponse.Content.ReadAsStringAsync();
                    apiResponse = JsonConvert.DeserializeObject<ApiResponse>(rawResponse);
                }
                catch (Exception error)
                {
#if MOBILE_CENTER
                    //Analytics.TrackEvent("PostJson.DeserializeObjectException", new Dictionary<string, string>() { { "route", route }, { "ExceptionMessage", error.Message } });
#endif
#if DEBUG
                    apiResponse = new ApiResponse(HttpStatusCode.InternalServerError, new ErrorResponse() { Ex = error, Response = httpResponse }, error.Message);
#else
                    apiResponse = new ApiResponse(HttpStatusCode.InternalServerError, "", "Oops, an error occured, please try again later");
#endif
                }

                if (apiResponse.Result == null)
                {
                    return default(T); //(T)Activator.CreateInstance<T>();
                }

                if (apiResponse.Result.GetType() == typeof(JObject))
                    return ((JObject)apiResponse.Result).ToObject<T>();

                try
                {
                    return ((JArray)apiResponse.Result).ToObject<T>();
                }
                catch (Exception)
                {
#if MOBILE_CENTER
                    //Analytics.TrackEvent("PostJson.UnknownJsonType", new Dictionary<string, string>() { { "route", route } });
#endif
                    return default(T);
                }
            }
        }

        private async Task<T> PostJsonWithoughtLoader<T>(string route, object model, int attempNr)
        {
            //Analytics.TrackEvent("PostJson", new Dictionary<string, string>() { { "route", route } });
            HttpResponseMessage httpResponse;
            using (var client = new HttpClient())
            {
                try
                {
                    client.BaseAddress = new Uri(BaseUrl);
                    if (!string.IsNullOrEmpty(_token))
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", _token);
                    client.Timeout = TimeSpan.FromSeconds(100);
                    if (route.Contains("GetLogAverageWithSetsV2"))
                    {
                        client.Timeout = TimeSpan.FromSeconds(200);
                    }
                    else if (route.Contains("FetchInbox") ||
                             route.Contains("FetchChatBox")
                || route.Contains("SendMessage"))

                    {
                        //client.Timeout = TimeSpan.FromSeconds(26);
                    }
                    else
                    {

                        //client.Timeout = TimeSpan.FromSeconds(20);
                    }
                    HttpContent content = new StringContent(JsonConvert.SerializeObject(model),
                        Encoding.UTF8,
                        "application/json");

                    System.Diagnostics.Debug.WriteLine($"From WithoughtLoader: {route}");
                    if(route.Contains("GetUserIdByEmail?email"))
                    {
                        httpResponse = await client.GetAsync(route);
                    }
                    else
                        httpResponse = await client.PostAsync(route, content);

                    //httpResponse = await client.PostAsJsonAsync(route, model);
                }
                catch (Exception error)
                {
                    if (attempNr >= 0 && !route.Contains("Account/SendMessage"))
                    {
                        if (Connectivity.NetworkAccess != NetworkAccess.Internet && route.Contains("GetOneRMForExercise"))
                            await Task.Delay(12000);
                        return await PostJsonWithoughtLoader<T>(route, model, attempNr - 1);
                    }

                    httpResponse = new HttpResponseMessage(HttpStatusCode.BadRequest);
#if DEBUG
                    httpResponse.Content = new StringContent(string.Concat(error.Message, error.StackTrace));
#else
                    httpResponse.Content = new StringContent("Oops, an error occured, please try again");
#endif
                }


                switch (httpResponse.StatusCode)
                {
                    case HttpStatusCode.OK:
                        break;
                    case HttpStatusCode.Unauthorized:
                        Unauthorized?.Invoke();
                        break;
                    default:

                        break;
                }

                ApiResponse apiResponse;

                try
                {
                    string rawResponse = await httpResponse.Content.ReadAsStringAsync();
                    apiResponse = JsonConvert.DeserializeObject<ApiResponse>(rawResponse);
                }
                catch (Exception error)
                {
#if MOBILE_CENTER
                    //Analytics.TrackEvent("PostJson.DeserializeObjectException", new Dictionary<string, string>() { { "route", route }, { "ExceptionMessage", error.Message } });
#endif
#if DEBUG
                    apiResponse = new ApiResponse(HttpStatusCode.InternalServerError, new ErrorResponse() { Ex = error, Response = httpResponse }, error.Message);
#else
                    apiResponse = new ApiResponse(HttpStatusCode.InternalServerError, "", "Oops, an error occured, please try again later");
#endif
                }

                if (apiResponse.Result == null)
                {
                    return default(T); //(T)Activator.CreateInstance<T>();
                }
                if (route.Contains("GetUserIdByEmail?email"))
                {
                    if (apiResponse.Result.ToString() is T result)
                    {
                        return result;
                    }
                }
                if (apiResponse.Result.GetType() == typeof(JObject))
                    return ((JObject)apiResponse.Result).ToObject<T>();

                try
                {
                    return ((JArray)apiResponse.Result).ToObject<T>();
                }
                catch (Exception ex)
                {
#if MOBILE_CENTER
                    //Analytics.TrackEvent("PostJson.UnknownJsonType", new Dictionary<string, string>() { { "route", route } });
#endif
                    return default(T);
                }
            }
        }

        private async Task<T> PostJsonWithoughtLoaderTimeout<T>(string route, object model, int attempNr)
        {
            //Analytics.TrackEvent("PostJson", new Dictionary<string, string>() { { "route", route } });
            HttpResponseMessage httpResponse;
            using (var client = new HttpClient())
            {
                try
                {
                    client.BaseAddress = new Uri(BaseUrl);
                    //client.Timeout = TimeSpan.FromSeconds(20);
                    if (!string.IsNullOrEmpty(_token))
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", _token);
                    System.Diagnostics.Debug.WriteLine(JsonConvert.SerializeObject(model));
                    HttpContent content = new StringContent(JsonConvert.SerializeObject(model),
                        Encoding.UTF8,
                        "application/json");

                    System.Diagnostics.Debug.WriteLine($"From WithoughtLoader Timeout: {route}");
                    httpResponse = await client.PostAsync(route, content);

                    //httpResponse = await client.PostAsJsonAsync(route, model);
                }



                catch (Exception error)
                {
                    //AlertConfig ShowAlertPopUp = new AlertConfig()
                    //{
                    //    Title = "Connection error",
                    //    Message = AppResources.PleaseCheckInternetConnection,
                    //    AndroidStyleId = Xamarin.Forms.DependencyService.Get<IStyles>()
                    //        .GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //    OkText = "Try again",
                    //    // CancelText = "Cancel",

                    //};
                    //await UserDialogs.Instance.AlertAsync(ShowAlertPopUp);
                    if (attempNr >= 0)
                        return await PostJsonWithoughtLoaderTimeout<T>(route, model, attempNr - 1);
                    httpResponse = new HttpResponseMessage(HttpStatusCode.BadRequest);
#if DEBUG
                    httpResponse.Content = new StringContent(string.Concat(error.Message, error.StackTrace));
#else
                    httpResponse.Content = new StringContent("Oops, an error occured, please try again");
#endif
                }


                switch (httpResponse.StatusCode)
                {
                    case HttpStatusCode.OK:
                        break;
                    case HttpStatusCode.Unauthorized:
                        Unauthorized?.Invoke();
                        break;
                    default:

                        break;
                }

                ApiResponse apiResponse;

                try
                {
                    string rawResponse = await httpResponse.Content.ReadAsStringAsync();
                    apiResponse = JsonConvert.DeserializeObject<ApiResponse>(rawResponse);
                }
                catch (Exception error)
                {
#if MOBILE_CENTER
                    //Analytics.TrackEvent("PostJson.DeserializeObjectException", new Dictionary<string, string>() { { "route", route }, { "ExceptionMessage", error.Message } });
#endif
#if DEBUG
                    apiResponse = new ApiResponse(HttpStatusCode.InternalServerError, new ErrorResponse() { Ex = error, Response = httpResponse }, error.Message);
#else
                    apiResponse = new ApiResponse(HttpStatusCode.InternalServerError, "", "Oops, an error occured, please try again later");
#endif
                }

                if (apiResponse.Result == null)
                {
                    return default(T); //(T)Activator.CreateInstance<T>();
                }

                if (apiResponse.Result.GetType() == typeof(JObject))
                    return ((JObject)apiResponse.Result).ToObject<T>();

                try
                {
                    return ((JArray)apiResponse.Result).ToObject<T>();
                }
                catch (Exception)
                {
#if MOBILE_CENTER
                    //Analytics.TrackEvent("PostJson.UnknownJsonType", new Dictionary<string, string>() { { "route", route } });
#endif
                    return default(T);
                }
            }
        }

        public async Task<BooleanModel> ResetExercise(ExerciceModel model)
        {
            return await PostJson<BooleanModel>("api/Exercise/ResetExercise", model);
        }

        public async Task<BooleanModel> ResetAllExercise()
        {
            return await PostJson<BooleanModel>("api/Exercise/ResetAllExercise", null);
        }

        public async Task<BooleanModel> RenameWorkoutTemplateGroup(WorkoutTemplateGroupModel model)
        {
            return await PostJson<BooleanModel>("api/Workout/RenameWorkoutTemplateGroup", model);
        }

        public async Task<BooleanModel> CreateNewWorkoutTemplate(WorkoutTemplateModel model)
        {
            return await PostJson<BooleanModel>("api/Workout/CreateNewWorkoutTemplate", model);
        }
        public async Task<BooleanModel> CreateNewWorkoutTemplateWithoutLoader(WorkoutTemplateModel model)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Workout/CreateNewWorkoutTemplate", model);
        }
        //
        public async Task<BooleanModel> CreateNewUserWorkoutTemplate(WorkoutTemplateModel model)
        {
            return await PostJson<BooleanModel>("api/Workout/CreateNewUserWorkoutTemplate", model);
        }
        public async Task<BooleanModel> CreateNewUserWorkoutTemplateWithoutLoader(WorkoutTemplateModel model)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Workout/CreateNewUserWorkoutTemplate", model);
        }
        public async Task<BooleanModel> RenameExercise(ExerciceModel model)
        {
            return await PostJson<BooleanModel>("api/Exercise/RenameExercise", model);
        }

        public async Task<BooleanModel> DeleteWorkoutTemplateGroupModel(WorkoutTemplateGroupModel model)
        {
            return await PostJson<BooleanModel>("api/Workout/DeleteWorkoutGroupTemplate", model);
        }

        public async Task<BooleanModel> DeleteExercise(ExerciceModel model)
        {
            return await PostJson<BooleanModel>("api/Exercise/DeleteExercise", model);
        }

        public async Task<BooleanModel> DeleteAccount()
        {
            return await PostJson<BooleanModel>("api/Account/DeleteUser", null);
        }
        public async Task<BooleanModel> IsEmailAlreadyExist(IsEmailAlreadyExistModel email)
        {
            return await PostJson<BooleanModel>("api/Account/IsEmailAlreadyExist", email);
        }
        public async Task<BooleanModel> IsEmailAlreadyExistbyAppleId(IsEmailAlreadyExistModel email)
        {
            return await PostJson<BooleanModel>("api/Account/IsEmailAlreadyExistbyAppleId", email);
        }

        public async Task<BooleanModel> IsEmailAlreadyExistWithoutLoader(IsEmailAlreadyExistModel email)
        {
            return await PostJsonWithoughtLoader<BooleanModel>("api/Account/IsEmailAlreadyExist", email);
        }

        public async Task<BooleanModel> UpdateEmail(IsEmailAlreadyExistModel email)
        {
            return await PostJson<BooleanModel>("api/Account/UpdateEmail", email);
        }

        public async Task<GetUserProgramInfoResponseModel> GetUserProgramInfo()
        {
            return await PostJson<GetUserProgramInfoResponseModel>("api/Workout/GetUserProgramInfo", null);
        }

        public async Task<GetUserExerciseResponseModel> GetUserExercise(string userName)
        {
            return await PostJson<GetUserExerciseResponseModel>("api/Exercise/GetUserExercise", userName);
        }


        public async Task<GetUserExerciseResponseModel> GetCustomExerciseForUser(string userName)
        {
            return await PostJsonWithoughtLoader<GetUserExerciseResponseModel>("api/Exercise/GetCustomExerciseForUser", userName);
        }
        public async Task<GetUserExerciseResponseModel> GetCustomExerciseForUserWithLoader(string userName)
        {
            return await PostJson<GetUserExerciseResponseModel>("api/Exercise/GetCustomExerciseForUser", userName);
        }

        public async Task<BooleanModel> IsAlive()
        {
            return await PostJson<BooleanModel>("api/Account/IsAlive", null);
        }

        public async Task<GetUserWorkoutLogAverageResponse> GetUserWorkoutLogAverage()
        {
            return await PostJson<GetUserWorkoutLogAverageResponse>("/api/WorkoutLog/GetUserWorkoutLogAverageV2", null);
        }
        public async Task<GetUserWorkoutLogAverageResponse> GetUserWorkoutLogAverageWithoutLoader()
        {
            return await PostJsonWithoughtLoader<GetUserWorkoutLogAverageResponse>("/api/WorkoutLog/GetUserWorkoutLogAverageV2", null);
        }
        //
        public async Task<GetUserWorkoutLogAverageResponse> GetUserWorkoutLogAverageWithUserStats()
        {
            return await PostJson<GetUserWorkoutLogAverageResponse>("/api/WorkoutLog/GetUserWorkoutLogAverageWithUserStatsV2", null);
        }
        public async Task<GetUserWorkoutLogAverageResponse> GetUserWorkoutLogAverageWithUserStatsWithoutLoader()
        {
            return await PostJsonWithoughtLoader<GetUserWorkoutLogAverageResponse>("/api/WorkoutLog/GetUserWorkoutLogAverageWithUserStatsV2", null);
        }
        public async Task<GetUserWorkoutLogAverageResponse> GetUserWorkoutLogAverageWithSets(TimeZoneInfo local)
        {
            //GetUserWorkoutLogAverageWithSetsTimeZoneInfo
            return await PostJson<GetUserWorkoutLogAverageResponse>("/api/WorkoutLog/GetUserWorkoutLogAverageWithSetsTimeZoneInfoV2", local);
        }

        public async Task<GetUserWorkoutLogAverageResponse> GetUserWorkoutLogAverageWithSetsWithoutLoader(TimeZoneInfo local)
        {
            return await PostJsonWithoughtLoader<GetUserWorkoutLogAverageResponse>("/api/WorkoutLog/GetUserWorkoutLogAverageWithSetsTimeZoneInfoV2", local);
        }

        public async Task<GetUserWorkoutLogAverageResponse> GetUserWorkoutProgramTimeZoneInfoWithoutLoader(TimeZoneInfo local)
        {
            return await PostJsonWithoughtLoader<GetUserWorkoutLogAverageResponse>("/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo", local);
        }

        public async Task<GetUserWorkoutLogAverageResponse> GetUserWorkoutProgramTimeZoneInfo(TimeZoneInfo local)
        {
            return await PostJson<GetUserWorkoutLogAverageResponse>("/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo", local);
        }
        public async Task<GetUserWorkoutLogAverageResponse> GetLogAverageWithSetsWithoughtLoader()
        {
            return await PostJsonWithoughtLoader<GetUserWorkoutLogAverageResponse>("/api/WorkoutLog/GetLogAverageWithSetsV2", null);
        }
        public async Task<List<ConsecutiveWeeksModel>> GetWeekStreaksWithoughtLoader()
        {
            return await PostJsonWithoughtLoader<List<ConsecutiveWeeksModel>>("/api/WorkoutLog/GetWeekStreaks", null);
        }

        public async Task<GetUserWorkoutLogAverageResponse> GetLogAverageWithSets()
        {
            return await PostJson<GetUserWorkoutLogAverageResponse>("/api/WorkoutLog/GetLogAverageWithSetsV2", null);
        }

        public async Task<BooleanModel> SetRepsMinimum(SingleIntegerModel val)
        {
            return await PostJson<BooleanModel>("/api/Account/SetRepsMinimum", val);
        }

        public async Task<BooleanModel> SetRepsMaximum(SingleIntegerModel val)
        {
            return await PostJson<BooleanModel>("/api/Account/SetRepsMaximum", val);
        }
        public async Task<BooleanModel> SetRepsRangeType(SingleIntegerModel val)
        {
            return await PostJson<BooleanModel>("/api/Account/SetRepsRangeType", val);
        }
        //
        public async Task<SingleIntegerModel> GetRepsMinimum()
        {
            return await PostJson<SingleIntegerModel>("/api/Account/GetRepsMinimum", null);
        }

        public async Task<SingleIntegerModel> GetRepsMaximum()
        {
            return await PostJson<SingleIntegerModel>("/api/Account/GetRepsMaximum", null);
        }
        public async Task<string> GetUserIdByEmail(string email)
        {
            var data = await PostJsonWithoughtLoader<string>($"api/Account/GetUserIdByEmail?email={email}", null);
            return data;
        }

        public async Task<bool> SaveUserNextWorkoutDetail(UserNextWorkoutDetail model)
        {
            return await PostJson<bool>("api/Workout/SaveUserNextWorkoutDetail", model);
        }
        public async Task<UserNextWorkoutDetail> GetUserNextWorkoutDetail(UserNextWorkoutDetail model)
        {
            return await PostJson<UserNextWorkoutDetail>("api/Workout/GetUserNextWorkoutDetail", model);
        }
    }
}
