﻿using DrMaxMuscle.Layout;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Helpers
{
    public class WorkoutLogContext
    {
        public WorkoutLogContext()
        {
        }

        public Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>> WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();

        public string ToJson()
        {
            return JsonConvert.SerializeObject(this);
        }

        public static WorkoutLogContext FromJson(string json)
        {
            return JsonConvert.DeserializeObject<WorkoutLogContext>(json);
        }

        public void SaveContexts()
        {
            LocalDBManager.Instance.SetDBSetting("workoutlog_contexts", ToJson());
        }

        public static WorkoutLogContext LoadContexts()
        {
            DBSetting workoutListContexts = LocalDBManager.Instance.GetDBSetting("workoutlog_contexts");

            if (workoutListContexts == null)
            {
                WorkoutLogContext tmp = new WorkoutLogContext();
                tmp.SaveContexts();
                return tmp;
            }

            return WorkoutLogContext.FromJson(workoutListContexts.Value);
        }

    }
}
