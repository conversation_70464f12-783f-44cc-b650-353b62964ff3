﻿
using DrMaxMuscle.Dependencies;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Resources;
using System.Text;
using System.Threading.Tasks;
using ResourceLoader = DrMaxMuscle.Helpers.ResourceLoader;

namespace DrMaxMuscle.Resx
{
    [ContentProperty("Text")]
    public class TranslateExtension : IMarkupExtension
    {
        readonly CultureInfo ci = null;
        const string ResourceId = "DrMaxMuscle.Resx.AppResources";

        public TranslateExtension()
        {
            if (Device.RuntimePlatform == Device.iOS || Device.RuntimePlatform == Device.Android)
            {
                try
                {
                    // Uncomment code please
                    //ci = DependencyService.Get<ILocalize>().GetCurrentCultureInfo();

                }
                catch (Exception ex)
                {

                }
            }
        }

        public string Text { get; set; }

        public object ProvideValue(IServiceProvider serviceProvider)
        {
            if (Text == null)
                return "";
            try
            {

                ResourceManager resourceManager = new ResourceManager(ResourceId, typeof(TranslateExtension).GetTypeInfo().Assembly);
                if (ResourceLoader.Instance == null)
                    new ResourceLoader(resourceManager);
                var translation = resourceManager.GetString(Text, ci);

                return translation;

            }
            catch (Exception ex)
            {
                return "";
            }
        }
    }
}
