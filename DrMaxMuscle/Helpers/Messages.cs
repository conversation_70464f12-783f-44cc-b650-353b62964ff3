﻿using DrMaxMuscle.Constants;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Helpers
{
    public class Messages : INotifyPropertyChanged
    {
        private bool isUnRead;

        public bool IsUnread
        {
            get { return isUnRead; }
            set
            {
                if (isUnRead != value)
                {
                    isUnRead = value;
                    OnPropertyChanged(nameof(IsUnread));
                }
            }
        }

        private string userId;

        public string UserId
        {
            get { return userId; }
            set
            {
                if (userId != value)
                {
                    userId = value;
                    OnPropertyChanged(nameof(UserId));
                }
            }
        }

        private string message;

        public string Message
        {
            get { return message; }
            set
            {
                if (message != value)
                {
                    message = value;
                    OnPropertyChanged(nameof(Message));
                }
            }
        }

        private long messageId;

        public long MessageId
        {
            get { return messageId; }
            set
            {
                if (messageId != value)
                {
                    messageId = value;
                    OnPropertyChanged(nameof(MessageId));
                }
            }
        }

        private string senderId;

        public string SenderId
        {
            get { return senderId; }
            set
            {
                if (senderId != value)
                {
                    senderId = value;
                    OnPropertyChanged(nameof(SenderId));
                }
            }
        }

        private string profileUrl;

        public string ProfileUrl
        {
            get { return profileUrl; }
            set
            {
                if (profileUrl != value)
                {
                    profileUrl = value;
                    OnPropertyChanged(nameof(ProfileUrl));
                }
            }
        }

        private string nickname;

        public string Nickname
        {
            get { return nickname; }
            set
            {
                if (nickname != value)
                {
                    nickname = value;
                    OnPropertyChanged(nameof(Nickname));
                }
            }
        }

        private DateTime dateTime;

        public DateTime CreatedDate
        {
            get { return dateTime; }
            set
            {
                if (dateTime != value)
                {
                    dateTime = value;
                    OnPropertyChanged(nameof(CreatedDate));
                }
            }
        }

        private bool isFromAI;

        public bool IsFromAI
        {
            get { return isFromAI; }
            set
            {
                if (isFromAI != value)
                {
                    isFromAI = value;
                    OnPropertyChanged(nameof(IsFromAI));
                }
            }
        }

        private bool isSend;

        public bool IsSend
        {
            get { return isSend; }
            set
            {
                if (isSend != value)
                {
                    isSend = value;
                    OnPropertyChanged(nameof(IsSend));
                }
            }
        }
        //public bool IsUnread
        //{
        //    get { return _isUnread; }
        //    set
        //    {
        //        _isUnread = value;
        //    }
        //}

        private long createdAt;

        public long CreatedAt
        {
            get { return createdAt; }
            set
            {
                if (createdAt != value)
                {
                    createdAt = value;
                    OnPropertyChanged(nameof(CreatedAt));
                }
            }
        }

        private ChannelType chatType;

        public ChannelType ChatType
        {
            get { return chatType; }
            set
            {
                if (chatType != value)
                {
                    chatType = value;
                    OnPropertyChanged(nameof(ChatType));
                }
            }
        }

        private string supportChannelUrl;

        public string SupportChannelUrl
        {
            get { return supportChannelUrl; }
            set
            {
                if (supportChannelUrl != value)
                {
                    supportChannelUrl = value;
                    OnPropertyChanged(nameof(SupportChannelUrl));
                }
            }
        }

        private string adminId;

        public string AdminId
        {
            get { return adminId; }
            set
            {
                if (adminId != value)
                {
                    adminId = value;
                    OnPropertyChanged(nameof(AdminId));
                }
            }
        }

        private string normalUSerEmail;

        public string NormalUSerEmail
        {
            get { return normalUSerEmail; }
            set
            {
                if (normalUSerEmail != value)
                {
                    normalUSerEmail = value;
                    OnPropertyChanged(nameof(NormalUSerEmail));
                }
            }
        }

        private string normalUSerName;

        public string NormalUSerName
        {
            get { return normalUSerName; }
            set
            {
                if (normalUSerName != value)
                {
                    normalUSerName = value;
                    OnPropertyChanged(nameof(NormalUSerName));
                }
            }
        }

        private string normalUSerId;

        public string NormalUSerId
        {
            get { return normalUSerId; }
            set
            {
                if (normalUSerId != value)
                {
                    normalUSerId = value;
                    OnPropertyChanged(nameof(NormalUSerId));
                }
            }
        }


        private bool isBothReplied;

        public bool IsBothReplied
        {
            get { return isBothReplied; }
            set
            {
                if (isBothReplied != value)
                {
                    isBothReplied = value;
                    OnPropertyChanged(nameof(IsBothReplied));
                }
            }
        }

        private long chatRoomId;

        public long ChatRoomId
        {
            get { return chatRoomId; }
            set
            {
                if (chatRoomId != value)
                {
                    chatRoomId = value;
                    OnPropertyChanged(nameof(ChatRoomId));
                }
            }
        }

        private bool isV1User;

        public bool IsV1User
        {
            get { return isV1User; }
            set
            {
                if (isV1User != value)
                {
                    isV1User = value;
                    OnPropertyChanged(nameof(IsV1User));
                }
            }
        }
        public string TImeAgo
        {
            get { return AppThemeConstants.ChatTimeAgoFromDate(CreatedDate); }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class GroupChannelType : ObservableCollection<Messages>
    {
        public string Name { get; set; }
        public ChannelType Type { get; set; }
    }

    public enum ChannelType
    {
        Open,
        Group
    }
}
