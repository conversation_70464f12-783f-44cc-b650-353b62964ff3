<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             x:Class="DrMaxMuscle.Controls.CustomImageButton">
    <StackLayout
    Grid.Row="2"
    Grid.Column="0"
    Grid.ColumnSpan="2"
    Spacing="10"
    Orientation="Horizontal">
        <StackLayout.GestureRecognizers>
            <TapGestureRecognizer
            Tapped="Button_Tapped" />
        </StackLayout.GestureRecognizers>
        <Image
        x:Name="ButtonImage"
        VerticalOptions="Center"
        HorizontalOptions="Start"
        WidthRequest="35"
        HeightRequest="35"
        Aspect="AspectFit" />
        <Label
        x:Name="ButtonText"
        Text="F4 Select Club"
        VerticalOptions="Center"
        VerticalTextAlignment="Center"
        HorizontalOptions="Start"
        TextColor="{x:Static app:AppThemeConstants.OffBlackColor}"
         />
    </StackLayout>
</ContentView>
