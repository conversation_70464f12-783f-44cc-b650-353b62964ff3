﻿using System.Xml;

namespace DrMaxMuscle.Controls;

public partial class DrMuscleImageButton : Frame
{
    #region Bindable Property
    public static readonly BindableProperty SourceProperty = BindableProperty.Create(nameof(Source), typeof(ImageSource), typeof(DrMuscleImageButton), null, BindingMode.TwoWay);
    public static readonly BindableProperty TextProperty = BindableProperty.Create(nameof(Text), typeof(string), typeof(DrMuscleImageButton), null, BindingMode.TwoWay);
    public static readonly BindableProperty FontSizeProperty = BindableProperty.Create(nameof(FontSize), typeof(string), typeof(DrMuscleImageButton), null, BindingMode.TwoWay);
    #endregion

    #region Public Properties
    public ImageSource Source
    {
        get { return (ImageSource)GetValue(SourceProperty); }
        set { SetValue(SourceProperty, value); }
    }
    public string Text
    {
        get { return (string)GetValue(TextProperty); }
        set { SetValue(TextProperty, value); }
    }
    public string FontSize
    {
        get { return (string)GetValue(FontSizeProperty); }
        set { SetValue(FontSizeProperty, value); }
    }
    #endregion

    #region Events & Commands
    public event EventHandler<EventArgs> Clicked;
    #endregion

    #region Constructor
    public DrMuscleImageButton()
    {
        InitializeComponent();
        Img.BindingContext = this;
        LblText.BindingContext = this;
    }
    #endregion

    #region Methods
    void TapGestureRecognizer_Tapped(object sender, EventArgs e)
    {
        Clicked?.Invoke(sender, e);
    }
    #endregion
}
