<?xml version="1.0" encoding="utf-8" ?>
<DataTemplate xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Controls.CalendarHeaderView"
             >
    
    <Grid
    Margin="0,2"
    Padding="0"
    HorizontalOptions="FillAndExpand"
    IsVisible="{Binding ShowMonthPicker}"
    VerticalOptions="Start">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="1*" />
            <ColumnDefinition Width="3*" />
            <ColumnDefinition Width="1*" />
        </Grid.ColumnDefinitions>

        <Frame
        Grid.Column="0"
        Padding="0"
            BorderColor="Transparent"
        BackgroundColor="White"
        Rotation="180"
        CornerRadius="18"
        HasShadow="False"
        HeightRequest="36"
        HorizontalOptions="CenterAndExpand"
        VerticalOptions="Center"
        WidthRequest="36">
            <Image
            
            WidthRequest="30"
            HeightRequest="30"
            Source="calarrow"
            HorizontalOptions="Center"
            VerticalOptions="Center"
            InputTransparent="True"
             />

            <Frame.GestureRecognizers>
                <TapGestureRecognizer Command="{Binding PrevLayoutUnitCommand}" />
            </Frame.GestureRecognizers>
        </Frame>

        <Label
        Grid.Column="1"
        FontAttributes="Bold"
        FontSize="Medium"
        HorizontalOptions="Center"
        TextColor="{Binding MonthLabelColor}"
        VerticalOptions="Center">
            <Label.FormattedText>
                <FormattedString>
                    <Span Text="{Binding Month,Converter={StaticResource MonthNameConverter}}" />
                    <Span Text=", " />
                    <Span Text="{Binding Year, Mode=TwoWay}" />
                </FormattedString>
            </Label.FormattedText>
        </Label>

        <Frame
            BorderColor="Transparent"
        Grid.Column="2"
        Padding="0"
        BackgroundColor="White"
        
        CornerRadius="18"
        HasShadow="False"
        HeightRequest="36"
        HorizontalOptions="CenterAndExpand"
        VerticalOptions="CenterAndExpand"
        WidthRequest="36">
            <Image
            WidthRequest="30"
            HeightRequest="30"
            Source="calarrow"

            HorizontalOptions="Center"
            VerticalOptions="Center"
            InputTransparent="True"
             />


            <Frame.GestureRecognizers>
                <!--<TapGestureRecognizer Command="{Binding NextMonthCommand}" />-->
                <TapGestureRecognizer Command="{Binding NextLayoutUnitCommand}" />
            </Frame.GestureRecognizers>
        </Frame>
    </Grid>
</DataTemplate>