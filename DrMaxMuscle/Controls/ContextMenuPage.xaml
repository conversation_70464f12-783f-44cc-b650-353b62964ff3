﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:PopupPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
                   xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                   x:Class="DrMaxMuscle.Controls.ContextMenuPage"
                   x:Name="ContextMenuPopup"
                   CloseWhenBackgroundIsClicked="True"
                   xmlns:t="clr-namespace:DrMaxMuscle.Layout"
                   xmlns:toolkit="clr-namespace:RGPopup.Maui.Pages;assembly=RGPopup.Maui">

    <Border
        x:Name="MainFrame"
        HorizontalOptions="End"
        VerticalOptions="Start"
        BackgroundColor="#123b54"
        Stroke="Transparent"
        Padding="{OnPlatform Android='0,0,0,0', iOS='0,0,0,0'}">
        <Border.StrokeShape>
            <RoundRectangle CornerRadius="2 "/>
        </Border.StrokeShape>
        <Border.GestureRecognizers>
            <TapGestureRecognizer Tapped="PageTapGestureRecognizer_Tapped"/>
        </Border.GestureRecognizers>
        <Grid VerticalOptions="Start">
            <t:DrMuscleListView
                x:Name="MenuListView"
                RowHeight="48"
                Margin="{OnPlatform Android='0,0,0,0', iOS='0,0,0,5'}"
                BackgroundColor="Transparent"
                SeparatorVisibility="None"
                VerticalScrollBarVisibility="Never"
                ItemsSource="{Binding Items, Source={Reference ContextMenuPopup}}">
                <t:DrMuscleListView.ItemTemplate>
                    <DataTemplate>

                        <ViewCell>
                            <StackLayout Orientation="Vertical"
                                         VerticalOptions="FillAndExpand"
                                         Spacing="0"
                                         BackgroundColor="#123b54">
                                <StackLayout.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped_1"/>
                                </StackLayout.GestureRecognizers>
                                <Border BackgroundColor="#77FFFFFF"
                                        HeightRequest="0.5"
                                        Margin="-15,0"
                                        IsVisible="{Binding IsDestructive}"></Border>
                                <Label Margin="15,0"
                                       HorizontalOptions="StartAndExpand"
                                       VerticalTextAlignment="Center"
                                       VerticalOptions="CenterAndExpand"
                                       FontSize="18"
                                       TextColor="White"
                                       Text="{Binding Text}"/>
                            </StackLayout>
                        </ViewCell>
                    </DataTemplate>
                </t:DrMuscleListView.ItemTemplate>
            </t:DrMuscleListView>
        </Grid>

    </Border>
</toolkit:PopupPage>