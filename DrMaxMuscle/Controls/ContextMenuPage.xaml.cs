namespace DrMaxMuscle.Controls;
using CommunityToolkit.Maui.Views;
using RGPopup.Maui.Extensions;
using System.Linq;
using RGPopup.Maui.Pages;
using Microcharts;
using System.Threading.Tasks;

public partial class ContextMenuPage : PopupPage
{
    private double _width;
    private double _height;


    #region ItemsContainerWidth

    public static readonly BindableProperty ItemsContainerWidthProperty = BindableProperty.Create(
        nameof(ItemsContainerWidth),
        typeof(double),
        typeof(ContextMenuPage),
        propertyChanged: OnItemsContainerWidthChanged);

    private static void OnItemsContainerWidthChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is ContextMenuPage contextMenuPage && newValue is double newValueDouble)
        {
            contextMenuPage.MainFrame.WidthRequest = newValueDouble;
        }
    }

    public double ItemsContainerWidth
    {
        get { return (double)GetValue(ItemsContainerWidthProperty); }
        set { SetValue(ItemsContainerWidthProperty, value); }
    }

    #endregion

    #region ItemsContainerHeight

    public static readonly BindableProperty ItemsContainerHeightProperty = BindableProperty.Create(
        nameof(ItemsContainerHeight),
        typeof(double),
        typeof(ContextMenuPage),
        propertyChanged: OnItemsContainerHeightChanged);

    private static void OnItemsContainerHeightChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is ContextMenuPage contextMenuPage && newValue is double newValueDouble)
        {
            contextMenuPage.MainFrame.HeightRequest = newValueDouble;
        }
    }

    public double ItemsContainerHeight
    {
        get { return (double)GetValue(ItemsContainerHeightProperty); }
        set { SetValue(ItemsContainerHeightProperty, value); }
    }

    #endregion


    private IEnumerable<MenuItem> _items;
    public IEnumerable<MenuItem> Items
    {
        get => _items;
        set
        {
            _items = value;
            OnPropertyChanged();
        }
    }

    public ContextMenuPage(IEnumerable<MenuItem> items)
    {
        Items = items;
        InitializeComponent();
        this.turnOffScroll();
        this.setHeightForAnroid();
    }

    public void turnOffScroll() 
    {
        #if IOS
        MenuListView.HandlerChanged += (s, e) =>
        {
            if (MenuListView.Handler?.PlatformView is UIKit.UITableView nativeTable)
            {
                nativeTable.ScrollEnabled = false; // Disable scrolling
            }
        };
        #endif
    }

    public async Task setHeightForAnroid() 
    {
        #if ANDROID
        await Task.Delay(100);
        var count = this.Items.Count();
        MainFrame.HeightRequest = (48 * count) + 5;
        #endif
    }

    public void SetPosition(int leftCenter, int leftOffset)
    {
        var screenHeight = (int)Application.Current.MainPage.Height;
        var screenWidth = (int)Application.Current.MainPage.Width;

        var marginTop = (screenHeight - (int)MainFrame.HeightRequest) / 2;
        var marginLeft = GetStartCoordinate(screenWidth, (int)MainFrame.WidthRequest, leftCenter, leftOffset);

        MainFrame.Margin = new Thickness(marginLeft, marginTop, 0, 0);
    }

    private int GetStartCoordinate(int screeSize, int menuSize, int baseCoordinate, int shift)
    {
        if (baseCoordinate + shift + menuSize > screeSize)
        {
            return System.Math.Max(0, baseCoordinate - shift - menuSize);
        }
        return baseCoordinate + shift;
    }

    private async void ItemTapGestureRecognizer_Tapped(object sender, System.EventArgs e)
    {
        if (sender is StackLayout senderControl && senderControl.BindingContext is MenuItem MenuItem)
        {
            //await this.CloseAsync();
            await Navigation.PopPopupAsync();
            MenuItem.Command?.Execute(MenuItem.CommandParameter);
        }
    }

    private async void PageTapGestureRecognizer_Tapped(object sender, System.EventArgs e)
    {
        await Navigation.PopPopupAsync();
    }

    async void TapGestureRecognizer_Tapped(System.Object sender, TappedEventArgs e)
    {
        if (sender is StackLayout senderControl && senderControl.BindingContext is MenuItem MenuItem)
        {
            //await this.CloseAsync();
            await Navigation.PopPopupAsync();
            MenuItem.Command?.Execute(MenuItem.CommandParameter);
        }
    }    // uncomment code please

    async void TapGestureRecognizer_Tapped_1(System.Object sender, Microsoft.Maui.Controls.TappedEventArgs e)
    {
        if (sender is StackLayout senderControl && senderControl.BindingContext is MenuItem MenuItem)
        {
            //await this.CloseAsync();
            await Navigation.PopPopupAsync();
            MenuItem.Command?.Execute(MenuItem.CommandParameter);
        }
    }    //protected override void OnSizeAllocated(double width, double height)
    //{
    //    base.OnSizeAllocated(width, height);
    //    if (_width != width && _height != height && _width > 0 && _height > 0)
    //    {
    //        this.Close();
    //        return;
    //    }
    //    _width = width;
    //    _height = height;
    //}
}
