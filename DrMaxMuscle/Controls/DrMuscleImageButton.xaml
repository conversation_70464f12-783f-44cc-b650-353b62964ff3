﻿<?xml version="1.0" encoding="utf-8" ?>
<Frame
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="DrMaxMuscle.Controls.DrMuscleImageButton"
    HeightRequest="60"
    BackgroundColor="Transparent"
    Margin="0"
    Padding="1"
    BorderColor="#195377"    
    CornerRadius="0"
    HasShadow="False">
    <Frame.GestureRecognizers>
        <TapGestureRecognizer
            NumberOfTapsRequired="1"
            Tapped="TapGestureRecognizer_Tapped"/>
    </Frame.GestureRecognizers>
    <Grid
        BackgroundColor="White"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand">

        <!--Horizontal Stacklayout (row 0; col 0)-->
        <StackLayout
            
            Margin="-27,0,0,0"
            HorizontalOptions="Center"
            VerticalOptions="FillAndExpand"
            Orientation="Horizontal"
            Spacing="8"
            BackgroundColor="Transparent">

            <!--Image-->
            <Image               
                x:Name="Img"
                Source="{Binding Source}"
                VerticalOptions="FillAndExpand"
                HorizontalOptions="Center"
                WidthRequest="25"
                Margin="0,18"/>

            <!--Text-->
            <Label                
                x:Name="LblText"
                Text="{Binding Text}"
                FontSize="{Binding FontSize}"
                HorizontalOptions="Center"
                VerticalOptions="FillAndExpand"
                HorizontalTextAlignment="Center"
                VerticalTextAlignment="Center"
                TextColor="#195377"
                FontAttributes="Bold"
                BackgroundColor="Transparent"/>
        </StackLayout>
    </Grid>
</Frame>
