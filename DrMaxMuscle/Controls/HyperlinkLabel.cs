﻿using DrMaxMuscle.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace DrMaxMuscle.Controls
{
    public class HyperlinkLabel : Label
    {
        public static readonly BindableProperty RawTextProperty = BindableProperty.Create(
        nameof(RawText),
        typeof(string),
        typeof(HyperlinkLabel),
        propertyChanged: OnRawTextChanged);

        public string RawText
        {
            get => (string)GetValue(RawTextProperty);
            set => SetValue(RawTextProperty, value);
        }

        private static void OnRawTextChanged(BindableObject bindable, object oldValue, object newValue)
        {
            if (bindable is HyperlinkLabel label && newValue is string text)
            {
                label.SetFormattedText(text);
            }
        }

        private void SetFormattedText(string rawText)
        {
            var formattedString = new FormattedString();

            // Regex to match links in the format [Text](URL)
            var regex = new System.Text.RegularExpressions.Regex(@"\[([^]]*)\]\(([^\s^\)]*)\)");
            var matches = regex.Matches(rawText);

            int currentIndex = 0;
            foreach (System.Text.RegularExpressions.Match match in matches)
            {
                // Add text before the link
                if (match.Index > currentIndex)
                {
                    formattedString.Spans.Add(new Span
                    {
                        Text = rawText.Substring(currentIndex, match.Index - currentIndex)
                    });
                }

                // Add the link
                string linkText = match.Groups[1].Value;
                string url = match.Groups[2].Value;

                var linkSpan = new Span
                {
                    Text = linkText,
                    TextColor = Constants.AppThemeConstants.BlueLightColor,
                    TextDecorations = TextDecorations.Underline
                };

                linkSpan.GestureRecognizers.Add(new TapGestureRecognizer
                {
                    Command = new Command(() => OpenUrl(url))
                });

                formattedString.Spans.Add(linkSpan);

                currentIndex = match.Index + match.Length;
            }

            // Add remaining text after the last link
            if (currentIndex < rawText.Length)
            {
                formattedString.Spans.Add(new Span
                {
                    Text = rawText.Substring(currentIndex)
                });
            }

            this.FormattedText = formattedString;
        }

        private async void OpenUrl(string url)
        {
            try
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    if (url.StartsWith("mailto:", StringComparison.OrdinalIgnoreCase))
                    {
                        var uri = new Uri(url);
                        var query = uri.Query;

                        var queryParameters = System.Web.HttpUtility.ParseQueryString(query);
                        string encodedSubject = queryParameters["subject"];
                        string decodedSubject = encodedSubject != null ? Uri.UnescapeDataString(encodedSubject) : "";
                        await HelperClass.SendMail(decodedSubject, "");
                    }
                    else
                    {
                        // Check if the URL uses a scheme supported by SFSafariViewController (http/https)
                        bool isHttpUrl = url.StartsWith("http://", StringComparison.OrdinalIgnoreCase) ||
                                        url.StartsWith("https://", StringComparison.OrdinalIgnoreCase);

                        var launchMode = isHttpUrl ? BrowserLaunchMode.SystemPreferred : BrowserLaunchMode.External;
                        await Browser.OpenAsync(url, launchMode);
                    }
                });
            }
            catch (Exception ex)
            {
                // Handle exception (e.g., invalid URL)
                Console.WriteLine($"Error opening URL: {ex.Message}");
            }
        }
    }

        //    public string RawText
        //    {
        //        get => (string)GetValue(RawTextProperty);
        //        set => SetValue(RawTextProperty, value);
        //    }

        //    public static readonly BindableProperty RawTextProperty =
        //        BindableProperty.Create(nameof(RawText), typeof(string), typeof(HyperlinkLabel), null);

        //    public string GetText(out List<HyperlinkLabelLink> links)
        //    {
        //        links = new List<HyperlinkLabelLink>();
        //        if (RawText == null)
        //            return null;

        //        string pattern = @"\[([^]]*)\]\(([^\s^\)]*)\)";

        //        var linksInText = Regex.Matches(RawText, pattern, RegexOptions.IgnoreCase);

        //        string Text = RawText;

        //        for (int i = 0; i < linksInText.Count; i++)
        //        {
        //            string fullMatch = linksInText[i].Groups[0].Value;
        //            string text = linksInText[i].Groups[1].Value;
        //            string link = linksInText[i].Groups[2].Value;

        //            int start = Text.IndexOf(fullMatch);

        //            if (start > -1)
        //            {
        //                Text =
        //                    $"{Text.Substring(0, start)}{text}{Text.Substring(start + fullMatch.Length)}";
        //                links.Add(new HyperlinkLabelLink(text, link, start));
        //            }
        //        }
        //        return Text;
        //    }
        //}

        //public class HyperlinkLabelLink
        //{
        //    internal HyperlinkLabelLink(string text, string link, int start)
        //    {
        //        Text = text;
        //        Link = link;
        //        Start = start;
        //    }

        //    public string Text { get; }
        //    public string Link { get; }
        //    public int Start { get; }
        //}
    }
