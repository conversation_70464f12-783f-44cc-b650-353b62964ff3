﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace DrMaxMuscle.Controls
{
    public class ExtendedLabel : Label
    {
        
    }

    public class ExtendedLabelLink : Label
    {
        public ExtendedLabelLink()
        {
            // Default constructor
            // Set up any required properties or behaviors here
        }
        protected override void OnPropertyChanged(string propertyName = null)
        {
            base.OnPropertyChanged(propertyName);

            if (propertyName == TextProperty.PropertyName)
            {
                // Access the Text property here
                var textValue = this.Text;
                if (!string.IsNullOrEmpty(textValue))
                {
                    if (Device.RuntimePlatform == Device.iOS)
                    {
                        Device.BeginInvokeOnMainThread(() =>
                        {
                            this.FormattedText = FormatTextWithHyperlinks(textValue);
                        });
                    }

                }
            }
        }
        public static FormattedString FormatTextWithHyperlinks(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;
            var formattedString = new FormattedString();
            try
            {
                var hyperlinkRegex = new Regex(@"(https?://[^\s]+)", RegexOptions.IgnoreCase);
                var matches = hyperlinkRegex.Matches(text);
                int lastIndex = 0;

                foreach (Match match in matches)
                {
                    // Add text before the hyperlink
                    if (match.Index > lastIndex)
                    {
                        formattedString.Spans.Add(new Span
                        {
                            Text = text.Substring(lastIndex, match.Index - lastIndex)
                        });
                    }

                    // Add the hyperlink
                    formattedString.Spans.Add(new Span
                    {
                        Text = match.Value,
                        TextDecorations = TextDecorations.Underline,
                        TextColor = Constants.AppThemeConstants.BlueColor, // Customize color as needed
                        GestureRecognizers =
                {
                    new TapGestureRecognizer
                    {
                        Command = new Command(() => OnHyperlinkTapped(match.Value))
                    }
                }
                    });

                    lastIndex = match.Index + match.Length;
                }

                // Add any remaining text
                if (lastIndex < text.Length)
                {
                    formattedString.Spans.Add(new Span
                    {
                        Text = text.Substring(lastIndex)
                    });
                }
            }
            catch (Exception ex)
            {
                return "";
            }

            return formattedString;
        }

        private static void OnHyperlinkTapped(string url)
        {
            // Open URL in WebView (or external browser)
            Browser.OpenAsync(url, BrowserLaunchMode.SystemPreferred);
            //Device.OpenUri(new Uri(url));
        }
    }
}
